#!/usr/bin/env node

/**
 * Script de build optimisé pour MeetVoice
 * Inclut toutes les optimisations de performance
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Build optimisé MeetVoice - Démarrage...\n');

async function optimizedBuild() {
  try {
    // 1. Nettoyer le dossier dist
    console.log('🧹 Nettoyage du dossier dist...');
    if (fs.existsSync('dist')) {
      fs.rmSync('dist', { recursive: true, force: true });
    }
    
    // 2. Générer le sitemap
    console.log('🗺️ Génération du sitemap...');
    execSync('npm run generate-sitemap', { stdio: 'inherit' });
    
    // 3. Build de production avec optimisations
    console.log('📦 Build de production...');
    execSync('npm run build', { 
      stdio: 'inherit',
      env: {
        ...process.env,
        NODE_ENV: 'production',
        VUE_APP_ENABLE_CDN: 'true'
      }
    });
    
    // 4. Analyser la taille des bundles
    console.log('📊 Analyse de la taille des bundles...');
    analyzeBundleSize();
    
    // 5. Optimiser les images
    console.log('🖼️ Optimisation des images...');
    await optimizeImages();
    
    // 6. Générer le rapport de performance
    console.log('📈 Génération du rapport de performance...');
    generatePerformanceReport();
    
    // 7. Vérifier la compression
    console.log('🗜️ Vérification de la compression...');
    checkCompression();
    
    console.log('\n✅ Build optimisé terminé avec succès!');
    console.log('📁 Fichiers générés dans le dossier dist/');
    console.log('🌐 Prêt pour le déploiement!');
    
  } catch (error) {
    console.error('❌ Erreur lors du build optimisé:', error.message);
    process.exit(1);
  }
}

function analyzeBundleSize() {
  const distPath = path.join(__dirname, '../dist');
  const jsFiles = fs.readdirSync(path.join(distPath, 'js'))
    .filter(file => file.endsWith('.js'))
    .map(file => {
      const filePath = path.join(distPath, 'js', file);
      const stats = fs.statSync(filePath);
      return {
        name: file,
        size: stats.size,
        sizeKB: Math.round(stats.size / 1024)
      };
    })
    .sort((a, b) => b.size - a.size);
  
  console.log('\n📊 Taille des bundles JavaScript:');
  jsFiles.forEach(file => {
    const sizeColor = file.sizeKB > 500 ? '🔴' : file.sizeKB > 200 ? '🟡' : '🟢';
    console.log(`   ${sizeColor} ${file.name}: ${file.sizeKB} KB`);
  });
  
  const totalSize = jsFiles.reduce((sum, file) => sum + file.sizeKB, 0);
  console.log(`   📦 Total JS: ${totalSize} KB`);
  
  // Vérifier les CSS
  const cssPath = path.join(distPath, 'css');
  if (fs.existsSync(cssPath)) {
    const cssFiles = fs.readdirSync(cssPath)
      .filter(file => file.endsWith('.css'))
      .map(file => {
        const filePath = path.join(cssPath, file);
        const stats = fs.statSync(filePath);
        return Math.round(stats.size / 1024);
      });
    
    const totalCSS = cssFiles.reduce((sum, size) => sum + size, 0);
    console.log(`   🎨 Total CSS: ${totalCSS} KB`);
  }
}

async function optimizeImages() {
  const distPath = path.join(__dirname, '../dist');
  const publicPath = path.join(__dirname, '../public');
  
  // Copier et optimiser les images statiques
  const imageDirs = ['img', 'images', 'assets'];
  
  imageDirs.forEach(dir => {
    const srcDir = path.join(publicPath, dir);
    const destDir = path.join(distPath, dir);
    
    if (fs.existsSync(srcDir)) {
      if (!fs.existsSync(destDir)) {
        fs.mkdirSync(destDir, { recursive: true });
      }
      
      const files = fs.readdirSync(srcDir);
      files.forEach(file => {
        const srcFile = path.join(srcDir, file);
        const destFile = path.join(destDir, file);
        
        if (fs.statSync(srcFile).isFile()) {
          fs.copyFileSync(srcFile, destFile);
          console.log(`   📸 Copié: ${file}`);
        }
      });
    }
  });
}

function checkCompression() {
  const distPath = path.join(__dirname, '../dist');
  
  // Vérifier les fichiers .gz et .br
  const checkDir = (dir) => {
    if (!fs.existsSync(dir)) return;
    
    const files = fs.readdirSync(dir);
    let gzipCount = 0;
    let brotliCount = 0;
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        const subResult = checkDir(filePath);
        gzipCount += subResult.gzip;
        brotliCount += subResult.brotli;
      } else {
        if (file.endsWith('.gz')) gzipCount++;
        if (file.endsWith('.br')) brotliCount++;
      }
    });
    
    return { gzip: gzipCount, brotli: brotliCount };
  };
  
  const compression = checkDir(distPath);
  
  console.log('\n🗜️ Fichiers compressés:');
  console.log(`   📦 Gzip: ${compression.gzip} fichiers`);
  console.log(`   🔥 Brotli: ${compression.brotli} fichiers`);
  
  if (compression.gzip === 0 && compression.brotli === 0) {
    console.log('   ⚠️ Aucun fichier compressé trouvé');
    console.log('   💡 Vérifiez la configuration webpack');
  }
}

function generatePerformanceReport() {
  const distPath = path.join(__dirname, '../dist');
  
  // Calculer la taille totale
  const getTotalSize = (dir) => {
    let totalSize = 0;
    const files = fs.readdirSync(dir);
    
    files.forEach(file => {
      const filePath = path.join(dir, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        totalSize += getTotalSize(filePath);
      } else {
        totalSize += stat.size;
      }
    });
    
    return totalSize;
  };
  
  const totalSize = getTotalSize(distPath);
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(2);
  
  const report = {
    buildDate: new Date().toISOString(),
    totalSize: totalSize,
    totalSizeMB: totalSizeMB,
    optimizations: {
      serviceWorker: fs.existsSync(path.join(distPath, 'sw.js')),
      sitemap: fs.existsSync(path.join(distPath, 'sitemap.xml')),
      robotsTxt: fs.existsSync(path.join(distPath, 'robots.txt')),
      manifest: fs.existsSync(path.join(distPath, 'manifest.json'))
    },
    recommendations: []
  };
  
  // Ajouter des recommandations
  if (parseFloat(totalSizeMB) > 5) {
    report.recommendations.push('Considérer le lazy loading pour réduire la taille initiale');
  }
  
  if (!report.optimizations.serviceWorker) {
    report.recommendations.push('Ajouter un Service Worker pour la mise en cache');
  }
  
  // Sauvegarder le rapport
  const reportPath = path.join(distPath, 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log('\n📈 Rapport de performance:');
  console.log(`   📦 Taille totale: ${totalSizeMB} MB`);
  console.log(`   🔧 Service Worker: ${report.optimizations.serviceWorker ? '✅' : '❌'}`);
  console.log(`   🗺️ Sitemap: ${report.optimizations.sitemap ? '✅' : '❌'}`);
  console.log(`   🤖 Robots.txt: ${report.optimizations.robotsTxt ? '✅' : '❌'}`);
  console.log(`   📱 Manifest: ${report.optimizations.manifest ? '✅' : '❌'}`);
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 Recommandations:');
    report.recommendations.forEach(rec => console.log(`   • ${rec}`));
  }
  
  console.log(`\n📄 Rapport détaillé: ${reportPath}`);
}

// Exécuter le build optimisé
if (require.main === module) {
  optimizedBuild();
}

module.exports = { optimizedBuild };
