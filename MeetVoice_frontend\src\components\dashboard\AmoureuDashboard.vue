<template>
  <div class="amoureu-dashboard">
    <div class="dashboard-header">
      <h2>Tableau de bord Amoureux</h2>
      <p>Trouvez l'amour et créez des connexions authentiques</p>
    </div>
    
    <div class="dashboard-content">
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">💕</div>
          <div class="stat-info">
            <h3>{{ matchesCount }}</h3>
            <p>Matchs</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">👀</div>
          <div class="stat-info">
            <h3>{{ viewsCount }}</h3>
            <p>Vues profil</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">⭐</div>
          <div class="stat-info">
            <h3>{{ likesCount }}</h3>
            <p>Likes reçus</p>
          </div>
        </div>
      </div>
      
      <div class="recent-matches">
        <h3>Nouveaux matchs</h3>
        <div class="matches-grid">
          <div v-for="match in recentMatches" :key="match.id" class="match-card">
            <img :src="match.photo" :alt="match.name" class="match-photo">
            <h4>{{ match.name }}</h4>
            <p>{{ match.age }} ans</p>
            <button class="btn-message">Message</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AmoureuDashboard',
  
  data() {
    return {
      matchesCount: 12,
      viewsCount: 89,
      likesCount: 34,
      recentMatches: [
        {
          id: 1,
          name: 'Sophie',
          age: 28,
          photo: '/images/profiles/sophie.jpg'
        },
        {
          id: 2,
          name: 'Emma',
          age: 25,
          photo: '/images/profiles/emma.jpg'
        },
        {
          id: 3,
          name: 'Julie',
          age: 30,
          photo: '/images/profiles/julie.jpg'
        }
      ]
    }
  }
}
</script>

<style scoped>
.amoureu-dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: #fce4ec;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.recent-matches {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-matches h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.matches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.match-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.match-photo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 12px;
}

.match-card h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.match-card p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.btn-message {
  background: #e91e63;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-message:hover {
  background: #c2185b;
}
</style>
