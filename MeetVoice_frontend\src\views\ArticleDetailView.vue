<!-- @format -->

<template>
  <div class="article-detail-page">
    <!-- Breadcrumbs -->
    <Breadcrumbs v-if="!loading && article" :breadcrumbs="breadcrumbs" />

    <!-- Loading State -->
    <div v-if="loading" class="loading-section">
      <div class="container">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner-circle"></div>
          </div>
          <h3 class="loading-title">Chargement de l'article...</h3>
        </div>
      </div>
    </div>

    <!-- Article Content -->
    <div v-else-if="article" class="article-content">
      <!-- Header -->
      <section class="article-header">
        <div class="container">
          <div class="back-button">
            <router-link to="/actualite" class="btn-back">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M19 12H5M12 19L5 12L12 5" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              Retour aux articles
            </router-link>
          </div>

          <div class="article-meta-new">
            <!-- Catégorie avec cadre rose -->
            <div class="category-pink-box">
              {{ article.theme }}
            </div>

            <!-- Informations auteur et stats -->
            <div class="article-author-stats">
              <div class="author-compact">
                <div class="author-avatar-small">
                  {{ article.auteur.full_name.charAt(0).toUpperCase() }}
                </div>
                <span class="author-name-small">{{ article.auteur.full_name }}</span>
              </div>

              <div class="article-meta-stats">
                <span class="meta-date">{{ formatDate(article.date_publication) }}</span>
                <div class="meta-views">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                    <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                    <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                  </svg>
                  {{ article.access_count }}
                </div>
              </div>
            </div>
          </div>
          
          <h1 class="article-title">{{ article.titre }}</h1>
          <p v-if="article.sous_titre" class="article-subtitle">{{ article.sous_titre }}</p>
        </div>
      </section>

      <!-- Article Image -->
      <section v-if="article.photo" class="article-image-section">
        <div class="container">
          <img
            :src="article.photo"
            :alt="`${article.titre} - Image principale de l'article`"
            class="article-main-image"
            loading="eager"
            :width="1200"
            :height="600"
          />


        </div>
      </section>

      <!-- Article Body -->
      <section class="article-body">
        <div class="container">
          <div class="content-layout">
            <!-- Main Content -->
            <div class="main-content">
              <div class="article-description">
                <p class="lead">{{ article.petit_description }}</p>
              </div>

              <div class="article-content-text" v-html="article.contenu"></div>

              <!-- Tags -->
              <div v-if="article && article.tags_list && article.tags_list.length > 0" class="article-tags">
                <h4>Tags :</h4>
                <div class="tags-list">
                  <span v-for="(tag, index) in article.tags_list" :key="index" class="tag">
                    #{{ tag }}
                  </span>
                </div>
              </div>
            </div>

            <!-- Sidebar -->
            <div class="sidebar">
              <div class="sidebar-sticky">
                <!-- Suggestions d'articles -->
                <div class="suggested-articles">
                  <h3 class="sidebar-title">Articles suggérés</h3>
                  <div v-if="suggestedArticles.length > 0" class="suggestions-list">
                    <div
                      v-for="suggestion in suggestedArticles"
                      :key="suggestion.id"
                      class="suggestion-item"
                      @click="goToArticle(suggestion.id)"
                    >
                      <div class="suggestion-image">
                        <img
                          v-if="suggestion.photo"
                          :src="suggestion.photo"
                          :alt="suggestion.titre"
                        />
                        <div v-else class="placeholder-image">
                          <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
                            <circle cx="8.5" cy="8.5" r="1.5" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 15L16 10L5 21" stroke="currentColor" stroke-width="2"/>
                          </svg>
                        </div>
                      </div>
                      <div class="suggestion-content">
                        <span class="suggestion-category">{{ suggestion.theme }}</span>
                        <h4 class="suggestion-title">{{ suggestion.titre }}</h4>
                        <p class="suggestion-description">{{ suggestion.petit_description }}</p>
                        <div class="suggestion-meta">
                          <span class="suggestion-date">{{ formatDate(suggestion.date_publication) }}</span>
                          <span class="suggestion-views">
                            <svg width="14" height="14" viewBox="0 0 24 24" fill="none">
                              <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                              <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                            </svg>
                            {{ suggestion.access_count }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div v-else class="no-suggestions">
                    <p>Aucun article suggéré pour le moment</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Back to Articles -->
      <section class="back-section">
        <div class="container">
          <router-link to="/actualite" class="btn-back-bottom">
            Retour aux articles
          </router-link>
        </div>
      </section>
    </div>

    <!-- Error State -->
    <div v-else class="error-section">
      <div class="container">
        <div class="error-content">
          <h2>Article non trouvé</h2>
          <p>L'article que vous recherchez n'existe pas ou a été supprimé.</p>
          <router-link to="/actualite" class="btn-back">
            Retour aux articles
          </router-link>
        </div>
      </div>
    </div>

    <!-- Bouton Retour en haut -->
    <button
      v-show="showScrollTop"
      @click="scrollToTop"
      class="scroll-to-top"
      aria-label="Retour en haut"
    >
      <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
        <path d="M18 15L12 9L6 15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
      </svg>
    </button>
  </div>
</template>

<script>
import SEOManager from '@/utils/seo.js';
import { extractArticleId, extractArticleSlug, validateSlug, redirectToCanonicalUrl, generateArticleUrl } from '@/utils/slug.js';
import Breadcrumbs from '@/components/Breadcrumbs.vue';

export default {
  name: "ArticleDetailView",
  components: {
    Breadcrumbs
  },
  data() {
    return {
      article: null,
      loading: true,
      suggestedArticles: [],
      showScrollTop: false,
    };
  },

  computed: {
    breadcrumbs() {
      if (!this.article) return [];

      return [
        { text: 'Actualités', to: '/actualite' },
        { text: this.article.theme, to: `/actualite?category=${this.article.theme}` },
        { text: this.article.titre }
      ];
    }
  },

  async mounted() {
    await this.loadArticle();
    await this.loadSuggestedArticles();
    this.setupScrollListener();
    this.updateSEOTags();
  },



  watch: {
    '$route'(to, from) {
      // Recharger l'article quand l'URL change (slug ou ID différent)
      const currentPath = to.path;
      const previousPath = from.path;

      if (currentPath !== previousPath) {
        this.loadArticle();
        this.loadSuggestedArticles();
        // Remonter en haut de la page
        window.scrollTo(0, 0);
      }
    },

    article: {
      handler() {
        this.updateSEOTags();
      },
      deep: true
    }
  },

  beforeUnmount() {
    window.removeEventListener('scroll', this.handleScroll);
    SEOManager.cleanupDynamicTags();
  },
  
  methods: {
    async loadArticle() {
      try {
        this.loading = true;
        const articleId = extractArticleId(this.$route.params);
        const articleSlug = extractArticleSlug(this.$route.params);

        let apiUrl;

        // Si on a un ID, utiliser l'endpoint par ID
        if (articleId) {
          apiUrl = `http://127.0.0.1:8000/actualite/api/articles/${articleId}/`;
        }
        // Sinon, si on a un slug, utiliser l'endpoint par slug
        else if (articleSlug) {
          apiUrl = `http://127.0.0.1:8000/actualite/api/articles/by-slug/${articleSlug}/`;
        }
        else {
          throw new Error('Aucun identifiant d\'article fourni');
        }

        // Charger l'article depuis l'API
        const response = await fetch(apiUrl, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          mode: 'cors',
        });
        
        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
        
        this.article = await response.json();
        console.log("Article chargé:", this.article);

        // Valider le slug dans l'URL
        const routeSlug = extractArticleSlug(this.$route.params);
        if (routeSlug && !validateSlug(routeSlug, this.article)) {
          console.warn('Slug incorrect dans l\'URL, redirection vers l\'URL canonique');
        }

        // Rediriger vers l'URL canonique si nécessaire
        redirectToCanonicalUrl(this.$route, this.article, this.$router);

        // Incrémenter le compteur de vues (utiliser l'ID de l'article chargé)
        await this.incrementViews(this.article.id);
        
      } catch (error) {
        console.error("Erreur lors du chargement de l'article:", error);
        this.article = null;
      } finally {
        this.loading = false;
      }
    },
    
    async incrementViews(articleId) {
      try {
        await fetch(
          `http://127.0.0.1:8000/actualite/api/articles/${articleId}/increment_views/`,
          {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            mode: 'cors',
          }
        );
      } catch (error) {
        console.error("Erreur lors de l'incrémentation des vues:", error);
      }
    },
    
    async loadSuggestedArticles() {
      try {
        const response = await fetch(
          'http://127.0.0.1:8000/actualite/api/articles/published/',
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            mode: 'cors',
          }
        );

        if (response.ok) {
          const data = await response.json();
          const allArticles = data.results || data;

          // Filtrer pour exclure l'article actuel et prendre 4 suggestions
          const currentArticleId = this.article ? this.article.id : null;
          this.suggestedArticles = allArticles
            .filter(article => article.id !== currentArticleId)
            .slice(0, 4);
        }
      } catch (error) {
        console.error("Erreur lors du chargement des suggestions:", error);
      }
    },

    updateSEOTags() {
      if (!this.article) return;
      SEOManager.setArticleSEO(this.article);
    },

    setupScrollListener() {
      window.addEventListener('scroll', this.handleScroll);
    },

    handleScroll() {
      this.showScrollTop = window.pageYOffset > 300;
    },

    scrollToTop() {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    },

    async goToArticle(articleId) {
      console.log("Navigation vers l'article:", articleId);
      try {
        // Trouver l'article dans les suggestions pour obtenir toutes ses données
        const article = this.suggestedArticles.find(a => a.id === articleId);

        if (article) {
          // Utiliser generateArticleUrl pour créer l'URL avec slug
          const articleUrl = generateArticleUrl(article);
          await this.$router.push(articleUrl);
        } else {
          // Fallback vers l'ancienne méthode si l'article n'est pas trouvé
          await this.$router.push(`/article/${articleId}`);
        }
      } catch (error) {
        console.error("Erreur lors de la navigation:", error);
      }
    },

    getCategoryClass(category) {
      return category === "recruteur" ? "category-recruteur" : "category-culture";
    },

    formatDate(dateString) {
      const options = {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      };
      return new Date(dateString).toLocaleDateString('fr-FR', options);
    },
  },
};
</script>

<style scoped>
/* Variables */
:root {
  --primary-color: #667eea;
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --dark-color: #2c3e50;
  --gray-light: #f8fafc;
  --gray-medium: #64748b;
  --gray-dark: #334155;
  --border-radius: 16px;
  --shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Container */
.article-detail-page {
  min-height: 100vh;
  background: var(--gray-light);
  padding-top: 0; /* Supprimé car .main-content a déjà padding-top: 80px */
}

/* Loading */
.loading-section {
  padding: 80px 0;
  text-align: center;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  margin: 0 auto 30px;
}

.spinner-circle {
  width: 100%;
  height: 100%;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-title {
  font-size: 1.5rem;
  color: var(--dark-color);
}

/* Article Header */
.article-header {
  background: white;
  padding: 30px 0;
  box-shadow: var(--shadow);
}

.back-button {
  margin-bottom: 20px;
}

.btn-back {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

.btn-back:hover {
  color: var(--secondary-color);
  transform: translateX(-4px);
}

/* Nouvelle structure meta simplifiée */
.article-meta-new {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

/* Catégorie avec cadre rose - STYLE FORCÉ */
.category-pink-box {
  background: linear-gradient(135deg, #f093fb, #f5576c) !important;
  color: white !important;
  padding: 10px 18px !important;
  border-radius: 10px !important;
  font-size: 0.9rem !important;
  font-weight: 800 !important;
  text-transform: capitalize !important;
  border: 4px solid #ff69b4 !important;
  box-shadow: 0 6px 16px rgba(240, 147, 251, 0.4) !important;
  display: inline-block !important;
  min-width: 90px !important;
  text-align: center !important;
  position: relative !important;
  z-index: 10 !important;
}

.article-date {
  color: var(--gray-medium);
  font-size: 0.9rem;
  font-weight: 500;
}

.article-views {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--gray-medium);
  font-size: 0.9rem;
  font-weight: 500;
}

/* Auteur et stats compacts */
.article-author-stats {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.author-compact {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-avatar-small {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.author-name-small {
  font-weight: 600;
  color: var(--dark-color);
  font-size: 0.9rem;
}

.article-meta-stats {
  display: flex;
  align-items: center;
  gap: 16px;
}

.meta-date {
  color: var(--gray-medium);
  font-size: 0.9rem;
  font-weight: 500;
}

.meta-views {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--gray-medium);
  font-size: 0.9rem;
  font-weight: 500;
}

.meta-views svg {
  color: var(--primary-color);
}

.article-views svg {
  color: var(--primary-color);
}

.theme-badge {
  padding: 6px 16px;
  background: var(--gray-light);
  color: var(--gray-dark);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 600;
}

.date {
  color: var(--gray-medium);
  font-size: 0.9rem;
}

.article-title {
  font-size: 3rem;
  font-weight: 800;
  color: var(--dark-color);
  margin-bottom: 12px;
  line-height: 1.2;
}

.article-subtitle {
  font-size: 1.5rem;
  color: var(--gray-medium);
  margin-bottom: 20px;
  font-weight: 500;
}

/* Article Image */
.article-image-section {
  padding: 5px 0;
}

.article-main-image {
  width: 100%;
  max-height: 500px;
  object-fit: cover;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
  margin-bottom: 20px;
}

/* Meta sous l'image */
.image-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
  border-top: 1px solid var(--gray-light);
  margin-top: 15px;
}

.image-author {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.2rem;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.author-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.author-name {
  color: var(--dark-color);
  font-weight: 700;
  font-size: 1rem;
}

.author-role {
  color: var(--gray-medium);
  font-size: 0.85rem;
  font-weight: 500;
}

.image-stats {
  display: flex;
  gap: 24px;
  align-items: center;
}

.stat-date,
.stat-views {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--gray-medium);
  font-size: 0.9rem;
  font-weight: 500;
}

.stat-date {
  color: var(--dark-color);
  font-weight: 600;
}

.stat-views svg {
  color: var(--primary-color);
}

.stat-views span {
  color: var(--dark-color);
  font-weight: 600;
}

/* Article Body */
.article-body {
  padding: 10px 0 20px 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 60px;
  max-width: 1200px;
  margin: 0 auto;
}

.main-content {
  min-width: 0;
  background: white;
  padding: 60px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow);
}

/* Sidebar */
.sidebar {
  position: relative;
}

.sidebar-sticky {
  position: sticky;
  top: 100px;
  max-height: calc(100vh - 120px);
  overflow-y: auto;
}

.sidebar-title {
  color: var(--dark-color);
  font-size: 1.4rem;
  font-weight: 800;
  margin-bottom: 24px;
  padding-bottom: 12px;
  border-bottom: 3px solid #ff69b4;
}

/* Suggestions d'articles */
.suggested-articles {
  background: var(--gray-light);
  border-radius: var(--border-radius);
  padding: 24px;
  border: 2px solid #ff69b4;
}

.suggestions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.suggestion-item {
  background: white;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 105, 180, 0.2);
  position: relative;
}

.suggestion-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(255, 105, 180, 0.25);
  border-color: #ff69b4;
  background: linear-gradient(135deg, #ffffff, #fef7ff);
}

.suggestion-item:active {
  transform: translateY(-1px);
}

.suggestion-image {
  width: 100%;
  height: 120px;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 12px;
  background: var(--gray-light);
}

.suggestion-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-image {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-medium);
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
}

.suggestion-category {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: capitalize;
}

.suggestion-title {
  color: var(--dark-color);
  font-size: 1rem;
  font-weight: 700;
  margin: 8px 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.suggestion-description {
  color: var(--gray-medium);
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 8px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.suggestion-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.75rem;
  color: var(--gray-medium);
}

.suggestion-views {
  display: flex;
  align-items: center;
  gap: 4px;
}

.no-suggestions {
  text-align: center;
  color: var(--gray-medium);
  font-style: italic;
}

.article-description .lead {
  font-size: 1.3rem;
  color: var(--gray-dark);
  line-height: 1.6;
  margin-bottom: 40px;
  font-weight: 500;
}

.article-content-text {
  font-size: 1.1rem;
  line-height: 1.8;
  color: var(--dark-color);
}

.article-content-text h1,
.article-content-text h2,
.article-content-text h3 {
  color: var(--dark-color);
  margin-top: 40px;
  margin-bottom: 20px;
}

.article-content-text p {
  margin-bottom: 20px;
}

.article-tags {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 2px solid var(--gray-light);
}

.article-tags h4 {
  color: var(--dark-color);
  margin-bottom: 16px;
}

.tags-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.tag {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
}

/* Back Section */
.back-section {
  padding: 40px 0;
  text-align: center;
}

.btn-back-bottom {
  display: inline-block;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  padding: 16px 32px;
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 700;
  transition: var(--transition);
}

.btn-back-bottom:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Error Section */
.error-section {
  padding: 80px 0;
  text-align: center;
}

.error-content h2 {
  color: var(--dark-color);
  margin-bottom: 16px;
}

.error-content p {
  color: var(--gray-medium);
  margin-bottom: 30px;
}

/* Responsive */
@media (max-width: 768px) {
  .article-detail-page {
    padding-top: 0; /* Supprimé car .main-content a déjà padding-top: 80px */
  }
  
  .article-title {
    font-size: 2rem;
  }
  
  .article-subtitle {
    font-size: 1.2rem;
  }
  
  .content-wrapper {
    padding: 30px 20px;
  }
}

/* Bouton Retour en haut */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(240, 147, 251, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1000;
  border: 2px solid #ff69b4;
}

.scroll-to-top:hover {
  transform: translateY(-3px) scale(1.1);
  box-shadow: 0 8px 30px rgba(240, 147, 251, 0.6);
}

.scroll-to-top:active {
  transform: translateY(-1px) scale(1.05);
}

/* Responsive pour sidebar */
@media (max-width: 1024px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .sidebar-sticky {
    position: static;
    max-height: none;
  }

  .suggested-articles {
    margin-top: 40px;
  }
}

@media (max-width: 768px) {
  .scroll-to-top {
    bottom: 20px;
    right: 20px;
    width: 45px;
    height: 45px;
  }

  .main-content {
    padding: 30px 20px;
  }

  .suggested-articles {
    padding: 20px;
  }

  .suggestion-item {
    padding: 12px;
  }

  .suggestion-image {
    height: 100px;
  }

  /* Responsive pour meta image */
  .image-meta {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .image-stats {
    gap: 16px;
  }

  .author-avatar {
    width: 45px;
    height: 45px;
    font-size: 1.1rem;
  }

  .stat-date,
  .stat-views {
    font-size: 0.85rem;
  }
}
</style>
