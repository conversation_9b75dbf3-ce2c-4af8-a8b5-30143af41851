import { loadStripe } from '@stripe/stripe-js'

class StripeService {
  constructor() {
    this.stripe = null
    this.elements = null
    this.card = null
    this.isInitialized = false
    this.publicKey = process.env.VUE_APP_STRIPE_PUBLIC_KEY
  }

  /**
   * Initialiser Stripe avec la clé publique
   */
  async initialize() {
    if (this.isInitialized) {
      return this.stripe
    }

    if (!this.publicKey) {
      throw new Error('Clé publique Stripe manquante. Vérifiez VUE_APP_STRIPE_PUBLIC_KEY')
    }

    try {
      this.stripe = await loadStripe(this.publicKey)
      this.isInitialized = true
      console.log('✅ Stripe initialisé avec succès')
      return this.stripe
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de Stripe:', error)
      throw error
    }
  }

  /**
   * Créer les éléments Stripe pour les formulaires de carte
   */
  createElements(options = {}) {
    if (!this.stripe) {
      throw new Error('Stripe non initialisé. Appelez initialize() d\'abord.')
    }

    const defaultOptions = {
      fonts: [
        {
          cssSrc: 'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap'
        }
      ],
      locale: 'fr'
    }

    this.elements = this.stripe.elements({ ...defaultOptions, ...options })
    return this.elements
  }

  /**
   * Créer un élément de carte de crédit
   */
  createCardElement(container, options = {}) {
    if (!this.elements) {
      this.createElements()
    }

    const defaultStyle = {
      base: {
        fontSize: '16px',
        color: '#424770',
        fontFamily: 'Inter, system-ui, sans-serif',
        fontSmoothing: 'antialiased',
        '::placeholder': {
          color: '#aab7c4'
        }
      },
      invalid: {
        color: '#9e2146',
        iconColor: '#9e2146'
      }
    }

    const cardOptions = {
      style: defaultStyle,
      hidePostalCode: false,
      ...options
    }

    this.card = this.elements.create('card', cardOptions)
    
    if (container) {
      this.card.mount(container)
    }

    return this.card
  }

  /**
   * Créer un Payment Intent
   */
  async createPaymentIntent(amount, currency = 'eur', metadata = {}) {
    try {
      const response = await fetch('/api/abonnement/create-payment-intent/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          amount: Math.round(amount * 100), // Convertir en centimes
          currency,
          metadata
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la création du Payment Intent')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Erreur Payment Intent:', error)
      throw error
    }
  }

  /**
   * Confirmer un paiement avec une carte
   */
  async confirmCardPayment(clientSecret, cardElement = null, billingDetails = {}) {
    if (!this.stripe) {
      throw new Error('Stripe non initialisé')
    }

    const card = cardElement || this.card
    if (!card) {
      throw new Error('Élément de carte non trouvé')
    }

    try {
      const result = await this.stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: card,
          billing_details: billingDetails
        }
      })

      if (result.error) {
        throw new Error(result.error.message)
      }

      return result.paymentIntent
    } catch (error) {
      console.error('Erreur confirmation paiement:', error)
      throw error
    }
  }

  /**
   * Créer une session de checkout
   */
  async createCheckoutSession(items, options = {}) {
    try {
      const response = await fetch('/api/abonnement/checkout/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          items,
          success_url: options.successUrl || `${window.location.origin}/payment/success`,
          cancel_url: options.cancelUrl || `${window.location.origin}/payment/cancel`,
          ...options
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la création de la session checkout')
      }

      const session = await response.json()
      return session
    } catch (error) {
      console.error('Erreur checkout session:', error)
      throw error
    }
  }

  /**
   * Rediriger vers Stripe Checkout
   */
  async redirectToCheckout(sessionId) {
    if (!this.stripe) {
      throw new Error('Stripe non initialisé')
    }

    try {
      const result = await this.stripe.redirectToCheckout({
        sessionId: sessionId
      })

      if (result.error) {
        throw new Error(result.error.message)
      }

      return result
    } catch (error) {
      console.error('Erreur redirection checkout:', error)
      throw error
    }
  }

  /**
   * Créer un Setup Intent pour sauvegarder une méthode de paiement
   */
  async createSetupIntent(customerId = null) {
    try {
      const response = await fetch('/api/abonnement/create-setup-intent/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          customer_id: customerId
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la création du Setup Intent')
      }

      const data = await response.json()
      return data
    } catch (error) {
      console.error('Erreur Setup Intent:', error)
      throw error
    }
  }

  /**
   * Confirmer un Setup Intent
   */
  async confirmCardSetup(clientSecret, cardElement = null, billingDetails = {}) {
    if (!this.stripe) {
      throw new Error('Stripe non initialisé')
    }

    const card = cardElement || this.card
    if (!card) {
      throw new Error('Élément de carte non trouvé')
    }

    try {
      const result = await this.stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: card,
          billing_details: billingDetails
        }
      })

      if (result.error) {
        throw new Error(result.error.message)
      }

      return result.setupIntent
    } catch (error) {
      console.error('Erreur confirmation setup:', error)
      throw error
    }
  }

  /**
   * Récupérer les méthodes de paiement sauvegardées
   */
  async getPaymentMethods() {
    try {
      const response = await fetch('/api/abonnement/payment-methods/', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des méthodes de paiement')
      }

      const data = await response.json()
      return data.payment_methods || []
    } catch (error) {
      console.error('Erreur récupération méthodes de paiement:', error)
      throw error
    }
  }

  /**
   * Supprimer une méthode de paiement
   */
  async deletePaymentMethod(paymentMethodId) {
    try {
      const response = await fetch(`/api/abonnement/payment-methods/${paymentMethodId}/`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de la méthode de paiement')
      }

      return true
    } catch (error) {
      console.error('Erreur suppression méthode de paiement:', error)
      throw error
    }
  }

  /**
   * Valider les informations de carte
   */
  validateCard(cardElement = null) {
    const card = cardElement || this.card
    if (!card) {
      return { isValid: false, error: 'Élément de carte non trouvé' }
    }

    // Cette méthode sera appelée lors des événements de changement
    // La validation réelle se fait côté Stripe
    return { isValid: true }
  }

  /**
   * Formater les erreurs Stripe en français
   */
  formatError(error) {
    const errorMessages = {
      'card_declined': 'Votre carte a été refusée.',
      'expired_card': 'Votre carte a expiré.',
      'incorrect_cvc': 'Le code de sécurité de votre carte est incorrect.',
      'incorrect_number': 'Le numéro de votre carte est incorrect.',
      'invalid_cvc': 'Le code de sécurité de votre carte est invalide.',
      'invalid_expiry_month': 'Le mois d\'expiration de votre carte est invalide.',
      'invalid_expiry_year': 'L\'année d\'expiration de votre carte est invalide.',
      'invalid_number': 'Le numéro de votre carte est invalide.',
      'processing_error': 'Une erreur s\'est produite lors du traitement de votre carte.',
      'insufficient_funds': 'Votre carte n\'a pas suffisamment de fonds.',
      'generic_decline': 'Votre carte a été refusée.',
      'lost_card': 'Votre carte a été signalée comme perdue.',
      'stolen_card': 'Votre carte a été signalée comme volée.',
      'pickup_card': 'Votre carte ne peut pas être utilisée.',
      'restricted_card': 'Votre carte a des restrictions.',
      'security_violation': 'Votre paiement a été refusé pour des raisons de sécurité.',
      'service_not_allowed': 'Votre carte ne supporte pas ce type de transaction.',
      'stop_payment_order': 'Votre carte a un ordre d\'arrêt de paiement.',
      'testmode_decline': 'Votre carte a été refusée en mode test.',
      'withdrawal_count_limit_exceeded': 'Vous avez dépassé la limite de transactions.'
    }

    return errorMessages[error.code] || error.message || 'Une erreur de paiement s\'est produite.'
  }

  /**
   * Nettoyer les ressources
   */
  destroy() {
    if (this.card) {
      this.card.destroy()
      this.card = null
    }
    
    this.elements = null
    this.stripe = null
    this.isInitialized = false
  }
}

// Instance singleton
const stripeService = new StripeService()

export default stripeService
