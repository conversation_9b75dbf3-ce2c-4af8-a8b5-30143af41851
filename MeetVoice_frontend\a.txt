1. respecter les bonnes pratiques de programation
2. faire un html acceptable en SEO
3. améliorer la performance
4. améliorer l'expérience utilisateur
5. respecter le parten design des couleurs du site:
{
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}
6. prendre les variables du back end (ne pas les inventers)
7. utiliser le store et le local storage le plus possible (évite de faire des appels api)
8. utiliser les composants le plus possible
9. un ui/ux agréable
10. ce site est un site de rencontre (on va sortir, meetic, wyddle)
11 il nous faut un interface inpisrée de meetic.com
12 il nous faut une interface inspirée de onvasortir.com
13 il nous faut une interface inspirée de wyydle.com
14 eviter le plus possible les divs préférés (
    <header>	En-tête de page ou de section
<nav>	Menu de navigation
<main>	Contenu principal unique de la page
<section>	Regroupe un ensemble logique de contenu
<article>	Contenu autonome (ex : article de blog, fiche produit…)
<aside>	Contenu secondaire (ex : sidebar, pub, liens connexes…)
<footer>	Pied de page ou de section
)