<template>
  <div class="create-party-view">
    <div class="container">
      <h1>Créer une soirée libertine</h1>
      <p>Organisez votre événement privé</p>
      
      <form @submit.prevent="createParty" class="party-form">
        <div class="form-group">
          <label>Titre de la soirée</label>
          <input v-model="party.title" type="text" required>
        </div>
        
        <div class="form-group">
          <label>Description</label>
          <textarea v-model="party.description" rows="4" required></textarea>
        </div>
        
        <div class="form-row">
          <div class="form-group">
            <label>Date</label>
            <input v-model="party.date" type="datetime-local" required>
          </div>
          
          <div class="form-group">
            <label>Lieu</label>
            <input v-model="party.location" type="text" required>
          </div>
        </div>
        
        <div class="form-actions">
          <button type="submit" class="btn-create">Créer la soirée</button>
          <router-link to="/libertin/parties" class="btn-cancel">Annuler</router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CreatePartyView',
  data() {
    return {
      party: {
        title: '',
        description: '',
        date: '',
        location: ''
      }
    }
  },
  methods: {
    createParty() {
      console.log('Création de soirée:', this.party)
      this.$router.push('/libertin/parties')
    }
  }
}
</script>

<style scoped>
.create-party-view {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

.party-form {
  background: white;
  padding: 32px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.form-group {
  margin-bottom: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.form-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
}

.btn-create {
  background: #9c27b0;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
}

.btn-cancel {
  background: #f8f9fa;
  color: #333;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 6px;
  border: 1px solid #ddd;
}
</style>
