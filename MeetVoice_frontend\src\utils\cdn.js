/**
 * Configuration CDN pour MeetVoice
 * Optimise le chargement des assets via CDN
 */

// Configuration des CDNs
const CDN_CONFIG = {
  // CDN principal (vous pouvez utiliser Cloudflare, AWS CloudFront, etc.)
  primary: process.env.VUE_APP_CDN_URL || '',
  
  // CDN de fallback
  fallback: process.env.VUE_APP_CDN_FALLBACK || '',
  
  // CDNs publics pour les librairies
  libraries: {
    bootstrap: 'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0',
    fontawesome: 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0',
    vue: 'https://unpkg.com/vue@3'
  }
};

export class CDNManager {
  
  /**
   * Optimiser l'URL d'une image
   */
  static optimizeImageUrl(url, options = {}) {
    if (!url || url.startsWith('data:')) return url;
    
    const {
      width = null,
      height = null,
      quality = 85,
      format = 'auto',
      lazy = true
    } = options;
    
    // Si c'est déjà une URL complète, la retourner
    if (url.startsWith('http')) {
      return this.addImageOptimization(url, { width, height, quality, format });
    }
    
    // Construire l'URL avec CDN
    const cdnUrl = CDN_CONFIG.primary || window.location.origin;
    const optimizedUrl = `${cdnUrl}${url}`;
    
    return this.addImageOptimization(optimizedUrl, { width, height, quality, format });
  }
  
  /**
   * Ajouter des paramètres d'optimisation d'image
   */
  static addImageOptimization(url, options) {
    const { width, height, quality, format } = options;
    const urlObj = new URL(url);
    
    // Paramètres pour Cloudflare Images ou services similaires
    if (width) urlObj.searchParams.set('w', width);
    if (height) urlObj.searchParams.set('h', height);
    if (quality) urlObj.searchParams.set('q', quality);
    if (format && format !== 'auto') urlObj.searchParams.set('f', format);
    
    return urlObj.toString();
  }
  
  /**
   * Précharger les ressources critiques
   */
  static preloadCriticalAssets() {
    const criticalAssets = [
      '/static/css/app.css',
      '/static/js/app.js',
      '/favicon.ico'
    ];
    
    criticalAssets.forEach(asset => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.href = this.getCDNUrl(asset);
      
      if (asset.endsWith('.css')) {
        link.as = 'style';
      } else if (asset.endsWith('.js')) {
        link.as = 'script';
      } else if (asset.includes('font')) {
        link.as = 'font';
        link.crossOrigin = 'anonymous';
      }
      
      document.head.appendChild(link);
    });
  }
  
  /**
   * Obtenir l'URL CDN pour un asset
   */
  static getCDNUrl(path) {
    if (path.startsWith('http')) return path;
    
    const cdnUrl = CDN_CONFIG.primary || window.location.origin;
    return `${cdnUrl}${path}`;
  }
  
  /**
   * Charger une librairie depuis un CDN
   */
  static async loadLibraryFromCDN(library, version = 'latest') {
    const cdnUrls = {
      'bootstrap-css': `${CDN_CONFIG.libraries.bootstrap}/dist/css/bootstrap.min.css`,
      'bootstrap-js': `${CDN_CONFIG.libraries.bootstrap}/dist/js/bootstrap.bundle.min.js`,
      'fontawesome': `${CDN_CONFIG.libraries.fontawesome}/css/all.min.css`,
      'vue': `${CDN_CONFIG.libraries.vue}/dist/vue.global.prod.js`
    };
    
    const url = cdnUrls[library];
    if (!url) {
      console.warn(`Librairie ${library} non trouvée dans la configuration CDN`);
      return false;
    }
    
    try {
      if (library.includes('css')) {
        return this.loadCSS(url);
      } else {
        return this.loadScript(url);
      }
    } catch (error) {
      console.error(`Erreur lors du chargement de ${library}:`, error);
      return false;
    }
  }
  
  /**
   * Charger un fichier CSS
   */
  static loadCSS(url) {
    return new Promise((resolve, reject) => {
      const link = document.createElement('link');
      link.rel = 'stylesheet';
      link.href = url;
      link.onload = () => resolve(true);
      link.onerror = () => reject(new Error(`Impossible de charger ${url}`));
      document.head.appendChild(link);
    });
  }
  
  /**
   * Charger un script
   */
  static loadScript(url) {
    return new Promise((resolve, reject) => {
      const script = document.createElement('script');
      script.src = url;
      script.onload = () => resolve(true);
      script.onerror = () => reject(new Error(`Impossible de charger ${url}`));
      document.head.appendChild(script);
    });
  }
  
  /**
   * Optimiser les images existantes dans le DOM
   */
  static optimizeExistingImages() {
    const images = document.querySelectorAll('img[data-optimize]');
    
    images.forEach(img => {
      const originalSrc = img.src;
      const width = img.getAttribute('data-width') || img.width;
      const height = img.getAttribute('data-height') || img.height;
      
      const optimizedSrc = this.optimizeImageUrl(originalSrc, {
        width: width || null,
        height: height || null,
        quality: 85,
        format: 'auto'
      });
      
      if (optimizedSrc !== originalSrc) {
        img.src = optimizedSrc;
      }
    });
  }
  
  /**
   * Configurer le lazy loading intelligent
   */
  static setupIntelligentLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            const src = img.getAttribute('data-src');
            
            if (src) {
              img.src = this.optimizeImageUrl(src, {
                width: img.getAttribute('data-width'),
                height: img.getAttribute('data-height')
              });
              img.removeAttribute('data-src');
              observer.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });
      
      // Observer toutes les images avec data-src
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }
  
  /**
   * Mesurer les performances CDN
   */
  static async measureCDNPerformance() {
    const testUrl = this.getCDNUrl('/favicon.ico');
    const startTime = performance.now();
    
    try {
      const response = await fetch(testUrl, { method: 'HEAD' });
      const endTime = performance.now();
      const latency = endTime - startTime;
      
      console.log(`📊 CDN Performance: ${latency.toFixed(2)}ms`);
      
      return {
        latency,
        status: response.status,
        cdn: CDN_CONFIG.primary ? 'CDN' : 'Origin'
      };
    } catch (error) {
      console.error('Erreur mesure performance CDN:', error);
      return null;
    }
  }
}

// Auto-initialisation
if (typeof window !== 'undefined') {
  // Précharger les assets critiques
  document.addEventListener('DOMContentLoaded', () => {
    CDNManager.preloadCriticalAssets();
    CDNManager.setupIntelligentLazyLoading();
    
    // Optimiser les images après un délai
    setTimeout(() => {
      CDNManager.optimizeExistingImages();
    }, 1000);
  });
}

export default CDNManager;
