/**
 * Utilitaires pour la génération de slugs SEO-friendly
 */

/**
 * Génère un slug SEO-friendly à partir d'un titre
 * @param {string} title - Le titre à convertir en slug
 * @returns {string} Le slug généré
 */
export function generateSlug(title) {
  if (!title) return '';
  
  return title
    .toLowerCase()
    .normalize('NFD') // Décomposer les caractères accentués
    .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
    .replace(/[^a-z0-9\s-]/g, '') // Supprimer les caractères spéciaux
    .replace(/\s+/g, '-') // Remplacer les espaces par des tirets
    .replace(/-+/g, '-') // Supprimer les tirets multiples
    .replace(/^-+|-+$/g, '') // Supprimer les tirets en début/fin
    .substring(0, 100); // Limiter la longueur
}

/**
 * Génère une URL d'article avec slug (sans ID)
 * @param {Object} article - L'objet article
 * @param {boolean} includeId - Inclure l'ID dans l'URL (pour compatibilité)
 * @returns {string} L'URL avec slug
 */
export function generateArticleUrl(article, includeId = false) {
  if (!article) return '/actualite';

  // Utiliser le slug du backend s'il existe
  if (article.slug) {
    return includeId ? `/article/${article.slug}/${article.id}` : `/article/${article.slug}`;
  }

  // Fallback: générer un slug depuis le titre
  const slug = generateSlug(article.titre);
  if (slug) {
    return includeId ? `/article/${slug}/${article.id}` : `/article/${slug}`;
  }

  // Fallback final vers l'ancienne URL avec ID
  return `/article/${article.id}`;
}

/**
 * Extrait l'ID d'article depuis les paramètres de route
 * @param {Object} routeParams - Les paramètres de la route
 * @returns {string|number|null} L'ID de l'article ou null si pas d'ID
 */
export function extractArticleId(routeParams) {
  // Si on a un slug, l'ID est dans le deuxième paramètre (optionnel)
  if (routeParams.slug) {
    return routeParams.id || null;
  }

  // Sinon, l'ID est directement dans le premier paramètre
  return routeParams.id;
}

/**
 * Extrait le slug depuis les paramètres de route
 * @param {Object} routeParams - Les paramètres de la route
 * @returns {string|null} Le slug de l'article
 */
export function extractArticleSlug(routeParams) {
  return routeParams.slug || null;
}

/**
 * Valide qu'un slug correspond au slug de l'article
 * @param {string} routeSlug - Le slug dans l'URL
 * @param {Object} article - L'objet article avec son slug
 * @returns {boolean} True si le slug correspond
 */
export function validateSlug(routeSlug, article) {
  if (!routeSlug || !article) return false;

  // Vérifier d'abord avec le slug du backend
  if (article.slug) {
    return routeSlug === article.slug;
  }

  // Fallback: vérifier avec un slug généré depuis le titre
  const expectedSlug = generateSlug(article.titre);
  return routeSlug === expectedSlug;
}

/**
 * Redirige vers l'URL canonique si nécessaire
 * @param {Object} route - La route actuelle
 * @param {Object} article - L'article chargé
 * @param {Object} router - L'instance du router Vue
 */
export function redirectToCanonicalUrl(route, article, router) {
  if (!article || !router) return;
  
  const currentPath = route.path;
  const canonicalUrl = generateArticleUrl(article);
  
  // Si l'URL actuelle n'est pas l'URL canonique, rediriger
  if (currentPath !== canonicalUrl) {
    router.replace(canonicalUrl);
  }
}

export default {
  generateSlug,
  generateArticleUrl,
  extractArticleId,
  extractArticleSlug,
  validateSlug,
  redirectToCanonicalUrl
};
