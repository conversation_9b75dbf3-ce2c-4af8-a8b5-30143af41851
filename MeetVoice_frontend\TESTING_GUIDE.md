# Guide de test - Nouvelles fonctionnalités MeetVoice

## 🚀 Accès aux tests

Le serveur de développement fonctionne sur : **http://localhost:8082**

### Pages de test disponibles :

1. **Configuration Audio/Vidéo** : http://localhost:8082/media-config
2. **Page de test complète** : http://localhost:8082/test-features
3. **Test vocal simple** : http://localhost:8082/voice-test
4. **Inscription améliorée** : http://localhost:8082/register
5. **Connexion modifiée** : http://localhost:8082/login

## 🧪 Tests à effectuer

### 1. Configuration Audio/Vidéo (http://localhost:8082/media-config)

Cette page permet de configurer et tester tous vos périphériques audio et vidéo :

#### Configuration Audio 🎤
- **Sélection du microphone** : Choisissez votre microphone préféré
- **Test du niveau audio** : Observez le niveau audio en temps réel
- **Sélection des haut-parleurs** : Choisissez vos haut-parleurs
- **Test des haut-parleurs** : Écoutez un son de test

#### Configuration Vidéo 📹
- **Sélection de la caméra** : Choisissez votre caméra
- **Qualité vidéo** : Sélectionnez la résolution (Basse/Moyenne/Haute)
- **Aperçu en temps réel** : Visualisez le flux vidéo
- **Capture d'image** : Prenez une photo de test

#### Tests Avancés 🧪
- **Test de reconnaissance vocale** : Vérifiez que votre micro fonctionne avec la reconnaissance
- **Test de synthèse vocale** : Vérifiez que vos haut-parleurs fonctionnent
- **Test d'enregistrement** : Enregistrez et écoutez un échantillon audio
- **Résumé de configuration** : Visualisez votre configuration actuelle

### 2. Page de test des fonctionnalités (http://localhost:8082/test-features)

Cette page permet de tester individuellement chaque nouvelle fonctionnalité :

#### Test de géolocalisation 🌍
- Cliquez sur l'onglet "🌍 Géolocalisation"
- Tapez le nom d'une ville dans le champ de recherche
- Vérifiez que les suggestions apparaissent
- Sélectionnez une ville et observez les résultats
- Testez la détection automatique (bouton 🎯)

#### Test d'interview vocal 🎤
- Cliquez sur l'onglet "🎤 Interview vocal"
- **Prérequis** : Utilisez Chrome, Safari ou Edge (Firefox non supporté)
- Cliquez sur "🎙️ Commencer l'interview vocal"
- Autorisez l'accès au microphone
- Répondez aux questions en parlant clairement
- Observez la transcription en temps réel

#### Test d'enregistreur vocal 🎙️
- Cliquez sur l'onglet "🎙️ Enregistreur"
- Cliquez sur "Commencer l'enregistrement"
- Parlez pendant quelques secondes
- Observez le visualiseur de niveau audio
- Arrêtez l'enregistrement et testez la lecture

#### Test des services ⚙️
- Cliquez sur l'onglet "⚙️ Services"
- Testez le service de géolocalisation
- Testez le service vocal (reconnaissance)
- Observez les résultats JSON

### 4. Inscription complète (http://localhost:8082/register)

#### Étape 1 : Informations de base
- Remplissez tous les champs obligatoires
- Testez la validation en temps réel
- Vérifiez que le bouton "Suivant" s'active

#### Étape 2 : Profil personnel
- Sélectionnez votre date de naissance (18+ requis)
- Choisissez genre et préférences
- Testez le bouton "🎯 Détecter ma position" pour la ville
- Ajoutez une description (optionnel)

#### Étape 3 : Photo de profil
- Testez l'upload d'une image (JPG/PNG, max 5MB)
- Vérifiez la prévisualisation
- Testez la suppression de photo

#### Étape 4 : Configuration des médias (nouveau !)
- Cliquez sur "🔧 Configurer mes périphériques"
- Testez la configuration audio/vidéo
- Ou cliquez "Passer cette étape"

#### Étape 5 : Interview vocal
- Cliquez sur "Interview vocal" pour passer à l'étape 5
- Testez l'interview vocal complet
- Ou cliquez "Passer cette étape"

### 3. Connexion modifiée (http://localhost:8082/login)

- Testez la connexion avec un **nom d'utilisateur** au lieu de l'email
- Vérifiez les messages d'erreur

## 🔧 Résolution des problèmes

### Problèmes courants et solutions :

#### "Reconnaissance vocale non supportée"
- **Solution** : Utilisez Chrome, Safari ou Edge
- **Alternative** : Firefox ne supporte pas la Web Speech API

#### "Permission microphone refusée"
1. Cliquez sur l'icône 🔒 dans la barre d'adresse
2. Autorisez l'accès au microphone
3. Rechargez la page

#### "Géolocalisation échouée"
- **Solution** : Autorisez l'accès à la localisation
- **Alternative** : Saisissez votre ville manuellement

#### Erreurs de compilation
- Vérifiez que le serveur fonctionne : `npm run serve`
- Rechargez la page si nécessaire

## 📱 Tests de compatibilité

### Navigateurs testés :
- ✅ **Chrome** : Toutes fonctionnalités supportées
- ✅ **Safari** : Toutes fonctionnalités supportées  
- ✅ **Edge** : Toutes fonctionnalités supportées
- ⚠️ **Firefox** : Géolocalisation et enregistrement OK, reconnaissance vocale non supportée

### Appareils testés :
- 💻 **Desktop** : Interface optimisée
- 📱 **Mobile** : Interface responsive

## 🎯 Scénarios de test recommandés

### Scénario 1 : Inscription complète avec toutes les fonctionnalités
1. Allez sur http://localhost:8082/register
2. Remplissez l'étape 1 avec des données valides
3. À l'étape 2, utilisez la géolocalisation automatique
4. Ajoutez une photo à l'étape 3
5. Complétez l'interview vocal à l'étape 4
6. Finalisez l'inscription

### Scénario 2 : Test des fonctionnalités individuelles
1. Allez sur http://localhost:8082/test-features
2. Testez chaque onglet un par un
3. Vérifiez les résultats JSON affichés
4. Testez les cas d'erreur (refus de permissions)

### Scénario 3 : Test de compatibilité
1. Testez sur différents navigateurs
2. Testez sur mobile et desktop
3. Vérifiez les fallbacks pour fonctionnalités non supportées

## 📊 Métriques à observer

### Performance :
- Temps de chargement des composants
- Réactivité de l'interface
- Fluidité des animations

### Fonctionnalité :
- Précision de la reconnaissance vocale
- Qualité de l'enregistrement audio
- Précision de la géolocalisation

### UX/UI :
- Clarté des messages d'erreur
- Intuitivité de la navigation
- Responsive design

## 🐛 Signalement de bugs

Si vous trouvez des problèmes :

1. **Notez** :
   - Navigateur et version
   - Étapes pour reproduire
   - Message d'erreur exact
   - Capture d'écran si possible

2. **Vérifiez** :
   - Console du navigateur (F12)
   - Permissions accordées
   - Connexion internet

3. **Testez** :
   - Sur un autre navigateur
   - Après rechargement de page
   - Avec des données différentes

## ✅ Checklist de validation

### Fonctionnalités de base :
- [ ] Inscription en 4 étapes fonctionne
- [ ] Connexion par username fonctionne
- [ ] Validation des champs en temps réel
- [ ] Upload de photos avec prévisualisation

### Fonctionnalités avancées :
- [ ] Géolocalisation automatique
- [ ] Recherche de villes
- [ ] Reconnaissance vocale
- [ ] Enregistrement audio
- [ ] Interview vocal complet

### Compatibilité :
- [ ] Chrome/Safari/Edge : toutes fonctionnalités
- [ ] Firefox : fonctionnalités de base
- [ ] Mobile : interface responsive
- [ ] Fallbacks pour fonctionnalités non supportées

### Performance :
- [ ] Chargement rapide des pages
- [ ] Pas d'erreurs en console
- [ ] Interface fluide et responsive

---

**Bon test ! 🚀**

*Guide de test MeetVoice v2.0 - 29 juin 2025*
