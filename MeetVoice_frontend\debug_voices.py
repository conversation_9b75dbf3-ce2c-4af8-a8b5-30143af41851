#!/usr/bin/env python3
"""
Debug des voix TTS - Vérifier pourquoi les voix sont identiques
"""

import requests
import json

def check_voices():
    """Vérifier les voix disponibles"""
    print("🔍 Vérification des voix dans l'API backend...")
    
    try:
        response = requests.get('http://127.0.0.1:8000/tts/public/voices/')
        voices = response.json()
        
        print(f"📊 {len(voices)} voix trouvées:")
        print("=" * 60)
        
        for voice in voices:
            status = "✅ Actif" if voice['is_active'] else "❌ Inactif"
            premium = "⭐ Premium" if voice['is_premium'] else "🆓 Gratuit"
            print(f"{voice['voice_type']:15} | {voice['name']:20} | {status} | {premium}")
        
        return voices
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_synthesis():
    """Tester la synthèse avec différentes voix"""
    print("\n🧪 Test de synthèse avec différentes voix:")
    print("-" * 60)
    
    # Test avec les voix qui posent problème
    test_voices = [
        ('female_young', 'Sophie'),
        ('male_mature', '<PERSON>'), 
        ('male_young', '<PERSON>'),
        ('female_mature', 'Camille'),
        ('neutral', 'Alex')
    ]
    
    results = {}
    
    for voice_type, voice_name in test_voices:
        payload = {
            'text': f'Je suis {voice_name}.',
            'voice_type': voice_type,
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            if response.status_code == 200:
                size = len(response.content)
                content_type = response.headers.get('content-type', 'unknown')
                results[voice_type] = {
                    'name': voice_name,
                    'size': size,
                    'content_type': content_type,
                    'status': 'OK'
                }
                print(f"{voice_type:15} | {voice_name:10} | {size:6} bytes | ✅ OK")
            else:
                results[voice_type] = {
                    'name': voice_name,
                    'status': f'Erreur {response.status_code}'
                }
                print(f"{voice_type:15} | {voice_name:10} | Erreur {response.status_code} | ❌ ERREUR")
                
        except Exception as e:
            results[voice_type] = {
                'name': voice_name,
                'status': f'Erreur: {str(e)[:30]}'
            }
            print(f"{voice_type:15} | {voice_name:10} | {str(e)[:30]} | ❌ ERREUR")
    
    return results

def analyze_results(results):
    """Analyser les résultats"""
    print("\n📊 Analyse des résultats:")
    print("-" * 40)
    
    # Vérifier si les tailles sont différentes
    sizes = [r['size'] for r in results.values() if 'size' in r]
    unique_sizes = set(sizes)
    
    if len(unique_sizes) == len(sizes):
        print("✅ Toutes les voix produisent des audios de tailles différentes")
        print("   → Le problème ne vient PAS du backend")
        print("   → Le problème vient probablement du cache ou du frontend")
    elif len(unique_sizes) == 1:
        print("❌ Toutes les voix produisent des audios de même taille")
        print("   → Le backend utilise probablement la même voix pour tous les types")
        print("   → Vérifiez la configuration TTS du backend")
    else:
        print(f"⚠️  {len(unique_sizes)} tailles différentes sur {len(sizes)} voix")
        print("   → Certaines voix sont identiques")
    
    print(f"\nTailles trouvées: {sorted(unique_sizes)}")

def main():
    """Fonction principale"""
    print("🎤 Debug des voix TTS - MeetVoice")
    print("=" * 50)
    
    # Vérifier les voix
    voices = check_voices()
    if not voices:
        return
    
    # Tester la synthèse
    results = test_synthesis()
    
    # Analyser les résultats
    analyze_results(results)
    
    print("\n💡 Recommandations:")
    print("1. Si les tailles sont identiques → Problème backend")
    print("2. Si les tailles sont différentes → Problème frontend/cache")
    print("3. Vérifiez les logs du serveur Django backend")

if __name__ == "__main__":
    main()
