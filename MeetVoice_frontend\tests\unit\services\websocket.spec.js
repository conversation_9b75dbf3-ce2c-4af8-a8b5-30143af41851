import WebSocketService from '@/services/websocket'

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: WebSocket.OPEN
}))

// Mock console methods
global.console = {
  ...console,
  log: jest.fn(),
  error: jest.fn(),
  warn: jest.fn()
}

describe('WebSocketService', () => {
  let websocketService
  let mockStore
  let mockWebSocket

  beforeEach(() => {
    // Créer une nouvelle instance pour chaque test
    websocketService = new (require('@/services/websocket').default.constructor)()
    
    // Mock du store Vuex
    mockStore = {
      dispatch: jest.fn(),
      commit: jest.fn(),
      getters: {
        'auth/isAuthenticated': true,
        'auth/token': 'test-token'
      }
    }

    // Mock WebSocket instance
    mockWebSocket = {
      send: jest.fn(),
      close: jest.fn(),
      readyState: WebSocket.OPEN,
      onopen: null,
      onmessage: null,
      onclose: null,
      onerror: null
    }

    global.WebSocket.mockImplementation(() => mockWebSocket)

    // Initialiser le service avec le store mock
    websocketService.init(mockStore)

    // Reset des mocks
    jest.clearAllMocks()
  })

  afterEach(() => {
    websocketService.disconnect()
  })

  describe('Initialization', () => {
    it('should initialize with store', () => {
      expect(websocketService.store).toBe(mockStore)
    })

    it('should start with disconnected state', () => {
      expect(websocketService.isConnected).toBe(false)
      expect(websocketService.socket).toBeNull()
    })
  })

  describe('Connection', () => {
    it('should connect successfully', async () => {
      const token = 'test-token'
      
      const connectPromise = websocketService.connect(token)
      
      // Simuler l'ouverture de la connexion
      mockWebSocket.onopen()
      
      await connectPromise
      
      expect(global.WebSocket).toHaveBeenCalledWith(
        expect.stringContaining(`token=${token}`)
      )
      expect(websocketService.isConnected).toBe(true)
      expect(websocketService.reconnectAttempts).toBe(0)
    })

    it('should handle connection error', async () => {
      const token = 'test-token'
      const error = new Error('Connection failed')
      
      const connectPromise = websocketService.connect(token)
      
      // Simuler une erreur de connexion
      mockWebSocket.onerror(error)
      
      await expect(connectPromise).rejects.toThrow('Connection failed')
      expect(websocketService.isConnected).toBe(false)
    })

    it('should not reconnect if already connected', async () => {
      websocketService.isConnected = true
      websocketService.socket = { readyState: WebSocket.OPEN }
      
      const result = await websocketService.connect('token')
      
      expect(global.WebSocket).not.toHaveBeenCalled()
      expect(result).toBeUndefined()
    })
  })

  describe('Message Handling', () => {
    beforeEach(async () => {
      const connectPromise = websocketService.connect('test-token')
      mockWebSocket.onopen()
      await connectPromise
    })

    it('should handle new message', () => {
      const messageData = {
        type: 'message',
        payload: {
          id: 1,
          content: 'Hello',
          sender: { id: 2, prenom: 'Alice' },
          conversationId: 'conv-1',
          conversation: { id: 'conv-1', lastMessage: 'Hello' }
        }
      }

      const event = { data: JSON.stringify(messageData) }
      mockWebSocket.onmessage(event)

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'profiles/updateConversation',
        messageData.payload.conversation
      )
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'notifications/createMessageNotification',
        messageData.payload
      )
    })

    it('should handle new match', () => {
      const matchData = {
        type: 'match',
        payload: {
          id: 'match-1',
          user: { id: 2, prenom: 'Bob' }
        }
      }

      const event = { data: JSON.stringify(matchData) }
      mockWebSocket.onmessage(event)

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'profiles/addMatch',
        matchData.payload
      )
      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'notifications/createMatchNotification',
        matchData.payload
      )
    })

    it('should handle new like', () => {
      const likeData = {
        type: 'like',
        payload: {
          user: { id: 3, prenom: 'Charlie' }
        }
      }

      const event = { data: JSON.stringify(likeData) }
      mockWebSocket.onmessage(event)

      expect(mockStore.dispatch).toHaveBeenCalledWith(
        'notifications/createLikeNotification',
        likeData.payload
      )
    })

    it('should handle user status update', () => {
      const statusData = {
        type: 'user_status',
        payload: {
          userId: 2,
          isOnline: true,
          lastConnection: new Date().toISOString()
        }
      }

      const event = { data: JSON.stringify(statusData) }
      mockWebSocket.onmessage(event)

      expect(mockStore.commit).toHaveBeenCalledWith(
        'profiles/updateMember',
        {
          id: statusData.payload.userId,
          isOnline: statusData.payload.isOnline,
          lastConnection: statusData.payload.lastConnection
        }
      )
    })

    it('should handle invalid JSON message', () => {
      const event = { data: 'invalid json' }
      mockWebSocket.onmessage(event)

      expect(console.error).toHaveBeenCalledWith(
        'Erreur lors du traitement du message WebSocket:',
        expect.any(Error)
      )
    })
  })

  describe('Sending Messages', () => {
    beforeEach(async () => {
      const connectPromise = websocketService.connect('test-token')
      mockWebSocket.onopen()
      await connectPromise
    })

    it('should send message when connected', () => {
      const type = 'test'
      const payload = { data: 'test' }

      websocketService.send(type, payload)

      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type,
          payload,
          timestamp: expect.any(String)
        })
      )
    })

    it('should queue message when not connected', () => {
      websocketService.isConnected = false
      mockWebSocket.readyState = WebSocket.CLOSED

      const type = 'test'
      const payload = { data: 'test' }

      websocketService.send(type, payload)

      expect(mockWebSocket.send).not.toHaveBeenCalled()
      expect(websocketService.messageQueue).toHaveLength(1)
    })

    it('should send chat message', () => {
      const conversationId = 'conv-1'
      const content = 'Hello world'
      const isVoiceMessage = false

      websocketService.sendMessage(conversationId, content, isVoiceMessage)

      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'message',
          payload: {
            conversationId,
            content,
            isVoiceMessage
          },
          timestamp: expect.any(String)
        })
      )
    })

    it('should send typing indicator', () => {
      const conversationId = 'conv-1'
      const isTyping = true

      websocketService.sendTyping(conversationId, isTyping)

      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'typing',
          payload: {
            conversationId,
            isTyping
          },
          timestamp: expect.any(String)
        })
      )
    })

    it('should send like', () => {
      const profileId = 'profile-1'

      websocketService.sendLike(profileId)

      expect(mockWebSocket.send).toHaveBeenCalledWith(
        JSON.stringify({
          type: 'like',
          payload: {
            profileId
          },
          timestamp: expect.any(String)
        })
      )
    })
  })

  describe('Event Listeners', () => {
    it('should add event listener', () => {
      const event = 'test-event'
      const callback = jest.fn()

      websocketService.on(event, callback)

      expect(websocketService.eventListeners.get(event)).toContain(callback)
    })

    it('should remove event listener', () => {
      const event = 'test-event'
      const callback = jest.fn()

      websocketService.on(event, callback)
      websocketService.off(event, callback)

      expect(websocketService.eventListeners.get(event)).not.toContain(callback)
    })

    it('should emit event to listeners', () => {
      const event = 'test-event'
      const callback1 = jest.fn()
      const callback2 = jest.fn()
      const data = { test: 'data' }

      websocketService.on(event, callback1)
      websocketService.on(event, callback2)
      websocketService.emit(event, data)

      expect(callback1).toHaveBeenCalledWith(data)
      expect(callback2).toHaveBeenCalledWith(data)
    })

    it('should handle listener error gracefully', () => {
      const event = 'test-event'
      const errorCallback = jest.fn().mockImplementation(() => {
        throw new Error('Listener error')
      })
      const normalCallback = jest.fn()

      websocketService.on(event, errorCallback)
      websocketService.on(event, normalCallback)
      websocketService.emit(event, {})

      expect(console.error).toHaveBeenCalledWith(
        'Erreur dans le listener d\'événement:',
        expect.any(Error)
      )
      expect(normalCallback).toHaveBeenCalled()
    })
  })

  describe('Reconnection', () => {
    beforeEach(() => {
      jest.useFakeTimers()
    })

    afterEach(() => {
      jest.useRealTimers()
    })

    it('should attempt reconnection on connection close', async () => {
      const token = 'test-token'
      
      const connectPromise = websocketService.connect(token)
      mockWebSocket.onopen()
      await connectPromise

      // Simuler une fermeture inattendue
      mockWebSocket.onclose({ wasClean: false, code: 1006 })

      expect(websocketService.isConnected).toBe(false)
      expect(websocketService.reconnectAttempts).toBe(0)

      // Avancer le timer pour déclencher la reconnexion
      jest.advanceTimersByTime(1000)

      expect(websocketService.reconnectAttempts).toBe(1)
    })

    it('should stop reconnecting after max attempts', () => {
      websocketService.reconnectAttempts = 5
      websocketService.maxReconnectAttempts = 5

      websocketService.reconnect('token')

      expect(console.error).toHaveBeenCalledWith(
        'Nombre maximum de tentatives de reconnexion atteint'
      )
    })
  })

  describe('Disconnection', () => {
    it('should disconnect cleanly', async () => {
      const connectPromise = websocketService.connect('test-token')
      mockWebSocket.onopen()
      await connectPromise

      websocketService.disconnect()

      expect(mockWebSocket.close).toHaveBeenCalledWith(1000, 'Déconnexion volontaire')
      expect(websocketService.socket).toBeNull()
      expect(websocketService.isConnected).toBe(false)
    })

    it('should handle disconnect when not connected', () => {
      websocketService.disconnect()

      expect(mockWebSocket.close).not.toHaveBeenCalled()
    })
  })

  describe('Connection Status', () => {
    it('should return correct connection status', async () => {
      expect(websocketService.isConnectedToWebSocket()).toBe(false)

      const connectPromise = websocketService.connect('test-token')
      mockWebSocket.onopen()
      await connectPromise

      expect(websocketService.isConnectedToWebSocket()).toBe(true)
    })
  })
})
