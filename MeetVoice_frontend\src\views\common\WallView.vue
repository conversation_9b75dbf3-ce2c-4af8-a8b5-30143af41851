<template>
  <main class="wall-container">
    <!-- Header -->
    <header class="wall-header">
      <section class="header-content">
        <h1>Mur social</h1>
        <p class="subtitle">Partagez vos moments et découvrez ceux de la communauté</p>
      </section>
      
      <nav class="wall-actions" aria-label="Actions mur">
        <button 
          @click="showCreatePost = true"
          class="btn-create-post"
        >
          <span aria-hidden="true">✍️</span>
          Nouveau post
        </button>
        
        <button 
          @click="showStoryCreator = true"
          class="btn-create-story"
        >
          <span aria-hidden="true">📸</span>
          Story
        </button>
      </nav>
    </header>

    <!-- Stories -->
    <section class="stories-section" v-if="stories.length > 0">
      <h2 class="section-title">Stories</h2>
      <div class="stories-container">
        <div class="story-item add-story" @click="showStoryCreator = true">
          <div class="story-avatar">
            <img 
              :src="user.avatar || '/default-avatar.jpg'"
              :alt="'Votre avatar'"
              class="avatar-image"
            >
            <div class="add-icon">+</div>
          </div>
          <span class="story-label">Votre story</span>
        </div>
        
        <div 
          v-for="story in stories" 
          :key="story.id"
          class="story-item"
          @click="viewStory(story)"
        >
          <div class="story-avatar">
            <img 
              :src="story.author.avatar || '/default-avatar.jpg'"
              :alt="`Avatar de ${story.author.username}`"
              class="avatar-image"
            >
            <div v-if="!story.viewed" class="story-ring"></div>
          </div>
          <span class="story-label">{{ story.author.username }}</span>
        </div>
      </div>
    </section>

    <!-- Filtres -->
    <section class="filters-section">
      <div class="filters-container">
        <div class="filter-tabs">
          <button 
            @click="activeFilter = 'all'"
            :class="['filter-tab', { active: activeFilter === 'all' }]"
          >
            Tous
          </button>
          <button 
            @click="activeFilter = 'following'"
            :class="['filter-tab', { active: activeFilter === 'following' }]"
          >
            Abonnements
          </button>
          <button 
            @click="activeFilter = 'trending'"
            :class="['filter-tab', { active: activeFilter === 'trending' }]"
          >
            Tendances
          </button>
        </div>
        
        <div class="sort-controls">
          <select v-model="sortBy" class="sort-select">
            <option value="recent">Plus récents</option>
            <option value="popular">Plus populaires</option>
            <option value="comments">Plus commentés</option>
          </select>
        </div>
      </div>
    </section>

    <!-- Posts -->
    <section class="posts-section">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Chargement du mur...</p>
      </div>
      
      <div v-else-if="filteredPosts.length === 0" class="empty-state">
        <h2>Aucun post à afficher</h2>
        <p>Soyez le premier à partager quelque chose !</p>
        <button 
          @click="showCreatePost = true"
          class="btn-create-first"
        >
          Créer le premier post
        </button>
      </div>
      
      <div v-else class="posts-list">
        <article 
          v-for="post in filteredPosts" 
          :key="post.id"
          class="post-card"
        >
          <header class="post-header">
            <div class="author-info">
              <img 
                :src="post.author.avatar || '/default-avatar.jpg'"
                :alt="`Avatar de ${post.author.username}`"
                class="author-avatar"
                @click="viewProfile(post.author.id)"
              >
              <div class="author-details">
                <h3 class="author-name" @click="viewProfile(post.author.id)">
                  {{ post.author.username }}
                </h3>
                <div class="post-meta">
                  <time class="post-time">{{ formatPostTime(post.createdAt) }}</time>
                  <span v-if="post.location" class="post-location">📍 {{ post.location }}</span>
                </div>
              </div>
            </div>
            
            <button class="btn-post-options" @click="showPostOptions(post.id)">
              <span aria-hidden="true">⋮</span>
            </button>
          </header>
          
          <div class="post-content">
            <p v-if="post.content" class="post-text">{{ post.content }}</p>
            
            <div v-if="post.media && post.media.length > 0" class="post-media">
              <div 
                v-for="(media, index) in post.media.slice(0, 4)" 
                :key="index"
                :class="['media-item', { 'has-more': post.media.length > 4 && index === 3 }]"
                @click="openMediaViewer(post.media, index)"
              >
                <img 
                  v-if="media.type === 'image'"
                  :src="media.url"
                  :alt="media.alt || 'Image du post'"
                  class="media-image"
                  loading="lazy"
                >
                <video 
                  v-else-if="media.type === 'video'"
                  :src="media.url"
                  class="media-video"
                  controls
                  preload="metadata"
                ></video>
                
                <div v-if="post.media.length > 4 && index === 3" class="media-overlay">
                  +{{ post.media.length - 4 }}
                </div>
              </div>
            </div>
            
            <div v-if="post.tags && post.tags.length > 0" class="post-tags">
              <span 
                v-for="tag in post.tags" 
                :key="tag"
                class="post-tag"
                @click="filterByTag(tag)"
              >
                #{{ tag }}
              </span>
            </div>
          </div>
          
          <footer class="post-footer">
            <div class="post-stats">
              <span class="likes-count">{{ post.likesCount || 0 }} ❤️</span>
              <span class="comments-count">{{ post.commentsCount || 0 }} 💬</span>
              <span class="shares-count">{{ post.sharesCount || 0 }} 🔄</span>
            </div>
            
            <div class="post-actions">
              <button 
                @click="toggleLike(post.id)"
                :class="['btn-like', { liked: post.isLiked }]"
              >
                <span aria-hidden="true">{{ post.isLiked ? '❤️' : '🤍' }}</span>
                J'aime
              </button>
              
              <button 
                @click="toggleComments(post.id)"
                class="btn-comment"
              >
                <span aria-hidden="true">💬</span>
                Commenter
              </button>
              
              <button 
                @click="sharePost(post.id)"
                class="btn-share"
              >
                <span aria-hidden="true">🔄</span>
                Partager
              </button>
              
              <button 
                @click="sendPrivateMessage(post.author.id)"
                class="btn-message"
              >
                <span aria-hidden="true">📩</span>
                Message
              </button>
            </div>
            
            <!-- Commentaires -->
            <div v-if="post.showComments" class="comments-section">
              <div class="comments-list">
                <div 
                  v-for="comment in post.comments?.slice(0, 3)" 
                  :key="comment.id"
                  class="comment-item"
                >
                  <img 
                    :src="comment.author.avatar || '/default-avatar.jpg'"
                    :alt="`Avatar de ${comment.author.username}`"
                    class="comment-avatar"
                  >
                  <div class="comment-content">
                    <span class="comment-author">{{ comment.author.username }}</span>
                    <p class="comment-text">{{ comment.content }}</p>
                    <time class="comment-time">{{ formatPostTime(comment.createdAt) }}</time>
                  </div>
                </div>
                
                <button 
                  v-if="post.commentsCount > 3"
                  @click="loadMoreComments(post.id)"
                  class="btn-load-comments"
                >
                  Voir les {{ post.commentsCount - 3 }} autres commentaires
                </button>
              </div>
              
              <div class="comment-form">
                <img 
                  :src="user.avatar || '/default-avatar.jpg'"
                  :alt="'Votre avatar'"
                  class="comment-avatar"
                >
                <input 
                  v-model="newComments[post.id]"
                  @keydown.enter="addComment(post.id)"
                  type="text"
                  placeholder="Écrivez un commentaire..."
                  class="comment-input"
                >
                <button 
                  @click="addComment(post.id)"
                  :disabled="!newComments[post.id]?.trim()"
                  class="btn-send-comment"
                >
                  Envoyer
                </button>
              </div>
            </div>
          </footer>
        </article>
      </div>
    </section>

    <!-- Modal de création de post -->
    <div v-if="showCreatePost" class="modal-overlay" @click="showCreatePost = false">
      <div class="modal-content" @click.stop>
        <header class="modal-header">
          <h2>Créer un nouveau post</h2>
          <button @click="showCreatePost = false" class="btn-close-modal">×</button>
        </header>
        
        <form @submit.prevent="createPost" class="post-form">
          <div class="form-group">
            <textarea
              v-model="newPost.content"
              placeholder="Que voulez-vous partager ?"
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="post-location">Lieu (optionnel) :</label>
            <input
              id="post-location"
              v-model="newPost.location"
              type="text"
              placeholder="Où êtes-vous ?"
              class="form-input"
            >
          </div>
          
          <div class="form-group">
            <label for="post-tags">Tags (séparés par des virgules) :</label>
            <input
              id="post-tags"
              v-model="newPost.tags"
              type="text"
              placeholder="voyage, amis, bonheur..."
              class="form-input"
            >
          </div>
          
          <div class="media-upload">
            <button type="button" class="btn-upload-media">
              📷 Ajouter des photos/vidéos
            </button>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="showCreatePost = false" class="btn-cancel">
              Annuler
            </button>
            <button type="submit" class="btn-create" :disabled="!newPost.content.trim()">
              Publier
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Modal de création de story -->
    <div v-if="showStoryCreator" class="modal-overlay" @click="showStoryCreator = false">
      <div class="modal-content story-modal" @click.stop>
        <header class="modal-header">
          <h2>Créer une story</h2>
          <button @click="showStoryCreator = false" class="btn-close-modal">×</button>
        </header>
        
        <div class="story-creator">
          <div class="story-preview">
            <div class="story-canvas">
              <p>Votre story apparaîtra ici</p>
            </div>
          </div>
          
          <div class="story-tools">
            <button class="btn-story-tool">📷 Photo</button>
            <button class="btn-story-tool">🎥 Vidéo</button>
            <button class="btn-story-tool">✏️ Texte</button>
            <button class="btn-story-tool">🎨 Dessiner</button>
          </div>
          
          <div class="story-actions">
            <button @click="showStoryCreator = false" class="btn-cancel">
              Annuler
            </button>
            <button class="btn-publish-story">
              Publier la story
            </button>
          </div>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'WallView',

  data() {
    return {
      loading: false,
      activeFilter: 'all',
      sortBy: 'recent',
      showCreatePost: false,
      showStoryCreator: false,
      newComments: {},

      newPost: {
        content: '',
        location: '',
        tags: '',
        media: []
      }
    }
  },

  computed: {
    ...mapState(['wall', 'user']),

    stories() {
      // Retourner les stories depuis le store
      return this.wall?.stories || [];
    },

    filteredPosts() {
      let posts = this.wall?.posts || [];

      // Filtrage
      switch (this.activeFilter) {
        case 'following':
          posts = posts.filter(post => post.author.isFollowing);
          break;
        case 'trending':
          posts = posts.filter(post => post.isTrending);
          break;
      }

      // Tri
      posts.sort((a, b) => {
        switch (this.sortBy) {
          case 'popular':
            return (b.likesCount || 0) - (a.likesCount || 0);
          case 'comments':
            return (b.commentsCount || 0) - (a.commentsCount || 0);
          default: // recent
            return new Date(b.createdAt) - new Date(a.createdAt);
        }
      });

      return posts;
    }
  },

  methods: {
    ...mapActions(['loadWall', 'createWallPost', 'likeWallPost']),

    formatPostTime(timestamp) {
      const now = new Date();
      const postTime = new Date(timestamp);
      const diffMs = now - postTime;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'À l\'instant';
      if (diffMins < 60) return `Il y a ${diffMins} min`;
      if (diffHours < 24) return `Il y a ${diffHours}h`;
      if (diffDays < 7) return `Il y a ${diffDays}j`;
      return postTime.toLocaleDateString('fr-FR');
    },

    viewProfile(userId) {
      this.$router.push(`/profile/${userId}`);
    },

    viewStory(story) {
      // Implémenter la visualisation de story
      console.log('Voir story:', story);
    },

    async toggleLike(postId) {
      try {
        await this.likeWallPost(postId);
      } catch (error) {
        console.error('Erreur lors du like:', error);
      }
    },

    toggleComments(postId) {
      const post = this.wall.posts.find(p => p.id === postId);
      if (post) {
        post.showComments = !post.showComments;
      }
    },

    async addComment(postId) {
      const content = this.newComments[postId];
      if (!content?.trim()) return;

      try {
        // Ici on ajouterait le commentaire via l'API
        console.log('Ajouter commentaire:', postId, content);
        this.newComments[postId] = '';
      } catch (error) {
        console.error('Erreur lors de l\'ajout du commentaire:', error);
      }
    },

    sharePost(postId) {
      // Implémenter le partage
      console.log('Partager post:', postId);
    },

    sendPrivateMessage(userId) {
      this.$router.push(`/messages/${userId}`);
    },

    showPostOptions(postId) {
      // Afficher les options du post
      console.log('Options post:', postId);
    },

    openMediaViewer(media, index) {
      // Ouvrir la visionneuse de médias
      console.log('Ouvrir média:', media, index);
    },

    loadMoreComments(postId) {
      // Charger plus de commentaires
      console.log('Charger plus de commentaires:', postId);
    },

    filterByTag(tag) {
      // Implémenter le filtrage par tag
      console.log('Filtrer par tag:', tag);
    },

    async createPost() {
      if (!this.newPost.content.trim()) return;

      try {
        const postData = {
          ...this.newPost,
          tags: this.newPost.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          author: this.user,
          createdAt: new Date().toISOString()
        };

        await this.createWallPost(postData);

        // Réinitialiser le formulaire
        this.newPost = {
          content: '',
          location: '',
          tags: '',
          media: []
        };

        this.showCreatePost = false;

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Post publié avec succès !',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors de la création du post:', error);
        this.$store.commit('addNotification', {
          type: 'error',
          message: 'Erreur lors de la publication',
          timestamp: new Date().toISOString()
        });
      }
    }
  },

  async mounted() {
    this.loading = true;
    try {
      await this.loadWall();
    } catch (error) {
      console.error('Erreur lors du chargement du mur:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --social-blue: #1877F2;
  --social-green: #42B883;
}

.wall-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.wall-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-blue);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.wall-actions {
  display: flex;
  gap: 12px;
}

.btn-create-post,
.btn-create-story {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-post:hover,
.btn-create-story:hover {
  background: var(--accent-blue);
  transform: translateY(-2px);
}

.stories-section {
  margin-bottom: 30px;
}

.section-title {
  color: var(--accent-purple);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.stories-container {
  display: flex;
  gap: 12px;
  overflow-x: auto;
  padding: 8px 0;
}

.story-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  min-width: 80px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.story-item:hover {
  transform: translateY(-4px);
}

.story-avatar {
  position: relative;
  width: 60px;
  height: 60px;
}

.avatar-image {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-blue);
}

.add-story .avatar-image {
  border-color: var(--wall-color);
}

.add-icon {
  position: absolute;
  bottom: -2px;
  right: -2px;
  width: 20px;
  height: 20px;
  background: var(--accent-blue);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.story-ring {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border: 3px solid var(--accent-purple);
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.story-label {
  font-size: 0.8rem;
  color: var(--slogan-gray);
  text-align: center;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.filters-section {
  margin-bottom: 30px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filters-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.filter-tabs {
  display: flex;
  gap: 8px;
}

.filter-tab {
  padding: 8px 16px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-tab.active {
  background: var(--accent-blue);
  color: var(--text-white);
  border-color: var(--accent-blue);
}

.filter-tab:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.sort-select {
  padding: 6px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.posts-section {
  margin-bottom: 40px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-state h2 {
  color: var(--accent-blue);
  margin-bottom: 16px;
}

.btn-create-first {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-first:hover {
  background: var(--accent-blue);
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.post-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.post-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--accent-blue);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--accent-blue);
  cursor: pointer;
}

.author-name {
  color: var(--accent-blue);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 4px 0;
  cursor: pointer;
}

.author-name:hover {
  text-decoration: underline;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.btn-post-options {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-post-options:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.post-content {
  margin-bottom: 16px;
}

.post-text {
  color: var(--text-white);
  line-height: 1.6;
  margin-bottom: 16px;
}

.post-media {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
  margin-bottom: 16px;
  border-radius: 8px;
  overflow: hidden;
}

.post-media:has(.media-item:only-child) {
  grid-template-columns: 1fr;
}

.media-item {
  position: relative;
  cursor: pointer;
  transition: all 0.3s ease;
}

.media-item:hover {
  transform: scale(1.02);
}

.media-image,
.media-video {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.media-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: var(--text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: bold;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.post-tag {
  padding: 4px 8px;
  background: rgba(0, 207, 255, 0.2);
  color: var(--accent-blue);
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-tag:hover {
  background: rgba(0, 207, 255, 0.3);
}

.post-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.post-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: var(--slogan-gray);
}

.post-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.btn-like,
.btn-comment,
.btn-share,
.btn-message {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-like:hover,
.btn-comment:hover,
.btn-share:hover,
.btn-message:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-like.liked {
  color: #ff6b6b;
  border-color: #ff6b6b;
}

.comments-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.comments-list {
  margin-bottom: 16px;
}

.comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid var(--accent-blue);
}

.comment-content {
  flex: 1;
}

.comment-author {
  color: var(--accent-blue);
  font-weight: 600;
  font-size: 0.9rem;
}

.comment-text {
  color: var(--text-white);
  margin: 4px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.comment-time {
  color: var(--icon-color);
  font-size: 0.7rem;
}

.btn-load-comments {
  color: var(--accent-blue);
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.8rem;
  margin-bottom: 12px;
}

.comment-form {
  display: flex;
  gap: 12px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.comment-input:focus {
  outline: none;
  border-color: var(--accent-blue);
}

.btn-send-comment {
  padding: 8px 16px;
  background: var(--accent-blue);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-send-comment:hover:not(:disabled) {
  background: var(--accent-purple);
}

.btn-send-comment:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--header-bg);
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.story-modal {
  max-width: 400px;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h2 {
  color: var(--accent-blue);
  font-size: 1.3rem;
  font-weight: 600;
}

.btn-close-modal {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.post-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: var(--slogan-gray);
  font-weight: 500;
}

.form-input,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.15);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.media-upload {
  padding: 20px;
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  text-align: center;
}

.btn-upload-media {
  padding: 10px 20px;
  background: var(--wall-color);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-upload-media:hover {
  background: var(--sweater-purple);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.btn-cancel,
.btn-create {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-create {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-create:hover:not(:disabled) {
  background: var(--accent-purple);
}

.btn-create:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Story Creator */
.story-creator {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.story-preview {
  display: flex;
  justify-content: center;
}

.story-canvas {
  width: 200px;
  height: 350px;
  background: linear-gradient(135deg, var(--accent-blue), var(--accent-purple));
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-white);
  text-align: center;
  padding: 20px;
}

.story-tools {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

.btn-story-tool {
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-story-tool:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: var(--accent-blue);
}

.story-actions {
  display: flex;
  justify-content: space-between;
  gap: 12px;
}

.btn-publish-story {
  flex: 1;
  padding: 12px 20px;
  background: var(--accent-blue);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-publish-story:hover {
  background: var(--accent-purple);
}

/* Responsive */
@media (max-width: 768px) {
  .wall-container {
    padding: 16px;
  }

  .wall-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filters-container {
    flex-direction: column;
    gap: 16px;
  }

  .post-actions {
    flex-wrap: wrap;
  }

  .comment-form {
    flex-direction: column;
    align-items: stretch;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
  }

  .story-tools {
    grid-template-columns: 1fr;
  }
}
</style>
