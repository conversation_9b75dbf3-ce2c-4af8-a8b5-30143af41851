version: '3.8'

services:
  # PostgreSQL pour les données utilisateurs
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: meetvoice
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 3

  # MongoDB pour les données temps réel
  mongodb:
    image: mongo:7
    environment:
      MONGO_INITDB_DATABASE: meetvoice
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    healthcheck:
      test: ["CMD", "mongosh", "--eval", "db.adminCommand('ping')"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis pour le cache (optionnel)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Serveur WebSocket MeetVoice
  meetvoice-websocket:
    build: .
    ports:
      - "8080:8080"
      - "9090:9090"  # Port P2P
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=8080
      - POSTGRES_URL=********************************************/meetvoice
      - MONGODB_URL=mongodb://mongodb:27017
      - MONGODB_DATABASE=meetvoice
      - P2P_PORT=9090
      - P2P_ENABLE_MDNS=true
      - P2P_ENABLE_RELAY=true
      - RUST_LOG=meetvoice_websocket=debug,libp2p=info
    depends_on:
      postgres:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    restart: unless-stopped

volumes:
  postgres_data:
  mongodb_data:
  redis_data:
