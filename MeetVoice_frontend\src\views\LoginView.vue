<!-- @format -->

<template>
  <main class="login-page" role="main">
    <!-- Split Screen Layout -->
    <div class="split-container">

      <!-- Left Side - Image/Branding -->
      <section class="left-side" aria-label="Présentation Meet Voice">
        <div class="brand-section">
          <header class="brand-content animate-slide-in">
            <h1 class="brand-logo animate-fade-in-up">
              <span class="logo-meet animate-bounce-in">Meet </span>
              <span class="logo-voice animate-bounce-in-delay">Voice</span>
            </h1>
            <p class="brand-tagline animate-fade-in-up-delay"><PERSON><PERSON><PERSON>, vibrez, rencontrez</p>
          </header>

          <section class="brand-features" aria-label="Fonctionnalités principales">
            <h2 class="sr-only">Nos fonctionnalités</h2>
            <div class="feature-item animate-slide-in-left" style="animation-delay: 0.5s;">
              <span class="feature-icon animate-pulse" aria-hidden="true">🎤</span>
              <span class="feature-text">Interview vocale intelligente</span>
            </div>
            <div class="feature-item animate-slide-in-left" style="animation-delay: 0.7s;">
              <span class="feature-icon animate-pulse" aria-hidden="true">💕</span>
              <span class="feature-text">Rencontres authentiques</span>
            </div>
            <div class="feature-item animate-slide-in-left" style="animation-delay: 0.9s;">
              <span class="feature-icon animate-pulse" aria-hidden="true">🌟</span>
              <span class="feature-text">Profils personnalisés</span>
            </div>
          </section>
        </div>
      </section>

      <!-- Right Side - Login Form -->
      <section class="right-side" aria-label="Formulaire de connexion">
        <div class="form-section animate-slide-in-right">
          <header class="form-header animate-fade-in-down">
            <h2 class="form-title">Connexion</h2>
            <p class="form-subtitle">Accédez à votre espace personnel</p>
          </header>

          <form @submit.prevent="submit" class="login-form">
            <!-- Champ email -->
            <div class="input-group">
              <label for="email" class="input-label">
                <span class="label-icon">📧</span>
                Email
              </label>
              <input
                type="email"
                id="email"
                v-model="user.email"
                placeholder="Entrez votre email"
                class="input-field"
                :class="{ 'error': errors && errors.includes('email') }"
                required
              />
            </div>

            <!-- Champ mot de passe -->
            <div class="input-group">
              <label for="password1" class="input-label">
                <span class="label-icon">🔒</span>
                Mot de passe
              </label>
              <div class="password-container">
                <input
                  :type="showPassword ? 'text' : 'password'"
                  id="password1"
                  v-model="user.password1"
                  placeholder="Entrez votre mot de passe"
                  class="input-field"
                  :class="{ 'error': errors && errors.includes('mot de passe') }"
                  required
                />
                <button
                  type="button"
                  @click="togglePassword"
                  class="password-toggle"
                >
                  <span v-if="showPassword">👁️</span>
                  <span v-else>👁️‍🗨️</span>
                </button>
              </div>
            </div>

            <!-- Message d'erreur -->
            <div v-if="errors" class="error-message">
              <span class="error-icon">⚠️</span>
              {{ errors }}
            </div>

            <!-- Options supplémentaires -->
            <div class="form-options">
              <label class="remember-me">
                <input type="checkbox" v-model="rememberMe">
                <span class="checkmark"></span>
                Se souvenir de moi
              </label>
              <a href="#" class="forgot-password">Mot de passe oublié ?</a>
            </div>

            <!-- Bouton de connexion -->
            <button
              type="submit"
              class="login-button"
              :disabled="isLoading || !user.email || !user.password1"
              :class="{ 'loading': isLoading }"
            >
              <span v-if="!isLoading" class="button-content">
                <span class="button-icon">🚀</span>
                Se connecter
              </span>
              <span v-else class="loading-content">
                <span class="spinner"></span>
                Connexion...
              </span>
            </button>

            <!-- Séparateur -->
            <div class="divider">
              <span class="divider-text">ou</span>
            </div>

            <!-- Lien d'inscription -->
            <div class="register-section">
              <p class="register-text">Nouveau sur Meet Voice ?</p>
              <router-link to="/register-enhanced" class="register-button">
                <span class="register-icon">✨</span>
                Créer un compte gratuitement
              </router-link>
            </div>
          </form>

          <!-- Footer -->
          <div class="form-footer">
            <p>&copy; 2024 Meet Voice. Tous droits réservés.</p>
          </div>
        </div>
      </section>
    </div>
  </main>
</template>

<script>
import { accountService } from "@/_services";
import store from "@/store";

export default {
  name: "LoginView",
  data() {
    return {
      user: {
        email: "",
        password1: "",
      },
      errors: "",
      showPassword: false,
      rememberMe: false,
    };
  },
  computed: {
    isLoading() {
      return this.$store.getters.isLoading;
    },
    storeError() {
      return this.$store.getters.getError;
    },
  },
  watch: {
    storeError(newError) {
      if (newError) {
        this.errors = newError;
      }
    },
  },
  methods: {
    togglePassword() {
      this.showPassword = !this.showPassword;
    },

    async submit() {
      try {
        // Réinitialiser les erreurs
        this.errors = "";
        this.$store.dispatch("clearError");

        // Validation côté client
        if (!this.user.email.trim()) {
          this.errors = "L'email est requis";
          return;
        }

        if (!this.user.password1.trim()) {
          this.errors = "Le mot de passe est requis";
          return;
        }

        if (this.user.password1.length < 6) {
          this.errors = "Le mot de passe doit contenir au moins 6 caractères";
          return;
        }

        const formData = new FormData();
        formData.append("email", this.user.email.trim());
        formData.append("password", this.user.password1);

        // Utiliser l'action Vuex pour la connexion
        await this.$store.dispatch("login", formData);

        // Sauvegarder les informations utilisateur supplémentaires si nécessaire
        const user = this.$store.getters.getUser;
        if (user) {
          // Sauvegarder les préférences utilisateur par défaut
          accountService.saveUserPreferences({
            theme: "default",
            language: "fr",
            notifications: true,
          });
        }

        // Message de succès
        this.$store.dispatch("setSuccess", "Connexion réussie ! Bienvenue !");

        // Rediriger vers la page demandée ou l'accueil
        const redirectPath = this.$route.query.redirect || '/';
        this.$router.push(redirectPath);
      } catch (error) {
        console.error("Erreur lors de la soumission du formulaire", error);

        // Gestion des erreurs spécifiques
        if (error.response) {
          switch (error.response.status) {
            case 401:
              this.errors = "Email ou mot de passe incorrect";
              break;
            case 403:
              this.errors = "Compte désactivé. Contactez l'administrateur";
              break;
            case 429:
              this.errors = "Trop de tentatives. Réessayez dans quelques minutes";
              break;
            default:
              this.errors = "Erreur de connexion. Veuillez réessayer";
          }
        } else {
          this.errors = "Erreur de réseau. Vérifiez votre connexion internet";
        }
      }
    },
  },

  mounted() {
    // Définir le titre de la page
    document.title = 'Connexion - MeetVoice';

    // Focus automatique sur le champ email
    this.$nextTick(() => {
      const emailField = document.getElementById('email');
      if (emailField) {
        emailField.focus();
      }
    });
  },
};
</script>

<style scoped>
/* Variables CSS */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-color: #667eea;
  --text-dark: #1e293b;
  --text-light: #64748b;
  --border-color: #e2e8f0;
  --success-color: #10b981;
  --error-color: #ef4444;
  --border-radius: 16px;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Page principale */
.login-page {
  min-height: 100vh;
  display: flex;
  background: #f8fafc;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Container split-screen */
.split-container {
  display: flex;
  width: 100%;
  min-height: 100vh;
}

/* Côté gauche - Branding */
.left-side {
  flex: 0 0 70%;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.25) 0%, rgba(124, 58, 237, 0.25) 100%),
              url('https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80') center/cover;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* Section branding */
.brand-section {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 500px;
  padding: 40px;
}

.brand-content {
  animation: fadeInUp 0.8s ease-out;
}

/* Logo et branding */
.brand-logo {
  font-size: 4rem;
  font-weight: 800;
  margin: 0 0 20px 0;
  line-height: 1;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo-meet {
  color: #00CFFF;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.logo-voice {
  color: #D68CFF;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.brand-tagline {
  font-size: 1.5rem;
  margin: 0 0 40px 0;
  color: #ffffff;
  font-weight: 400;
  letter-spacing: 0.5px;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Features */
.brand-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 40px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 20px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text {
  font-size: 1rem;
  font-weight: 500;
  color: #ffffff;
  text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}



/* Côté droit - Formulaire */
.right-side {
  flex: 0 0 30%;
  background: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  box-shadow: -5px 0 15px rgba(0,0,0,0.1);
}

.form-section {
  width: 100%;
  max-width: 450px;
  background: #faf9f7;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.25);
  border: 1px solid #e2e8f0;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #111827 !important;
  margin: 0 0 10px 0;
}

.form-subtitle {
  font-size: 1.1rem;
  color: #6b7280 !important;
  margin: 0;
  font-weight: 400;
}

/* Formulaire */
.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Groupes d'input */
.input-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.input-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #111827 !important;
  font-size: 15px;
}

.label-icon {
  font-size: 18px;
  color: #2563eb;
}

/* Champs de saisie */
.input-field {
  width: 100%;
  padding: 18px 20px;
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 16px;
  background: white;
  transition: var(--transition);
  box-sizing: border-box;
  font-family: inherit;
}

.input-field:focus {
  outline: none;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.input-field.error {
  border-color: var(--error-color);
  background: #fef2f2;
}

.input-field.error:focus {
  border-color: var(--error-color);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

/* Container mot de passe */
.password-container {
  position: relative;
}

.password-toggle {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 18px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s;
  color: var(--text-light);
}

.password-toggle:hover {
  background: rgba(0, 0, 0, 0.05);
  color: var(--accent-color);
}

/* Messages d'erreur */
.error-message {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  color: var(--error-color);
  font-size: 14px;
  font-weight: 500;
}

.error-icon {
  font-size: 18px;
}

/* Options du formulaire */
.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 10px 0;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
  font-size: 14px;
  color: #1e293b !important;
}

.remember-me input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  position: relative;
  transition: var(--transition);
}

.remember-me input[type="checkbox"]:checked + .checkmark {
  background: var(--accent-color);
  border-color: var(--accent-color);
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password {
  color: var(--accent-color);
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: var(--transition);
}

.forgot-password:hover {
  color: #5a67d8;
  text-decoration: underline;
}

/* Bouton de connexion */
.login-button {
  width: 100%;
  padding: 18px 24px;
  background: var(--primary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow);
  font-family: inherit;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.login-button.loading {
  pointer-events: none;
}

.button-content,
.loading-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.button-icon {
  font-size: 18px;
}

/* Spinner de chargement */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Séparateur */
.divider {
  position: relative;
  text-align: center;
  margin: 30px 0;
}

.divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--border-color);
}

.divider-text {
  background: white;
  padding: 0 20px;
  color: #64748b !important;
  font-size: 14px;
  position: relative;
}

/* Section d'inscription */
.register-section {
  text-align: center;
}

.register-text {
  margin: 0 0 15px 0;
  color: #64748b !important;
  font-size: 15px;
}

.register-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  width: 100%;
  padding: 16px 24px;
  background: white;
  color: var(--accent-color);
  border: 2px solid var(--accent-color);
  border-radius: var(--border-radius);
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
}

.register-button:hover {
  background: var(--accent-color);
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
}

.register-icon {
  font-size: 18px;
}

/* Footer */
.form-footer {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}

.form-footer p {
  margin: 0;
  color: #64748b !important;
  font-size: 12px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .split-container {
    flex-direction: column;
  }

  .left-side {
    min-height: 40vh;
  }

  .right-side {
    min-height: 60vh;
    padding: 30px 20px;
  }

  .brand-logo {
    font-size: 3rem;
  }

  .brand-tagline {
    font-size: 1.2rem;
  }

  .feature-item {
    padding: 12px 16px;
  }
}

@media (max-width: 768px) {
  .left-side {
    min-height: 30vh;
    padding: 20px;
  }

  .right-side {
    padding: 20px 15px;
  }

  .brand-logo {
    font-size: 2.5rem;
    margin-bottom: 15px;
  }

  .brand-tagline {
    font-size: 1rem;
    margin-bottom: 20px;
  }

  .brand-features {
    gap: 15px;
    margin-top: 20px;
  }

  .feature-item {
    padding: 10px 15px;
    font-size: 0.9rem;
  }

  .form-title {
    font-size: 2rem;
  }

  .form-subtitle {
    font-size: 1rem;
  }

  .input-field {
    padding: 16px 18px;
    font-size: 16px; /* Évite le zoom sur iOS */
  }

  .form-options {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .login-page {
    min-height: 100vh;
  }

  .left-side {
    min-height: 25vh;
    padding: 15px;
  }

  .right-side {
    padding: 15px 10px;
  }

  .brand-logo {
    font-size: 2rem;
  }

  .brand-features {
    display: none; /* Masquer sur très petits écrans */
  }

  .form-section {
    max-width: 100%;
  }

  .form-title {
    font-size: 1.8rem;
  }

  .circle {
    display: none; /* Masquer les éléments décoratifs */
  }
}

/* Améliorations d'accessibilité */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
  .right-side {
    background: #1e293b;
    color: white;
  }

  .form-title {
    color: white;
  }

  .input-field {
    background: #334155;
    border-color: #475569;
    color: white;
  }

  .input-field:focus {
    background: #475569;
  }

  .divider-text {
    background: #1e293b;
  }

  .form-footer {
    border-color: #475569;
  }
}

/* Animations */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

/* Classes d'animation */
.animate-slide-in {
  animation: slideIn 0.8s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out;
}

.animate-slide-in-left {
  animation: slideInLeft 0.6s ease-out;
  animation-fill-mode: both;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out;
}

.animate-fade-in-up-delay {
  animation: fadeInUp 0.8s ease-out 0.3s;
  animation-fill-mode: both;
}

.animate-fade-in-down {
  animation: fadeInDown 0.6s ease-out 0.2s;
  animation-fill-mode: both;
}

.animate-bounce-in {
  animation: bounceIn 1s ease-out;
}

.animate-bounce-in-delay {
  animation: bounceIn 1s ease-out 0.2s;
  animation-fill-mode: both;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

/* Hover animations */
.login-button {
  transition: all 0.3s ease;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.3);
}

.register-button {
  transition: all 0.3s ease;
}

.register-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(124, 58, 237, 0.3);
}

.input-field {
  transition: all 0.3s ease;
}

.input-field:focus {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(37, 99, 235, 0.1);
}

.feature-item {
  transition: all 0.3s ease;
}

.feature-item:hover {
  transform: translateX(10px);
}

.feature-item:hover .feature-icon {
  transform: scale(1.2);
}

/* Accessibilité et SEO */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Focus visible pour l'accessibilité */
.input-field:focus-visible,
.login-button:focus-visible,
.register-button:focus-visible {
  outline: 2px solid #2563eb;
  outline-offset: 2px;
}

/* Amélioration du contraste pour l'accessibilité */
@media (prefers-contrast: high) {
  .brand-tagline,
  .feature-text {
    color: #ffffff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

/* Respect des préférences de mouvement réduit */
@media (prefers-reduced-motion: reduce) {
  .animate-slide-in,
  .animate-slide-in-right,
  .animate-slide-in-left,
  .animate-fade-in-up,
  .animate-fade-in-up-delay,
  .animate-fade-in-down,
  .animate-bounce-in,
  .animate-bounce-in-delay,
  .animate-pulse {
    animation: none;
  }

  .login-button:hover,
  .register-button:hover,
  .input-field:focus,
  .feature-item:hover {
    transform: none;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .split-container {
    flex-direction: column;
  }

  .left-side,
  .right-side {
    flex: none;
    min-height: 50vh;
  }

  .form-section {
    max-width: 100%;
    margin: 20px;
  }
}
</style>
