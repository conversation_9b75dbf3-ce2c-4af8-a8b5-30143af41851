import { mount } from '@vue/test-utils'
import { createStore } from 'vuex'
import { createRouter, createWebHistory } from 'vue-router'
import ProfileView from '@/views/common/ProfileView.vue'

// Mock du router
const router = createRouter({
  history: createWebHistory(),
  routes: [
    { path: '/profile/:id', component: ProfileView }
  ]
})

// Mock du store
const createMockStore = (state = {}) => {
  return createStore({
    state: {
      user: { id: 1, prenom: 'TestUser' },
      userType: 'amical',
      currentProfile: null,
      ...state
    },
    getters: {
      user: state => state.user,
      userType: state => state.userType
    },
    actions: {
      loadProfile: jest.fn(),
      likeProfile: jest.fn(),
      superLikeProfile: jest.fn()
    },
    commit: jest.fn()
  })
}

describe('ProfileView', () => {
  let wrapper
  let store
  let mockProfile

  beforeEach(() => {
    mockProfile = {
      id: 2,
      prenom: 'Alice',
      age: 28,
      ville: 'Paris',
      photos: ['/avatars/alice.jpg', '/avatars/alice2.jpg'],
      description: 'Passionnée de voyages et de photographie',
      isOnline: true,
      isVerified: true,
      stats: {
        profileViews: 150,
        likes: 45,
        matches: 12,
        events: 8
      },
      hobbies: 'voyage, photographie, cuisine',
      posts: [
        {
          id: 1,
          content: 'Belle journée !',
          createdAt: '2024-01-15T10:00:00Z',
          likesCount: 5,
          commentsCount: 2,
          media: []
        }
      ]
    }

    store = createMockStore({
      currentProfile: mockProfile
    })

    // Mock de la route
    router.push('/profile/2')
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  const createWrapper = (options = {}) => {
    return mount(ProfileView, {
      global: {
        plugins: [store, router],
        mocks: {
          $route: {
            params: { id: '2' }
          },
          $router: {
            push: jest.fn(),
            go: jest.fn()
          }
        }
      },
      ...options
    })
  }

  describe('Rendering', () => {
    it('should render profile information correctly', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.profile-name').text()).toContain('Alice')
      expect(wrapper.find('.profile-name').text()).toContain('28')
      expect(wrapper.find('.profile-location').text()).toContain('Paris')
      expect(wrapper.find('.description-text').text()).toBe(mockProfile.description)
    })

    it('should show online indicator when user is online', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.online-indicator').exists()).toBe(true)
    })

    it('should show verified badge when user is verified', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      expect(wrapper.find('.verified-badge').exists()).toBe(true)
    })

    it('should display profile stats', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const stats = wrapper.findAll('.stat-number')
      expect(stats[0].text()).toBe('150')
      expect(stats[1].text()).toBe('45')
      expect(stats[2].text()).toBe('12')
      expect(stats[3].text()).toBe('8')
    })

    it('should display hobbies as tags', async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()

      const hobbyTags = wrapper.findAll('.hobby-tag')
      expect(hobbyTags).toHaveLength(3)
      expect(hobbyTags[0].text()).toBe('voyage')
      expect(hobbyTags[1].text()).toBe('photographie')
      expect(hobbyTags[2].text()).toBe('cuisine')
    })
  })

  describe('Loading State', () => {
    it('should show loading spinner when loading', async () => {
      store.state.currentProfile = null
      wrapper = createWrapper()
      
      // Simuler l'état de chargement
      await wrapper.setData({ loading: true })

      expect(wrapper.find('.loading-spinner').exists()).toBe(true)
      expect(wrapper.find('.loading-state').text()).toContain('Chargement du profil')
    })

    it('should show error state when profile not found', async () => {
      store.state.currentProfile = null
      wrapper = createWrapper()
      
      await wrapper.setData({ loading: false })

      expect(wrapper.find('.error-state').exists()).toBe(true)
      expect(wrapper.find('.error-state h2').text()).toBe('Profil introuvable')
    })
  })

  describe('Tabs Navigation', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('should show about tab by default', () => {
      expect(wrapper.vm.activeTab).toBe('about')
      expect(wrapper.find('.tab-button.active').text()).toContain('À propos')
    })

    it('should switch to photos tab', async () => {
      const photosTab = wrapper.findAll('.tab-button')[1]
      await photosTab.trigger('click')

      expect(wrapper.vm.activeTab).toBe('photos')
      expect(wrapper.find('.photos-section').exists()).toBe(true)
    })

    it('should switch to posts tab', async () => {
      const postsTab = wrapper.findAll('.tab-button')[2]
      await postsTab.trigger('click')

      expect(wrapper.vm.activeTab).toBe('posts')
      expect(wrapper.find('.posts-section').exists()).toBe(true)
    })

    it('should show correct tab count in navigation', () => {
      const photosTab = wrapper.findAll('.tab-button')[1]
      expect(photosTab.text()).toContain('Photos (2)')
    })
  })

  describe('Photos Section', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      await wrapper.setData({ activeTab: 'photos' })
    })

    it('should display photos grid', () => {
      const photoItems = wrapper.findAll('.photo-item')
      expect(photoItems).toHaveLength(2)
    })

    it('should open photo viewer on photo click', async () => {
      const firstPhoto = wrapper.find('.photo-item')
      await firstPhoto.trigger('click')

      expect(wrapper.vm.photoViewerOpen).toBe(true)
      expect(wrapper.vm.currentPhotoIndex).toBe(0)
      expect(wrapper.find('.photo-viewer').exists()).toBe(true)
    })
  })

  describe('Photo Viewer', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      await wrapper.setData({ 
        activeTab: 'photos',
        photoViewerOpen: true,
        currentPhotoIndex: 0
      })
    })

    it('should show current photo', () => {
      const viewerImage = wrapper.find('.viewer-image')
      expect(viewerImage.attributes('src')).toBe('/avatars/alice.jpg')
    })

    it('should show photo counter', () => {
      const counter = wrapper.find('.photo-counter')
      expect(counter.text()).toBe('1 / 2')
    })

    it('should navigate to next photo', async () => {
      const nextButton = wrapper.find('.btn-nav.next')
      await nextButton.trigger('click')

      expect(wrapper.vm.currentPhotoIndex).toBe(1)
    })

    it('should navigate to previous photo', async () => {
      await wrapper.setData({ currentPhotoIndex: 1 })
      
      const prevButton = wrapper.find('.btn-nav.prev')
      await prevButton.trigger('click')

      expect(wrapper.vm.currentPhotoIndex).toBe(0)
    })

    it('should close photo viewer', async () => {
      const closeButton = wrapper.find('.btn-close-viewer')
      await closeButton.trigger('click')

      expect(wrapper.vm.photoViewerOpen).toBe(false)
    })

    it('should disable navigation buttons at boundaries', async () => {
      // Au début
      expect(wrapper.find('.btn-nav.prev').attributes('disabled')).toBeDefined()
      
      // À la fin
      await wrapper.setData({ currentPhotoIndex: 1 })
      await wrapper.vm.$nextTick()
      
      expect(wrapper.find('.btn-nav.next').attributes('disabled')).toBeDefined()
    })
  })

  describe('Profile Actions', () => {
    describe('For other users profile', () => {
      beforeEach(async () => {
        wrapper = createWrapper()
        await wrapper.vm.$nextTick()
      })

      it('should show action buttons for other users', () => {
        expect(wrapper.find('.btn-like').exists()).toBe(true)
        expect(wrapper.find('.btn-message').exists()).toBe(true)
        expect(wrapper.find('.btn-super-like').exists()).toBe(true)
        expect(wrapper.find('.btn-block').exists()).toBe(true)
      })

      it('should handle like action', async () => {
        const likeButton = wrapper.find('.btn-like')
        await likeButton.trigger('click')

        expect(store._actions.likeProfile[0]).toHaveBeenCalledWith(
          expect.any(Object),
          mockProfile.id
        )
      })

      it('should handle super like action', async () => {
        const superLikeButton = wrapper.find('.btn-super-like')
        await superLikeButton.trigger('click')

        expect(store._actions.superLikeProfile[0]).toHaveBeenCalledWith(
          expect.any(Object),
          mockProfile.id
        )
      })

      it('should navigate to messages on message button click', async () => {
        const messageButton = wrapper.find('.btn-message')
        await messageButton.trigger('click')

        expect(wrapper.vm.$router.push).toHaveBeenCalledWith(`/messages/${mockProfile.id}`)
      })
    })

    describe('For own profile', () => {
      beforeEach(async () => {
        store.state.currentProfile = { ...mockProfile, id: 1 } // Same as user ID
        wrapper = createWrapper()
        await wrapper.vm.$nextTick()
      })

      it('should show edit buttons for own profile', () => {
        expect(wrapper.find('.btn-edit').exists()).toBe(true)
        expect(wrapper.find('.btn-settings').exists()).toBe(true)
        expect(wrapper.find('.btn-like').exists()).toBe(false)
      })

      it('should navigate to edit profile', async () => {
        const editButton = wrapper.find('.btn-edit')
        await editButton.trigger('click')

        expect(wrapper.vm.$router.push).toHaveBeenCalledWith('/profile/edit')
      })

      it('should navigate to settings', async () => {
        const settingsButton = wrapper.find('.btn-settings')
        await settingsButton.trigger('click')

        expect(wrapper.vm.$router.push).toHaveBeenCalledWith('/settings')
      })
    })
  })

  describe('Posts Section', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
      await wrapper.setData({ activeTab: 'posts' })
    })

    it('should display posts', () => {
      const postCards = wrapper.findAll('.post-card')
      expect(postCards).toHaveLength(1)
    })

    it('should show post content', () => {
      const postText = wrapper.find('.post-text')
      expect(postText.text()).toBe('Belle journée !')
    })

    it('should show post stats', () => {
      const postStats = wrapper.find('.post-stats')
      expect(postStats.text()).toContain('5 ❤️')
      expect(postStats.text()).toContain('2 💬')
    })
  })

  describe('Computed Properties', () => {
    beforeEach(async () => {
      wrapper = createWrapper()
      await wrapper.vm.$nextTick()
    })

    it('should correctly identify own profile', () => {
      expect(wrapper.vm.isOwnProfile).toBe(false)
      
      // Changer pour le profil de l'utilisateur connecté
      store.state.currentProfile = { ...mockProfile, id: 1 }
      expect(wrapper.vm.isOwnProfile).toBe(true)
    })

    it('should get correct profile status', () => {
      expect(wrapper.vm.getProfileStatus()).toBe('En ligne')
      
      // Tester avec un utilisateur hors ligne
      store.state.currentProfile = { ...mockProfile, isOnline: false }
      expect(wrapper.vm.getProfileStatus()).toBe('Hors ligne')
    })
  })

  describe('Lifecycle', () => {
    it('should load profile on mount', async () => {
      wrapper = createWrapper()
      
      expect(store._actions.loadProfile[0]).toHaveBeenCalledWith(
        expect.any(Object),
        '2'
      )
    })

    it('should watch route changes', async () => {
      wrapper = createWrapper()
      
      // Simuler un changement de route
      await wrapper.vm.$options.watch['$route.params.id'].handler.call(wrapper.vm, '3')
      
      expect(store._actions.loadProfile[0]).toHaveBeenCalledWith(
        expect.any(Object),
        '3'
      )
    })
  })
})
