<template>
  <div class="vosk-test-container">
    <div class="header">
      <h1>🧪 Tests API Vosk</h1>
      <p>Interface de test pour l'API Vosk backend</p>
    </div>

    <div class="test-controls">
      <button 
        @click="runQuickTest" 
        :disabled="isRunning"
        class="btn btn-primary"
      >
        ⚡ Test Rapide
      </button>
      
      <button 
        @click="runAllTests" 
        :disabled="isRunning"
        class="btn btn-success"
      >
        🧪 Tests Complets
      </button>
      
      <button 
        @click="clearResults" 
        :disabled="isRunning"
        class="btn btn-secondary"
      >
        🗑️ Effacer
      </button>
    </div>

    <div v-if="isRunning" class="loading">
      <div class="spinner"></div>
      <p>Tests en cours...</p>
    </div>

    <div v-if="testResults.length > 0" class="results-section">
      <h2>📊 Résultats des Tests</h2>
      
      <div class="summary">
        <div class="stat success">
          <span class="number">{{ successCount }}</span>
          <span class="label">Réussis</span>
        </div>
        <div class="stat failed">
          <span class="number">{{ failedCount }}</span>
          <span class="label">Échoués</span>
        </div>
        <div class="stat total">
          <span class="number">{{ testResults.length }}</span>
          <span class="label">Total</span>
        </div>
      </div>

      <div class="test-list">
        <div 
          v-for="test in testResults" 
          :key="test.name"
          :class="['test-item', test.status.toLowerCase()]"
        >
          <div class="test-header">
            <span class="test-icon">
              {{ test.status === 'SUCCESS' ? '✅' : '❌' }}
            </span>
            <span class="test-name">{{ test.name }}</span>
            <span class="test-time">{{ formatTime(test.timestamp) }}</span>
          </div>
          
          <div v-if="test.result" class="test-result">
            <pre>{{ JSON.stringify(test.result, null, 2) }}</pre>
          </div>
          
          <div v-if="test.error" class="test-error">
            <strong>Erreur:</strong> {{ test.error }}
          </div>
        </div>
      </div>
    </div>

    <div class="api-info">
      <h3>🔗 Informations API</h3>
      <div class="info-grid">
        <div class="info-item">
          <strong>Base URL:</strong> {{ baseUrl }}
        </div>
        <div class="info-item">
          <strong>Endpoints testés:</strong>
          <ul>
            <li>GET /api/vosk/status/</li>
            <li>POST /api/vosk/speech-to-text/</li>
            <li>GET /api/vosk/languages/</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import voskTestService from '@/_services/vosk-test.service.js';

export default {
  name: 'VoskTestView',
  data() {
    return {
      isRunning: false,
      testResults: [],
      baseUrl: 'http://127.0.0.1:8000/api'
    };
  },
  computed: {
    successCount() {
      return this.testResults.filter(t => t.status === 'SUCCESS').length;
    },
    failedCount() {
      return this.testResults.filter(t => t.status === 'FAILED').length;
    }
  },
  methods: {
    async runQuickTest() {
      this.isRunning = true;
      console.log('⚡ Lancement du test rapide...');
      
      try {
        const result = await voskTestService.quickTest();
        this.testResults = [{
          name: 'Test Rapide API Vosk',
          status: result ? 'SUCCESS' : 'FAILED',
          result: result ? { message: 'API Vosk disponible' } : null,
          error: result ? null : 'API Vosk indisponible',
          timestamp: new Date().toISOString()
        }];
      } catch (error) {
        console.error('Erreur test rapide:', error);
      } finally {
        this.isRunning = false;
      }
    },

    async runAllTests() {
      this.isRunning = true;
      console.log('🧪 Lancement des tests complets...');
      
      try {
        const results = await voskTestService.runAllTests();
        this.testResults = results;
      } catch (error) {
        console.error('Erreur tests complets:', error);
      } finally {
        this.isRunning = false;
      }
    },

    clearResults() {
      this.testResults = [];
      console.log('🗑️ Résultats effacés');
    },

    formatTime(timestamp) {
      return new Date(timestamp).toLocaleTimeString();
    }
  },
  mounted() {
    console.log('🧪 Page de test Vosk chargée');
  }
};
</script>

<style scoped>
.vosk-test-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.test-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 30px;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.loading {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.results-section {
  margin-top: 30px;
}

.summary {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 30px;
}

.stat {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  min-width: 100px;
}

.stat.success {
  background: #d5f4e6;
  color: #27ae60;
}

.stat.failed {
  background: #fadbd8;
  color: #e74c3c;
}

.stat.total {
  background: #ebf3fd;
  color: #3498db;
}

.stat .number {
  display: block;
  font-size: 2em;
  font-weight: bold;
}

.stat .label {
  font-size: 0.9em;
  text-transform: uppercase;
}

.test-list {
  space-y: 15px;
}

.test-item {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
}

.test-item.success {
  border-left: 4px solid #27ae60;
  background: #f8fff9;
}

.test-item.failed {
  border-left: 4px solid #e74c3c;
  background: #fff8f8;
}

.test-header {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 10px;
}

.test-icon {
  font-size: 1.2em;
}

.test-name {
  font-weight: 600;
  flex: 1;
}

.test-time {
  color: #666;
  font-size: 0.9em;
}

.test-result, .test-error {
  margin-top: 15px;
  padding: 15px;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
}

.test-result {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
}

.test-error {
  background: #fff5f5;
  border: 1px solid #fed7d7;
  color: #c53030;
}

.test-result pre {
  margin: 0;
  white-space: pre-wrap;
  font-size: 0.9em;
}

.api-info {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-grid {
  display: grid;
  gap: 15px;
}

.info-item {
  padding: 10px 0;
}

.info-item ul {
  margin: 10px 0 0 20px;
}

.info-item li {
  margin: 5px 0;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}
</style>
