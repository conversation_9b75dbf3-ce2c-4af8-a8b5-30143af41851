<template>
  <div class="checkout-view">
    <div class="container">
      <!-- En-tête -->
      <div class="checkout-header">
        <h1>Finaliser votre abonnement</h1>
        <div class="security-badges">
          <span class="badge">🔒 Paiement sécurisé</span>
          <span class="badge">✅ SSL</span>
          <span class="badge">💳 Stripe</span>
        </div>
      </div>

      <div class="checkout-content">
        <!-- Résumé de la commande -->
        <div class="order-summary">
          <h2>Résumé de votre commande</h2>
          
          <div v-if="selectedPlan" class="plan-details">
            <div class="plan-card">
              <div class="plan-header">
                <h3>{{ selectedPlan.name }}</h3>
                <div class="plan-price">
                  {{ formatPrice(selectedPlan.price) }} €
                  <span class="billing-cycle">/ {{ getBillingCycleText(selectedPlan.billing_cycle) }}</span>
                </div>
              </div>
              
              <div class="plan-features">
                <h4>Inclus dans votre abonnement :</h4>
                <ul>
                  <li v-for="feature in selectedPlan.features" :key="feature">
                    ✅ {{ feature }}
                  </li>
                </ul>
              </div>
            </div>
          </div>

          <!-- Détails de facturation -->
          <div class="billing-details">
            <div class="billing-line">
              <span>Sous-total</span>
              <span>{{ formatPrice(selectedPlan?.price || 0) }} €</span>
            </div>
            
            <div class="billing-line" v-if="taxAmount > 0">
              <span>TVA ({{ taxRate }}%)</span>
              <span>{{ formatPrice(taxAmount) }} €</span>
            </div>
            
            <div class="billing-line total">
              <span>Total</span>
              <span>{{ formatPrice(totalAmount) }} €</span>
            </div>
          </div>

          <!-- Garanties -->
          <div class="guarantees">
            <div class="guarantee-item">
              <span class="icon">🔄</span>
              <span>Annulation à tout moment</span>
            </div>
            <div class="guarantee-item">
              <span class="icon">💰</span>
              <span>Remboursement sous 14 jours</span>
            </div>
            <div class="guarantee-item">
              <span class="icon">🛡️</span>
              <span>Données sécurisées</span>
            </div>
          </div>
        </div>

        <!-- Formulaire de paiement -->
        <div class="payment-section">
          <!-- Méthodes de paiement existantes -->
          <div v-if="hasPaymentMethods && !useNewCard" class="saved-methods">
            <h2>Choisir une méthode de paiement</h2>
            
            <div class="payment-methods-list">
              <div
                v-for="method in paymentMethods"
                :key="method.id"
                class="payment-method-option"
                :class="{ 'selected': selectedMethodId === method.id }"
                @click="selectedMethodId = method.id"
              >
                <input
                  type="radio"
                  :id="`method-${method.id}`"
                  :value="method.id"
                  v-model="selectedMethodId"
                />
                <label :for="`method-${method.id}`" class="method-label">
                  <img :src="getBrandIcon(method.card.brand)" :alt="method.card.brand" />
                  <span class="card-info">
                    **** **** **** {{ method.card.last4 }}
                    <small>Expire {{ method.card.exp_month.toString().padStart(2, '0') }}/{{ method.card.exp_year }}</small>
                  </span>
                  <span v-if="method.is_default" class="default-badge">Par défaut</span>
                </label>
              </div>
            </div>

            <button @click="useNewCard = true" class="btn-new-card">
              + Utiliser une nouvelle carte
            </button>
          </div>

          <!-- Nouvelle carte ou pas de méthodes sauvegardées -->
          <div v-if="!hasPaymentMethods || useNewCard" class="new-card-section">
            <div class="section-header">
              <h2>Informations de paiement</h2>
              <button
                v-if="hasPaymentMethods"
                @click="useNewCard = false"
                class="btn-back"
              >
                ← Utiliser une carte sauvegardée
              </button>
            </div>

            <CreditCardForm
              :amount="totalAmount"
              :description="`Abonnement ${selectedPlan?.name}`"
              :processing="processing"
              submit-text="Finaliser l'abonnement"
              @submit="handleNewCardPayment"
              @cancel="$router.go(-1)"
            />
          </div>

          <!-- Paiement avec méthode sauvegardée -->
          <div v-if="hasPaymentMethods && !useNewCard" class="saved-card-payment">
            <button
              @click="handleSavedCardPayment"
              class="btn-pay-final"
              :disabled="!selectedMethodId || processing"
            >
              <span v-if="processing" class="spinner"></span>
              <span v-else>
                Finaliser l'abonnement - {{ formatPrice(totalAmount) }} €
              </span>
            </button>
          </div>
        </div>
      </div>

      <!-- Conditions d'utilisation -->
      <div class="terms-section">
        <p>
          En finalisant votre commande, vous acceptez nos
          <router-link to="/terms" target="_blank">Conditions d'utilisation</router-link>
          et notre
          <router-link to="/privacy" target="_blank">Politique de confidentialité</router-link>.
          Votre abonnement se renouvellera automatiquement sauf annulation.
        </p>
      </div>
    </div>

    <!-- Messages d'erreur/succès -->
    <div v-if="error" class="alert alert-error">
      {{ error }}
      <button @click="clearError" class="alert-close">×</button>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import CreditCardForm from '@/components/payment/CreditCardForm.vue'

export default {
  name: 'CheckoutView',
  
  components: {
    CreditCardForm
  },
  
  data() {
    return {
      selectedPlan: null,
      selectedMethodId: null,
      useNewCard: false,
      processing: false,
      taxRate: 20, // TVA française
      error: null
    }
  },
  
  computed: {
    ...mapState('payment', ['loading']),
    ...mapGetters('payment', ['paymentMethods', 'hasPaymentMethods']),
    
    taxAmount() {
      if (!this.selectedPlan) return 0
      return (this.selectedPlan.price * this.taxRate) / 100
    },
    
    totalAmount() {
      if (!this.selectedPlan) return 0
      return this.selectedPlan.price + this.taxAmount
    }
  },
  
  async mounted() {
    await this.initializeCheckout()
  },
  
  methods: {
    ...mapActions('payment', [
      'loadPaymentMethods',
      'payWithSavedMethod',
      'processCardPayment',
      'clearError'
    ]),
    
    async initializeCheckout() {
      try {
        // Récupérer l'ID du plan depuis les paramètres de route
        const planId = this.$route.params.planId || this.$route.query.plan
        
        if (!planId) {
          this.$router.push('/abonnement')
          return
        }
        
        // Charger les détails du plan
        await this.loadPlanDetails(planId)
        
        // Charger les méthodes de paiement existantes
        await this.loadPaymentMethods()
        
        // Sélectionner la méthode par défaut
        if (this.hasPaymentMethods) {
          const defaultMethod = this.paymentMethods.find(m => m.is_default)
          this.selectedMethodId = defaultMethod?.id || this.paymentMethods[0]?.id
        } else {
          this.useNewCard = true
        }
        
      } catch (error) {
        console.error('Erreur initialisation checkout:', error)
        this.error = 'Erreur lors du chargement des informations'
      }
    },
    
    async loadPlanDetails(planId) {
      try {
        const response = await fetch(`/api/abonnement/api/abonnements/${planId}/`)
        if (!response.ok) {
          throw new Error('Plan non trouvé')
        }
        this.selectedPlan = await response.json()
      } catch (error) {
        console.error('Erreur chargement plan:', error)
        this.$router.push('/abonnement')
      }
    },
    
    async handleSavedCardPayment() {
      if (!this.selectedMethodId) return
      
      try {
        this.processing = true
        this.error = null
        
        await this.payWithSavedMethod({
          paymentMethodId: this.selectedMethodId,
          amount: this.totalAmount,
          metadata: {
            plan_id: this.selectedPlan.id,
            plan_name: this.selectedPlan.name
          }
        })
        
        // Rediriger vers la page de succès
        this.$router.push('/payment/success')
        
      } catch (error) {
        this.error = error.message || 'Erreur lors du paiement'
      } finally {
        this.processing = false
      }
    },
    
    async handleNewCardPayment(paymentData) {
      try {
        this.processing = true
        this.error = null
        
        await this.processCardPayment({
          amount: this.totalAmount,
          billingDetails: paymentData.billingDetails,
          metadata: {
            plan_id: this.selectedPlan.id,
            plan_name: this.selectedPlan.name
          }
        })
        
        // Rediriger vers la page de succès
        this.$router.push('/payment/success')
        
      } catch (error) {
        this.error = error.message || 'Erreur lors du paiement'
      } finally {
        this.processing = false
      }
    },
    
    getBrandIcon(brand) {
      const icons = {
        visa: '/images/cards/visa.svg',
        mastercard: '/images/cards/mastercard.svg',
        amex: '/images/cards/amex.svg'
      }
      return icons[brand] || '/images/cards/generic.svg'
    },
    
    formatPrice(price) {
      return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(price)
    },
    
    getBillingCycleText(cycle) {
      const cycles = {
        monthly: 'mois',
        quarterly: 'trimestre',
        yearly: 'an'
      }
      return cycles[cycle] || cycle
    }
  }
}
</script>

<style scoped>
.checkout-view {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.checkout-header {
  text-align: center;
  margin-bottom: 40px;
}

.checkout-header h1 {
  color: #333;
  font-size: 32px;
  font-weight: 600;
  margin-bottom: 15px;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
}

.badge {
  background: #e8f5e8;
  color: #28a745;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 40px;
  margin-bottom: 40px;
}

/* Résumé de commande */
.order-summary {
  background: white;
  border-radius: 12px;
  padding: 30px;
  height: fit-content;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.order-summary h2 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
}

.plan-card {
  border: 2px solid #e1e5e9;
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 20px;
}

.plan-header {
  margin-bottom: 15px;
}

.plan-header h3 {
  color: #333;
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 5px;
}

.plan-price {
  font-size: 24px;
  font-weight: 700;
  color: #007bff;
}

.billing-cycle {
  font-size: 14px;
  color: #666;
  font-weight: 400;
}

.plan-features h4 {
  color: #333;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  padding: 5px 0;
  font-size: 14px;
  color: #666;
}

.billing-details {
  border-top: 1px solid #e1e5e9;
  padding-top: 20px;
  margin-bottom: 20px;
}

.billing-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  font-size: 16px;
}

.billing-line.total {
  border-top: 1px solid #e1e5e9;
  padding-top: 10px;
  font-weight: 600;
  font-size: 18px;
  color: #333;
}

.guarantees {
  border-top: 1px solid #e1e5e9;
  padding-top: 20px;
}

.guarantee-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.guarantee-item .icon {
  font-size: 16px;
}

/* Section paiement */
.payment-section {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.payment-section h2 {
  color: #333;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.btn-back {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 14px;
}

.btn-back:hover {
  text-decoration: underline;
}

.payment-methods-list {
  margin-bottom: 20px;
}

.payment-method-option {
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-method-option:hover,
.payment-method-option.selected {
  border-color: #007bff;
  background: #f8f9ff;
}

.payment-method-option input[type="radio"] {
  display: none;
}

.method-label {
  display: flex;
  align-items: center;
  gap: 15px;
  cursor: pointer;
  width: 100%;
}

.method-label img {
  height: 24px;
}

.card-info {
  flex: 1;
  font-weight: 500;
}

.card-info small {
  display: block;
  color: #666;
  font-weight: 400;
}

.default-badge {
  background: #28a745;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.btn-new-card {
  background: none;
  border: 2px dashed #007bff;
  color: #007bff;
  padding: 15px;
  border-radius: 8px;
  cursor: pointer;
  width: 100%;
  font-weight: 500;
}

.btn-new-card:hover {
  background: #f8f9ff;
}

.btn-pay-final {
  width: 100%;
  padding: 15px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-pay-final:hover:not(:disabled) {
  background: #0056b3;
}

.btn-pay-final:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Conditions */
.terms-section {
  text-align: center;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.terms-section a {
  color: #007bff;
  text-decoration: none;
}

.terms-section a:hover {
  text-decoration: underline;
}

/* Alertes */
.alert {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 20px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
  z-index: 1000;
  max-width: 400px;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
}

/* Responsive */
@media (max-width: 768px) {
  .checkout-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .security-badges {
    justify-content: center;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .method-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
}
</style>
