# 🐸 Coqui TTS - Guide de démarrage rapide

## 🚀 Installation et test en 3 minutes

### Étape 1 : Installer Coqui TTS

```bash
# Installation simple
pip install TTS

# Ou avec toutes les dépendances
pip install TTS[all]
```

### Étape 2 : <PERSON><PERSON><PERSON><PERSON> le serveur

```bash
# Méthode 1 : Serveur personnalisé <PERSON> (recommandé)
cd MeetVoice_frontend
python coqui_tts_server.py

# Méthode 2 : Serveur officiel Coqui
tts-server --model_name tts_models/fr/css10/vits --port 5002

# Méthode 3 : Docker
docker run --rm -it -p 5002:5002 ghcr.io/coqui-ai/tts-cpu
```

### Étape 3 : Tester dans MeetVoice

1. Ouvrez : http://localhost:8082/coqui-tts
2. Vérifiez que le serveur est connecté (✅)
3. Tapez votre texte et cliquez "Générer avec Coqui TTS"

## 🎯 Test rapide en ligne de commande

```bash
# Test simple
tts --text "Bonjour MeetVoice !" --model_name tts_models/fr/css10/vits --out_path test.wav

# Écouter le résultat (Linux/Mac)
aplay test.wav  # ou paplay test.wav

# Windows
start test.wav
```

## 🔧 Résolution des problèmes

### Problème : "TTS not found"
```bash
pip install --upgrade TTS
```

### Problème : "CUDA out of memory"
```bash
# Forcer l'utilisation du CPU
export CUDA_VISIBLE_DEVICES=""
python coqui_tts_server.py
```

### Problème : "Port 5002 already in use"
```bash
# Changer le port dans coqui_tts_server.py
PORT = 5003  # Ligne 25
```

### Problème : Serveur déconnecté dans MeetVoice
1. Vérifiez que le serveur fonctionne : http://localhost:5002/api/tts
2. Vérifiez les logs du serveur Python
3. Redémarrez le serveur si nécessaire

## 🎤 Modèles recommandés

### Pour commencer (rapide)
```bash
tts-server --model_name tts_models/fr/css10/vits
```

### Pour la qualité maximale
```bash
tts-server --model_name tts_models/multilingual/multi-dataset/xtts_v2
```

### Pour le clonage vocal
```python
from TTS.api import TTS
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")
tts.tts_to_file(
    text="Bonjour !",
    speaker_wav="votre_voix.wav",  # 3-10 secondes d'audio
    language="fr",
    file_path="output.wav"
)
```

## 📊 Comparaison des performances

| Modèle | Qualité | Vitesse | Taille | Clonage vocal |
|--------|---------|---------|--------|---------------|
| css10/vits | ⭐⭐⭐⭐ | ⚡⚡⚡ | 50MB | ❌ |
| xtts_v2 | ⭐⭐⭐⭐⭐ | ⚡⚡ | 1.8GB | ✅ |
| fairseq/vits | ⭐⭐⭐ | ⚡⚡⚡ | 100MB | ❌ |

## 🌟 Exemples d'utilisation

### API Python directe
```python
from TTS.api import TTS

# Initialiser
tts = TTS("tts_models/fr/css10/vits")

# Générer audio
wav = tts.tts("Bonjour MeetVoice !")

# Sauvegarder
tts.tts_to_file("Bonjour MeetVoice !", file_path="output.wav")
```

### Requête HTTP au serveur
```javascript
// JavaScript/Frontend
const response = await fetch('http://localhost:5002/api/tts', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    text: 'Bonjour MeetVoice !',
    model_name: 'tts_models/fr/css10/vits'
  })
});

const audioBlob = await response.blob();
const audioUrl = URL.createObjectURL(audioBlob);
```

### cURL
```bash
curl -X POST http://localhost:5002/api/tts \
  -H "Content-Type: application/json" \
  -d '{"text":"Bonjour MeetVoice !"}' \
  --output test.wav
```

## 🎯 Intégration dans MeetVoice

### 1. Interview vocal avec Coqui TTS
Le composant `VoiceInterview.vue` peut utiliser Coqui TTS pour des questions plus naturelles.

### 2. Configuration des médias
Le composant `MediaDeviceConfig.vue` inclut maintenant un test Coqui TTS.

### 3. Synthèse vocale avancée
Page dédiée : http://localhost:8082/coqui-tts

## 🔗 Liens utiles

- **GitHub** : https://github.com/coqui-ai/TTS
- **Documentation** : https://tts.readthedocs.io/
- **Modèles** : https://github.com/coqui-ai/TTS/releases
- **Démo XTTS** : https://huggingface.co/spaces/coqui/xtts

## 💡 Conseils

1. **Commencez par css10/vits** - Rapide et bonne qualité
2. **Utilisez XTTS v2** pour le clonage vocal
3. **Gardez le serveur en marche** pendant les tests
4. **Testez différents textes** pour évaluer la qualité
5. **Surveillez l'utilisation mémoire** avec les gros modèles

## 🎉 Résultat attendu

Avec Coqui TTS, vous obtiendrez :
- ✅ Voix française ultra-naturelle
- ✅ Qualité professionnelle
- ✅ Latence faible (<1 seconde)
- ✅ Contrôle total (open source)
- ✅ Possibilité de clonage vocal

**Coqui TTS transforme complètement la qualité vocale de MeetVoice ! 🚀**
