use anyhow::Result;
use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde_json::{json, Value};
use std::sync::Arc;
use tokio::net::TcpListener;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};

mod config;
mod database;
mod models;
mod p2p;
mod websocket;

use config::AppConfig;
use database::DatabaseManager;
use p2p::P2PManager;
use websocket::WebSocketManager;

#[derive(Clone)]
pub struct AppState {
    pub db: Arc<DatabaseManager>,
    pub websocket: Arc<WebSocketManager>,
    pub p2p: Arc<P2PManager>,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialisation du logging
    tracing_subscriber::fmt()
        .with_env_filter("meetvoice_websocket=debug,libp2p=info")
        .init();

    info!("🚀 Démarrage du serveur MeetVoice WebSocket");

    // Chargement de la configuration
    let config = AppConfig::load()?;
    info!("✅ Configuration chargée");

    // Initialisation des bases de données
    let db_manager = Arc::new(DatabaseManager::new(&config).await?);
    info!("✅ Connexions aux bases de données établies");

    // Initialisation du gestionnaire WebSocket
    let websocket_manager = Arc::new(WebSocketManager::new());
    info!("✅ Gestionnaire WebSocket initialisé");

    // Initialisation du gestionnaire P2P
    let p2p_manager = Arc::new(P2PManager::new(&config).await?);
    info!("✅ Gestionnaire P2P initialisé");

    // État partagé de l'application
    let app_state = AppState {
        db: db_manager,
        websocket: websocket_manager,
        p2p: p2p_manager,
    };

    // Configuration des routes
    let app = Router::new()
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .route("/ws", get(websocket::websocket_handler))
        .route("/p2p/peers", get(get_peers))
        .route("/p2p/connect", post(connect_peer))
        .layer(CorsLayer::permissive())
        .with_state(app_state);

    // Démarrage du serveur
    let addr = format!("{}:{}", config.server.host, config.server.port);
    let listener = TcpListener::bind(&addr).await?;
    
    info!("🌐 Serveur en écoute sur http://{}", addr);
    info!("🔌 WebSocket disponible sur ws://{}/ws", addr);
    
    axum::serve(listener, app).await?;

    Ok(())
}

async fn health_check() -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "service": "MeetVoice WebSocket Server",
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": chrono::Utc::now()
    }))
}

async fn get_peers(State(state): State<AppState>) -> Result<Json<Value>, StatusCode> {
    match state.p2p.get_connected_peers().await {
        Ok(peers) => Ok(Json(json!({
            "peers": peers,
            "count": peers.len()
        }))),
        Err(e) => {
            warn!("Erreur lors de la récupération des peers: {}", e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}

async fn connect_peer(
    State(state): State<AppState>,
    Json(payload): Json<Value>,
) -> Result<Json<Value>, StatusCode> {
    let peer_addr = payload["address"]
        .as_str()
        .ok_or(StatusCode::BAD_REQUEST)?;

    match state.p2p.connect_to_peer(peer_addr).await {
        Ok(_) => Ok(Json(json!({
            "status": "connected",
            "peer": peer_addr
        }))),
        Err(e) => {
            warn!("Erreur lors de la connexion au peer {}: {}", peer_addr, e);
            Err(StatusCode::INTERNAL_SERVER_ERROR)
        }
    }
}
