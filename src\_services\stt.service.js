/** @format */

/**
 * Service de reconnaissance vocale utilisant l'API STT MeetVoice Backend
 * Remplace l'API browser peu fiable par une solution backend robuste
 */

class STTService {
  constructor() {
    this.baseUrl = 'http://127.0.0.1:8000/api/speech-to-text/';
    this.isRecording = false;
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.stream = null;
  }

  /**
   * Vérifier si l'API STT backend est disponible
   * @returns {Promise<boolean>} True si disponible
   */
  async checkAvailability() {
    try {
      const response = await fetch('http://127.0.0.1:8000/api/stt-languages/', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      return response.ok;
    } catch (error) {
      console.warn('⚠️ API STT backend non disponible:', error.message);
      return false;
    }
  }

  /**
   * Démarrer l'enregistrement audio pour la reconnaissance vocale
   * @param {Object} options - Options d'enregistrement
   * @returns {Promise<string>} Texte reconnu
   */
  async startListening(options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        // Vérifier la disponibilité de l'API
        const isAvailable = await this.checkAvailability();
        if (!isAvailable) {
          throw new Error('API STT backend non disponible');
        }

        // Configuration du microphone
        const constraints = {
          audio: {
            deviceId: options.microphoneId ? { exact: options.microphoneId } : undefined,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 16000, // Optimisé pour la reconnaissance vocale
            channelCount: 1 // Mono
          }
        };

        // Obtenir l'accès au microphone
        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('🎤 Accès microphone obtenu pour STT backend');

        // Initialiser MediaRecorder
        const mimeType = this.getSupportedMimeType();
        this.mediaRecorder = new MediaRecorder(this.stream, {
          mimeType: mimeType,
          audioBitsPerSecond: 128000
        });

        this.audioChunks = [];
        this.isRecording = true;

        // Événements MediaRecorder
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data);
          }
        };

        this.mediaRecorder.onstop = async () => {
          try {
            console.log('🎤 Enregistrement terminé, envoi au backend...');
            
            // Créer le blob audio
            const audioBlob = new Blob(this.audioChunks, { type: mimeType });
            
            // Envoyer au backend pour reconnaissance
            const transcription = await this.sendAudioToBackend(audioBlob, options);
            
            // Nettoyer les ressources
            this.cleanup();
            
            resolve(transcription);
            
          } catch (error) {
            this.cleanup();
            reject(error);
          }
        };

        this.mediaRecorder.onerror = (error) => {
          console.error('❌ Erreur MediaRecorder:', error);
          this.cleanup();
          reject(new Error(`Erreur d'enregistrement: ${error.message}`));
        };

        // Démarrer l'enregistrement
        this.mediaRecorder.start(1000); // Collecte des données toutes les secondes
        console.log('🎤 Enregistrement STT backend démarré');

        // Notifier le démarrage
        if (options.onStart) {
          options.onStart();
        }

        // Gestion du timeout manuel (mode continu)
        if (options.timeout === 0) {
          console.log('🔄 Mode manuel activé - enregistrement continu');
          // En mode manuel, on attend que stopListening() soit appelé
        } else {
          // Timeout automatique
          const timeout = options.timeout || 30000; // 30s par défaut
          setTimeout(() => {
            if (this.isRecording) {
              console.log('⏰ Timeout atteint, arrêt de l\'enregistrement');
              this.stopListening();
            }
          }, timeout);
        }

      } catch (error) {
        console.error('❌ Erreur démarrage STT backend:', error);
        this.cleanup();
        reject(error);
      }
    });
  }

  /**
   * Arrêter l'enregistrement et la reconnaissance
   */
  stopListening() {
    if (this.isRecording && this.mediaRecorder) {
      console.log('🛑 Arrêt de l\'enregistrement STT backend');
      this.isRecording = false;
      this.mediaRecorder.stop();
    }
  }

  /**
   * Envoyer l'audio au backend pour reconnaissance
   * @param {Blob} audioBlob - Données audio
   * @param {Object} options - Options de reconnaissance
   * @returns {Promise<string>} Texte reconnu
   */
  async sendAudioToBackend(audioBlob, options = {}) {
    try {
      console.log(`📤 Envoi audio au backend (${audioBlob.size} bytes)`);

      // Préparer FormData
      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.webm');
      formData.append('language', options.language || 'fr-FR');
      
      // Paramètres optionnels
      if (options.model) {
        formData.append('model', options.model);
      }

      // Envoyer la requête
      const response = await fetch(this.baseUrl, {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Erreur API STT (${response.status}): ${errorText}`);
      }

      const result = await response.json();
      console.log('✅ Réponse STT backend:', result);

      // Extraire le texte reconnu
      const transcription = result.text || result.transcription || '';
      
      if (!transcription.trim()) {
        console.warn('⚠️ Aucun texte reconnu par le backend');
        return '';
      }

      console.log('📝 Texte reconnu:', transcription);
      return transcription.trim();

    } catch (error) {
      console.error('❌ Erreur envoi audio au backend:', error);
      throw new Error(`Échec de la reconnaissance vocale: ${error.message}`);
    }
  }

  /**
   * Obtenir le type MIME supporté pour l'enregistrement
   * @returns {string} Type MIME
   */
  getSupportedMimeType() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        console.log('🎵 Type MIME sélectionné:', type);
        return type;
      }
    }

    console.warn('⚠️ Aucun type MIME optimal trouvé, utilisation par défaut');
    return 'audio/webm';
  }

  /**
   * Nettoyer les ressources
   */
  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;
    
    console.log('🧹 Ressources STT nettoyées');
  }

  /**
   * Vérifier si l'enregistrement est en cours
   * @returns {boolean} True si en cours d'enregistrement
   */
  isListening() {
    return this.isRecording;
  }
}

// Instance singleton
const sttService = new STTService();

export default sttService;
