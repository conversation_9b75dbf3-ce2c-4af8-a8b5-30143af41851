<template>
  <div class="search-view">
    <div class="container">
      <h1>Recherche</h1>
      
      <div class="search-content">
        <div class="search-form">
          <div class="search-input-group">
            <input 
              v-model="searchQuery" 
              type="text" 
              placeholder="Rechercher des utilisateurs, événements, groupes..."
              @keyup.enter="performSearch"
              class="search-input"
            >
            <button @click="performSearch" class="search-button">
              <span class="search-icon">🔍</span>
            </button>
          </div>
          
          <div class="search-filters">
            <label class="filter-label">
              <input v-model="filters.users" type="checkbox">
              Utilisateurs
            </label>
            
            <label class="filter-label">
              <input v-model="filters.events" type="checkbox">
              Événements
            </label>
            
            <label class="filter-label">
              <input v-model="filters.groups" type="checkbox">
              Groupes
            </label>
          </div>
        </div>
        
        <div v-if="loading" class="loading">
          <div class="spinner"></div>
          <p>Recherche en cours...</p>
        </div>
        
        <div v-else-if="searchResults.length > 0" class="search-results">
          <h2>Résultats de recherche ({{ searchResults.length }})</h2>
          
          <div class="results-list">
            <div 
              v-for="result in searchResults" 
              :key="result.id" 
              class="result-item"
              :class="result.type"
            >
              <div class="result-icon">{{ getResultIcon(result.type) }}</div>
              <div class="result-content">
                <h3>{{ result.title }}</h3>
                <p>{{ result.description }}</p>
                <span class="result-type">{{ getResultTypeText(result.type) }}</span>
              </div>
              <button @click="viewResult(result)" class="btn-view">Voir</button>
            </div>
          </div>
        </div>
        
        <div v-else-if="hasSearched" class="no-results">
          <div class="no-results-icon">🔍</div>
          <h2>Aucun résultat trouvé</h2>
          <p>Essayez avec d'autres mots-clés ou modifiez vos filtres</p>
        </div>
        
        <div v-else class="search-suggestions">
          <h2>Suggestions de recherche</h2>
          <div class="suggestions-list">
            <button 
              v-for="suggestion in suggestions" 
              :key="suggestion" 
              @click="searchSuggestion(suggestion)"
              class="suggestion-item"
            >
              {{ suggestion }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SearchView',
  
  data() {
    return {
      searchQuery: '',
      loading: false,
      hasSearched: false,
      searchResults: [],
      filters: {
        users: true,
        events: true,
        groups: true
      },
      suggestions: [
        'Soirée jeux de société',
        'Randonnée',
        'Cuisine',
        'Sport',
        'Musique',
        'Cinéma',
        'Voyage',
        'Art'
      ]
    }
  },
  
  methods: {
    async performSearch() {
      if (!this.searchQuery.trim()) return
      
      this.loading = true
      this.hasSearched = true
      
      try {
        // Simuler une recherche
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Résultats d'exemple
        this.searchResults = [
          {
            id: 1,
            type: 'user',
            title: 'Marie Dupont',
            description: 'Passionnée de jeux de société et de randonnée'
          },
          {
            id: 2,
            type: 'event',
            title: 'Soirée jeux de société',
            description: 'Venez passer une soirée conviviale autour de jeux'
          },
          {
            id: 3,
            type: 'group',
            title: 'Randonneurs du dimanche',
            description: 'Groupe pour les amateurs de randonnée'
          }
        ].filter(result => {
          // Filtrer selon les filtres sélectionnés
          if (result.type === 'user' && !this.filters.users) return false
          if (result.type === 'event' && !this.filters.events) return false
          if (result.type === 'group' && !this.filters.groups) return false
          
          // Filtrer selon la requête de recherche
          return result.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
                 result.description.toLowerCase().includes(this.searchQuery.toLowerCase())
        })
        
      } catch (error) {
        console.error('Erreur lors de la recherche:', error)
        this.searchResults = []
      } finally {
        this.loading = false
      }
    },
    
    searchSuggestion(suggestion) {
      this.searchQuery = suggestion
      this.performSearch()
    },
    
    viewResult(result) {
      // Rediriger selon le type de résultat
      switch (result.type) {
        case 'user':
          this.$router.push(`/profile/${result.id}`)
          break
        case 'event':
          this.$router.push(`/amical/events/${result.id}`)
          break
        case 'group':
          this.$router.push(`/groups/${result.id}`)
          break
      }
    },
    
    getResultIcon(type) {
      const icons = {
        user: '👤',
        event: '🎉',
        group: '👥'
      }
      return icons[type] || '📄'
    },
    
    getResultTypeText(type) {
      const types = {
        user: 'Utilisateur',
        event: 'Événement',
        group: 'Groupe'
      }
      return types[type] || 'Résultat'
    }
  }
}
</script>

<style scoped>
.search-view {
  padding: 40px 20px;
  max-width: 1000px;
  margin: 0 auto;
}

.search-view h1 {
  margin: 0 0 32px 0;
  color: #333;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
}

.search-content {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-form {
  margin-bottom: 32px;
}

.search-input-group {
  display: flex;
  gap: 0;
  margin-bottom: 20px;
}

.search-input {
  flex: 1;
  padding: 16px 20px;
  border: 2px solid #e9ecef;
  border-right: none;
  border-radius: 8px 0 0 8px;
  font-size: 16px;
  outline: none;
}

.search-input:focus {
  border-color: #007bff;
}

.search-button {
  background: #007bff;
  color: white;
  border: 2px solid #007bff;
  border-radius: 0 8px 8px 0;
  padding: 16px 20px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.search-button:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.search-icon {
  font-size: 18px;
}

.search-filters {
  display: flex;
  gap: 24px;
  justify-content: center;
}

.filter-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-weight: 500;
  color: #333;
}

.filter-label input[type="checkbox"] {
  width: 18px;
  height: 18px;
}

.loading {
  text-align: center;
  padding: 40px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.search-results h2 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.result-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.result-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.result-content {
  flex: 1;
}

.result-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.result-content p {
  margin: 0 0 8px 0;
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

.result-type {
  background: #007bff;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.btn-view {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-view:hover {
  background: #0056b3;
}

.no-results {
  text-align: center;
  padding: 60px 20px;
}

.no-results-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.no-results h2 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.no-results p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.search-suggestions {
  text-align: center;
}

.search-suggestions h2 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.suggestions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.suggestion-item {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
}

.suggestion-item:hover {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

@media (max-width: 768px) {
  .search-filters {
    flex-direction: column;
    align-items: center;
    gap: 16px;
  }
  
  .result-item {
    flex-direction: column;
    text-align: center;
  }
  
  .suggestions-list {
    flex-direction: column;
    align-items: center;
  }
}
</style>
