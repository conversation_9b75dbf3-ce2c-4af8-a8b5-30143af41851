<template>
  <div class="libertin-quick-actions">
    <h3>Actions rapides - Libertin</h3>
    <div class="actions-grid">
      <router-link to="/libertin/parties" class="action-card">
        <div class="action-icon">🎭</div>
        <h4>Soirées</h4>
        <p>Découvrir les soirées libertines</p>
      </router-link>
      
      <router-link to="/libertin/create-party" class="action-card">
        <div class="action-icon">✨</div>
        <h4>Organiser</h4>
        <p>Créer une soirée</p>
      </router-link>
      
      <router-link to="/libertin/groups" class="action-card">
        <div class="action-icon">👫</div>
        <h4>Groupes</h4>
        <p>Rejoindre des groupes</p>
      </router-link>
      
      <router-link to="/libertin/private" class="action-card">
        <div class="action-icon">🔒</div>
        <h4>Privé</h4>
        <p>Ren<PERSON><PERSON> discrètes</p>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LibertinQuickActions'
}
</script>

<style scoped>
.libertin-quick-actions {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.libertin-quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.action-card {
  display: block;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.action-card:hover {
  background: #f3e5f5;
  border-color: #9c27b0;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(156, 39, 176, 0.2);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.action-card p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-card {
    padding: 16px;
  }
  
  .action-icon {
    font-size: 24px;
  }
}
</style>
