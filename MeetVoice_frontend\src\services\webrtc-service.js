/**
 * Service WebRTC pour les appels vidéo/audio
 * Intégré avec le service WebSocket MeetVoice
 */

import meetVoiceWebSocketService from './meetvoice-websocket'

class WebRTCService {
  constructor() {
    this.localStream = null
    this.remoteStreams = new Map() // Map<userId, MediaStream>
    this.peerConnections = new Map() // Map<userId, RTCPeerConnection>
    this.isInCall = false
    this.currentCallType = null // 'voice' | 'video' | 'screen'
    
    // Configuration WebRTC
    this.rtcConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' }
      ],
      iceCandidatePoolSize: 10
    }
    
    // Contraintes média
    this.mediaConstraints = {
      voice: {
        video: false,
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000
        }
      },
      video: {
        video: {
          width: { ideal: 1280, max: 1920 },
          height: { ideal: 720, max: 1080 },
          frameRate: { ideal: 30, max: 60 }
        },
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 48000
        }
      },
      screen: {
        video: {
          mediaSource: 'screen',
          width: { ideal: 1920 },
          height: { ideal: 1080 },
          frameRate: { ideal: 30 }
        },
        audio: true
      }
    }
    
    // Événements WebSocket
    this.setupWebSocketListeners()
  }

  /**
   * Configurer les listeners WebSocket pour WebRTC
   */
  setupWebSocketListeners() {
    meetVoiceWebSocketService.on('WebRTCOffer', this.handleWebRTCOffer.bind(this))
    meetVoiceWebSocketService.on('WebRTCAnswer', this.handleWebRTCAnswer.bind(this))
    meetVoiceWebSocketService.on('ICECandidate', this.handleICECandidate.bind(this))
  }

  /**
   * Démarrer un appel (voice, video, ou screen)
   */
  async startCall(targetUserId, callType = 'voice') {
    try {
      console.log(`📞 Démarrage d'un appel ${callType} vers:`, targetUserId)
      
      // Obtenir le stream local
      this.localStream = await this.getUserMedia(callType)
      this.currentCallType = callType
      this.isInCall = true
      
      // Créer la connexion peer
      const peerConnection = await this.createPeerConnection(targetUserId)
      
      // Ajouter le stream local
      this.localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, this.localStream)
      })
      
      // Créer et envoyer l'offre
      const offer = await peerConnection.createOffer()
      await peerConnection.setLocalDescription(offer)
      
      // Envoyer l'offre via WebSocket
      meetVoiceWebSocketService.sendMessage({
        type: 'WebRTCOffer',
        data: {
          to_user: targetUserId,
          offer_type: this.mapCallTypeToOfferType(callType),
          sdp: offer.sdp
        }
      })
      
      console.log('📤 Offre WebRTC envoyée')
      return this.localStream
      
    } catch (error) {
      console.error('❌ Erreur lors du démarrage de l\'appel:', error)
      this.endCall()
      throw error
    }
  }

  /**
   * Répondre à un appel
   */
  async answerCall(fromUserId, offer, callType = 'voice') {
    try {
      console.log(`📞 Réponse à un appel ${callType} de:`, fromUserId)
      
      // Obtenir le stream local
      this.localStream = await this.getUserMedia(callType)
      this.currentCallType = callType
      this.isInCall = true
      
      // Créer la connexion peer
      const peerConnection = await this.createPeerConnection(fromUserId)
      
      // Ajouter le stream local
      this.localStream.getTracks().forEach(track => {
        peerConnection.addTrack(track, this.localStream)
      })
      
      // Définir la description distante (offre)
      await peerConnection.setRemoteDescription(new RTCSessionDescription({
        type: 'offer',
        sdp: offer.sdp
      }))
      
      // Créer et envoyer la réponse
      const answer = await peerConnection.createAnswer()
      await peerConnection.setLocalDescription(answer)
      
      // Envoyer la réponse via WebSocket
      meetVoiceWebSocketService.sendMessage({
        type: 'WebRTCAnswer',
        data: {
          to_user: fromUserId,
          sdp: answer.sdp
        }
      })
      
      console.log('📤 Réponse WebRTC envoyée')
      return this.localStream
      
    } catch (error) {
      console.error('❌ Erreur lors de la réponse à l\'appel:', error)
      this.endCall()
      throw error
    }
  }

  /**
   * Terminer l'appel
   */
  endCall() {
    console.log('📞 Fin de l\'appel')
    
    // Arrêter le stream local
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }
    
    // Fermer toutes les connexions peer
    this.peerConnections.forEach((pc, userId) => {
      pc.close()
      console.log(`🔌 Connexion fermée avec:`, userId)
    })
    this.peerConnections.clear()
    
    // Nettoyer les streams distants
    this.remoteStreams.clear()
    
    this.isInCall = false
    this.currentCallType = null
    
    // Notifier les composants
    this.emit('callEnded')
  }

  /**
   * Obtenir le média utilisateur
   */
  async getUserMedia(callType) {
    try {
      const constraints = this.mediaConstraints[callType]
      
      if (callType === 'screen') {
        // Partage d'écran
        return await navigator.mediaDevices.getDisplayMedia(constraints)
      } else {
        // Audio/Vidéo
        return await navigator.mediaDevices.getUserMedia(constraints)
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'accès aux médias:', error)
      throw new Error('Impossible d\'accéder à la caméra/microphone')
    }
  }

  /**
   * Créer une connexion peer
   */
  async createPeerConnection(userId) {
    const peerConnection = new RTCPeerConnection(this.rtcConfiguration)
    this.peerConnections.set(userId, peerConnection)
    
    // Gestion des candidats ICE
    peerConnection.onicecandidate = (event) => {
      if (event.candidate) {
        meetVoiceWebSocketService.sendMessage({
          type: 'ICECandidate',
          data: {
            to_user: userId,
            candidate: JSON.stringify(event.candidate)
          }
        })
      }
    }
    
    // Gestion du stream distant
    peerConnection.ontrack = (event) => {
      console.log('📺 Stream distant reçu de:', userId)
      const remoteStream = event.streams[0]
      this.remoteStreams.set(userId, remoteStream)
      this.emit('remoteStreamAdded', { userId, stream: remoteStream })
    }
    
    // Gestion des changements d'état de connexion
    peerConnection.onconnectionstatechange = () => {
      console.log(`🔌 État de connexion avec ${userId}:`, peerConnection.connectionState)
      
      if (peerConnection.connectionState === 'disconnected' || 
          peerConnection.connectionState === 'failed') {
        this.handlePeerDisconnected(userId)
      }
    }
    
    return peerConnection
  }

  /**
   * Gérer une offre WebRTC reçue
   */
  async handleWebRTCOffer(data) {
    console.log('📨 Offre WebRTC reçue:', data)
    
    // Notifier l'interface utilisateur de l'appel entrant
    this.emit('incomingCall', {
      fromUserId: data.from_user,
      offerType: data.offer_type,
      offer: { sdp: data.sdp }
    })
  }

  /**
   * Gérer une réponse WebRTC reçue
   */
  async handleWebRTCAnswer(data) {
    console.log('📨 Réponse WebRTC reçue:', data)
    
    const peerConnection = this.peerConnections.get(data.from_user)
    if (peerConnection) {
      await peerConnection.setRemoteDescription(new RTCSessionDescription({
        type: 'answer',
        sdp: data.sdp
      }))
      console.log('✅ Description distante définie')
    }
  }

  /**
   * Gérer un candidat ICE reçu
   */
  async handleICECandidate(data) {
    console.log('📨 Candidat ICE reçu:', data)
    
    const peerConnection = this.peerConnections.get(data.from_user)
    if (peerConnection) {
      try {
        const candidate = JSON.parse(data.candidate)
        await peerConnection.addIceCandidate(new RTCIceCandidate(candidate))
        console.log('✅ Candidat ICE ajouté')
      } catch (error) {
        console.error('❌ Erreur lors de l\'ajout du candidat ICE:', error)
      }
    }
  }

  /**
   * Gérer la déconnexion d'un peer
   */
  handlePeerDisconnected(userId) {
    console.log('🔌 Peer déconnecté:', userId)
    
    // Supprimer la connexion
    const peerConnection = this.peerConnections.get(userId)
    if (peerConnection) {
      peerConnection.close()
      this.peerConnections.delete(userId)
    }
    
    // Supprimer le stream distant
    this.remoteStreams.delete(userId)
    
    // Notifier l'interface
    this.emit('remoteStreamRemoved', { userId })
  }

  /**
   * Basculer le microphone
   */
  toggleMicrophone() {
    if (this.localStream) {
      const audioTrack = this.localStream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !audioTrack.enabled
        console.log('🎤 Microphone:', audioTrack.enabled ? 'activé' : 'désactivé')
        return audioTrack.enabled
      }
    }
    return false
  }

  /**
   * Basculer la caméra
   */
  toggleCamera() {
    if (this.localStream) {
      const videoTrack = this.localStream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !videoTrack.enabled
        console.log('📹 Caméra:', videoTrack.enabled ? 'activée' : 'désactivée')
        return videoTrack.enabled
      }
    }
    return false
  }

  /**
   * Changer de caméra (avant/arrière sur mobile)
   */
  async switchCamera() {
    if (this.localStream && this.currentCallType === 'video') {
      try {
        const videoTrack = this.localStream.getVideoTracks()[0]
        const currentFacingMode = videoTrack.getSettings().facingMode
        const newFacingMode = currentFacingMode === 'user' ? 'environment' : 'user'
        
        // Arrêter le track actuel
        videoTrack.stop()
        
        // Obtenir un nouveau stream avec la nouvelle caméra
        const newStream = await navigator.mediaDevices.getUserMedia({
          video: { facingMode: newFacingMode },
          audio: false
        })
        
        const newVideoTrack = newStream.getVideoTracks()[0]
        
        // Remplacer le track dans toutes les connexions peer
        this.peerConnections.forEach(pc => {
          const sender = pc.getSenders().find(s => s.track && s.track.kind === 'video')
          if (sender) {
            sender.replaceTrack(newVideoTrack)
          }
        })
        
        // Remplacer dans le stream local
        this.localStream.removeTrack(videoTrack)
        this.localStream.addTrack(newVideoTrack)
        
        console.log('📹 Caméra changée:', newFacingMode)
        
      } catch (error) {
        console.error('❌ Erreur lors du changement de caméra:', error)
      }
    }
  }

  /**
   * Mapper le type d'appel vers le type d'offre
   */
  mapCallTypeToOfferType(callType) {
    const mapping = {
      'voice': 'Voice',
      'video': 'Video',
      'screen': 'Screen'
    }
    return mapping[callType] || 'Voice'
  }

  /**
   * Obtenir les statistiques de l'appel
   */
  async getCallStats() {
    const stats = {}
    
    for (const [userId, pc] of this.peerConnections) {
      try {
        const rtcStats = await pc.getStats()
        stats[userId] = this.parseRTCStats(rtcStats)
      } catch (error) {
        console.error(`❌ Erreur lors de la récupération des stats pour ${userId}:`, error)
      }
    }
    
    return stats
  }

  /**
   * Parser les statistiques RTC
   */
  parseRTCStats(rtcStats) {
    const stats = {
      audio: { bitrate: 0, packetsLost: 0, jitter: 0 },
      video: { bitrate: 0, packetsLost: 0, frameRate: 0 }
    }
    
    rtcStats.forEach(report => {
      if (report.type === 'inbound-rtp') {
        if (report.kind === 'audio') {
          stats.audio.bitrate = report.bytesReceived * 8 / report.timestamp * 1000
          stats.audio.packetsLost = report.packetsLost
          stats.audio.jitter = report.jitter
        } else if (report.kind === 'video') {
          stats.video.bitrate = report.bytesReceived * 8 / report.timestamp * 1000
          stats.video.packetsLost = report.packetsLost
          stats.video.frameRate = report.framesPerSecond
        }
      }
    })
    
    return stats
  }

  /**
   * Système d'événements simple
   */
  on(event, callback) {
    if (!this.eventListeners) {
      this.eventListeners = new Map()
    }
    
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    
    this.eventListeners.get(event).push(callback)
  }

  off(event, callback) {
    if (this.eventListeners && this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.eventListeners && this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('❌ Erreur dans le listener WebRTC:', error)
        }
      })
    }
  }

  /**
   * Vérifier le support WebRTC
   */
  static isSupported() {
    return !!(navigator.mediaDevices && 
              navigator.mediaDevices.getUserMedia && 
              window.RTCPeerConnection)
  }

  /**
   * Obtenir les périphériques disponibles
   */
  async getAvailableDevices() {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices()
      return {
        audioInputs: devices.filter(d => d.kind === 'audioinput'),
        videoInputs: devices.filter(d => d.kind === 'videoinput'),
        audioOutputs: devices.filter(d => d.kind === 'audiooutput')
      }
    } catch (error) {
      console.error('❌ Erreur lors de l\'énumération des périphériques:', error)
      return { audioInputs: [], videoInputs: [], audioOutputs: [] }
    }
  }
}

// Instance singleton
const webRTCService = new WebRTCService()

export default webRTCService
