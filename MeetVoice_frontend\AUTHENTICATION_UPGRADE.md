# Documentation - Amélioration du système d'authentification et profil utilisateur MeetVoice

## Vue d'ensemble

Cette documentation décrit les améliorations apportées au système d'authentification et de gestion des profils utilisateurs de MeetVoice, incluant la géolocalisation, les fonctionnalités vocales et une interface utilisateur modernisée.

## Modifications apportées

### 1. Système de connexion modifié

**Fichiers modifiés :**
- `src/views/LoginView.vue`
- `tests/account.service.test.js`

**Changements :**
- Remplacement du champ email par un champ nom d'utilisateur
- Mise à jour de la validation côté client
- Adaptation des tests unitaires

**Utilisation :**
```javascript
// Ancien système
formData.append("email", this.user.email);

// Nouveau système
formData.append("username", this.user.username);
```

### 2. Formulaire d'inscription amélioré

**Fichier :** `src/views/RegisterView.vue`

**Nouvelles fonctionnalités :**
- Système d'étapes avec indicateur de progression
- Validation en temps réel des champs
- Upload de photo de profil avec prévisualisation
- Champs étendus : nom, prénom, username, date de naissance, genre, préférences

**Structure des étapes :**
1. **Étape 1** : Informations de base (nom, prénom, username, email, mots de passe)
2. **Étape 2** : Profil personnel (date de naissance, genre, préférences, ville, bio)
3. **Étape 3** : Photo de profil (optionnel)

**Validation :**
- Mots de passe : minimum 8 caractères, confirmation requise
- Email : format valide
- Âge : minimum 18 ans
- Photo : formats JPG/PNG, taille max 5MB

### 3. Page de modification de profil

**Fichier :** `src/views/ProfileEditView.vue`
**Route :** `/profile/edit` (authentification requise)

**Fonctionnalités :**
- Formulaire pré-rempli avec données utilisateur actuelles
- Gestion des photos de profil (ajout/suppression/modification)
- Sauvegarde via API avec feedback utilisateur
- Email non modifiable (sécurité)

**Utilisation :**
```vue
<template>
  <router-link to="/profile/edit">Modifier mon profil</router-link>
</template>
```

## Services développés

### 1. Service de géolocalisation

**Fichier :** `src/_services/geolocation.service.js`

**API utilisée :** Nominatim OpenStreetMap (gratuite)

**Fonctionnalités principales :**

#### Détection de position
```javascript
import { geolocationService } from '@/_services';

// Obtenir la position actuelle
const position = await geolocationService.getCurrentPosition();
// Retourne : { latitude, longitude, accuracy, timestamp }
```

#### Géocodage inverse
```javascript
// Convertir coordonnées en adresse
const address = await geolocationService.reverseGeocode(48.8566, 2.3522);
// Retourne : { display_name, address, coordinates, raw }
```

#### Recherche d'adresses
```javascript
// Rechercher des adresses
const results = await geolocationService.searchAddresses('Paris');
// Retourne : Array<{ display_name, address, coordinates, importance, type }>
```

#### Validation de cohérence
```javascript
// Vérifier cohérence position déclarée vs IP
const validation = await geolocationService.validateLocationConsistency(
  { latitude: 48.8566, longitude: 2.3522 },
  100 // tolérance en km
);
// Retourne : { isConsistent, distance, warning }
```

#### Calcul de distance
```javascript
// Distance entre deux points (formule Haversine)
const distance = geolocationService.calculateDistance(
  48.8566, 2.3522, // Paris
  45.7640, 4.8357  // Lyon
);
// Retourne : distance en kilomètres
```

### 2. Service vocal

**Fichier :** `src/_services/voice.service.js`

**APIs utilisées :** Web Speech API (reconnaissance et synthèse)

**Fonctionnalités principales :**

#### Reconnaissance vocale
```javascript
import { voiceService } from '@/_services';

// Démarrer l'écoute
const text = await voiceService.startListening({
  timeout: 15000,
  onStart: () => console.log('Écoute démarrée'),
  onResult: (final, interim) => console.log('Résultat:', final),
  onEnd: () => console.log('Écoute terminée')
});
```

#### Synthèse vocale
```javascript
// Faire parler le navigateur
await voiceService.speak('Bonjour, comment allez-vous ?', {
  language: 'fr-FR',
  rate: 1.0,
  pitch: 1.0,
  volume: 1.0
});
```

#### Gestion des permissions
```javascript
// Demander l'accès au microphone
const hasPermission = await voiceService.requestMicrophonePermission();
```

## Composants développés

### 1. Sélecteur de localisation

**Fichier :** `src/components/LocationSelector.vue`

**Utilisation :**
```vue
<template>
  <LocationSelector
    v-model="userLocation"
    :validate-with-ip="true"
    :required="true"
    @location-selected="onLocationSelected"
    @validation-result="onValidationResult"
  />
</template>
```

**Props :**
- `modelValue` : Objet de localisation sélectionnée
- `validateWithIP` : Activer la validation avec géolocalisation IP
- `required` : Champ obligatoire

**Événements :**
- `location-selected` : Localisation sélectionnée
- `validation-result` : Résultat de la validation

### 2. Interview vocal

**Fichier :** `src/components/VoiceInterview.vue`

**Utilisation :**
```vue
<template>
  <VoiceInterview
    :questions="customQuestions"
    @interview-completed="onInterviewCompleted"
    @answers-saved="onAnswersSaved"
  />
</template>
```

**Props :**
- `questions` : Array des questions à poser

**Événements :**
- `interview-completed` : Interview terminée
- `answers-saved` : Réponses sauvegardées

**Questions par défaut :**
1. Parlez-nous de vous en quelques mots
2. Que recherchez-vous dans une relation ?
3. Quels sont vos hobbies et centres d'intérêt ?
4. Décrivez votre style de vie
5. Qu'est-ce qui vous rend unique ?

### 3. Enregistreur vocal

**Fichier :** `src/components/VoiceRecorder.vue`

**Utilisation :**
```vue
<template>
  <VoiceRecorder
    title="Ma présentation vocale"
    :max-duration="300"
    :show-waveform="true"
    :auto-save="false"
    @recording-saved="onRecordingSaved"
  />
</template>
```

**Props :**
- `title` : Titre du composant
- `description` : Description
- `maxDuration` : Durée max en secondes (défaut: 300)
- `showWaveform` : Afficher la forme d'onde
- `autoSave` : Sauvegarde automatique

**Fonctionnalités :**
- Enregistrement audio avec visualiseur de niveau
- Lecture avec contrôles (play/pause/seek)
- Téléchargement du fichier audio
- Sauvegarde dans le profil utilisateur

## Configuration et installation

### Dépendances requises

Aucune nouvelle dépendance externe n'est requise. Les fonctionnalités utilisent les APIs natives du navigateur :

- **Web Speech API** : Reconnaissance et synthèse vocale
- **Geolocation API** : Détection de position
- **MediaRecorder API** : Enregistrement audio
- **Fetch API** : Appels vers Nominatim OpenStreetMap

### Compatibilité navigateurs

| Fonctionnalité | Chrome | Firefox | Safari | Edge |
|----------------|--------|---------|--------|------|
| Reconnaissance vocale | ✅ | ❌ | ✅ | ✅ |
| Synthèse vocale | ✅ | ✅ | ✅ | ✅ |
| Géolocalisation | ✅ | ✅ | ✅ | ✅ |
| Enregistrement audio | ✅ | ✅ | ✅ | ✅ |

### Variables d'environnement

Aucune variable d'environnement supplémentaire n'est requise. Les services utilisent des APIs publiques gratuites.

## Tests

### Tests unitaires créés

1. **`tests/geolocation.service.test.js`** : Tests du service de géolocalisation
2. **`tests/voice.service.test.js`** : Tests du service vocal
3. **`tests/account.service.test.js`** : Tests mis à jour pour le nouveau système de connexion

### Exécution des tests

```bash
# Exécuter tous les tests
npm test

# Exécuter les tests avec couverture
npm run test:coverage

# Exécuter les tests en mode watch
npm run test:watch
```

### Couverture de tests

Les tests couvrent :
- ✅ Fonctions principales des services
- ✅ Gestion des erreurs
- ✅ Cas limites et edge cases
- ✅ Mocks des APIs navigateur
- ✅ Validation des paramètres

## Sécurité et bonnes pratiques

### Gestion des permissions

- **Microphone** : Demande explicite de permission avant utilisation
- **Géolocalisation** : Consentement utilisateur requis
- **Données sensibles** : Validation côté client et serveur

### Protection des données

- **Géolocalisation** : Validation de cohérence sans stockage permanent des données IP
- **Audio** : Enregistrements stockés localement avant sauvegarde explicite
- **Profil** : Email non modifiable pour éviter les usurpations

### Limitations et considérations

1. **Reconnaissance vocale** : Limitée à Chrome, Safari et Edge
2. **Géolocalisation IP** : Précision variable selon le fournisseur d'accès
3. **Taille des fichiers audio** : Limitation recommandée à 5MB
4. **Timeout** : Limitation des sessions d'écoute à 30 secondes max

## Intégration backend

### Endpoints API requis

Les nouvelles fonctionnalités nécessitent les endpoints suivants :

```javascript
// Inscription avec champs étendus
POST /auth/register/
// FormData avec : first_name, last_name, username, email, password1, password2,
//                 birth_date, gender, looking_for, city, bio, profile_photo

// Mise à jour profil
PUT /api/user/profile/
// FormData avec tous les champs modifiables + photos

// Sauvegarde enregistrement vocal
POST /api/user/voice-recording/
// FormData avec : voice_recording (blob), duration, recorded_at
```

### Structure de données recommandée

```python
# Modèle utilisateur étendu (Django)
class User(AbstractUser):
    username = models.CharField(max_length=150, unique=True)  # Connexion
    email = models.EmailField(unique=True)  # Non modifiable
    first_name = models.CharField(max_length=30)
    last_name = models.CharField(max_length=30)
    birth_date = models.DateField()
    gender = models.CharField(max_length=1, choices=[('M', 'Homme'), ('F', 'Femme'), ('O', 'Autre')])
    looking_for = models.CharField(max_length=1, choices=[('M', 'Hommes'), ('F', 'Femmes'), ('B', 'Les deux')])
    city = models.CharField(max_length=100)
    bio = models.TextField(blank=True)
    profile_photo = models.ImageField(upload_to='profiles/', blank=True)
    voice_recording = models.FileField(upload_to='voice/', blank=True)
    
    # Données d'interview vocal
    bio_vocal = models.TextField(blank=True)
    recherche_vocal = models.TextField(blank=True)
    hobbies_vocal = models.TextField(blank=True)
    lifestyle_vocal = models.TextField(blank=True)
    unique_vocal = models.TextField(blank=True)
```

## Maintenance et évolutions

### Améliorations futures possibles

1. **Reconnaissance vocale offline** : Intégration de Vosk.js
2. **Géolocalisation avancée** : Intégration de cartes interactives
3. **Compression audio** : Optimisation des fichiers vocaux
4. **Transcription automatique** : Conversion speech-to-text des enregistrements
5. **Analyse sentiment** : Analyse des réponses vocales

### Monitoring recommandé

- Taux de succès des reconnaissances vocales
- Précision de la géolocalisation
- Taille moyenne des fichiers audio
- Temps de réponse des APIs externes

---

## Guide d'utilisation pour les développeurs

### Intégration des nouveaux composants

#### 1. Utilisation du sélecteur de localisation

```vue
<template>
  <div class="profile-form">
    <LocationSelector
      v-model="userLocation"
      :validate-with-ip="true"
      @location-selected="handleLocationSelected"
      @validation-result="handleValidation"
    />
  </div>
</template>

<script>
import LocationSelector from '@/components/LocationSelector.vue';

export default {
  components: { LocationSelector },
  data() {
    return {
      userLocation: null
    };
  },
  methods: {
    handleLocationSelected(location) {
      console.log('Localisation sélectionnée:', location);
      // location.address.city, location.coordinates, etc.
    },
    handleValidation(result) {
      if (!result.isConsistent) {
        console.warn('Localisation suspecte:', result.warning);
      }
    }
  }
};
</script>
```

#### 2. Intégration de l'interview vocal

```vue
<template>
  <div class="registration-process">
    <VoiceInterview
      :questions="interviewQuestions"
      @interview-completed="handleInterviewCompleted"
      @answers-saved="handleAnswersSaved"
    />
  </div>
</template>

<script>
import VoiceInterview from '@/components/VoiceInterview.vue';

export default {
  components: { VoiceInterview },
  data() {
    return {
      interviewQuestions: [
        {
          text: "Décrivez-vous en quelques mots",
          hint: "Parlez de votre personnalité, vos passions..."
        },
        // Autres questions personnalisées
      ]
    };
  },
  methods: {
    handleInterviewCompleted(answers) {
      console.log('Interview terminée:', answers);
      // Traiter les réponses
    },
    async handleAnswersSaved(interviewData) {
      // Sauvegarder via API
      try {
        const response = await fetch('/api/user/voice-interview/', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.$store.getters.getToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(interviewData)
        });

        if (response.ok) {
          this.$router.push('/profile');
        }
      } catch (error) {
        console.error('Erreur sauvegarde:', error);
      }
    }
  }
};
</script>
```

#### 3. Utilisation de l'enregistreur vocal

```vue
<template>
  <div class="voice-section">
    <VoiceRecorder
      title="Ma présentation vocale"
      description="Enregistrez une présentation de 2-3 minutes"
      :max-duration="180"
      :show-waveform="true"
      @recording-saved="handleRecordingSaved"
    />
  </div>
</template>

<script>
import VoiceRecorder from '@/components/VoiceRecorder.vue';

export default {
  components: { VoiceRecorder },
  methods: {
    async handleRecordingSaved(recordingData) {
      try {
        // Sauvegarder l'enregistrement via API
        const response = await fetch('/api/user/voice-recording/', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.$store.getters.getToken}`
          },
          body: recordingData.formData
        });

        if (response.ok) {
          this.$emit('recording-uploaded');
        }
      } catch (error) {
        console.error('Erreur upload:', error);
      }
    }
  }
};
</script>
```

### Gestion des erreurs et fallbacks

#### 1. Vérification du support des fonctionnalités

```javascript
import { voiceService, geolocationService } from '@/_services';

export default {
  data() {
    return {
      features: {
        voice: false,
        geolocation: false,
        mediaRecorder: false
      }
    };
  },

  mounted() {
    this.checkFeatureSupport();
  },

  methods: {
    checkFeatureSupport() {
      // Vérifier le support vocal
      this.features.voice = voiceService.checkSupport();

      // Vérifier la géolocalisation
      this.features.geolocation = !!navigator.geolocation;

      // Vérifier MediaRecorder
      this.features.mediaRecorder = !!(navigator.mediaDevices && MediaRecorder);

      // Adapter l'interface selon les capacités
      this.adaptInterface();
    },

    adaptInterface() {
      if (!this.features.voice) {
        // Masquer les fonctionnalités vocales
        this.showVoiceFeatures = false;
        this.showTextAlternatives = true;
      }

      if (!this.features.geolocation) {
        // Proposer saisie manuelle de la ville
        this.showManualLocationInput = true;
      }
    }
  }
};
```

#### 2. Gestion des permissions

```javascript
methods: {
  async requestPermissions() {
    const permissions = {
      microphone: false,
      geolocation: false
    };

    // Permission microphone
    try {
      permissions.microphone = await voiceService.requestMicrophonePermission();
    } catch (error) {
      console.warn('Permission microphone refusée');
    }

    // Permission géolocalisation
    try {
      await geolocationService.getCurrentPosition();
      permissions.geolocation = true;
    } catch (error) {
      console.warn('Permission géolocalisation refusée');
    }

    return permissions;
  },

  async handlePermissionDenied(type) {
    const messages = {
      microphone: 'Pour utiliser les fonctionnalités vocales, veuillez autoriser l\'accès au microphone dans les paramètres de votre navigateur.',
      geolocation: 'Pour une meilleure expérience, veuillez autoriser l\'accès à votre localisation.'
    };

    this.showPermissionHelp(messages[type]);
  }
}
```

### Optimisations et performances

#### 1. Lazy loading des composants

```javascript
// Dans le router ou les composants parents
const VoiceInterview = () => import('@/components/VoiceInterview.vue');
const VoiceRecorder = () => import('@/components/VoiceRecorder.vue');
const LocationSelector = () => import('@/components/LocationSelector.vue');

export default {
  components: {
    VoiceInterview,
    VoiceRecorder,
    LocationSelector
  }
};
```

#### 2. Debouncing pour la recherche de localisation

```javascript
// Déjà implémenté dans LocationSelector.vue
methods: {
  onSearchInput() {
    // Débounce automatique de 300ms
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }

    this.searchTimeout = setTimeout(() => {
      this.searchAddresses();
    }, 300);
  }
}
```

#### 3. Nettoyage des ressources

```javascript
// Dans les composants utilisant les APIs audio/vidéo
beforeUnmount() {
  // Arrêter l'écoute vocale
  if (this.isListening) {
    voiceService.stopListening();
  }

  // Nettoyer les streams audio
  if (this.audioStream) {
    this.audioStream.getTracks().forEach(track => track.stop());
  }

  // Révoquer les URLs d'objets
  if (this.audioUrl) {
    URL.revokeObjectURL(this.audioUrl);
  }
}
```

### Exemples d'intégration complète

#### Page d'inscription avec toutes les fonctionnalités

```vue
<template>
  <div class="enhanced-registration">
    <!-- Étapes de base (déjà implémentées dans RegisterView.vue) -->

    <!-- Étape géolocalisation -->
    <div v-if="currentStep === 4" class="step-location">
      <h3>Votre localisation</h3>
      <LocationSelector
        v-model="userLocation"
        :validate-with-ip="true"
        :required="true"
        @validation-result="handleLocationValidation"
      />
    </div>

    <!-- Étape interview vocal -->
    <div v-if="currentStep === 5" class="step-voice-interview">
      <VoiceInterview
        v-if="features.voice"
        @interview-completed="handleInterviewCompleted"
      />
      <div v-else class="voice-fallback">
        <!-- Formulaire texte alternatif -->
        <textarea
          v-model="textBio"
          placeholder="Décrivez-vous en quelques mots..."
        ></textarea>
      </div>
    </div>

    <!-- Étape enregistrement vocal -->
    <div v-if="currentStep === 6" class="step-voice-recording">
      <VoiceRecorder
        v-if="features.mediaRecorder"
        title="Votre présentation vocale"
        :max-duration="180"
        @recording-saved="handleRecordingSaved"
      />
      <button v-else @click="skipVoiceRecording">
        Passer cette étape
      </button>
    </div>
  </div>
</template>
```

*Documentation générée le 29 juin 2025 pour MeetVoice Frontend v2.0*
