/**
 * Configuration centralisée des images MeetVoice
 * Gère les URLs optimisées avec support WebP
 */

// Mapping des images avec leurs versions WebP
export const IMAGE_ASSETS = {
  // Logos
  logo: {
    webp: '@/assets/logo.webp',
    fallback: '@/assets/logo.jpg',
    alt: 'Logo MeetVoice',
    width: 200,
    height: 80
  },

  logoAlt: {
    webp: '@/assets/logo1.webp',
    fallback: '@/assets/logo1.png',
    alt: 'Logo alternatif MeetVoice',
    width: 150,
    height: 60
  },

  // Images de fond
  backgroundHome: {
    webp: '@/assets/Accueil.webp',
    fallback: '@/assets/Accueil.jpg',
    alt: 'Image d\'accueil MeetVoice',
    width: 1920,
    height: 1080
  },

  // Images de présentation
  presentationSite: {
    webp: '@/assets/pseudo-site-rencontre-Pr.webp',
    fallback: '@/assets/pseudo-site-rencontre-Pr.jpg',
    alt: 'Présentation du site de rencontre MeetVoice',
    width: 800,
    height: 600
  },

  // Favicon
  favicon: {
    webp: '/favicon.webp',
    fallback: '/favicon.png',
    alt: 'Icône MeetVoice',
    width: 32,
    height: 32
  }
};

// Images par défaut pour les articles
export const DEFAULT_IMAGES = {
  article: '/api/static/default-article.webp',
  articleFallback: '/api/static/default-article.jpg',
  
  profile: '/api/static/default-profile.webp',
  profileFallback: '/api/static/default-profile.jpg',
  
  placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkNoYXJnZW1lbnQuLi48L3RleHQ+PC9zdmc+'
};

/**
 * Obtenir l'URL optimisée d'une image
 */
export function getOptimizedImageUrl(imageKey, options = {}) {
  const image = IMAGE_ASSETS[imageKey];

  if (!image) {
    console.warn(`Image non trouvée: ${imageKey}`);
    return DEFAULT_IMAGES.placeholder;
  }

  // Vérifier le support WebP
  const supportsWebP = typeof window !== 'undefined' && window.webpSupported !== false;

  return supportsWebP ? image.webp : image.fallback;
}

/**
 * Obtenir les métadonnées d'une image
 */
export function getImageMetadata(imageKey) {
  const image = IMAGE_ASSETS[imageKey];
  
  if (!image) {
    return {
      alt: 'Image MeetVoice',
      width: null,
      height: null
    };
  }
  
  return {
    alt: image.alt,
    width: image.width,
    height: image.height
  };
}

/**
 * Générer un srcset responsive
 */
export function generateSrcSet(imageKey, sizes = [400, 800, 1200]) {
  const image = IMAGE_ASSETS[imageKey];
  
  if (!image) return '';
  
  const supportsWebP = window.webpSupported !== false;
  const baseUrl = supportsWebP ? image.webp : image.fallback;
  
  // Pour l'instant, retourner l'URL de base
  // Dans un vrai projet, vous auriez différentes tailles
  return sizes.map(size => `${baseUrl} ${size}w`).join(', ');
}

/**
 * Précharger les images critiques
 */
export function preloadCriticalImages() {
  const criticalImages = ['logo', 'backgroundHome'];
  
  criticalImages.forEach(imageKey => {
    const url = getOptimizedImageUrl(imageKey);
    
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'image';
    link.href = url;
    
    if (url.includes('.webp')) {
      link.type = 'image/webp';
    }
    
    document.head.appendChild(link);
  });
}

/**
 * Plugin Vue pour les images optimisées
 */
export const ImageOptimizationPlugin = {
  install(app) {
    // Méthode globale pour obtenir une image optimisée
    app.config.globalProperties.$getImage = getOptimizedImageUrl;
    app.config.globalProperties.$getImageMeta = getImageMetadata;
    
    // Directive pour les images optimisées
    app.directive('optimized-src', {
      mounted(el, binding) {
        const imageKey = binding.value;
        const url = getOptimizedImageUrl(imageKey);
        
        if (el.tagName === 'IMG') {
          el.src = url;
          
          // Ajouter les métadonnées
          const meta = getImageMetadata(imageKey);
          if (meta.alt && !el.alt) el.alt = meta.alt;
          if (meta.width && !el.width) el.width = meta.width;
          if (meta.height && !el.height) el.height = meta.height;
        }
      },
      
      updated(el, binding) {
        const imageKey = binding.value;
        const url = getOptimizedImageUrl(imageKey);
        
        if (el.tagName === 'IMG' && el.src !== url) {
          el.src = url;
        }
      }
    });
  }
};

export default {
  IMAGE_ASSETS,
  DEFAULT_IMAGES,
  getOptimizedImageUrl,
  getImageMetadata,
  generateSrcSet,
  preloadCriticalImages,
  ImageOptimizationPlugin
};
