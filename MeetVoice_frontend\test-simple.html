<!DOCTYPE html>
<html>
<head>
    <title>Test Simple TTS</title>
</head>
<body>
    <h1>Test Simple TTS MeetVoice</h1>
    
    <button onclick="testSimple()">Test Simple (Voice ID 1)</button>
    <div id="result"></div>
    
    <script>
        async function testSimple() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '🔄 Test en cours...';
            
            try {
                // Test avec les paramètres les plus simples
                const payload = {
                    text: 'Bonjour, ceci est un test simple',
                    voice_id: 1,
                    language: 'fr',
                    speed: 1.0,
                    pitch: 1.0
                };
                
                console.log('🔊 Payload simple:', payload);
                
                const response = await fetch('http://127.0.0.1:8000/tts/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                console.log('📡 Response status:', response.status);
                console.log('📡 Response headers:', [...response.headers.entries()]);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('🎵 Response data:', data);
                    
                    if (data.audio_url) {
                        console.log('🔗 Audio URL:', data.audio_url);
                        
                        // Test direct de l'URL
                        const audio = new Audio(data.audio_url);
                        
                        audio.onloadstart = () => console.log('🔄 Audio loading...');
                        audio.oncanplay = () => console.log('✅ Audio can play');
                        audio.onplay = () => console.log('▶️ Audio playing');
                        audio.onended = () => console.log('✅ Audio ended');
                        audio.onerror = (e) => console.error('❌ Audio error:', e);
                        
                        await audio.play();
                        
                        resultDiv.innerHTML = '✅ Test réussi - Audio joué !';
                    } else {
                        resultDiv.innerHTML = '❌ Pas d\'URL audio dans la réponse';
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ Error response:', errorText);
                    resultDiv.innerHTML = `❌ Erreur ${response.status}: ${errorText}`;
                }
                
            } catch (error) {
                console.error('❌ Exception:', error);
                resultDiv.innerHTML = `❌ Exception: ${error.message}`;
            }
        }
    </script>
</body>
</html>
