<!-- @format -->

<template>
  <div class="access-denied-page">
    <div class="container">
      <div class="access-denied-content">
        <div class="icon-section">
          <svg width="120" height="120" viewBox="0 0 24 24" fill="none" class="lock-icon">
            <path d="M6 10V8C6 5.79086 7.79086 4 10 4H14C16.2091 4 18 5.79086 18 8V10" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            <rect x="3" y="10" width="18" height="10" rx="2" stroke="currentColor" stroke-width="2"/>
            <circle cx="12" cy="15" r="1" fill="currentColor"/>
          </svg>
        </div>
        
        <h1 class="title">Accès Restreint</h1>
        <p class="subtitle">Cette section est réservée aux utilisateurs connectés</p>
        
        <div class="info-section">
          <h3>Pour accéder à cette page, vous devez :</h3>
          <ul class="requirements-list">
            <li>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              Avoir un compte utilisateur
            </li>
            <li>
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M20 6L9 17L4 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
              Être connecté à votre compte
            </li>
          </ul>
        </div>
        
        <div class="actions-section">
          <router-link to="/login" class="btn btn-primary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M15 3H19C20.1046 3 21 3.89543 21 5V19C21 20.1046 20.1046 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M10 17L15 12L10 7" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <path d="M15 12H3" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            Se connecter
          </router-link>
          
          <router-link to="/register" class="btn btn-secondary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M16 21V19C16 16.7909 14.2091 15 12 15H5C2.79086 15 1 16.7909 1 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              <circle cx="8.5" cy="7" r="4" stroke="currentColor" stroke-width="2"/>
              <path d="M20 8V14M23 11H17" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            Créer un compte
          </router-link>
          
          <router-link to="/" class="btn btn-outline">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
              <path d="M3 12L5 10M5 10L12 3L19 10M5 10V20C5 20.5523 5.44772 21 6 21H9M19 10L21 12M19 10V20C19 20.5523 18.5523 21 18 21H15M9 21C9.55228 21 10 20.5523 10 20V16C10 15.4477 10.4477 15 11 15H13C13.5523 15 14 15.4477 14 16V20C14 20.5523 14.4477 21 15 21M9 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
            </svg>
            Retour à l'accueil
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AccessDeniedView",
};
</script>

<style scoped>
.access-denied-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding-top: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.container {
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
}

.access-denied-content {
  background: white;
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.icon-section {
  margin-bottom: 30px;
}

.lock-icon {
  color: #64748b;
}

.title {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1e293b;
  margin-bottom: 16px;
}

.subtitle {
  font-size: 1.2rem;
  color: #64748b;
  margin-bottom: 40px;
}

.info-section {
  background: #f8fafc;
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 40px;
  text-align: left;
}

.info-section h3 {
  color: #1e293b;
  font-size: 1.1rem;
  font-weight: 700;
  margin-bottom: 20px;
  text-align: center;
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 0;
  color: #475569;
  font-weight: 500;
}

.requirements-list li svg {
  color: #10b981;
  flex-shrink: 0;
}

.actions-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 24px;
  border-radius: 12px;
  text-decoration: none;
  font-weight: 700;
  font-size: 1rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(240, 147, 251, 0.3);
}

.btn-outline {
  background: transparent;
  color: #64748b;
  border-color: #e2e8f0;
}

.btn-outline:hover {
  background: #f8fafc;
  border-color: #cbd5e1;
  color: #475569;
}

/* Responsive */
@media (max-width: 768px) {
  .access-denied-page {
    padding-top: 70px;
  }
  
  .access-denied-content {
    padding: 40px 24px;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1.1rem;
  }
  
  .info-section {
    padding: 24px;
  }
  
  .actions-section {
    gap: 12px;
  }
  
  .btn {
    padding: 14px 20px;
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 16px;
  }
  
  .access-denied-content {
    padding: 30px 20px;
  }
  
  .title {
    font-size: 1.75rem;
  }
  
  .lock-icon {
    width: 80px;
    height: 80px;
  }
}
</style>
