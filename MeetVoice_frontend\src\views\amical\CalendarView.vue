<template>
  <main class="calendar-container">
    <header class="calendar-header">
      <section class="header-content">
        <h1>Mon calendrier</h1>
        <p class="subtitle"><PERSON><PERSON>rez vos événements et découvrez les prochaines sorties</p>
      </section>
      
      <nav class="calendar-actions" aria-label="Actions calendrier">
        <button 
          @click="$router.push('/events/create')"
          class="btn-create-event"
        >
          <span aria-hidden="true">➕</span>
          Nouvel événement
        </button>
        
        <button 
          @click="$router.push('/events')"
          class="btn-all-events"
        >
          <span aria-hidden="true">📋</span>
          Tous les événements
        </button>
      </nav>
    </header>

    <!-- Navigation du calendrier -->
    <section class="calendar-navigation">
      <div class="nav-controls">
        <button 
          @click="previousMonth"
          class="btn-nav"
          aria-label="Mois précédent"
        >
          ‹
        </button>
        
        <h2 class="current-month">
          {{ formatMonthYear(currentDate) }}
        </h2>
        
        <button 
          @click="nextMonth"
          class="btn-nav"
          aria-label="Mois suivant"
        >
          ›
        </button>
      </div>
      
      <div class="view-controls">
        <button 
          @click="goToToday"
          class="btn-today"
        >
          Aujourd'hui
        </button>
        
        <div class="view-selector">
          <button 
            @click="currentView = 'month'"
            :class="['btn-view', { active: currentView === 'month' }]"
          >
            Mois
          </button>
          <button 
            @click="currentView = 'week'"
            :class="['btn-view', { active: currentView === 'week' }]"
          >
            Semaine
          </button>
          <button 
            @click="currentView = 'list'"
            :class="['btn-view', { active: currentView === 'list' }]"
          >
            Liste
          </button>
        </div>
      </div>
    </section>

    <!-- Vue calendrier -->
    <section class="calendar-content">
      <!-- Vue mois -->
      <div v-if="currentView === 'month'" class="month-view">
        <div class="calendar-grid">
          <!-- En-têtes des jours -->
          <div class="day-header" v-for="day in dayHeaders" :key="day">
            {{ day }}
          </div>
          
          <!-- Jours du calendrier -->
          <div 
            v-for="day in calendarDays" 
            :key="day.date"
            :class="[
              'calendar-day',
              {
                'other-month': !day.isCurrentMonth,
                'today': day.isToday,
                'has-events': day.events.length > 0,
                'selected': day.date === selectedDate
              }
            ]"
            @click="selectDay(day)"
            role="button"
            tabindex="0"
            @keydown.enter="selectDay(day)"
            @keydown.space.prevent="selectDay(day)"
          >
            <span class="day-number">{{ day.dayNumber }}</span>
            
            <div class="day-events" v-if="day.events.length > 0">
              <div 
                v-for="event in day.events.slice(0, 3)" 
                :key="event.id"
                :class="['event-indicator', `category-${event.category}`]"
                :title="event.title"
              >
                <span class="event-time">{{ formatTime(event.time) }}</span>
                <span class="event-title">{{ event.title }}</span>
              </div>
              
              <div 
                v-if="day.events.length > 3"
                class="more-events"
              >
                +{{ day.events.length - 3 }} autres
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vue semaine -->
      <div v-else-if="currentView === 'week'" class="week-view">
        <div class="week-grid">
          <div class="time-column">
            <div class="time-header"></div>
            <div 
              v-for="hour in hours" 
              :key="hour"
              class="time-slot"
            >
              {{ hour }}:00
            </div>
          </div>
          
          <div 
            v-for="day in weekDays" 
            :key="day.date"
            class="day-column"
          >
            <div class="day-header">
              <span class="day-name">{{ day.dayName }}</span>
              <span class="day-number">{{ day.dayNumber }}</span>
            </div>
            
            <div class="day-timeline">
              <div 
                v-for="event in day.events" 
                :key="event.id"
                :class="['week-event', `category-${event.category}`]"
                :style="getEventStyle(event)"
                @click="viewEvent(event.id)"
              >
                <div class="event-time">{{ formatTime(event.time) }}</div>
                <div class="event-title">{{ event.title }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Vue liste -->
      <div v-else-if="currentView === 'list'" class="list-view">
        <div v-if="upcomingEvents.length === 0" class="empty-state">
          <h3>Aucun événement à venir</h3>
          <p>Créez votre premier événement ou rejoignez la communauté !</p>
          <button 
            @click="$router.push('/events/create')"
            class="btn-create-first"
          >
            Créer un événement
          </button>
        </div>
        
        <div v-else class="events-list">
          <article 
            v-for="event in upcomingEvents" 
            :key="event.id"
            class="list-event"
            @click="viewEvent(event.id)"
          >
            <div class="event-date">
              <span class="month">{{ formatMonth(event.date) }}</span>
              <span class="day">{{ formatDay(event.date) }}</span>
              <span class="weekday">{{ formatWeekday(event.date) }}</span>
            </div>
            
            <div class="event-info">
              <div class="event-header">
                <h3 class="event-title">{{ event.title }}</h3>
                <span :class="['event-category', `category-${event.category}`]">
                  {{ getCategoryIcon(event.category) }} {{ getCategoryLabel(event.category) }}
                </span>
              </div>
              
              <div class="event-details">
                <div class="event-time">
                  <span aria-hidden="true">🕐</span>
                  {{ formatTime(event.time) }}
                  <span v-if="event.duration"> - {{ event.duration }}</span>
                </div>
                <div class="event-location">
                  <span aria-hidden="true">📍</span>
                  {{ event.location }}
                </div>
              </div>
              
              <div class="event-participants">
                {{ event.participants?.length || 0 }}/{{ event.maxParticipants }} participants
              </div>
            </div>
            
            <div class="event-actions">
              <button 
                v-if="isOrganizer(event)"
                @click.stop="editEvent(event.id)"
                class="btn-edit"
              >
                Modifier
              </button>
              <button 
                v-else-if="!isParticipating(event.id)"
                @click.stop="joinEvent(event.id)"
                class="btn-join"
                :disabled="isEventFull(event)"
              >
                {{ isEventFull(event) ? 'Complet' : 'Participer' }}
              </button>
              <button 
                v-else
                @click.stop="leaveEvent(event.id)"
                class="btn-leave"
              >
                Se désinscrire
              </button>
            </div>
          </article>
        </div>
      </div>
    </section>

    <!-- Détails du jour sélectionné -->
    <aside v-if="selectedDayEvents.length > 0" class="day-details">
      <h3>{{ formatSelectedDate(selectedDate) }}</h3>
      
      <div class="selected-day-events">
        <article 
          v-for="event in selectedDayEvents" 
          :key="event.id"
          class="day-event"
          @click="viewEvent(event.id)"
        >
          <div class="event-time">{{ formatTime(event.time) }}</div>
          <div class="event-content">
            <h4 class="event-title">{{ event.title }}</h4>
            <p class="event-location">{{ event.location }}</p>
          </div>
          <div :class="['event-status', getEventStatus(event)]">
            {{ getEventStatusLabel(event) }}
          </div>
        </article>
      </div>
    </aside>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'CalendarView',
  
  data() {
    return {
      currentDate: new Date(),
      selectedDate: null,
      currentView: 'month', // month, week, list
      dayHeaders: ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
      hours: Array.from({ length: 24 }, (_, i) => i)
    }
  },
  
  computed: {
    ...mapState(['events', 'userEvents', 'user']),
    
    calendarDays() {
      const year = this.currentDate.getFullYear();
      const month = this.currentDate.getMonth();
      
      // Premier jour du mois
      const firstDay = new Date(year, month, 1);
      // Dernier jour du mois
      const lastDay = new Date(year, month + 1, 0);
      
      // Premier lundi de la grille
      const startDate = new Date(firstDay);
      startDate.setDate(startDate.getDate() - (firstDay.getDay() + 6) % 7);
      
      // Dernier dimanche de la grille
      const endDate = new Date(lastDay);
      endDate.setDate(endDate.getDate() + (7 - lastDay.getDay()) % 7);
      
      const days = [];
      const currentDate = new Date(startDate);
      
      while (currentDate <= endDate) {
        const dayEvents = this.getEventsForDate(currentDate);
        
        days.push({
          date: currentDate.toISOString().split('T')[0],
          dayNumber: currentDate.getDate(),
          isCurrentMonth: currentDate.getMonth() === month,
          isToday: this.isToday(currentDate),
          events: dayEvents
        });
        
        currentDate.setDate(currentDate.getDate() + 1);
      }
      
      return days;
    },
    
    weekDays() {
      const startOfWeek = this.getStartOfWeek(this.currentDate);
      const days = [];
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(date.getDate() + i);
        
        days.push({
          date: date.toISOString().split('T')[0],
          dayName: this.dayHeaders[i],
          dayNumber: date.getDate(),
          events: this.getEventsForDate(date)
        });
      }
      
      return days;
    },
    
    upcomingEvents() {
      const now = new Date();
      return this.events
        .filter(event => new Date(event.date) >= now)
        .sort((a, b) => new Date(a.date) - new Date(b.date))
        .slice(0, 20); // Limiter à 20 événements
    },
    
    selectedDayEvents() {
      if (!this.selectedDate) return [];
      return this.getEventsForDate(new Date(this.selectedDate));
    }
  },
  
  methods: {
    ...mapActions(['loadEvents', 'joinEvent', 'leaveEvent']),
    
    formatMonthYear(date) {
      return date.toLocaleDateString('fr-FR', {
        month: 'long',
        year: 'numeric'
      });
    },
    
    formatTime(time) {
      return new Date(`2000-01-01T${time}`).toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    formatMonth(date) {
      return new Date(date).toLocaleDateString('fr-FR', { month: 'short' });
    },
    
    formatDay(date) {
      return new Date(date).getDate();
    },
    
    formatWeekday(date) {
      return new Date(date).toLocaleDateString('fr-FR', { weekday: 'short' });
    },
    
    formatSelectedDate(date) {
      return new Date(date).toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    
    getCategoryLabel(category) {
      const labels = {
        sport: 'Sport',
        jeux: 'Jeux',
        danse: 'Danse',
        restaurant: 'Restaurant',
        cinéma: 'Cinéma'
      };
      return labels[category] || category;
    },
    
    getCategoryIcon(category) {
      const icons = {
        sport: '⚽',
        jeux: '🎮',
        danse: '💃',
        restaurant: '🍽️',
        cinéma: '🎬'
      };
      return icons[category] || '📅';
    },
    
    getEventsForDate(date) {
      const dateStr = date.toISOString().split('T')[0];
      return this.events.filter(event => event.date === dateStr);
    },
    
    isToday(date) {
      const today = new Date();
      return date.toDateString() === today.toDateString();
    },
    
    getStartOfWeek(date) {
      const startOfWeek = new Date(date);
      const day = startOfWeek.getDay();
      const diff = startOfWeek.getDate() - day + (day === 0 ? -6 : 1);
      startOfWeek.setDate(diff);
      return startOfWeek;
    },
    
    previousMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() - 1, 1);
    },
    
    nextMonth() {
      this.currentDate = new Date(this.currentDate.getFullYear(), this.currentDate.getMonth() + 1, 1);
    },
    
    goToToday() {
      this.currentDate = new Date();
      this.selectedDate = new Date().toISOString().split('T')[0];
    },
    
    selectDay(day) {
      this.selectedDate = day.date;
    },
    
    viewEvent(eventId) {
      this.$router.push(`/events/${eventId}`);
    },
    
    editEvent(eventId) {
      this.$router.push(`/events/${eventId}/edit`);
    },
    
    isOrganizer(event) {
      return event.organizer?.id === this.user?.id;
    },
    
    isParticipating(eventId) {
      const event = this.events.find(e => e.id === eventId);
      return event?.participants?.some(p => p.id === this.user?.id);
    },
    
    isEventFull(event) {
      return event.participants?.length >= event.maxParticipants;
    },
    
    getEventStatus(event) {
      if (this.isOrganizer(event)) return 'organizer';
      if (this.isParticipating(event.id)) return 'participating';
      if (this.isEventFull(event)) return 'full';
      return 'available';
    },
    
    getEventStatusLabel(event) {
      const status = this.getEventStatus(event);
      const labels = {
        organizer: 'Organisateur',
        participating: 'Participant',
        full: 'Complet',
        available: 'Disponible'
      };
      return labels[status];
    },
    
    getEventStyle(event) {
      const [hours, minutes] = event.time.split(':').map(Number);
      const top = (hours * 60 + minutes) / 60 * 50; // 50px par heure
      
      return {
        top: `${top}px`,
        height: '40px' // Hauteur fixe pour simplifier
      };
    }
  },
  
  async mounted() {
    await this.loadEvents();
    this.selectedDate = new Date().toISOString().split('T')[0];
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}

.calendar-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-blue);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.calendar-actions {
  display: flex;
  gap: 12px;
}

.btn-create-event,
.btn-all-events {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-event:hover,
.btn-all-events:hover {
  background: var(--accent-blue);
  transform: translateY(-2px);
}

.calendar-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.nav-controls {
  display: flex;
  align-items: center;
  gap: 20px;
}

.btn-nav {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--accent-purple);
  color: var(--text-white);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-nav:hover {
  background: var(--accent-blue);
  transform: scale(1.1);
}

.current-month {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-blue);
  text-transform: capitalize;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.btn-today {
  padding: 8px 16px;
  background: var(--wall-color);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-today:hover {
  background: var(--sweater-purple);
}

.view-selector {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.btn-view {
  padding: 8px 16px;
  background: transparent;
  color: var(--slogan-gray);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view.active {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-view:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.calendar-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Vue mois */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.day-header {
  background: var(--accent-purple);
  color: var(--text-white);
  padding: 12px 8px;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
}

.calendar-day {
  background: rgba(255, 255, 255, 0.05);
  min-height: 120px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.calendar-day:hover {
  background: rgba(255, 255, 255, 0.1);
}

.calendar-day.other-month {
  opacity: 0.3;
}

.calendar-day.today {
  background: rgba(0, 207, 255, 0.2);
  border: 2px solid var(--accent-blue);
}

.calendar-day.selected {
  background: rgba(214, 140, 255, 0.2);
  border: 2px solid var(--accent-purple);
}

.day-number {
  font-weight: 600;
  font-size: 1rem;
  color: var(--text-white);
}

.day-events {
  margin-top: 4px;
}

.event-indicator {
  background: var(--accent-blue);
  color: var(--text-white);
  padding: 2px 6px;
  margin: 2px 0;
  border-radius: 4px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  gap: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.event-indicator.category-sport { background: #22c55e; }
.event-indicator.category-jeux { background: #8b5cf6; }
.event-indicator.category-danse { background: #ec4899; }
.event-indicator.category-restaurant { background: #f59e0b; }
.event-indicator.category-cinéma { background: #ef4444; }

.more-events {
  font-size: 0.6rem;
  color: var(--slogan-gray);
  margin-top: 2px;
}

/* Vue liste */
.list-view {
  max-height: 600px;
  overflow-y: auto;
}

.events-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.list-event {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.list-event:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  padding: 12px;
  background: var(--accent-purple);
  border-radius: 8px;
  text-align: center;
}

.event-date .month {
  font-size: 0.8rem;
  text-transform: uppercase;
  font-weight: 600;
}

.event-date .day {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 4px 0;
}

.event-date .weekday {
  font-size: 0.7rem;
  opacity: 0.8;
}

.event-info {
  flex: 1;
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.event-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-blue);
  margin: 0;
}

.event-category {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.event-details {
  display: flex;
  gap: 16px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--slogan-gray);
}

.event-time,
.event-location {
  display: flex;
  align-items: center;
  gap: 4px;
}

.event-participants {
  font-size: 0.8rem;
  color: var(--icon-color);
}

.event-actions {
  display: flex;
  gap: 8px;
}

.btn-edit,
.btn-join,
.btn-leave {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-edit {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-join {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-leave {
  background: transparent;
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.btn-edit:hover,
.btn-join:hover:not(:disabled),
.btn-leave:hover {
  transform: translateY(-1px);
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-state h3 {
  color: var(--accent-blue);
  margin-bottom: 16px;
}

.btn-create-first {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-first:hover {
  background: var(--accent-blue);
}

/* Responsive */
@media (max-width: 1024px) {
  .calendar-navigation {
    flex-direction: column;
    gap: 16px;
  }
  
  .view-controls {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .calendar-container {
    padding: 16px;
  }
  
  .calendar-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .calendar-grid {
    font-size: 0.8rem;
  }
  
  .calendar-day {
    min-height: 80px;
    padding: 4px;
  }
  
  .list-event {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .event-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
}
</style>
