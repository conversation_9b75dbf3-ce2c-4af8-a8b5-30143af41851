{"name": "front", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "build:seo": "node scripts/generate-sitemap.js && vue-cli-service build", "build:production": "node scripts/build-production.js", "build:optimized": "node scripts/build-optimized.js", "generate-sitemap": "node scripts/generate-sitemap.js", "test:seo": "node scripts/test-seo.js", "audit:seo": "node scripts/seo-audit.js", "analyze:bundle": "vue-cli-service build --report", "performance:test": "npm run build:optimized && npm run serve:dist", "prerender": "npm run build:seo", "serve:dist": "npx http-server dist -p 8080 -c-1"}, "dependencies": {"@stripe/stripe-js": "^2.1.0", "@types/emscripten": "^1.40.1", "axios": "^1.6.0", "bootstrap": "^5.3.2", "core-js": "^3.8.3", "pinia": "^2.1.7", "vosk-browser": "^0.0.8", "vue": "^3.2.13", "vue-router": "^4.0.3", "vuex": "^4.0.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "compression-webpack-plugin": "^11.1.0", "cypress": "^14.5.0", "http-server": "^14.1.1", "node-fetch": "^2.7.0", "prerender-spa-plugin": "^3.4.0"}}