<template>
  <div id="app">
    <navbar/>
    <main class="main-content">
      <router-view />
    </main>
    <AppFooter />
  </div>
</template>


<script>
import router from './router/index.js';
import navbar from '@/components/navbar.vue';
import AppFooter from '@/components/AppFooter.vue';
import 'bootstrap/dist/css/bootstrap.css';
import 'bootstrap/dist/js/bootstrap.js';

export default {
  components: {
    navbar,
    AppFooter,
},
 
  router,
  methods: {
    beforeEnter() {
      document.body.classList.add('no-scroll');

    },
    afterLeave() {
      document.body.classList.remove('no-scroll');
    }
  }
}
</script>

<style>
*,
::before,
::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  overflow-x: hidden; /* Empêche seulement le scroll horizontal */
  overflow-y: auto;   /* Permet le scroll vertical */
  position: relative;
}

.no-scroll {
  overflow-y: hidden;
}

#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-content {
  flex: 1;
  padding-top: 80px; /* Espace pour la navbar fixe */
}

/* Import Font Awesome pour les icônes des réseaux sociaux */
@import url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css');
</style>