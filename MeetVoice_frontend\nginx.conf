# Configuration Nginx optimisée pour MeetVoice
# Performances maximales avec compression et mise en cache

server {
    listen 80;
    listen [::]:80;
    server_name meetvoice.fr www.meetvoice.fr;
    
    # Redirection HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name meetvoice.fr www.meetvoice.fr;
    
    # Certificats SSL (à configurer selon votre fournisseur)
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Configuration SSL optimisée
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Dossier racine
    root /var/www/meetvoice/dist;
    index index.html;
    
    # Compression Gzip
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        application/atom+xml
        application/geo+json
        application/javascript
        application/x-javascript
        application/json
        application/ld+json
        application/manifest+json
        application/rdf+xml
        application/rss+xml
        application/xhtml+xml
        application/xml
        font/eot
        font/otf
        font/ttf
        image/svg+xml
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Compression Brotli (si module disponible)
    brotli on;
    brotli_comp_level 6;
    brotli_types
        application/atom+xml
        application/javascript
        application/json
        application/rss+xml
        application/vnd.ms-fontobject
        application/x-font-opentype
        application/x-font-truetype
        application/x-font-ttf
        application/x-javascript
        application/xhtml+xml
        application/xml
        font/eot
        font/opentype
        font/otf
        font/truetype
        image/svg+xml
        image/vnd.microsoft.icon
        image/x-icon
        image/x-win-bitmap
        text/css
        text/javascript
        text/plain
        text/xml;
    
    # Headers de sécurité
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # Cache des assets statiques
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Vary "Accept-Encoding";
        
        # Servir les fichiers compressés si disponibles
        location ~* \.(css|js)$ {
            add_header Vary "Accept-Encoding";
            
            # Essayer Brotli en premier
            location ~ \.(css|js)$ {
                try_files $uri$arg_v.br $uri$arg_v =404;
                add_header Content-Encoding br;
                add_header Vary "Accept-Encoding";
            }
            
            # Fallback Gzip
            location ~ \.(css|js)$ {
                try_files $uri$arg_v.gz $uri$arg_v =404;
                add_header Content-Encoding gzip;
                add_header Vary "Accept-Encoding";
            }
        }
    }
    
    # Cache des images
    location ~* \.(png|jpg|jpeg|gif|webp|avif)$ {
        expires 6M;
        add_header Cache-Control "public";
        add_header Vary "Accept-Encoding";
    }
    
    # Cache des polices
    location ~* \.(woff|woff2|ttf|eot|otf)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        add_header Access-Control-Allow-Origin "*";
    }
    
    # Service Worker
    location = /sw.js {
        expires 0;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
        add_header Pragma "no-cache";
    }
    
    # Manifest et fichiers PWA
    location = /manifest.json {
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # Sitemap et robots.txt
    location = /sitemap.xml {
        expires 1d;
        add_header Cache-Control "public";
    }
    
    location = /robots.txt {
        expires 1d;
        add_header Cache-Control "public";
    }
    
    # API Proxy (optionnel)
    location /api/ {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache des réponses API
        proxy_cache_valid 200 5m;
        proxy_cache_valid 404 1m;
        add_header X-Cache-Status $upstream_cache_status;
    }
    
    # Gestion des routes Vue.js (SPA)
    location / {
        try_files $uri $uri/ /index.html;
        
        # Cache des pages HTML
        expires 1h;
        add_header Cache-Control "public, must-revalidate";
        
        # Préchargement des ressources critiques
        add_header Link "</static/css/app.css>; rel=preload; as=style" always;
        add_header Link "</static/js/app.js>; rel=preload; as=script" always;
    }
    
    # URLs SEO-friendly pour les articles
    location ~ ^/article/([^/]+)/?$ {
        try_files $uri $uri/ /index.html;
    }
    
    # Optimisation des logs
    access_log /var/log/nginx/meetvoice_access.log combined buffer=16k flush=2m;
    error_log /var/log/nginx/meetvoice_error.log warn;
    
    # Limites de sécurité
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
    keepalive_timeout 65s;
    send_timeout 60s;
    
    # Protection contre les attaques
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }
    
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
