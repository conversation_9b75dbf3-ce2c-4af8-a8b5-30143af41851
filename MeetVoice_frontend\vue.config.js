const { defineConfig } = require('@vue/cli-service');
const PrerenderSPAPlugin = require('prerender-spa-plugin');
const Renderer = PrerenderSPAPlugin.PuppeteerRenderer;
const CompressionPlugin = require('compression-webpack-plugin');
const path = require('path');

module.exports = defineConfig({
  transpileDependencies: true,

  configureWebpack: config => {
    // Optimisations de performance
    config.optimization = {
      ...config.optimization,
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            test: /[\\/]node_modules[\\/]/,
            name: 'vendors',
            chunks: 'all',
          },
        },
      },
    };

    // Compression Gzip et Brotli en production
    if (process.env.NODE_ENV === 'production') {
      // Compression Gzip
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'gzip',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
          filename: '[path][base].gz'
        })
      );

      // Compression Brotli (plus efficace que Gzip)
      config.plugins.push(
        new CompressionPlugin({
          algorithm: 'brotliCompress',
          test: /\.(js|css|html|svg)$/,
          threshold: 8192,
          minRatio: 0.8,
          filename: '[path][base].br'
        })
      );
    }

    // Configuration du pre-rendering uniquement en production
    if (process.env.NODE_ENV === 'production') {
      config.plugins.push(
        new PrerenderSPAPlugin({
          // Chemin vers le dossier de build
          staticDir: path.join(__dirname, 'dist'),

          // Routes à pre-render
          routes: [
            '/',
            '/actualite',
            '/article/tout-savoir-sur-st-valentin',
            '/article/pourquoi-la-kpop-est-si-populaire-une-nouvelle-tendance-qui-fait-parler',
            '/article/rencontre-libertin-cest-quoi-une-nouvelle-tendance-qui-fait-parler',
            '/article/decouverte-culturelle-la-fete-de-ma-musique'
          ],

          // Configuration du renderer
          renderer: new Renderer({
            inject: {
              foo: 'bar'
            },
            headless: true,
            // Attendre que l'application soit prête
            renderAfterDocumentEvent: 'render-event',
            // Timeout pour le rendu
            renderAfterTime: 5000,
            // Ignorer les erreurs HTTPS
            ignoreHTTPSErrors: true,
            // Args pour Puppeteer
            args: ['--no-sandbox', '--disable-setuid-sandbox']
          }),

          // Post-processing pour optimiser le HTML
          postProcess(renderedRoute) {
            // Nettoyer le HTML généré
            renderedRoute.html = renderedRoute.html
              .replace(/<script (.*?)>/gi, '<script $1 defer>')
              .replace('id="app"', 'id="app" data-server-rendered="true"');

            return renderedRoute;
          }
        })
      );
    }
  },

  // Configuration du serveur de développement
  devServer: {
    port: 8081,
    host: 'localhost'
  }
});
