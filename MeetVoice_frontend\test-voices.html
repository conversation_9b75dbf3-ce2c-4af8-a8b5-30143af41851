<!DOCTYPE html>
<html>
<head>
    <title>Test Voix MeetVoice</title>
</head>
<body>
    <h1>Test des voix MeetVoice</h1>
    
    <div>
        <h3>Test Voice ID 1 (<PERSON>)</h3>
        <button onclick="testVoice(1, '<PERSON>')">Tester <PERSON> (ID 1)</button>
    </div>
    
    <div>
        <h3>Test Voice ID 2 (<PERSON>)</h3>
        <button onclick="testVoice(2, '<PERSON>')"><PERSON><PERSON> (ID 2)</button>
    </div>
    
    <div>
        <h3>Test Voice ID 3 (<PERSON>)</h3>
        <button onclick="testVoice(3, '<PERSON>')">Test<PERSON> (ID 3)</button>
    </div>
    
    <div>
        <h3>Test Voice ID 4 (<PERSON>)</h3>
        <button onclick="testVoice(4, '<PERSON>')">Tester <PERSON> (ID 4)</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        async function testVoice(voiceId, voiceName) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `🔄 Test de ${voiceName} (ID ${voiceId})...`;
            
            try {
                const payload = {
                    text: `<PERSON><PERSON><PERSON>, je suis ${voiceName}, voix numéro ${voiceId}`,
                    voice_id: voiceId,
                    language: 'fr',
                    speed: 1.0,
                    pitch: 1.0
                };
                
                console.log('🔊 Test payload:', payload);
                
                const response = await fetch('http://127.0.0.1:8000/tts/public/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });
                
                console.log('📡 Response:', response.status, response.statusText);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('🎵 Audio URL:', data.audio_url);
                    
                    // Jouer l'audio
                    const audio = new Audio(data.audio_url);
                    audio.play();
                    
                    resultDiv.innerHTML = `✅ ${voiceName} (ID ${voiceId}) - Audio joué !`;
                } else {
                    const errorData = await response.json().catch(() => ({}));
                    resultDiv.innerHTML = `❌ Erreur ${response.status}: ${errorData.error || 'Erreur inconnue'}`;
                }
                
            } catch (error) {
                console.error('Erreur:', error);
                resultDiv.innerHTML = `❌ Erreur: ${error.message}`;
            }
        }
    </script>
</body>
</html>
