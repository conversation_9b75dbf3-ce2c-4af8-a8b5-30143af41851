<!-- @format -->

<template>
  <body>
    <div class="bloc">
      <h4>Inscription</h4>

      <!-- Indicateur de progression -->
      <div class="progress-indicator">
        <div class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <span class="step-number">1</span>
          <span class="step-label">Interview vocal</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <span class="step-number">2</span>
          <span class="step-label">Informations de base</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
          <span class="step-number">3</span>
          <span class="step-label">Profil personnel</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
          <span class="step-number">4</span>
          <span class="step-label">Photo (optionnel)</span>
        </div>
        <div class="step" :class="{ active: currentStep >= 5, completed: currentStep > 5 }">
          <span class="step-number">5</span>
          <span class="step-label">Finalisation</span>
        </div>
      </div>

      <!-- Barre de progression -->
      <div class="progress-bar">
        <div
          class="progress-fill"
          :style="{ width: `${(currentStep / 5) * 100}%` }"
        ></div>
      </div>
      <div class="progress-text">
        Étape {{ currentStep }} sur 5
      </div>

      <div class="text">
        <form @submit.prevent="submit">
          <!-- Étape 1: Interview vocal -->
          <div v-if="currentStep === 1" class="step-content">
            <h5>🎤 Interview vocal interactif</h5>
            <p class="step-description">
              Commencez par notre interview vocal pour créer votre profil rapidement et naturellement !
            </p>

            <!-- Configuration de la voix -->
            <div class="voice-config-section">
              <h6>🎵 Configuration de la voix</h6>

              <!-- Sélection de la voix -->
              <div class="voice-selector">
                <label for="voice-select">Choisir une voix :</label>
                <select id="voice-select" v-model="selectedVoiceId" @change="onVoiceChange">
                  <optgroup label="🇫🇷 Voix françaises" v-if="apiVoicesFrench.length > 0">
                    <option
                      v-for="voice in apiVoicesFrench"
                      :key="'api-' + voice.id"
                      :value="voice.id"
                    >
                      {{ voice.name }} - {{ voice.description }}
                    </option>
                  </optgroup>
                </select>
              </div>

              <!-- Contrôles de vitesse et tonalité -->
              <div class="voice-controls">
                <div class="control-group">
                  <label for="voice-speed">Vitesse: {{ voiceSettings.speed }}</label>
                  <input
                    id="voice-speed"
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    v-model.number="voiceSettings.speed"
                    @input="onVoiceParamChange"
                  />
                </div>

                <div class="control-group">
                  <label for="voice-pitch">Tonalité: {{ voiceSettings.pitch }}</label>
                  <input
                    id="voice-pitch"
                    type="range"
                    min="0.5"
                    max="2.0"
                    step="0.1"
                    v-model.number="voiceSettings.pitch"
                    @input="onVoiceParamChange"
                  />
                </div>
              </div>

              <!-- Test de la voix -->
              <div class="voice-test">
                <button
                  type="button"
                  @click="testVoice"
                  :disabled="isTestingVoice"
                  class="btn-test-voice"
                >
                  {{ isTestingVoice ? '🔊 Test en cours...' : '🎵 Tester cette voix' }}
                </button>
              </div>
            </div>

            <!-- Test du microphone -->
            <div class="microphone-section">
              <h6>🎤 Test du microphone</h6>
              <p>Vérifiez que votre microphone fonctionne correctement</p>

              <button
                type="button"
                @click="testMicrophone"
                :disabled="isTestingMicrophone"
                class="btn-test-microphone"
              >
                {{ isTestingMicrophone ? '🎤 Test en cours...' : '🎤 Tester le microphone' }}
              </button>

              <div v-if="microphoneTestResult" class="microphone-result">
                <div v-if="microphoneTestResult.success" class="success-message">
                  ✅ {{ microphoneTestResult.message }}
                </div>
                <div v-else class="error-message">
                  ❌ {{ microphoneTestResult.message }}
                </div>
              </div>
            </div>

            <!-- Démarrage de l'interview -->
            <div v-if="!voiceInterviewStarted" class="interview-start">
              <div class="interview-benefits">
                <div class="benefit-item">
                  <span class="benefit-icon">⚡</span>
                  <span>Plus rapide qu'un formulaire</span>
                </div>
                <div class="benefit-item">
                  <span class="benefit-icon">🎯</span>
                  <span>Profil personnalisé automatiquement</span>
                </div>
                <div class="benefit-item">
                  <span class="benefit-icon">💬</span>
                  <span>Expression naturelle</span>
                </div>
              </div>

              <div class="interview-actions">
                <button
                  type="button"
                  @click="startVoiceInterview"
                  class="btn-start-interview"
                  :disabled="!voiceSupported"
                >
                  🚀 Commencer l'interview vocal
                </button>
                <button
                  type="button"
                  @click="skipToForm"
                  class="btn-skip-to-form"
                >
                  📝 Passer au formulaire classique
                </button>
              </div>
            </div>

            <!-- Interview en cours -->
            <div v-else-if="voiceInterviewStarted && !voiceInterviewCompleted" class="interview-container">
              <VoiceInterview
                :user-email="user.email"
                @interview-completed="onInterviewCompleted"
                @answers-saved="onAnswersSaved"
              />
            </div>

            <!-- Interview terminée -->
            <div v-else-if="voiceInterviewCompleted" class="interview-completed">
              <div class="completion-message">
                <h6>🎉 Interview terminée !</h6>
                <p>Excellent ! Votre profil a été créé automatiquement.</p>
              </div>

              <div class="completion-actions">
                <button
                  type="button"
                  @click="proceedToReview"
                  class="btn-proceed"
                >
                  ✓ Voir mon profil
                </button>
                <button
                  type="button"
                  @click="restartInterview"
                  class="btn-restart"
                >
                  🔄 Recommencer l'interview
                </button>
              </div>
            </div>
          </div>

          <!-- Étape 2: Informations de base (ou données extraites) -->
          <div v-if="currentStep === 2" class="step-content">
            <h5>Informations de base</h5>

            <div v-if="extractedData && Object.keys(extractedData).length > 0" class="extracted-data-section">
              <div class="extracted-header">
                <h6>📋 Données extraites de votre interview</h6>
                <p>Vérifiez et complétez si nécessaire</p>
              </div>
            </div>

            <label for="first_name">Prénom :</label>
            <input
              type="text"
              id="first_name"
              v-model="user.first_name"
              placeholder="Votre prénom"
              required
            />
            <div v-if="fieldErrors.first_name" class="field-error">{{ fieldErrors.first_name }}</div>

            <label for="last_name">Nom :</label>
            <input
              type="text"
              id="last_name"
              v-model="user.last_name"
              placeholder="Votre nom"
              required
            />
            <div v-if="fieldErrors.last_name" class="field-error">{{ fieldErrors.last_name }}</div>

            <label for="username">Nom d'utilisateur :</label>
            <input
              type="text"
              id="username"
              v-model="user.username"
              placeholder="Votre nom d'utilisateur"
              required
            />
            <div v-if="fieldErrors.username" class="field-error">{{ fieldErrors.username }}</div>

            <label for="email">Email :</label>
            <input
              type="email"
              id="email"
              v-model="user.email"
              placeholder="Votre email"
              required
            />
            <div v-if="fieldErrors.email" class="field-error">{{ fieldErrors.email }}</div>

            <label for="password1">Mot de passe :</label>
            <input
              type="password"
              id="password1"
              v-model="user.password1"
              placeholder="Mot de passe (min. 8 caractères)"
              required
            />
            <div v-if="fieldErrors.password1" class="field-error">{{ fieldErrors.password1 }}</div>

            <label for="password2">Confirmer le mot de passe :</label>
            <input
              type="password"
              id="password2"
              v-model="user.password2"
              placeholder="Confirmer le mot de passe"
              required
            />
            <div v-if="fieldErrors.password2" class="field-error">{{ fieldErrors.password2 }}</div>
          </div>

          <!-- Étape 2: Profil personnel -->
          <div v-if="currentStep === 2" class="step-content">
            <h5>Profil personnel</h5>

            <label for="birth_date">Date de naissance :</label>
            <input
              type="date"
              id="birth_date"
              v-model="user.birth_date"
              required
            />
            <div v-if="fieldErrors.birth_date" class="field-error">{{ fieldErrors.birth_date }}</div>

            <label for="gender">Genre :</label>
            <select id="gender" v-model="user.gender" required>
              <option value="">Sélectionnez votre genre</option>
              <option value="M">Homme</option>
              <option value="F">Femme</option>
              <option value="O">Autre</option>
            </select>
            <div v-if="fieldErrors.gender" class="field-error">{{ fieldErrors.gender }}</div>

            <label for="looking_for">Vous recherchez :</label>
            <select id="looking_for" v-model="user.looking_for" required>
              <option value="">Que recherchez-vous ?</option>
              <option value="M">Des hommes</option>
              <option value="F">Des femmes</option>
              <option value="B">Les deux</option>
            </select>
            <div v-if="fieldErrors.looking_for" class="field-error">{{ fieldErrors.looking_for }}</div>

            <label for="city">Ville :</label>
            <input
              type="text"
              id="city"
              v-model="user.city"
              placeholder="Votre ville"
              required
            />
            <div v-if="fieldErrors.city" class="field-error">{{ fieldErrors.city }}</div>

            <!-- Bouton pour activer la géolocalisation (optionnel) -->
            <button
              type="button"
              @click="detectLocation"
              class="btn-location"
              v-if="geolocationSupported"
            >
              🎯 Détecter ma position
            </button>

            <label for="bio">Description (optionnel) :</label>
            <textarea
              id="bio"
              v-model="user.bio"
              placeholder="Parlez-nous de vous..."
              rows="4"
            ></textarea>
          </div>

          <!-- Étape 3: Photo de profil -->
          <div v-if="currentStep === 3" class="step-content">
            <h5>Photo de profil (optionnel)</h5>

            <div class="photo-upload-section">
              <div v-if="!photoPreview" class="upload-placeholder">
                <input
                  type="file"
                  id="profile_photo"
                  @change="handlePhotoUpload"
                  accept="image/*"
                  style="display: none"
                />
                <label for="profile_photo" class="upload-button">
                  <span>📷</span>
                  <span>Ajouter une photo</span>
                </label>
                <p class="upload-hint">Formats acceptés: JPG, PNG (max 5MB)</p>
              </div>

              <div v-if="photoPreview" class="photo-preview">
                <img :src="photoPreview" alt="Aperçu de la photo" />
                <button type="button" @click="removePhoto" class="remove-photo">
                  ✕ Supprimer
                </button>
              </div>
            </div>
          </div>

          <!-- Étape 4: Configuration des médias -->
          <div v-if="currentStep === 4" class="step-content">
            <h5>Configuration Audio/Vidéo (optionnel)</h5>
            <p class="step-description">
              Configurez vos périphériques audio et vidéo pour une meilleure expérience.
            </p>

            <div class="media-config-preview">
              <div class="config-option">
                <div class="option-icon">🎤</div>
                <div class="option-content">
                  <h6>Configuration du microphone</h6>
                  <p>Testez et configurez votre microphone pour l'interview vocal</p>
                </div>
              </div>

              <div class="config-option">
                <div class="option-icon">🔊</div>
                <div class="option-content">
                  <h6>Configuration des haut-parleurs</h6>
                  <p>Vérifiez que vos haut-parleurs fonctionnent correctement</p>
                </div>
              </div>

              <div class="config-option">
                <div class="option-icon">📹</div>
                <div class="option-content">
                  <h6>Configuration de la caméra</h6>
                  <p>Configurez votre caméra pour les futures vidéoconférences</p>
                </div>
              </div>
            </div>

            <div class="config-actions">
              <button
                type="button"
                @click="openMediaConfig"
                class="btn-config"
              >
                🔧 Configurer mes périphériques
              </button>
              <button
                type="button"
                @click="skipMediaConfig"
                class="btn-skip-config"
              >
                Passer cette étape
              </button>
            </div>

            <div v-if="mediaConfigCompleted" class="config-completed">
              <div class="completed-icon">✅</div>
              <p>Configuration terminée ! Vous pouvez maintenant passer à l'interview vocal.</p>
            </div>
          </div>

          <!-- Étape 5: Interview vocal -->
          <div v-if="currentStep === 5" class="step-content">
            <h5>Interview vocal (optionnel)</h5>
            <p class="step-description">
              Complétez votre profil avec une interview vocale pour vous démarquer !
            </p>

            <div v-if="!voiceSupported" class="voice-not-supported">
              <p>⚠️ Votre navigateur ne supporte pas la reconnaissance vocale.</p>
              <p>Vous pouvez passer cette étape ou utiliser Chrome/Safari/Edge.</p>
            </div>

            <div v-else-if="!voiceInterviewStarted" class="voice-interview-intro">
              <div class="voice-features">
                <div class="feature-item">
                  <span class="feature-icon">🎤</span>
                  <span>Répondez à 5 questions courtes</span>
                </div>
                <div class="feature-item">
                  <span class="feature-icon">⏱️</span>
                  <span>2-3 minutes seulement</span>
                </div>
                <div class="feature-item">
                  <span class="feature-icon">✨</span>
                  <span>Démarquez-vous des autres profils</span>
                </div>
              </div>

              <div class="voice-actions">
                <button
                  type="button"
                  @click="startVoiceInterview"
                  class="btn-start-interview"
                >
                  🎙️ Commencer l'interview vocal
                </button>
                <button
                  type="button"
                  @click="skipVoiceInterview"
                  class="btn-skip"
                >
                  Passer cette étape
                </button>
              </div>
            </div>


          </div>

          <!-- Messages d'erreur et de succès -->
          <p v-if="errors" class="error-message">{{ errors }}</p>
          <p v-if="successMessage" class="success-message">{{ successMessage }}</p>

          <!-- Boutons de navigation -->
          <div class="navigation-buttons">
            <button
              v-if="currentStep > 1"
              type="button"
              @click="previousStep"
              class="btn btn-secondary"
            >
              Précédent
            </button>

            <button
              v-if="currentStep < 5"
              type="button"
              @click="nextStep"
              class="btn btnr"
              :disabled="!canProceedToNextStep"
            >
              {{ getNextButtonText() }}
            </button>

            <button
              v-if="currentStep === 5"
              type="submit"
              class="btn btnr"
              :disabled="isLoading"
            >
              {{ isLoading ? 'Inscription...' : 'Finaliser l\'inscription' }}
            </button>
          </div>

          <p class="login-link">
            Déjà un compte ?
            <router-link to="/login" class="link">Se connecter</router-link>
          </p>
        </form>
      </div>
    </div>
  </body>
</template>

<script>
import { accountService } from "@/_services";
import VoiceInterview from "@/components/VoiceInterview.vue";

export default {
  name: "RegisterView",
  components: {
    VoiceInterview
  },
  data() {
    return {
      currentStep: 1,
      user: {
        first_name: "",
        last_name: "",
        username: "",
        email: "",
        password1: "",
        password2: "",
        birth_date: "",
        gender: "",
        looking_for: "",
        city: "",
        bio: "",
        profile_photo: null,
      },
      selectedLocation: null,
      photoPreview: null,
      errors: "",
      successMessage: "",
      isLoading: false,
      fieldErrors: {},
      voiceSupported: false,
      voiceInterviewStarted: false,
      voiceInterviewCompleted: false,
      voiceAnswers: [],
      extractedData: {},

      // Configuration de la voix
      apiVoices: [],
      selectedVoiceId: null,
      voiceSettings: {
        speed: 1.0,
        pitch: 1.0
      },
      isTestingVoice: false,
      isTestingMicrophone: false,
      microphoneTestResult: null,
      geolocationSupported: false,
      mediaConfigCompleted: false,

    };
  },

  async mounted() {
    await this.checkFeatureSupport();
  },

  computed: {
    canProceedToNextStep() {
      if (this.currentStep === 1) {
        return true; // Interview vocal ou skip
      }
      if (this.currentStep === 2) {
        return this.user.first_name &&
               this.user.last_name &&
               this.user.username &&
               this.user.email &&
               this.user.password1 &&
               this.user.password2 &&
               this.user.password1 === this.user.password2 &&
               this.user.password1.length >= 8;
      }
      if (this.currentStep === 3) {
        return this.user.birth_date &&
               this.user.gender &&
               this.user.looking_for &&
               this.user.city;
      }
      if (this.currentStep === 4) {
        return true; // Photo optionnelle
      }
      if (this.currentStep === 5) {
        return true; // Finalisation
      }
      return true;
    },

    apiVoicesFrench() {
      return this.apiVoices.filter(voice => voice.language === 'fr');
    }
  },
  methods: {
    async checkFeatureSupport() {
      // Vérifier le support de la reconnaissance vocale
      this.voiceSupported = !!(window.SpeechRecognition || window.webkitSpeechRecognition);

      // Vérifier le support de la géolocalisation
      this.geolocationSupported = !!navigator.geolocation;

      // Charger les voix disponibles
      await this.loadAvailableVoices();
    },

    async loadAvailableVoices() {
      try {
        const voices = await ttsService.getAvailableVoices();
        this.apiVoices = voices;

        // Sélectionner une voix française par défaut
        const defaultVoice = this.apiVoicesFrench.find(voice =>
          voice.name.toLowerCase().includes('jenny') ||
          voice.name.toLowerCase().includes('camille')
        ) || this.apiVoicesFrench[0];

        if (defaultVoice) {
          this.selectedVoiceId = defaultVoice.id;
        }

        console.log('🎵 Voix chargées:', voices.length);
      } catch (error) {
        console.error('❌ Erreur chargement voix:', error);
      }
    },

    onVoiceChange() {
      console.log('🔄 Voix changée:', this.selectedVoiceId);
      this.saveVoicePreferences();
    },

    onVoiceParamChange() {
      console.log('🎛️ Paramètres voix modifiés:', this.voiceSettings);
      this.saveVoicePreferences();
    },

    saveVoicePreferences() {
      const preferences = {
        voice: this.selectedVoiceId,
        rate: this.voiceSettings.speed,
        pitch: this.voiceSettings.pitch,
        timestamp: new Date().toISOString()
      };

      localStorage.setItem('meetvoice_voice_preferences', JSON.stringify(preferences));
      console.log('💾 Préférences vocales sauvegardées');
    },

    async testVoice() {
      if (!this.selectedVoiceId) {
        alert('Veuillez sélectionner une voix');
        return;
      }

      try {
        this.isTestingVoice = true;

        const selectedVoice = this.apiVoices.find(v => v.id == this.selectedVoiceId);
        const testText = `Bonjour ! Je suis ${selectedVoice?.name}. Voici un test de ma voix avec une vitesse de ${this.voiceSettings.speed} et une tonalité de ${this.voiceSettings.pitch}.`;

        await ttsService.speak(testText, {
          voiceId: this.selectedVoiceId,
          speed: this.voiceSettings.speed,
          pitch: this.voiceSettings.pitch
        });

      } catch (error) {
        console.error('❌ Erreur test voix:', error);
        alert('Erreur lors du test de la voix');
      } finally {
        this.isTestingVoice = false;
      }
    },

    async testMicrophone() {
      try {
        this.isTestingMicrophone = true;
        this.microphoneTestResult = null;

        // Demander l'accès au microphone
        const stream = await navigator.mediaDevices.getUserMedia({
          audio: {
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        });

        // Test réussi
        this.microphoneTestResult = {
          success: true,
          message: 'Microphone fonctionnel ! Vous pouvez commencer l\'interview.'
        };

        // Arrêter le stream
        stream.getTracks().forEach(track => track.stop());

      } catch (error) {
        console.error('❌ Erreur test microphone:', error);
        this.microphoneTestResult = {
          success: false,
          message: 'Impossible d\'accéder au microphone. Vérifiez les permissions.'
        };
      } finally {
        this.isTestingMicrophone = false;
      }
    },

    async detectLocation() {
      if (!this.geolocationSupported) {
        this.errors = "Géolocalisation non supportée par votre navigateur";
        return;
      }

      try {
        this.isLoading = true;
        this.errors = "";

        const position = await new Promise((resolve, reject) => {
          navigator.geolocation.getCurrentPosition(resolve, reject, {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000
          });
        });

        // Utiliser Nominatim pour obtenir l'adresse
        const response = await fetch(
          `https://nominatim.openstreetmap.org/reverse?format=json&lat=${position.coords.latitude}&lon=${position.coords.longitude}&addressdetails=1&accept-language=fr`
        );

        if (response.ok) {
          const data = await response.json();
          this.user.city = data.address?.city || data.address?.town || data.address?.village || '';
          this.successMessage = "Position détectée avec succès !";
          setTimeout(() => this.successMessage = "", 3000);
        }
      } catch (error) {
        console.error('Erreur géolocalisation:', error);
        this.errors = "Impossible de détecter votre position. Veuillez saisir votre ville manuellement.";
      } finally {
        this.isLoading = false;
      }
    },

    startVoiceInterview() {
      this.voiceInterviewStarted = true;
    },

    skipToForm() {
      // Passer directement à l'étape 2 (formulaire)
      this.currentStep = 2;
    },

    onInterviewCompleted(result) {
      this.voiceAnswers = result.answers || [];
      this.extractedData = result.extractedData || {};
      this.voiceInterviewCompleted = true;

      // Pré-remplir les champs du formulaire avec les données extraites
      if (this.extractedData.first_name) this.user.first_name = this.extractedData.first_name;
      if (this.extractedData.last_name) this.user.last_name = this.extractedData.last_name;
      if (this.extractedData.username) this.user.username = this.extractedData.username;
      if (this.extractedData.email) this.user.email = this.extractedData.email;
      if (this.extractedData.birth_date) this.user.birth_date = this.extractedData.birth_date;
      if (this.extractedData.gender) this.user.gender = this.extractedData.gender;
      if (this.extractedData.looking_for) this.user.looking_for = this.extractedData.looking_for;
      if (this.extractedData.city) this.user.city = this.extractedData.city;
      if (this.extractedData.password) {
        this.user.password1 = this.extractedData.password;
        this.user.password2 = this.extractedData.password;
      }

      // Gérer la photo si elle existe
      if (result.profilePhoto) {
        this.user.profile_photo = result.profilePhoto;
      }

      this.successMessage = "Interview vocal terminé avec succès !";
    },

    onProfileDataExtracted(data) {
      this.extractedData = { ...this.extractedData, ...data };
    },

    onAnswersSaved(interviewData) {
      console.log('Données de l\'interview sauvegardées:', interviewData);

      // Mapper les données vocales vers le formulaire utilisateur
      if (interviewData.backend_fields) {
        Object.assign(this.user, interviewData.backend_fields);
      }

      this.voiceInterviewCompleted = true;
      this.successMessage = "Interview vocal terminé et données sauvegardées !";
    },

    proceedToReview() {
      // Si toutes les données sont présentes, aller directement à la finalisation
      if (this.extractedData.first_name && this.extractedData.email && this.extractedData.password) {
        this.currentStep = 5; // Finalisation
      } else {
        this.currentStep = 2; // Compléter les informations manquantes
      }
    },

    restartInterview() {
      this.voiceInterviewStarted = false;
      this.voiceInterviewCompleted = false;
      this.voiceAnswers = [];
      this.extractedData = {};
      this.successMessage = '';
    },

    skipVoiceInterview() {
      this.voiceInterviewCompleted = true;
      this.voiceAnswers = [];
    },



    openMediaConfig() {
      // Ouvrir la configuration des médias dans un nouvel onglet
      window.open('/media-config', '_blank');
      this.mediaConfigCompleted = true;
    },

    skipMediaConfig() {
      this.mediaConfigCompleted = true;
    },

    getNextButtonText() {
      switch (this.currentStep) {
        case 1: return 'Suivant';
        case 2: return 'Suivant';
        case 3: return 'Configuration';
        case 4: return 'Interview vocal';
        default: return 'Suivant';
      }
    },

    nextStep() {
      this.clearFieldErrors();
      if (this.validateCurrentStep()) {
        this.currentStep++;
      }
    },

    previousStep() {
      this.currentStep--;
      this.clearFieldErrors();
    },

    validateCurrentStep() {
      this.fieldErrors = {};
      let isValid = true;

      if (this.currentStep === 1) {
        if (!this.user.first_name) {
          this.fieldErrors.first_name = "Le prénom est requis";
          isValid = false;
        }
        if (!this.user.last_name) {
          this.fieldErrors.last_name = "Le nom est requis";
          isValid = false;
        }
        if (!this.user.username) {
          this.fieldErrors.username = "Le nom d'utilisateur est requis";
          isValid = false;
        }
        if (!this.user.email) {
          this.fieldErrors.email = "L'email est requis";
          isValid = false;
        } else if (!this.isValidEmail(this.user.email)) {
          this.fieldErrors.email = "Format d'email invalide";
          isValid = false;
        }
        if (!this.user.password1) {
          this.fieldErrors.password1 = "Le mot de passe est requis";
          isValid = false;
        } else if (this.user.password1.length < 8) {
          this.fieldErrors.password1 = "Le mot de passe doit contenir au moins 8 caractères";
          isValid = false;
        }
        if (!this.user.password2) {
          this.fieldErrors.password2 = "La confirmation du mot de passe est requise";
          isValid = false;
        } else if (this.user.password1 !== this.user.password2) {
          this.fieldErrors.password2 = "Les mots de passe ne correspondent pas";
          isValid = false;
        }
      }

      if (this.currentStep === 2) {
        if (!this.user.birth_date) {
          this.fieldErrors.birth_date = "La date de naissance est requise";
          isValid = false;
        } else if (!this.isValidAge(this.user.birth_date)) {
          this.fieldErrors.birth_date = "Vous devez avoir au moins 18 ans";
          isValid = false;
        }
        if (!this.user.gender) {
          this.fieldErrors.gender = "Le genre est requis";
          isValid = false;
        }
        if (!this.user.looking_for) {
          this.fieldErrors.looking_for = "Veuillez indiquer ce que vous recherchez";
          isValid = false;
        }
        if (!this.user.city) {
          this.fieldErrors.city = "La ville est requise";
          isValid = false;
        }
      }

      return isValid;
    },

    clearFieldErrors() {
      this.fieldErrors = {};
    },

    isValidEmail(email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    },

    isValidAge(birthDate) {
      const today = new Date();
      const birth = new Date(birthDate);
      const age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        return age - 1 >= 18;
      }
      return age >= 18;
    },

    onLocationSelected(location) {
      if (location && location.address) {
        this.user.city = location.address.city;
        this.selectedLocation = location;
        this.clearFieldErrors();
      }
    },

    onLocationValidation(result) {
      if (!result.isConsistent && result.warning) {
        // Afficher un avertissement mais ne pas bloquer l'inscription
        console.warn('Validation géolocalisation:', result.warning);
      }
    },

    handlePhotoUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // Validation de la taille (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          this.errors = "La photo ne doit pas dépasser 5MB";
          return;
        }

        // Validation du type
        if (!file.type.startsWith('image/')) {
          this.errors = "Veuillez sélectionner une image valide";
          return;
        }

        this.user.profile_photo = file;

        // Créer l'aperçu
        const reader = new FileReader();
        reader.onload = (e) => {
          this.photoPreview = e.target.result;
        };
        reader.readAsDataURL(file);

        this.errors = "";
      }
    },

    removePhoto() {
      this.user.profile_photo = null;
      this.photoPreview = null;
      // Réinitialiser l'input file
      const fileInput = document.getElementById('profile_photo');
      if (fileInput) {
        fileInput.value = '';
      }
    },

    async submit() {
      try {
        // Réinitialiser les messages
        this.errors = "";
        this.successMessage = "";
        this.isLoading = true;

        // Validation finale
        if (!this.validateCurrentStep()) {
          this.isLoading = false;
          return;
        }

        const formData = new FormData();
        formData.append("first_name", this.user.first_name);
        formData.append("last_name", this.user.last_name);
        formData.append("username", this.user.username);
        formData.append("email", this.user.email);
        formData.append("password1", this.user.password1);
        formData.append("password2", this.user.password2);
        formData.append("birth_date", this.user.birth_date);
        formData.append("gender", this.user.gender);
        formData.append("looking_for", this.user.looking_for);
        formData.append("city", this.user.city);

        if (this.user.bio) {
          formData.append("bio", this.user.bio);
        }

        if (this.user.profile_photo) {
          formData.append("profile_photo", this.user.profile_photo);
        }

        // Ajouter les données de l'interview vocal si disponibles
        if (this.voiceAnswers && this.voiceAnswers.length > 0) {
          formData.append("voice_interview_completed", "true");
          this.voiceAnswers.forEach((answer, index) => {
            formData.append(`voice_answer_${index}`, answer);
          });
          formData.append("voice_answers_count", this.voiceAnswers.length);
        }

        // Appel API d'inscription
        const response = await fetch("http://127.0.0.1:8000/auth/register/", {
          method: "POST",
          body: formData,
        });

        if (response.ok) {
          this.successMessage = "Inscription réussie ! Vous pouvez maintenant vous connecter.";

          // Rediriger vers la page de connexion après 2 secondes
          setTimeout(() => {
            this.$router.push("/login");
          }, 2000);
        } else {
          const errorData = await response.json();
          this.errors = errorData.message || "Erreur lors de l'inscription";
        }
      } catch (error) {
        console.error("Erreur lors de l'inscription", error);
        this.errors = "Erreur de connexion au serveur";
      } finally {
        this.isLoading = false;
      }
    },
  },
};
</script>

<style scoped>
body {
  display: flex;
  height: 100vh;
  background-image: url("@/assets/Accueil.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

h4 {
  padding-top: 30px;
  font-size: 30px;
  font-style: italic;
  color: #ffffff;
  padding-bottom: 30px;
}

.bloc {
  text-align: center;
  width: 500px;
  min-height: 800px;
  background: #3c0940 !important;
  border: 1px solid black;
  margin-top: 20px; /* Réduit car .main-content a déjà padding-top: 80px */
  margin-left: auto;
  margin-right: auto;
  padding-bottom: 20px;
  border-radius: 10px;
}

.text {
  font-family: "Times New Roman", Times, serif;
  color: white !important;
  font-weight: lighter;
  font-size: 18px;
  position: relative;
  display: flex;
  flex-flow: column wrap;
  align-items: center;
  padding: 0 20px;
}

.text label {
  margin-top: 15px;
  margin-bottom: 5px;
  align-self: flex-start;
}

.text input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.btnr {
  margin-top: 20px;
  background-color: rgb(58, 58, 64) !important;
  color: white !important;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btnr:hover:not(:disabled) {
  border: 1px solid;
  color: #1f8ea5 !important;
  background-color: black !important;
}

.btnr:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.error-message {
  color: #ff6b6b;
  font-size: 14px;
  margin-top: 10px;
}

.success-message {
  color: #51cf66;
  font-size: 14px;
  margin-top: 10px;
}

.login-link {
  margin-top: 20px;
  font-size: 14px;
}

.link {
  color: #1f8ea5;
  text-decoration: none;
}

.link:hover {
  text-decoration: underline;
}

/* Barre de progression */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #666;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #1f8ea5;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #ccc;
  margin-bottom: 30px;
}

/* Styles pour l'indicateur de progression */
.progress-indicator {
  display: flex;
  justify-content: space-between;
  margin: 20px 0 30px 0;
  padding: 0 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  position: relative;
}

.step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background-color: #666;
  z-index: 1;
}

.step.completed:not(:last-child)::after {
  background-color: #1f8ea5;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #666;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 5px;
  position: relative;
  z-index: 2;
}

.step.active .step-number {
  background-color: #1f8ea5;
}

.step.completed .step-number {
  background-color: #51cf66;
}

.step-label {
  font-size: 12px;
  color: #ccc;
  text-align: center;
}

.step.active .step-label {
  color: #1f8ea5;
  font-weight: bold;
}

.step.completed .step-label {
  color: #51cf66;
}

/* Styles pour les étapes */
.step-content {
  min-height: 400px;
}

.step-content h5 {
  color: #1f8ea5;
  margin-bottom: 20px;
  font-size: 18px;
}

/* Styles pour les champs de formulaire */
.text select {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  background-color: white;
  color: #333;
}

.text textarea {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  resize: vertical;
  font-family: inherit;
}

/* Messages d'erreur par champ */
.field-error {
  color: #ff6b6b;
  font-size: 12px;
  text-align: left;
  margin-top: -8px;
  margin-bottom: 10px;
}

/* Styles pour l'upload de photo */
.photo-upload-section {
  margin: 20px 0;
}

.upload-placeholder {
  text-align: center;
  padding: 40px 20px;
  border: 2px dashed #666;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-button {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background-color: #1f8ea5;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-button:hover {
  background-color: #1a7a94;
}

.upload-button span:first-child {
  font-size: 24px;
}

.upload-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #ccc;
}

.photo-preview {
  text-align: center;
}

.photo-preview img {
  max-width: 200px;
  max-height: 200px;
  border-radius: 8px;
  margin-bottom: 10px;
}

.remove-photo {
  background-color: #ff6b6b;
  color: white;
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.remove-photo:hover {
  background-color: #ff5252;
}

/* Boutons de navigation */
.navigation-buttons {
  display: flex;
  justify-content: space-between;
  gap: 10px;
  margin-top: 30px;
}

.btn-secondary {
  background-color: #666 !important;
  color: white !important;
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.btn-secondary:hover {
  background-color: #555 !important;
}

/* Styles pour la géolocalisation */
.btn-location {
  background-color: #1f8ea5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-top: 10px;
  transition: background-color 0.3s;
}

.btn-location:hover {
  background-color: #1a7a94;
}

/* === STYLES INTERVIEW VOCAL === */
.step-description {
  color: #ccc;
  font-size: 14px;
  margin-bottom: 20px;
  text-align: center;
}

/* Configuration de la voix */
.voice-config-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid #444;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.voice-config-section h6 {
  color: #fff;
  margin-bottom: 15px;
  font-size: 16px;
}

.voice-selector {
  margin-bottom: 15px;
}

.voice-selector label {
  display: block;
  color: #ccc;
  margin-bottom: 5px;
  font-size: 14px;
}

.voice-selector select {
  width: 100%;
  padding: 8px 12px;
  background: #2a2a2a;
  border: 1px solid #555;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
}

.voice-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-bottom: 15px;
}

.control-group {
  display: flex;
  flex-direction: column;
}

.control-group label {
  color: #ccc;
  font-size: 12px;
  margin-bottom: 5px;
}

.control-group input[type="range"] {
  width: 100%;
  height: 6px;
  background: #444;
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.control-group input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.voice-test {
  text-align: center;
}

.btn-test-voice {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.btn-test-voice:hover:not(:disabled) {
  background: #0056b3;
}

.btn-test-voice:disabled {
  background: #666;
  cursor: not-allowed;
}

/* Section microphone */
.microphone-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid #444;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  text-align: center;
}

.microphone-section h6 {
  color: #fff;
  margin-bottom: 8px;
}

.microphone-section p {
  color: #ccc;
  font-size: 14px;
  margin-bottom: 15px;
}

.btn-test-microphone {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.3s ease;
}

.btn-test-microphone:hover:not(:disabled) {
  background: #218838;
}

.btn-test-microphone:disabled {
  background: #666;
  cursor: not-allowed;
}

.microphone-result {
  margin-top: 15px;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.success-message {
  background: rgba(40, 167, 69, 0.2);
  border: 1px solid #28a745;
  color: #28a745;
}

.error-message {
  background: rgba(220, 53, 69, 0.2);
  border: 1px solid #dc3545;
  color: #dc3545;
}

/* Section démarrage interview */
.interview-start {
  text-align: center;
  margin-top: 20px;
}

.interview-benefits {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.benefit-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ccc;
  font-size: 14px;
}

.benefit-icon {
  font-size: 18px;
}

.interview-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-start-interview {
  background: #28a745;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: background 0.3s ease;
}

.btn-start-interview:hover:not(:disabled) {
  background: #218838;
}

.btn-start-interview:disabled {
  background: #666;
  cursor: not-allowed;
}

.btn-skip-to-form {
  background: transparent;
  color: #007bff;
  border: 2px solid #007bff;
  padding: 13px 28px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.3s ease;
}

.btn-skip-to-form:hover {
  background: #007bff;
  color: white;
}

/* Interview terminée */
.interview-completed {
  text-align: center;
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid #28a745;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
}

.completion-message h6 {
  color: #28a745;
  margin-bottom: 8px;
}

.completion-message p {
  color: #ccc;
  margin-bottom: 20px;
}

.completion-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-proceed {
  background: #28a745;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn-proceed:hover {
  background: #218838;
}

.btn-restart {
  background: transparent;
  color: #6c757d;
  border: 1px solid #6c757d;
  padding: 12px 24px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
}

.btn-restart:hover {
  background: #6c757d;
  color: white;
}

/* Container interview */
.interview-container {
  margin-top: 20px;
  border: 1px solid #444;
  border-radius: 8px;
  overflow: hidden;
}

/* Données extraites */
.extracted-data-section {
  background: rgba(40, 167, 69, 0.1);
  border: 1px solid #28a745;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.extracted-header h6 {
  color: #28a745;
  margin-bottom: 5px;
}

.extracted-header p {
  color: #ccc;
  font-size: 14px;
  margin: 0;
}

.voice-not-supported {
  text-align: center;
  padding: 20px;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid #ffc107;
  border-radius: 8px;
  margin-bottom: 20px;
}

.voice-not-supported p {
  color: #ffc107;
  margin: 5px 0;
}

.voice-interview-intro {
  text-align: center;
}

.voice-features {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 30px 0;
  padding: 20px;
  background-color: rgba(31, 142, 165, 0.1);
  border-radius: 8px;
  border: 1px solid #1f8ea5;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 15px;
  color: #ccc;
}

.feature-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.voice-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

.btn-start-interview {
  background-color: #1f8ea5;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-start-interview:hover {
  background-color: #1a7a94;
  transform: translateY(-2px);
}

.btn-skip {
  background-color: transparent;
  color: #ccc;
  border: 1px solid #666;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-skip:hover {
  background-color: #666;
  color: white;
}

.voice-interview-container {
  margin-top: 20px;
}

/* Responsive pour les nouvelles fonctionnalités */
@media (max-width: 768px) {
  .voice-controls {
    grid-template-columns: 1fr;
  }

  .interview-benefits {
    flex-direction: column;
    align-items: center;
  }

  .interview-actions,
  .completion-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn-start-interview,
  .btn-skip-to-form,
  .btn-proceed,
  .btn-restart {
    width: 100%;
    max-width: 300px;
  }
  .voice-features {
    padding: 15px;
  }

  .feature-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .feature-icon {
    width: auto;
  }

  .btn-start-interview {
    padding: 12px 24px;
    font-size: 14px;
  }
}

/* Styles pour la configuration des médias */
.media-config-preview {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
}

.config-option {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background-color: rgba(31, 142, 165, 0.1);
  border: 1px solid #1f8ea5;
  border-radius: 8px;
}

.option-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.option-content h6 {
  color: #1f8ea5;
  margin: 0 0 5px 0;
  font-size: 16px;
}

.option-content p {
  color: #ccc;
  margin: 0;
  font-size: 14px;
}

.config-actions {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
  margin: 30px 0;
}

.btn-config {
  background-color: #1f8ea5;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-config:hover {
  background-color: #1a7a94;
  transform: translateY(-2px);
}

.btn-skip-config {
  background-color: transparent;
  color: #ccc;
  border: 1px solid #666;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-skip-config:hover {
  background-color: #666;
  color: white;
}

.config-completed {
  text-align: center;
  padding: 20px;
  background-color: rgba(81, 207, 102, 0.1);
  border: 1px solid #51cf66;
  border-radius: 8px;
  margin-top: 20px;
}

.completed-icon {
  font-size: 48px;
  margin-bottom: 10px;
}

.config-completed p {
  color: #51cf66;
  margin: 0;
  font-weight: 500;
}

/* Responsive pour la configuration des médias */
@media (max-width: 768px) {
  .config-option {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .option-icon {
    width: auto;
  }

  .btn-config {
    padding: 12px 24px;
    font-size: 14px;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .bloc {
    width: 90%;
    margin-left: 5%;
    margin-top: 20px; /* Réduit car .main-content a déjà padding-top: 80px */
  }
}
</style>
