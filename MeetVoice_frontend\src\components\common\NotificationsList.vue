<template>
  <div class="notifications-list">
    <div class="notifications-header">
      <h3>Notifications</h3>
      <button @click="markAllAsRead" class="btn-mark-all">Tout marquer lu</button>
    </div>
    
    <div class="notifications-content">
      <div v-if="notifications.length === 0" class="no-notifications">
        <div class="no-notifications-icon">🔔</div>
        <p>Aucune notification</p>
      </div>
      
      <div v-else class="notifications-items">
        <div 
          v-for="notification in notifications" 
          :key="notification.id" 
          class="notification-item"
          :class="{ 'unread': !notification.read }"
          @click="markAsRead(notification.id)"
        >
          <div class="notification-icon">{{ notification.icon }}</div>
          <div class="notification-content">
            <h4>{{ notification.title }}</h4>
            <p>{{ notification.message }}</p>
            <span class="notification-time">{{ formatTime(notification.time) }}</span>
          </div>
          <div v-if="!notification.read" class="unread-indicator"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'NotificationsList',
  
  computed: {
    ...mapState('notifications', ['notifications'])
  },
  
  methods: {
    ...mapActions('notifications', ['markAsRead', 'markAllAsRead']),
    
    formatTime(date) {
      const now = new Date()
      const diff = now - new Date(date)
      const minutes = Math.floor(diff / (1000 * 60))
      const hours = Math.floor(diff / (1000 * 60 * 60))
      const days = Math.floor(diff / (1000 * 60 * 60 * 24))
      
      if (minutes < 60) {
        return `Il y a ${minutes} min`
      } else if (hours < 24) {
        return `Il y a ${hours}h`
      } else {
        return `Il y a ${days}j`
      }
    }
  },
  
  mounted() {
    // Charger les notifications au montage du composant
    this.$store.dispatch('notifications/loadNotifications')
  }
}
</script>

<style scoped>
.notifications-list {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.notifications-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.notifications-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.btn-mark-all {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-mark-all:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.notifications-content {
  max-height: 400px;
  overflow-y: auto;
}

.no-notifications {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-notifications-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-notifications p {
  margin: 0;
  font-size: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 16px 24px;
  border-bottom: 1px solid #f8f9fa;
  cursor: pointer;
  transition: background 0.3s ease;
  position: relative;
}

.notification-item:hover {
  background: #f8f9fa;
}

.notification-item.unread {
  background: #f0f8ff;
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  background: #f8f9fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-content h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.notification-content p {
  margin: 0 0 4px 0;
  font-size: 13px;
  color: #666;
  line-height: 1.4;
}

.notification-time {
  font-size: 11px;
  color: #999;
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background: #007bff;
  border-radius: 50%;
  flex-shrink: 0;
  margin-top: 6px;
}

/* Scrollbar personnalisée */
.notifications-content::-webkit-scrollbar {
  width: 6px;
}

.notifications-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.notifications-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.notifications-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

@media (max-width: 768px) {
  .notifications-header {
    padding: 16px 20px;
  }
  
  .notification-item {
    padding: 12px 20px;
  }
  
  .notification-icon {
    width: 32px;
    height: 32px;
    font-size: 20px;
  }
}
</style>
