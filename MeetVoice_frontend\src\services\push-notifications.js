class PushNotificationService {
  constructor() {
    this.registration = null
    this.permission = Notification.permission
    this.isSupported = 'serviceWorker' in navigator && 'PushManager' in window
  }

  // Vérifier si les notifications push sont supportées
  isSupported() {
    return this.isSupported
  }

  // Demander la permission pour les notifications
  async requestPermission() {
    if (!this.isSupported) {
      throw new Error('Les notifications push ne sont pas supportées')
    }

    if (this.permission === 'granted') {
      return true
    }

    if (this.permission === 'denied') {
      throw new Error('Les notifications ont été refusées par l\'utilisateur')
    }

    const permission = await Notification.requestPermission()
    this.permission = permission

    return permission === 'granted'
  }

  // Enregistrer le service worker
  async registerServiceWorker() {
    if (!this.isSupported) {
      throw new Error('Service Worker non supporté')
    }

    try {
      this.registration = await navigator.serviceWorker.register('/sw.js')
      console.log('Service Worker enregistré:', this.registration)
      return this.registration
    } catch (error) {
      console.error('Erreur lors de l\'enregistrement du Service Worker:', error)
      throw error
    }
  }

  // S'abonner aux notifications push
  async subscribe() {
    if (!this.registration) {
      await this.registerServiceWorker()
    }

    const hasPermission = await this.requestPermission()
    if (!hasPermission) {
      throw new Error('Permission refusée pour les notifications')
    }

    try {
      const subscription = await this.registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: this.urlBase64ToUint8Array(
          process.env.VUE_APP_VAPID_PUBLIC_KEY || 'YOUR_VAPID_PUBLIC_KEY'
        )
      })

      console.log('Abonnement push créé:', subscription)
      
      // Envoyer l'abonnement au serveur
      await this.sendSubscriptionToServer(subscription)
      
      return subscription
    } catch (error) {
      console.error('Erreur lors de l\'abonnement push:', error)
      throw error
    }
  }

  // Se désabonner des notifications push
  async unsubscribe() {
    if (!this.registration) {
      return false
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription()
      if (subscription) {
        await subscription.unsubscribe()
        await this.removeSubscriptionFromServer(subscription)
        console.log('Désabonnement push réussi')
        return true
      }
      return false
    } catch (error) {
      console.error('Erreur lors du désabonnement push:', error)
      throw error
    }
  }

  // Vérifier si l'utilisateur est abonné
  async isSubscribed() {
    if (!this.registration) {
      return false
    }

    try {
      const subscription = await this.registration.pushManager.getSubscription()
      return !!subscription
    } catch (error) {
      console.error('Erreur lors de la vérification de l\'abonnement:', error)
      return false
    }
  }

  // Envoyer l'abonnement au serveur
  async sendSubscriptionToServer(subscription) {
    try {
      const response = await fetch('/api/push-subscriptions/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          subscription: subscription.toJSON(),
          user_agent: navigator.userAgent
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de l\'envoi de l\'abonnement au serveur')
      }

      console.log('Abonnement envoyé au serveur avec succès')
    } catch (error) {
      console.error('Erreur lors de l\'envoi de l\'abonnement:', error)
      throw error
    }
  }

  // Supprimer l'abonnement du serveur
  async removeSubscriptionFromServer(subscription) {
    try {
      const response = await fetch('/api/push-subscriptions/', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          subscription: subscription.toJSON()
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la suppression de l\'abonnement du serveur')
      }

      console.log('Abonnement supprimé du serveur avec succès')
    } catch (error) {
      console.error('Erreur lors de la suppression de l\'abonnement:', error)
      throw error
    }
  }

  // Afficher une notification locale
  showNotification(title, options = {}) {
    if (this.permission !== 'granted') {
      console.warn('Permission non accordée pour les notifications')
      return
    }

    const defaultOptions = {
      icon: '/icons/icon-192x192.png',
      badge: '/icons/badge-72x72.png',
      vibrate: [200, 100, 200],
      tag: 'meetvoice-notification',
      renotify: true,
      requireInteraction: false,
      silent: false
    }

    const notificationOptions = { ...defaultOptions, ...options }

    if (this.registration && this.registration.showNotification) {
      // Utiliser le service worker pour afficher la notification
      return this.registration.showNotification(title, notificationOptions)
    } else {
      // Fallback vers l'API Notification standard
      return new Notification(title, notificationOptions)
    }
  }

  // Afficher une notification de message
  showMessageNotification(message) {
    return this.showNotification('Nouveau message', {
      body: `${message.sender.prenom}: ${message.content}`,
      icon: message.sender.avatar || '/icons/icon-192x192.png',
      tag: `message-${message.conversationId}`,
      data: {
        type: 'message',
        conversationId: message.conversationId,
        senderId: message.sender.id
      },
      actions: [
        {
          action: 'reply',
          title: 'Répondre',
          icon: '/icons/reply.png'
        },
        {
          action: 'view',
          title: 'Voir',
          icon: '/icons/view.png'
        }
      ]
    })
  }

  // Afficher une notification de match
  showMatchNotification(match) {
    return this.showNotification('Nouveau match !', {
      body: `Vous avez un nouveau match avec ${match.user.prenom}`,
      icon: match.user.avatar || '/icons/icon-192x192.png',
      tag: `match-${match.id}`,
      data: {
        type: 'match',
        matchId: match.id,
        userId: match.user.id
      },
      actions: [
        {
          action: 'message',
          title: 'Envoyer un message',
          icon: '/icons/message.png'
        },
        {
          action: 'view',
          title: 'Voir le profil',
          icon: '/icons/profile.png'
        }
      ]
    })
  }

  // Afficher une notification de like
  showLikeNotification(like) {
    return this.showNotification('Nouveau like', {
      body: `${like.user.prenom} a aimé votre profil`,
      icon: like.user.avatar || '/icons/icon-192x192.png',
      tag: `like-${like.user.id}`,
      data: {
        type: 'like',
        userId: like.user.id
      },
      actions: [
        {
          action: 'view',
          title: 'Voir le profil',
          icon: '/icons/profile.png'
        }
      ]
    })
  }

  // Convertir une clé VAPID base64 en Uint8Array
  urlBase64ToUint8Array(base64String) {
    const padding = '='.repeat((4 - base64String.length % 4) % 4)
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/')

    const rawData = window.atob(base64)
    const outputArray = new Uint8Array(rawData.length)

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i)
    }
    return outputArray
  }

  // Gérer les clics sur les notifications
  handleNotificationClick(event) {
    event.notification.close()

    const data = event.notification.data
    const action = event.action

    if (data && data.type) {
      switch (data.type) {
        case 'message':
          if (action === 'reply') {
            // Ouvrir l'interface de réponse rapide
            this.openQuickReply(data.conversationId)
          } else {
            // Ouvrir la conversation
            this.openConversation(data.conversationId)
          }
          break
        case 'match':
          if (action === 'message') {
            // Ouvrir la conversation avec le match
            this.openConversation(data.matchId)
          } else {
            // Ouvrir le profil
            this.openProfile(data.userId)
          }
          break
        case 'like':
          // Ouvrir le profil
          this.openProfile(data.userId)
          break
        default:
          // Ouvrir l'application
          this.openApp()
      }
    } else {
      this.openApp()
    }
  }

  // Ouvrir l'application
  openApp() {
    if (clients && clients.openWindow) {
      return clients.openWindow('/')
    }
    window.open('/', '_blank')
  }

  // Ouvrir une conversation
  openConversation(conversationId) {
    const url = `/messages/${conversationId}`
    if (clients && clients.openWindow) {
      return clients.openWindow(url)
    }
    window.open(url, '_blank')
  }

  // Ouvrir un profil
  openProfile(userId) {
    const url = `/profile/${userId}`
    if (clients && clients.openWindow) {
      return clients.openWindow(url)
    }
    window.open(url, '_blank')
  }

  // Ouvrir l'interface de réponse rapide
  openQuickReply(conversationId) {
    // Cette fonctionnalité nécessiterait une implémentation spécifique
    // Pour l'instant, on redirige vers la conversation
    this.openConversation(conversationId)
  }
}

// Instance singleton
const pushNotificationService = new PushNotificationService()

export default pushNotificationService
