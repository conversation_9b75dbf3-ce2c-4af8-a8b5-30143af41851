<!DOCTYPE html>
<html>
<head>
    <title>Test STT Backend</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { padding: 10px 15px; margin: 5px; }
        pre { background: #f5f5f5; padding: 10px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Backend STT MeetVoice</h1>
    
    <div class="test-section">
        <h3>1. Test Disponibilité Backend</h3>
        <button onclick="testBackendAvailability()">Tester Disponibilité</button>
        <div id="availability-result"></div>
    </div>

    <div class="test-section">
        <h3>2. Test Langues STT</h3>
        <button onclick="testSTTLanguages()">Obtenir Langues</button>
        <div id="languages-result"></div>
    </div>

    <div class="test-section">
        <h3>3. Test Enregistrement + STT</h3>
        <button id="record-btn" onclick="toggleRecording()">🎤 Démarrer Enregistrement</button>
        <div id="recording-status"></div>
        <div id="stt-result"></div>
    </div>

    <div class="test-section">
        <h3>4. Test TTS Sophie</h3>
        <button onclick="testTTS()">🗣️ Test Sophie</button>
        <div id="tts-result"></div>
    </div>

    <script>
        let mediaRecorder = null;
        let audioChunks = [];
        let isRecording = false;

        // Test 1: Disponibilité Backend
        async function testBackendAvailability() {
            const resultDiv = document.getElementById('availability-result');
            resultDiv.innerHTML = '<div class="info">⏳ Test en cours...</div>';

            try {
                const response = await fetch('http://127.0.0.1:8000/api/stt-languages/');
                if (response.ok) {
                    resultDiv.innerHTML = '<div class="success">✅ Backend STT disponible</div>';
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ Backend répond avec erreur: ${response.status}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Backend non accessible: ${error.message}</div>`;
            }
        }

        // Test 2: Langues STT
        async function testSTTLanguages() {
            const resultDiv = document.getElementById('languages-result');
            resultDiv.innerHTML = '<div class="info">⏳ Récupération langues...</div>';

            try {
                const response = await fetch('http://127.0.0.1:8000/api/stt-languages/');
                const data = await response.json();
                
                resultDiv.innerHTML = `
                    <div class="success">✅ Langues disponibles:</div>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Erreur: ${error.message}</div>`;
            }
        }

        // Test 3: Enregistrement + STT
        async function toggleRecording() {
            if (!isRecording) {
                await startRecording();
            } else {
                await stopRecording();
            }
        }

        async function startRecording() {
            const statusDiv = document.getElementById('recording-status');
            const btn = document.getElementById('record-btn');

            try {
                const stream = await navigator.mediaDevices.getUserMedia({ 
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true,
                        sampleRate: 16000,
                        channelCount: 1
                    }
                });

                mediaRecorder = new MediaRecorder(stream);
                audioChunks = [];

                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };

                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                    await sendToSTT(audioBlob);
                    stream.getTracks().forEach(track => track.stop());
                };

                mediaRecorder.start();
                isRecording = true;
                btn.textContent = '🛑 Arrêter Enregistrement';
                statusDiv.innerHTML = '<div class="info">🎤 Enregistrement en cours... Parlez maintenant!</div>';

            } catch (error) {
                statusDiv.innerHTML = `<div class="error">❌ Erreur microphone: ${error.message}</div>`;
            }
        }

        async function stopRecording() {
            const btn = document.getElementById('record-btn');
            const statusDiv = document.getElementById('recording-status');

            if (mediaRecorder && isRecording) {
                mediaRecorder.stop();
                isRecording = false;
                btn.textContent = '🎤 Démarrer Enregistrement';
                statusDiv.innerHTML = '<div class="info">⏳ Traitement audio...</div>';
            }
        }

        async function sendToSTT(audioBlob) {
            const resultDiv = document.getElementById('stt-result');
            const statusDiv = document.getElementById('recording-status');

            try {
                const formData = new FormData();
                formData.append('audio', audioBlob, 'recording.webm');
                formData.append('language', 'fr-FR');

                statusDiv.innerHTML = '<div class="info">📤 Envoi au backend STT...</div>';

                const response = await fetch('http://127.0.0.1:8000/api/speech-to-text/', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const data = await response.json();
                    resultDiv.innerHTML = `
                        <div class="success">✅ Transcription réussie:</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                        <div><strong>Texte:</strong> "${data.text || data.transcription || 'Aucun texte'}"</div>
                    `;
                    statusDiv.innerHTML = '<div class="success">✅ Traitement terminé</div>';
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">❌ Erreur STT (${response.status}): ${errorText}</div>`;
                    statusDiv.innerHTML = '<div class="error">❌ Échec traitement</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Erreur envoi: ${error.message}</div>`;
                statusDiv.innerHTML = '<div class="error">❌ Erreur réseau</div>';
            }
        }

        // Test 4: TTS Sophie
        async function testTTS() {
            const resultDiv = document.getElementById('tts-result');
            resultDiv.innerHTML = '<div class="info">⏳ Test TTS Sophie...</div>';

            try {
                const response = await fetch('http://127.0.0.1:8000/api/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        text: 'Bonjour ! Je suis Sophie, votre assistante vocale MeetVoice. Le système fonctionne parfaitement !',
                        voice_id: 4, // Sophie
                        language: 'fr',
                        speed: 1.2,
                        pitch: 1.0
                    })
                });

                if (response.ok) {
                    const data = await response.json();

                    // Afficher la réponse complète pour debug
                    resultDiv.innerHTML = `
                        <div class="info">📋 Réponse complète du backend:</div>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;

                    // Analyser la réponse
                    if (data.success) {
                        // Tester différents champs possibles pour l'URL audio
                        let audioUrl = data.audio_url || data.file_path || data.url || data.file_url || data.audio_file || data.audio;

                        if (audioUrl && audioUrl !== null) {
                            const audio = new Audio(audioUrl);
                            audio.play();
                            resultDiv.innerHTML += `
                                <div class="success">✅ TTS Sophie fonctionne !</div>
                                <div><strong>URL audio:</strong> ${audioUrl}</div>
                                <div><strong>Voix:</strong> ${data.voice_used}</div>
                            `;
                        } else {
                            resultDiv.innerHTML += `
                                <div class="error">🚨 PROBLÈME BACKEND TTS:</div>
                                <div><strong>Synthèse réussie:</strong> ${data.success}</div>
                                <div><strong>Message:</strong> ${data.message}</div>
                                <div><strong>Voix utilisée:</strong> ${data.voice_used}</div>
                                <div><strong>audio_url:</strong> ${data.audio_url}</div>
                                <div><strong>file_path:</strong> ${data.file_path}</div>
                                <div class="error">❌ Le backend ne génère pas de fichier audio accessible</div>
                                <div><strong>Action requise:</strong> Vérifier la configuration du backend TTS</div>
                            `;
                        }
                    } else {
                        resultDiv.innerHTML += `
                            <div class="error">❌ Échec de la synthèse TTS</div>
                            <div><strong>Message:</strong> ${data.message || 'Erreur inconnue'}</div>
                        `;
                    }
                } else {
                    const errorText = await response.text();
                    resultDiv.innerHTML = `<div class="error">❌ Erreur TTS (${response.status}): ${errorText}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ Erreur TTS: ${error.message}</div>`;
            }
        }

        // Auto-test au chargement
        window.onload = function() {
            console.log('🧪 Page de test STT/TTS chargée');
            testBackendAvailability();
        };
    </script>
</body>
</html>
