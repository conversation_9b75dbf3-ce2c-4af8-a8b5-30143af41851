import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000/api'

export default {
  namespaced: true,
  
  state: {
    events: [],
    userEvents: [],
    currentEvent: null,
    // Catégories selon votre modèle Django
    categories: [
      { value: 'soiree', label: 'Soirée' },
      { value: 'boire_verre', label: 'Boire un verre' },
      { value: 'cinema', label: 'Cinéma' },
      { value: 'sport', label: 'Sport' },
      { value: 'restaurant', label: 'Restaurant' },
      { value: 'concert', label: 'Concert' },
      { value: 'exposition', label: 'Exposition' },
      { value: 'autre', label: 'Autre' }
    ],
    loading: false,
    error: null,
    filters: {
      event_type: '',
      event_date: '',
      location: '',
      max_participants: { min: 1, max: 100 }
    }
  },
  
  mutations: {
    setEvents(state, events) {
      state.events = events
    },
    
    setUserEvents(state, events) {
      state.userEvents = events
    },
    
    setCurrentEvent(state, event) {
      state.currentEvent = event
    },
    
    addEvent(state, event) {
      state.events.unshift(event)
      if (event.isOrganizer) {
        state.userEvents.unshift(event)
      }
    },
    
    updateEvent(state, updatedEvent) {
      // Mettre à jour dans la liste générale
      const index = state.events.findIndex(e => e.id === updatedEvent.id)
      if (index !== -1) {
        state.events.splice(index, 1, updatedEvent)
      }
      
      // Mettre à jour dans les événements utilisateur
      const userIndex = state.userEvents.findIndex(e => e.id === updatedEvent.id)
      if (userIndex !== -1) {
        state.userEvents.splice(userIndex, 1, updatedEvent)
      }
      
      // Mettre à jour l'événement actuel
      if (state.currentEvent && state.currentEvent.id === updatedEvent.id) {
        state.currentEvent = updatedEvent
      }
    },
    
    removeEvent(state, eventId) {
      state.events = state.events.filter(e => e.id !== eventId)
      state.userEvents = state.userEvents.filter(e => e.id !== eventId)
      
      if (state.currentEvent && state.currentEvent.id === eventId) {
        state.currentEvent = null
      }
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    },
    
    setFilters(state, filters) {
      state.filters = { ...state.filters, ...filters }
    }
  },
  
  actions: {
    async loadEvents({ commit, state }) {
      try {
        commit('setLoading', true)
        commit('clearError')

        const params = new URLSearchParams()

        if (state.filters.event_type) {
          params.append('event_type', state.filters.event_type)
        }

        if (state.filters.event_date) {
          params.append('event_date', state.filters.event_date)
        }

        if (state.filters.location) {
          params.append('location', state.filters.location)
        }

        params.append('max_participants__gte', state.filters.max_participants.min)
        params.append('max_participants__lte', state.filters.max_participants.max)

        // Filtrer seulement les événements approuvés et actifs
        params.append('is_approved', 'true')
        params.append('is_active', 'true')

        const response = await axios.get(`${API_BASE_URL}/events/api/events/?${params}`)
        commit('setEvents', response.data.results || response.data)

        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des événements')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async loadUserEvents({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/events/my-events/`)
        commit('setUserEvents', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement de vos événements')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async loadEvent({ commit }, eventId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/events/${eventId}/`)
        commit('setCurrentEvent', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement de l\'événement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async createEvent({ commit }, eventData) {
      try {
        commit('setLoading', true)
        commit('clearError')

        // Adapter les données au format Django
        const djangoEventData = {
          title: eventData.title,
          description: eventData.description,
          event_date: eventData.event_date,
          event_time: eventData.event_time,
          location: eventData.location,
          event_type: eventData.event_type || 'autre',
          max_participants: eventData.max_participants || 10
        }

        const response = await axios.post(`${API_BASE_URL}/events/api/events/`, djangoEventData)
        commit('addEvent', response.data)

        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la création de l\'événement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async updateEvent({ commit }, { eventId, eventData }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.put(`${API_BASE_URL}/events/${eventId}/`, eventData)
        commit('updateEvent', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la modification de l\'événement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async deleteEvent({ commit }, eventId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        await axios.delete(`${API_BASE_URL}/events/${eventId}/`)
        commit('removeEvent', eventId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de la suppression de l\'événement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async joinEvent({ commit }, eventId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/events/${eventId}/join/`)
        commit('updateEvent', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'inscription à l\'événement')
        throw error
      }
    },
    
    async leaveEvent({ commit }, eventId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/events/${eventId}/leave/`)
        commit('updateEvent', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la désinscription de l\'événement')
        throw error
      }
    },
    
    async addComment({ commit }, { eventId, content }) {
      try {
        const response = await axios.post(`${API_BASE_URL}/events/${eventId}/comments/`, {
          content
        })
        
        // Recharger l'événement pour avoir les commentaires mis à jour
        const eventResponse = await axios.get(`${API_BASE_URL}/events/${eventId}/`)
        commit('updateEvent', eventResponse.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'ajout du commentaire')
        throw error
      }
    },
    
    async rateEvent({ commit }, { eventId, rating, comment }) {
      try {
        const response = await axios.post(`${API_BASE_URL}/events/${eventId}/rate/`, {
          rating,
          comment
        })
        
        commit('updateEvent', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'évaluation de l\'événement')
        throw error
      }
    },
    
    updateFilters({ commit }, filters) {
      commit('setFilters', filters)
    },
    
    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    events: state => state.events,
    userEvents: state => state.userEvents,
    currentEvent: state => state.currentEvent,
    categories: state => state.categories,
    loading: state => state.loading,
    error: state => state.error,
    filters: state => state.filters,
    
    upcomingEvents: state => {
      const now = new Date()
      return state.events.filter(event => new Date(event.date) > now)
    },
    
    pastEvents: state => {
      const now = new Date()
      return state.events.filter(event => new Date(event.date) <= now)
    },
    
    organizedEvents: state => {
      return state.userEvents.filter(event => event.isOrganizer)
    },
    
    participatingEvents: state => {
      return state.userEvents.filter(event => !event.isOrganizer && event.isParticipating)
    },
    
    eventsByCategory: state => {
      const grouped = {}
      state.events.forEach(event => {
        if (!grouped[event.category]) {
          grouped[event.category] = []
        }
        grouped[event.category].push(event)
      })
      return grouped
    }
  }
}
