#!/bin/bash

# Script de démonstration MeetVoice WebSocket Server

set -e

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

log_header() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

log_demo() {
    echo -e "${CYAN}🎯 $1${NC}"
}

# Fonction pour nettoyer les processus
cleanup() {
    log_info "Nettoyage des processus..."
    pkill -f simple-server 2>/dev/null || true
    pkill -f meetvoice-websocket 2>/dev/null || true
}

# Gérer l'interruption (Ctrl+C)
trap cleanup EXIT

echo "🎉 Démonstration MeetVoice WebSocket Server"
echo "=========================================="
echo ""

log_header "1. Vérification de l'environnement"
echo ""

# Vérifier Rust
if command -v cargo &> /dev/null; then
    RUST_VERSION=$(cargo --version)
    log_success "Rust installé: $RUST_VERSION"
else
    log_error "Rust n'est pas installé"
    exit 1
fi

# Vérifier curl
if command -v curl &> /dev/null; then
    log_success "curl disponible"
else
    log_warning "curl non disponible, certains tests seront ignorés"
fi

echo ""
log_header "2. Compilation du projet"
echo ""

log_info "Compilation du serveur simple..."
if source $HOME/.cargo/env && cargo build --bin simple-server --quiet; then
    log_success "Compilation réussie"
else
    log_error "Échec de la compilation"
    exit 1
fi

echo ""
log_header "3. Tests unitaires"
echo ""

log_info "Lancement des tests..."
if source $HOME/.cargo/env && cargo test --bin simple-server --quiet; then
    log_success "Tous les tests passent"
else
    log_error "Échec des tests"
    exit 1
fi

echo ""
log_header "4. Démonstration du serveur"
echo ""

log_info "Démarrage du serveur en arrière-plan..."
source $HOME/.cargo/env && cargo run --bin simple-server --quiet &
SERVER_PID=$!

# Attendre que le serveur démarre
log_info "Attente du démarrage du serveur..."
sleep 3

# Vérifier que le serveur répond
if curl -s http://localhost:8080/health > /dev/null 2>&1; then
    log_success "Serveur démarré et accessible sur http://localhost:8080"
else
    log_error "Serveur non accessible"
    kill $SERVER_PID 2>/dev/null || true
    exit 1
fi

echo ""
log_demo "Test de l'endpoint de santé:"
echo ""
HEALTH_RESPONSE=$(curl -s http://localhost:8080/health)
echo "$HEALTH_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$HEALTH_RESPONSE"

echo ""
log_demo "Test de l'endpoint de test:"
echo ""
TEST_RESPONSE=$(curl -s http://localhost:8080/test)
echo "$TEST_RESPONSE" | python3 -m json.tool 2>/dev/null || echo "$TEST_RESPONSE"

echo ""
log_demo "Test de performance (10 requêtes simultanées):"
echo ""

# Test de charge simple
for i in {1..10}; do
    curl -s http://localhost:8080/health > /dev/null &
done
wait

log_success "10 requêtes simultanées traitées avec succès"

echo ""
log_header "5. Fonctionnalités disponibles"
echo ""

echo "✅ Serveur HTTP/REST avec Axum"
echo "✅ Logging avec tracing"
echo "✅ Configuration avec variables d'environnement"
echo "✅ Gestion d'état partagé"
echo "✅ CORS activé"
echo "✅ Tests unitaires"
echo "✅ Compilation optimisée"
echo ""
echo "🚧 En cours de développement:"
echo "   • WebSocket complet avec authentification"
echo "   • Intégration P2P avec libp2p"
echo "   • Bases de données PostgreSQL + MongoDB"
echo "   • Protocoles vocaux/vidéo personnalisés"
echo "   • Tests d'intégration complets"

echo ""
log_header "6. Architecture du projet"
echo ""

echo "📁 Structure du projet:"
echo "├── src/"
echo "│   ├── main.rs              # Serveur complet (en développement)"
echo "│   ├── simple_main.rs       # Serveur de démonstration"
echo "│   ├── config.rs            # Configuration"
echo "│   ├── models/              # Modèles de données"
echo "│   ├── database/            # Gestionnaires de BDD"
echo "│   ├── websocket/           # Gestionnaire WebSocket"
echo "│   └── p2p/                 # Gestionnaire P2P"
echo "├── tests/                   # Tests d'intégration"
echo "├── scripts/                 # Scripts utilitaires"
echo "├── sql/                     # Scripts SQL"
echo "├── docker-compose.yml       # Services Docker"
echo "└── README.md               # Documentation"

echo ""
log_header "7. Commandes utiles"
echo ""

echo "🔧 Développement:"
echo "   cargo run --bin simple-server    # Serveur de démo"
echo "   cargo test                       # Tous les tests"
echo "   cargo build --release           # Build optimisé"
echo ""
echo "🐳 Docker:"
echo "   docker-compose up -d             # Services complets"
echo "   docker-compose logs -f           # Voir les logs"
echo ""
echo "🧪 Tests:"
echo "   ./scripts/test.sh                # Suite de tests complète"
echo "   curl http://localhost:8080/health # Test manuel"

echo ""
log_header "8. Prochaines étapes"
echo ""

echo "1. 🔐 Implémenter l'authentification JWT"
echo "2. 🔌 Compléter le module WebSocket"
echo "3. 🌐 Intégrer libp2p pour le P2P"
echo "4. 🗄️  Connecter les bases de données"
echo "5. 🎤 Développer les protocoles audio/vidéo"
echo "6. 📊 Ajouter les métriques et monitoring"
echo "7. 🚀 Optimiser les performances"
echo "8. 📖 Compléter la documentation"

echo ""
log_success "Démonstration terminée avec succès!"
echo ""
echo "Le serveur continue de fonctionner en arrière-plan."
echo "Vous pouvez tester les endpoints:"
echo "  • http://localhost:8080/health"
echo "  • http://localhost:8080/test"
echo ""
echo "Pour arrêter le serveur: pkill -f simple-server"
echo ""
log_info "Merci d'avoir testé MeetVoice WebSocket Server! 🎉"
