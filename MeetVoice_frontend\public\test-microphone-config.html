<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎤 Test Configuration Microphone</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
        }
        .config-item {
            margin: 15px 0;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .config-item label {
            font-weight: bold;
            display: block;
            margin-bottom: 5px;
        }
        .config-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎤 Test Configuration Microphone</h1>
        <p>Vérification de la configuration du microphone dans l'interview</p>
    </div>

    <div class="test-section">
        <h3>🔍 Diagnostic Configuration</h3>
        <button class="btn" onclick="checkCurrentConfig()">
            🔍 Vérifier Configuration Actuelle
        </button>
        <button class="btn success" onclick="testMicrophoneAccess()">
            🎤 Tester Accès Microphone
        </button>
        <button class="btn danger" onclick="clearAllConfigs()">
            🗑️ Effacer Toutes les Configs
        </button>
        
        <div id="config-results" class="result" style="display: none;"></div>
    </div>

    <div class="test-section">
        <h3>⚙️ Configuration Manuelle</h3>
        <div class="config-item">
            <label for="mic-select">Sélectionner un microphone :</label>
            <select id="mic-select" onchange="saveMicrophoneConfig()">
                <option value="">Microphone par défaut</option>
            </select>
        </div>
        
        <button class="btn" onclick="loadMicrophones()">
            🔄 Charger Microphones
        </button>
        <button class="btn success" onclick="testSelectedMicrophone()">
            🧪 Tester Microphone Sélectionné
        </button>
        
        <div id="manual-results" class="result" style="display: none;"></div>
    </div>

    <script>
        let availableMicrophones = [];

        // Vérifier la configuration actuelle
        async function checkCurrentConfig() {
            const resultDiv = document.getElementById('config-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔍 === DIAGNOSTIC CONFIGURATION MICROPHONE ===\n\n';
            
            // 1. Vérifier localStorage
            appendResult('🔍 1. Configuration localStorage:');
            const localStorageConfig = localStorage.getItem('meetvoice_microphone');
            const localStorageConfigFull = localStorage.getItem('meetvoice_microphone_config');
            appendResult(`   meetvoice_microphone: ${localStorageConfig || 'NON DÉFINI'}`);
            appendResult(`   meetvoice_microphone_config: ${localStorageConfigFull || 'NON DÉFINI'}`);
            
            if (localStorageConfigFull) {
                try {
                    const parsed = JSON.parse(localStorageConfigFull);
                    appendResult(`   Parsed config: ${JSON.stringify(parsed, null, 2)}`);
                } catch (e) {
                    appendResult(`   Erreur parsing: ${e.message}`);
                }
            }
            
            // 2. Vérifier les microphones disponibles
            appendResult('\n🔍 2. Microphones disponibles:');
            try {
                await navigator.mediaDevices.getUserMedia({ audio: true });
                const devices = await navigator.mediaDevices.enumerateDevices();
                const microphones = devices.filter(device => device.kind === 'audioinput');
                
                appendResult(`   Nombre de microphones: ${microphones.length}`);
                microphones.forEach((mic, index) => {
                    appendResult(`   ${index + 1}. ${mic.label || 'Microphone sans nom'} (ID: ${mic.deviceId.substring(0, 20)}...)`);
                });
                
                // 3. Vérifier si le microphone configuré existe
                appendResult('\n🔍 3. Validation configuration:');
                if (localStorageConfig) {
                    const micExists = microphones.find(m => m.deviceId === localStorageConfig);
                    if (micExists) {
                        appendResult(`   ✅ Microphone configuré trouvé: ${micExists.label}`);
                    } else {
                        appendResult(`   ❌ Microphone configuré INTROUVABLE !`);
                        appendResult(`   🔧 Solution: Reconfigurer le microphone`);
                    }
                } else {
                    appendResult(`   ⚠️ Aucun microphone configuré - utilisera le défaut`);
                }
                
            } catch (error) {
                appendResult(`   ❌ Erreur accès microphones: ${error.message}`);
            }
            
            // 4. Simuler le chargement de l'interview
            appendResult('\n🔍 4. Simulation chargement interview:');
            simulateInterviewLoading();
        }

        // Simuler le chargement de l'interview
        function simulateInterviewLoading() {
            const savedMic = localStorage.getItem('meetvoice_microphone');
            const savedMicConfig = localStorage.getItem('meetvoice_microphone_config');
            
            appendResult('   📋 Étapes de chargement interview:');
            appendResult('   1. mounted() appelé');
            appendResult('   2. loadSavedConfigs() appelé');
            appendResult('   3. loadAvailableMicrophones() appelé');
            
            if (savedMic) {
                appendResult(`   4. Microphone sauvegardé trouvé: ${savedMic.substring(0, 20)}...`);
                appendResult(`   5. selectedMicrophoneId = "${savedMic.substring(0, 20)}..."`);
            } else {
                appendResult(`   4. Aucun microphone sauvegardé`);
                appendResult(`   5. selectedMicrophoneId = "" (défaut)`);
            }
            
            appendResult('\n🎯 RÉSULTAT:');
            if (savedMic) {
                appendResult(`   Le microphone "${savedMic.substring(0, 20)}..." devrait être utilisé`);
            } else {
                appendResult(`   Le microphone par défaut devrait être utilisé`);
            }
        }

        // Tester l'accès au microphone
        async function testMicrophoneAccess() {
            const resultDiv = document.getElementById('config-results');
            resultDiv.style.display = 'block';
            appendResult('\n🎤 === TEST ACCÈS MICROPHONE ===\n');
            
            const savedMic = localStorage.getItem('meetvoice_microphone');
            
            if (savedMic) {
                appendResult(`🔍 Test avec microphone configuré: ${savedMic.substring(0, 20)}...`);
                
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            deviceId: { exact: savedMic }
                        }
                    });
                    
                    appendResult('✅ Accès réussi avec microphone configuré !');
                    stream.getTracks().forEach(track => track.stop());
                    
                } catch (error) {
                    appendResult(`❌ Échec accès microphone configuré: ${error.message}`);
                    appendResult('🔧 Essai avec microphone par défaut...');
                    
                    try {
                        const defaultStream = await navigator.mediaDevices.getUserMedia({ audio: true });
                        appendResult('✅ Accès réussi avec microphone par défaut');
                        defaultStream.getTracks().forEach(track => track.stop());
                    } catch (defaultError) {
                        appendResult(`❌ Échec accès microphone par défaut: ${defaultError.message}`);
                    }
                }
            } else {
                appendResult('🔍 Test avec microphone par défaut (aucun configuré)');
                
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    appendResult('✅ Accès réussi avec microphone par défaut !');
                    stream.getTracks().forEach(track => track.stop());
                } catch (error) {
                    appendResult(`❌ Échec accès microphone: ${error.message}`);
                }
            }
        }

        // Charger les microphones disponibles
        async function loadMicrophones() {
            try {
                await navigator.mediaDevices.getUserMedia({ audio: true });
                const devices = await navigator.mediaDevices.enumerateDevices();
                
                availableMicrophones = devices
                    .filter(device => device.kind === 'audioinput')
                    .map(device => ({
                        deviceId: device.deviceId,
                        label: device.label || `Microphone ${device.deviceId.substring(0, 8)}`
                    }));

                const select = document.getElementById('mic-select');
                select.innerHTML = '<option value="">Microphone par défaut</option>';
                
                availableMicrophones.forEach(mic => {
                    const option = document.createElement('option');
                    option.value = mic.deviceId;
                    option.textContent = mic.label;
                    select.appendChild(option);
                });

                // Sélectionner le microphone sauvegardé
                const savedMic = localStorage.getItem('meetvoice_microphone');
                if (savedMic) {
                    select.value = savedMic;
                }

                console.log('🎤 Microphones chargés:', availableMicrophones);
                
            } catch (error) {
                console.error('❌ Erreur chargement microphones:', error);
            }
        }

        // Sauvegarder la configuration du microphone
        function saveMicrophoneConfig() {
            const select = document.getElementById('mic-select');
            const selectedMicId = select.value;
            
            // Sauvegarder dans localStorage (comme l'interview)
            if (selectedMicId) {
                localStorage.setItem('meetvoice_microphone', selectedMicId);
                
                // Sauvegarder aussi dans le format store
                const config = { selectedMicrophoneId: selectedMicId };
                localStorage.setItem('meetvoice_microphone_config', JSON.stringify(config));
                
                console.log('💾 Microphone sauvegardé:', selectedMicId);
                
                const resultDiv = document.getElementById('manual-results');
                resultDiv.style.display = 'block';
                resultDiv.textContent = `✅ Microphone sauvegardé: ${select.options[select.selectedIndex].text}\nID: ${selectedMicId}`;
            } else {
                localStorage.removeItem('meetvoice_microphone');
                localStorage.removeItem('meetvoice_microphone_config');
                
                console.log('🗑️ Configuration microphone effacée');
                
                const resultDiv = document.getElementById('manual-results');
                resultDiv.style.display = 'block';
                resultDiv.textContent = '🗑️ Configuration microphone effacée - utilisera le défaut';
            }
        }

        // Tester le microphone sélectionné
        async function testSelectedMicrophone() {
            const select = document.getElementById('mic-select');
            const selectedMicId = select.value;
            
            const resultDiv = document.getElementById('manual-results');
            resultDiv.style.display = 'block';
            
            if (selectedMicId) {
                resultDiv.textContent = `🧪 Test du microphone: ${select.options[select.selectedIndex].text}...\n`;
                
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            deviceId: { exact: selectedMicId }
                        }
                    });
                    
                    resultDiv.textContent += '✅ Test réussi ! Microphone fonctionnel.\n';
                    stream.getTracks().forEach(track => track.stop());
                    
                } catch (error) {
                    resultDiv.textContent += `❌ Test échoué: ${error.message}\n`;
                }
            } else {
                resultDiv.textContent = '🧪 Test du microphone par défaut...\n';
                
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                    resultDiv.textContent += '✅ Test réussi ! Microphone par défaut fonctionnel.\n';
                    stream.getTracks().forEach(track => track.stop());
                } catch (error) {
                    resultDiv.textContent += `❌ Test échoué: ${error.message}\n`;
                }
            }
        }

        // Effacer toutes les configurations
        function clearAllConfigs() {
            localStorage.removeItem('meetvoice_microphone');
            localStorage.removeItem('meetvoice_microphone_config');
            localStorage.removeItem('meetvoice_voice_config');
            
            const resultDiv = document.getElementById('config-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🗑️ Toutes les configurations ont été effacées.\n\nRedémarrez l\'interview pour voir l\'effet.';
            
            // Réinitialiser le select
            const select = document.getElementById('mic-select');
            select.value = '';
        }

        // Fonction utilitaire
        function appendResult(text) {
            const resultDiv = document.getElementById('config-results');
            resultDiv.textContent += text + '\n';
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        // Charger les microphones au démarrage
        window.addEventListener('load', () => {
            loadMicrophones();
        });

        console.log('🎤 Test configuration microphone chargé !');
    </script>
</body>
</html>
