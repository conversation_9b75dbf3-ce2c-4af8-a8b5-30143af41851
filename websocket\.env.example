# Configuration du serveur
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Configuration PostgreSQL (pour les données utilisateurs)
POSTGRES_URL=postgresql://postgres:password@localhost:5432/meetvoice

# Configuration MongoDB (pour les données temps réel)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=meetvoice

# Configuration P2P
P2P_PORT=9090
P2P_ENABLE_MDNS=true
P2P_ENABLE_RELAY=true

# Logging
RUST_LOG=meetvoice_websocket=debug,libp2p=info
