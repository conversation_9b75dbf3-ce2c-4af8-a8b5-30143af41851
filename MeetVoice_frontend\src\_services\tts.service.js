/** @format */

/**
 * Service TTS (Text-To-Speech) pour MeetVoice
 * Utilise l'API TTS backend pour la synthèse vocale
 */

import Axios from './caller.service.js';

class TTSService {
  constructor() {
    this.baseUrl = '/api'; // Utilise le baseURL d'Axios
    this.isPlaying = false;
    this.currentAudio = null;
    this.availableVoices = [];
    this.defaultSettings = {
      speed: 1.2,
      pitch: 1.0,
      volume: 1.0
    };
  }

  /**
   * Obtenir les voix disponibles depuis l'API
   * @returns {Promise<Array>} Liste des voix disponibles
   */
  async getAvailableVoices() {
    try {
      const response = await Axios.get(`${this.baseUrl}/voices/`);

      const voices = response.data;
      this.availableVoices = voices;
      
      // Ajouter des voix par défaut si l'API ne répond pas
      if (!voices || voices.length === 0) {
        this.availableVoices = this.getDefaultVoices();
      }
      
      return this.availableVoices;
    } catch (error) {
      console.error('❌ Erreur chargement voix TTS:', error);
      // Fallback sur des voix par défaut
      this.availableVoices = this.getDefaultVoices();
      return this.availableVoices;
    }
  }

  /**
   * Voix par défaut en cas d'erreur API
   * @returns {Array} Liste des voix par défaut
   */
  getDefaultVoices() {
    return [
      {
        id: 'julie',
        name: 'Julie',
        language: 'fr',
        gender: 'female',
        description: 'Voix française naturelle et claire (recommandée)'
      },
    
    ];
  }

  /**
   * Synthétiser du texte en audio
   * @param {string} text - Texte à synthétiser
   * @param {Object} options - Options de synthèse
   * @returns {Promise<void>}
   */
  async speak(text, options = {}) {
    try {
      console.log('🔊 TTS.SPEAK APPELÉ avec texte:', text);
      console.log('🔊 TTS.SPEAK options reçues:', options);
      console.log('🔊 TTS.SPEAK defaultSettings:', this.defaultSettings);
      console.log('🔊 TTS.SPEAK état actuel isPlaying:', this.isPlaying);

      // Arrêter la lecture en cours
      this.stop();

      // Réinitialiser l'état
      this.isPlaying = false;

      const settings = {
        ...this.defaultSettings,
        ...options
      };

      console.log('🔊 TTS.SPEAK settings finaux:', settings);

      // Préparer les données pour l'API
      const requestData = {
        text: text,
        voice_id: settings.voiceId || 'julie',
        language: settings.language || 'fr',
        speed: settings.speed || 1.0,
        pitch: settings.pitch || 1.0,
        volume: settings.volume || 1.0
      };

      console.log('🎤 TTS Service - Données envoyées à l\'API:', requestData);
      console.log('🎤 TTS Service - Settings reçus:', settings);

      console.log('🎵 Synthèse TTS:', requestData);

      // Appel à l'API TTS
      const response = await Axios.post(`${this.baseUrl}/synthesize/`, requestData, {
        responseType: 'json' // Forcer JSON d'abord
      });

      console.log('🎵 Réponse TTS complète:', response.data);
      console.log('🎵 Headers de réponse:', response.headers);

      let audioUrl;

      // Vérifier si la synthèse a réussi
      if (!response.data.success) {
        throw new Error(`Erreur backend TTS: ${response.data.message || 'Synthèse échouée'}`);
      }

      // Tester différents champs possibles pour l'URL audio
      audioUrl = response.data.audio_url ||
                 response.data.file_path ||
                 response.data.url ||
                 response.data.file_url ||
                 response.data.audio_file ||
                 response.data.audio ||
                 response.data.mp3_url ||
                 response.data.wav_url;

      // Si l'URL est null ou vide, essayer de récupérer directement l'audio
      if (!audioUrl || audioUrl === null) {
        console.warn('⚠️ Backend TTS ne retourne pas d\'URL audio. Tentative de récupération directe...');
        console.warn('🔍 Réponse backend:', response.data);

        try {
          // Essayer de récupérer l'audio directement en blob
          const blobResponse = await Axios.post(`${this.baseUrl}/synthesize/`, requestData, {
            responseType: 'blob'
          });

          if (blobResponse.data && blobResponse.data.size > 0) {
            audioUrl = URL.createObjectURL(blobResponse.data);
            console.log('🎵 Audio blob créé:', audioUrl);
          } else {
            throw new Error('Backend ne génère pas de fichier audio');
          }
        } catch (blobError) {
          console.error('❌ Erreur récupération blob:', blobError);

          // Erreur détaillée pour le debug
          const errorMsg = `
🚨 PROBLÈME BACKEND TTS DÉTECTÉ:
- Synthèse réussie: ${response.data.success}
- Message: ${response.data.message}
- Voix utilisée: ${response.data.voice_used}
- audio_url: ${response.data.audio_url}
- file_path: ${response.data.file_path}

❌ Le backend ne génère pas de fichier audio accessible.
Vérifiez la configuration du backend TTS.
          `;

          console.error(errorMsg);
          throw new Error('Backend TTS configuré incorrectement - aucun fichier audio généré');
        }
      } else {
        console.log('🎵 URL audio trouvée:', audioUrl);
      }

      // Créer et jouer l'audio
      this.currentAudio = new Audio(audioUrl);
      this.currentAudio.volume = settings.volume || 1.0;

      // Appliquer les modifications de vitesse et tonalité côté frontend
      if (settings.speed !== 1.0) {
        this.currentAudio.playbackRate = settings.speed;
        console.log('🎵 Vitesse appliquée:', settings.speed);
      }

      // Gérer les événements audio
      this.currentAudio.onplay = () => {
        this.isPlaying = true;
        console.log('🔊 Lecture TTS démarrée avec succès');
        if (options.onStart) options.onStart();
      };

      this.currentAudio.onended = () => {
        this.isPlaying = false;
        // Révoquer l'URL seulement si c'est un blob créé localement
        if (audioUrl && audioUrl.startsWith('blob:')) {
          URL.revokeObjectURL(audioUrl);
          console.log('🗑️ Blob URL révoqué');
        }
        console.log('🔇 Lecture TTS terminée');
        if (options.onEnd) options.onEnd();
      };

      this.currentAudio.onerror = (error) => {
        this.isPlaying = false;
        // Révoquer l'URL seulement si c'est un blob créé localement
        if (audioUrl && audioUrl.startsWith('blob:')) {
          URL.revokeObjectURL(audioUrl);
          console.log('🗑️ Blob URL révoqué après erreur');
        }
        console.error('❌ Erreur lecture audio:', error);
        if (options.onError) options.onError(error);
      };

      // Démarrer la lecture
      console.log('🎵 Démarrage lecture audio...');
      await this.currentAudio.play();

    } catch (error) {
      console.error('❌ Erreur synthèse TTS:', error);

      // IMPORTANT: Réinitialiser l'état pour éviter le blocage
      this.isPlaying = false;
      this.currentAudio = null;

      // Déterminer le type d'erreur pour un message plus précis
      let errorMessage = 'API TTS indisponible';

      if (error.message.includes('Failed to fetch') || error.message.includes('NetworkError')) {
        errorMessage = 'Serveur TTS MeetVoice non accessible. Vérifiez que le backend Django est démarré sur http://localhost:8000';
      } else if (error.message.includes('404')) {
        errorMessage = 'Endpoint TTS non trouvé. Vérifiez la configuration du serveur backend.';
      } else if (error.message.includes('500')) {
        errorMessage = 'Erreur interne du serveur TTS. Vérifiez les logs du backend Django.';
      } else if (error.message.includes('NotSupportedError')) {
        errorMessage = 'Format audio non supporté par le navigateur.';
      }

      // Appeler le callback d'erreur si fourni
      if (options.onError) {
        options.onError(new Error(`${errorMessage}: ${error.message}`));
      }

      console.warn('⚠️ TTS Service réinitialisé après erreur - prêt pour la prochaine synthèse');
      console.warn('⚠️ Erreur détaillée:', errorMessage);

      // NE PAS throw l'erreur pour éviter de bloquer l'interview
      // throw new Error(`${errorMessage}: ${error.message}`);
    }
  }



  /**
   * Arrêter la synthèse en cours (API TTS UNIQUEMENT)
   */
  stop() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio.currentTime = 0;
      this.currentAudio = null;
    }

    this.isPlaying = false;
    console.log('⏹️ Synthèse TTS API arrêtée');
  }

  /**
   * Réinitialiser complètement le service TTS
   */
  reset() {
    console.log('🔄 Réinitialisation complète du service TTS');
    this.stop();
    this.isPlaying = false;
    this.currentAudio = null;
    console.log('✅ Service TTS réinitialisé');
  }

  /**
   * Vérifier si une synthèse est en cours
   * @returns {boolean}
   */
  isSpeaking() {
    return this.isPlaying;
  }

  /**
   * Obtenir les paramètres par défaut
   * @returns {Object}
   */
  getDefaultSettings() {
    return { ...this.defaultSettings };
  }

  /**
   * Tester une voix avec un texte d'exemple
   * @param {string} voiceId - ID de la voix à tester
   * @param {Object} settings - Paramètres de test
   */
  async testVoice(voiceId, settings = {}) {
    const voice = this.availableVoices.find(v => v.id === voiceId);
    const testText = voice 
      ? `Bonjour ! Je suis ${voice.name}. Voici un test de ma voix.`
      : 'Bonjour ! Voici un test de synthèse vocale.';

    await this.speak(testText, {
      voiceId: voiceId,
      ...settings
    });
  }

  /**
   * Obtenir les informations d'une voix
   * @param {string} voiceId - ID de la voix
   * @returns {Object|null}
   */
  getVoiceInfo(voiceId) {
    return this.availableVoices.find(voice => voice.id === voiceId) || null;
  }
}

// Instance singleton
const ttsService = new TTSService();

export default ttsService;
export { ttsService };
