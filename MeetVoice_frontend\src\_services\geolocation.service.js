/** @format */

/**
 * Service de géolocalisation utilisant l'API Nominatim OpenStreetMap
 * Fournit des fonctionnalités de géolocalisation, recherche d'adresses et validation
 */

const NOMINATIM_BASE_URL = 'https://nominatim.openstreetmap.org';

/**
 * Obtenir la position actuelle de l'utilisateur
 * @param {Object} options - Options de géolocalisation
 * @returns {Promise<Object>} Position avec latitude et longitude
 */
const getCurrentPosition = (options = {}) => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('La géolocalisation n\'est pas supportée par ce navigateur'));
      return;
    }

    const defaultOptions = {
      enableHighAccuracy: true,
      timeout: 10000,
      maximumAge: 300000, // 5 minutes
      ...options
    };

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          latitude: position.coords.latitude,
          longitude: position.coords.longitude,
          accuracy: position.coords.accuracy,
          timestamp: position.timestamp
        });
      },
      (error) => {
        let errorMessage = 'Erreur de géolocalisation';
        
        switch (error.code) {
          case error.PERMISSION_DENIED:
            errorMessage = 'L\'accès à la géolocalisation a été refusé';
            break;
          case error.POSITION_UNAVAILABLE:
            errorMessage = 'Les informations de localisation ne sont pas disponibles';
            break;
          case error.TIMEOUT:
            errorMessage = 'La demande de géolocalisation a expiré';
            break;
        }
        
        reject(new Error(errorMessage));
      },
      defaultOptions
    );
  });
};

/**
 * Convertir des coordonnées en adresse (géocodage inverse)
 * @param {number} latitude - Latitude
 * @param {number} longitude - Longitude
 * @returns {Promise<Object>} Informations d'adresse
 */
const reverseGeocode = async (latitude, longitude) => {
  try {
    const response = await fetch(
      `${NOMINATIM_BASE_URL}/reverse?format=json&lat=${latitude}&lon=${longitude}&addressdetails=1&accept-language=fr`
    );
    
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération de l\'adresse');
    }
    
    const data = await response.json();
    
    if (!data || data.error) {
      throw new Error('Aucune adresse trouvée pour ces coordonnées');
    }
    
    return {
      display_name: data.display_name,
      address: {
        city: data.address?.city || data.address?.town || data.address?.village || '',
        postcode: data.address?.postcode || '',
        country: data.address?.country || '',
        state: data.address?.state || '',
        road: data.address?.road || '',
        house_number: data.address?.house_number || ''
      },
      coordinates: {
        latitude: parseFloat(data.lat),
        longitude: parseFloat(data.lon)
      },
      raw: data
    };
  } catch (error) {
    console.error('Erreur lors du géocodage inverse:', error);
    throw error;
  }
};

/**
 * Rechercher des adresses par texte (géocodage)
 * @param {string} query - Texte de recherche
 * @param {Object} options - Options de recherche
 * @returns {Promise<Array>} Liste des adresses trouvées
 */
const searchAddresses = async (query, options = {}) => {
  try {
    if (!query || query.trim().length < 3) {
      throw new Error('La recherche doit contenir au moins 3 caractères');
    }
    
    const defaultOptions = {
      limit: 10,
      countrycodes: 'fr', // Limiter à la France par défaut
      addressdetails: 1,
      ...options
    };
    
    const params = new URLSearchParams({
      format: 'json',
      q: query.trim(),
      limit: defaultOptions.limit,
      addressdetails: defaultOptions.addressdetails,
      'accept-language': 'fr'
    });
    
    if (defaultOptions.countrycodes) {
      params.append('countrycodes', defaultOptions.countrycodes);
    }
    
    const response = await fetch(`${NOMINATIM_BASE_URL}/search?${params}`);
    
    if (!response.ok) {
      throw new Error('Erreur lors de la recherche d\'adresses');
    }
    
    const data = await response.json();
    
    return data.map(item => ({
      display_name: item.display_name,
      address: {
        city: item.address?.city || item.address?.town || item.address?.village || '',
        postcode: item.address?.postcode || '',
        country: item.address?.country || '',
        state: item.address?.state || '',
        road: item.address?.road || '',
        house_number: item.address?.house_number || ''
      },
      coordinates: {
        latitude: parseFloat(item.lat),
        longitude: parseFloat(item.lon)
      },
      importance: item.importance,
      type: item.type,
      raw: item
    }));
  } catch (error) {
    console.error('Erreur lors de la recherche d\'adresses:', error);
    throw error;
  }
};

/**
 * Calculer la distance entre deux points (formule de Haversine)
 * @param {number} lat1 - Latitude du premier point
 * @param {number} lon1 - Longitude du premier point
 * @param {number} lat2 - Latitude du second point
 * @param {number} lon2 - Longitude du second point
 * @returns {number} Distance en kilomètres
 */
const calculateDistance = (lat1, lon1, lat2, lon2) => {
  const R = 6371; // Rayon de la Terre en kilomètres
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  const distance = R * c;
  
  return Math.round(distance * 100) / 100; // Arrondir à 2 décimales
};

/**
 * Vérifier si l'utilisateur est dans une zone géographique donnée
 * @param {Object} userPosition - Position de l'utilisateur {latitude, longitude}
 * @param {Object} targetPosition - Position cible {latitude, longitude}
 * @param {number} radiusKm - Rayon en kilomètres
 * @returns {boolean} True si l'utilisateur est dans la zone
 */
const isWithinRadius = (userPosition, targetPosition, radiusKm) => {
  const distance = calculateDistance(
    userPosition.latitude,
    userPosition.longitude,
    targetPosition.latitude,
    targetPosition.longitude
  );
  
  return distance <= radiusKm;
};

/**
 * Obtenir l'adresse IP approximative de l'utilisateur (pour validation)
 * @returns {Promise<Object>} Informations de géolocalisation IP
 */
const getIPLocation = async () => {
  try {
    // Utiliser un service gratuit de géolocalisation IP
    const response = await fetch('https://ipapi.co/json/');
    
    if (!response.ok) {
      throw new Error('Erreur lors de la récupération de la géolocalisation IP');
    }
    
    const data = await response.json();
    
    return {
      city: data.city,
      region: data.region,
      country: data.country_name,
      coordinates: {
        latitude: data.latitude,
        longitude: data.longitude
      },
      timezone: data.timezone,
      raw: data
    };
  } catch (error) {
    console.error('Erreur lors de la géolocalisation IP:', error);
    throw error;
  }
};

/**
 * Valider la cohérence entre la position déclarée et la géolocalisation IP
 * @param {Object} declaredPosition - Position déclarée par l'utilisateur
 * @param {number} toleranceKm - Tolérance en kilomètres (défaut: 100km)
 * @returns {Promise<Object>} Résultat de la validation
 */
const validateLocationConsistency = async (declaredPosition, toleranceKm = 100) => {
  try {
    const ipLocation = await getIPLocation();
    
    const distance = calculateDistance(
      declaredPosition.latitude,
      declaredPosition.longitude,
      ipLocation.coordinates.latitude,
      ipLocation.coordinates.longitude
    );
    
    const isConsistent = distance <= toleranceKm;
    
    return {
      isConsistent,
      distance,
      tolerance: toleranceKm,
      ipLocation,
      declaredPosition,
      warning: !isConsistent ? 
        `La position déclarée semble éloignée de votre localisation IP (${distance}km)` : 
        null
    };
  } catch (error) {
    console.error('Erreur lors de la validation de localisation:', error);
    // En cas d'erreur, on considère que c'est valide pour ne pas bloquer l'utilisateur
    return {
      isConsistent: true,
      error: error.message,
      warning: 'Impossible de valider la localisation'
    };
  }
};

export const geolocationService = {
  getCurrentPosition,
  reverseGeocode,
  searchAddresses,
  calculateDistance,
  isWithinRadius,
  getIPLocation,
  validateLocationConsistency
};
