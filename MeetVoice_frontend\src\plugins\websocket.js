import websocketService from '@/services/websocket'

export default {
  install(app, options = {}) {
    // Initialiser le service WebSocket avec le store
    if (options.store) {
      websocketService.init(options.store)
    }

    // Ajouter le service WebSocket aux propriétés globales de Vue
    app.config.globalProperties.$ws = websocketService
    
    // Fournir le service via provide/inject
    app.provide('websocket', websocketService)

    // Mixin global pour faciliter l'utilisation du WebSocket
    app.mixin({
      beforeUnmount() {
        // Nettoyer les listeners WebSocket quand le composant est détruit
        if (this._wsListeners) {
          this._wsListeners.forEach(({ event, callback }) => {
            websocketService.off(event, callback)
          })
        }
      },

      methods: {
        // Méthode helper pour écouter les événements WebSocket
        $wsOn(event, callback) {
          if (!this._wsListeners) {
            this._wsListeners = []
          }
          
          websocketService.on(event, callback)
          this._wsListeners.push({ event, callback })
        },

        // Méthode helper pour arrêter d'écouter les événements WebSocket
        $wsOff(event, callback) {
          websocketService.off(event, callback)
          
          if (this._wsListeners) {
            this._wsListeners = this._wsListeners.filter(
              listener => !(listener.event === event && listener.callback === callback)
            )
          }
        },

        // Méthode helper pour envoyer des messages WebSocket
        $wsSend(type, payload) {
          websocketService.send(type, payload)
        },

        // Méthodes spécifiques pour les actions courantes
        $wsSendMessage(conversationId, content, isVoiceMessage = false) {
          websocketService.sendMessage(conversationId, content, isVoiceMessage)
        },

        $wsSendTyping(conversationId, isTyping) {
          websocketService.sendTyping(conversationId, isTyping)
        },

        $wsSendLike(profileId) {
          websocketService.sendLike(profileId)
        },

        $wsUpdateStatus(isOnline) {
          websocketService.updateStatus(isOnline)
        }
      }
    })

    // Auto-connexion si l'utilisateur est authentifié
    if (options.store) {
      const store = options.store
      
      // Surveiller les changements d'authentification
      store.watch(
        (state, getters) => getters['auth/isAuthenticated'],
        (isAuthenticated) => {
          if (isAuthenticated) {
            const token = store.getters['auth/token']
            if (token) {
              websocketService.connect(token).catch(error => {
                console.error('Erreur de connexion WebSocket:', error)
              })
            }
          } else {
            websocketService.disconnect()
          }
        },
        { immediate: true }
      )

      // Mettre à jour le statut en ligne quand la visibilité de la page change
      document.addEventListener('visibilitychange', () => {
        if (store.getters['auth/isAuthenticated']) {
          const isOnline = !document.hidden
          websocketService.updateStatus(isOnline)
        }
      })

      // Mettre à jour le statut quand la fenêtre se ferme
      window.addEventListener('beforeunload', () => {
        if (store.getters['auth/isAuthenticated']) {
          websocketService.updateStatus(false)
        }
      })
    }
  }
}

// Composable pour utiliser le WebSocket dans la Composition API
export function useWebSocket() {
  const websocket = websocketService

  const on = (event, callback) => {
    websocket.on(event, callback)
  }

  const off = (event, callback) => {
    websocket.off(event, callback)
  }

  const send = (type, payload) => {
    websocket.send(type, payload)
  }

  const sendMessage = (conversationId, content, isVoiceMessage = false) => {
    websocket.sendMessage(conversationId, content, isVoiceMessage)
  }

  const sendTyping = (conversationId, isTyping) => {
    websocket.sendTyping(conversationId, isTyping)
  }

  const sendLike = (profileId) => {
    websocket.sendLike(profileId)
  }

  const updateStatus = (isOnline) => {
    websocket.updateStatus(isOnline)
  }

  const isConnected = () => {
    return websocket.isConnectedToWebSocket()
  }

  return {
    on,
    off,
    send,
    sendMessage,
    sendTyping,
    sendLike,
    updateStatus,
    isConnected
  }
}
