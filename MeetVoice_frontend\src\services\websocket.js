class WebSocketService {
  constructor() {
    this.socket = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
    this.isConnected = false
    this.messageQueue = []
    this.eventListeners = new Map()
    this.store = null

    // Gestion des appels vidéo/audio
    this.localStream = null
    this.remoteStreams = new Map()
    this.peerConnections = new Map()
    this.isInCall = false
    this.currentCallId = null
    this.mediaConstraints = {
      video: true,
      audio: true
    }

    // Configuration ICE servers
    this.iceServers = [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' }
    ]

    // Ping/Pong pour maintenir la connexion
    this.pingInterval = null
    this.pongTimeout = null
    this.pingIntervalTime = 30000 // 30 secondes
    this.pongTimeoutTime = 5000 // 5 secondes
  }

  // Initialiser le service avec le store Vuex
  init(store) {
    this.store = store
  }

  // Se connecter au WebSocket
  connect(token) {
    if (this.socket && this.socket.readyState === WebSocket.OPEN) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        const wsUrl = process.env.VUE_APP_WS_URL || 'ws://127.0.0.1:8000/ws'
        const url = `${wsUrl}/chat/?token=${token}`
        
        this.socket = new WebSocket(url)

        this.socket.onopen = () => {
          console.log('WebSocket connecté')
          this.isConnected = true
          this.reconnectAttempts = 0
          
          // Envoyer les messages en attente
          this.flushMessageQueue()
          
          resolve()
        }

        this.socket.onmessage = (event) => {
          this.handleMessage(event)
        }

        this.socket.onclose = (event) => {
          console.log('WebSocket fermé:', event.code, event.reason)
          this.isConnected = false
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect(token)
          }
        }

        this.socket.onerror = (error) => {
          console.error('Erreur WebSocket:', error)
          this.isConnected = false
          reject(error)
        }

      } catch (error) {
        console.error('Erreur lors de la connexion WebSocket:', error)
        reject(error)
      }
    })
  }

  // Reconnecter automatiquement
  reconnect(token) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Nombre maximum de tentatives de reconnexion atteint')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)

    console.log(`Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`)

    setTimeout(() => {
      this.connect(token).catch(error => {
        console.error('Échec de la reconnexion:', error)
      })
    }, delay)
  }

  // Gérer les messages reçus
  handleMessage(event) {
    try {
      const data = JSON.parse(event.data)
      console.log('Message WebSocket reçu:', data)

      switch (data.type) {
        case 'message':
          this.handleNewMessage(data.payload)
          break
        case 'match':
          this.handleNewMatch(data.payload)
          break
        case 'like':
          this.handleNewLike(data.payload)
          break
        case 'notification':
          this.handleNotification(data.payload)
          break
        case 'user_status':
          this.handleUserStatusUpdate(data.payload)
          break
        case 'typing':
          this.handleTypingIndicator(data.payload)
          break
        case 'voice_call':
          this.handleVoiceCall(data.payload)
          break
        default:
          console.warn('Type de message WebSocket non géré:', data.type)
      }

      // Émettre l'événement pour les listeners personnalisés
      this.emit(data.type, data.payload)

    } catch (error) {
      console.error('Erreur lors du traitement du message WebSocket:', error)
    }
  }

  // Gérer les nouveaux messages
  handleNewMessage(message) {
    if (this.store) {
      // Mettre à jour la conversation dans le store
      this.store.dispatch('profiles/updateConversation', message.conversation)
      
      // Ajouter une notification
      this.store.dispatch('notifications/createMessageNotification', message)
      
      // Jouer un son de notification si l'utilisateur n'est pas sur la page de conversation
      if (window.location.pathname !== `/messages/${message.conversationId}`) {
        this.playNotificationSound()
      }
    }
  }

  // Gérer les nouveaux matchs
  handleNewMatch(match) {
    if (this.store) {
      this.store.dispatch('profiles/addMatch', match)
      this.store.dispatch('notifications/createMatchNotification', match)
      this.playNotificationSound('match')
    }
  }

  // Gérer les nouveaux likes
  handleNewLike(like) {
    if (this.store) {
      this.store.dispatch('notifications/createLikeNotification', like)
      this.playNotificationSound('like')
    }
  }

  // Gérer les notifications générales
  handleNotification(notification) {
    if (this.store) {
      this.store.dispatch('notifications/addLocalNotification', notification)
      this.playNotificationSound()
    }
  }

  // Gérer les mises à jour de statut utilisateur
  handleUserStatusUpdate(statusUpdate) {
    if (this.store) {
      // Mettre à jour le statut en ligne des utilisateurs
      this.store.commit('profiles/updateMember', {
        id: statusUpdate.userId,
        isOnline: statusUpdate.isOnline,
        lastConnection: statusUpdate.lastConnection
      })
    }
  }

  // Gérer les indicateurs de frappe
  handleTypingIndicator(typing) {
    // Émettre l'événement pour les composants qui écoutent
    this.emit('typing', typing)
  }

  // Gérer les appels vocaux
  handleVoiceCall(callData) {
    if (this.store) {
      // Ajouter une notification d'appel
      this.store.dispatch('notifications/addLocalNotification', {
        type: 'voice_call',
        title: 'Appel vocal entrant',
        message: `${callData.caller.prenom} vous appelle`,
        data: callData
      })
      
      this.playNotificationSound('call')
    }
  }

  // Envoyer un message
  send(type, payload) {
    const message = {
      type,
      payload,
      timestamp: new Date().toISOString()
    }

    if (this.isConnected && this.socket.readyState === WebSocket.OPEN) {
      this.socket.send(JSON.stringify(message))
    } else {
      // Ajouter à la queue si pas connecté
      this.messageQueue.push(message)
    }
  }

  // Envoyer un message de chat
  sendMessage(conversationId, content, isVoiceMessage = false) {
    this.send('message', {
      conversationId,
      content,
      isVoiceMessage
    })
  }

  // Envoyer un indicateur de frappe
  sendTyping(conversationId, isTyping) {
    this.send('typing', {
      conversationId,
      isTyping
    })
  }

  // Envoyer un like
  sendLike(profileId) {
    this.send('like', {
      profileId
    })
  }

  // Envoyer une mise à jour de statut
  updateStatus(isOnline) {
    this.send('user_status', {
      isOnline
    })
  }

  // Vider la queue de messages
  flushMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.socket.send(JSON.stringify(message))
    }
  }

  // Ajouter un listener d'événement
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  // Supprimer un listener d'événement
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // Émettre un événement
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Erreur dans le listener d\'événement:', error)
        }
      })
    }
  }

  // Jouer un son de notification
  playNotificationSound(type = 'default') {
    try {
      const sounds = {
        default: '/sounds/notification.mp3',
        match: '/sounds/match.mp3',
        like: '/sounds/like.mp3',
        call: '/sounds/call.mp3'
      }

      const audio = new Audio(sounds[type] || sounds.default)
      audio.volume = 0.5
      audio.play().catch(error => {
        console.warn('Impossible de jouer le son de notification:', error)
      })
    } catch (error) {
      console.warn('Erreur lors de la lecture du son:', error)
    }
  }

  // Fermer la connexion
  disconnect() {
    if (this.socket) {
      this.socket.close(1000, 'Déconnexion volontaire')
      this.socket = null
      this.isConnected = false
    }
  }

  // Vérifier l'état de la connexion
  isConnectedToWebSocket() {
    return this.isConnected && this.socket && this.socket.readyState === WebSocket.OPEN
  }
}

// Instance singleton
const websocketService = new WebSocketService()

export default websocketService
