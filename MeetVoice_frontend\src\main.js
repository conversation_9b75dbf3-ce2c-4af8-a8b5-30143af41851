/** @format */

import { createApp } from "vue";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import WebPDetector from "./utils/webp-detection.js";

// Import des plugins et services
import WebSocketPlugin from "./plugins/websocket.js";
import pushNotificationService from "./services/push-notifications.js";
import cacheService from "./services/cache.js";

// Import des composants globaux
import LazyImage from "./components/common/LazyImage.vue";
import VirtualList from "./components/common/VirtualList.vue";

import "bootstrap/dist/css/bootstrap.css";
import "bootstrap/dist/js/bootstrap.js";

// Enregistrer le Service Worker
if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then(registration => {
        console.log('✅ Service Worker enregistré:', registration.scope);

        // Nettoyage périodique du cache
        setInterval(() => {
          registration.active?.postMessage({ type: 'CLEAN_CACHE' });
        }, 30 * 60 * 1000); // Toutes les 30 minutes
      })
      .catch(error => {
        console.error('❌ Erreur Service Worker:', error);
      });
  });
}

// Initialiser la détection WebP
WebPDetector.initWebPDetection().then(() => {
  console.log('🖼️ Détection WebP initialisée');
});

// Créer l'application Vue
const app = createApp(App);

// Enregistrer les composants globaux
app.component('LazyImage', LazyImage);
app.component('VirtualList', VirtualList);

// Utiliser les plugins
app.use(store);
app.use(router);
app.use(WebSocketPlugin, { store });

// Initialiser les services
async function initializeServices() {
  try {
    // Initialiser l'authentification
    await store.dispatch("auth/initializeAuth");

    // Initialiser l'application globale
    await store.dispatch("initializeApp");

    // Initialiser les notifications push si supportées
    if (pushNotificationService.isSupported() && store.getters['auth/isAuthenticated']) {
      try {
        await pushNotificationService.subscribe();
        console.log('✅ Notifications push initialisées');
      } catch (error) {
        console.warn('⚠️ Notifications push non disponibles:', error.message);
      }
    }

    // Charger les configurations sauvegardées
    store.dispatch("loadSavedConfigs");

    console.log('✅ Services initialisés avec succès');
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des services:', error);
  }
}

// Initialiser et monter l'application
initializeServices()
  .then(() => {
    // Monter l'application après l'initialisation des services
    app.mount("#app");

    // Émettre l'événement pour le pre-rendering
    setTimeout(() => {
      document.dispatchEvent(new Event('render-event'));
    }, 1000);
  })
  .catch((error) => {
    console.error("Erreur lors de l'initialisation:", error);

    // Monter l'application même en cas d'erreur
    app.mount("#app");

    // Émettre l'événement pour le pre-rendering même en cas d'erreur
    setTimeout(() => {
      document.dispatchEvent(new Event('render-event'));
    }, 1000);
  });

// Gestion des erreurs globales
app.config.errorHandler = (error, instance, info) => {
  console.error('Erreur Vue globale:', error, info);

  // Optionnel: envoyer l'erreur à un service de monitoring
  if (process.env.NODE_ENV === 'production') {
    // Exemple: Sentry, LogRocket, etc.
    // errorReportingService.captureException(error, { extra: { info } });
  }
};

// Gestion des promesses rejetées non capturées
window.addEventListener('unhandledrejection', event => {
  console.error('Promise rejetée non capturée:', event.reason);

  if (process.env.NODE_ENV === 'production') {
    // Optionnel: envoyer l'erreur à un service de monitoring
    // errorReportingService.captureException(event.reason);
  }
});

// Optimisations de performance
if (process.env.NODE_ENV === 'production') {
  // Précharger les routes critiques
  router.beforeEach((to, from, next) => {
    // Précharger les données critiques pour certaines routes
    if (to.name === 'profiles' && store.getters['auth/isAuthenticated']) {
      store.dispatch('profiles/loadProfiles').catch(console.error);
    }
    next();
  });

  // Nettoyage périodique du cache
  setInterval(() => {
    cacheService.cleanup();
  }, 5 * 60 * 1000); // Toutes les 5 minutes
}
