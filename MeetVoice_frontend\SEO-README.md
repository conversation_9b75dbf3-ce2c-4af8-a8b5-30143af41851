# 🚀 Guide SEO - MeetVoice Frontend

Ce document explique comment utiliser et maintenir le système SEO optimisé de MeetVoice.

## 📋 Fonctionnalités SEO Implémentées

### ✅ Pre-rendering SPA
- **Plugin**: `prerender-spa-plugin` avec <PERSON>peteer
- **Pages pre-rendues**: Page d'accueil, actualités, et top 20 des articles
- **Avantages**: Google voit le contenu réel dès le premier crawl

### ✅ URLs SEO-Friendly
- **Format moderne**: `/article/titre-de-l-article` (sans ID)
- **Compatibilité**: `/article/titre-de-l-article/123` et `/article/123` supportés
- **Slugs backend**: Utilise les slugs générés par l'API Django
- **Chargement intelligent**: Par slug ou par ID selon l'URL
- **Redirections**: Vers l'URL canonique si nécessaire
- **Validation**: Vérification de la cohérence slug/article

### ✅ Meta Tags Dynamiques
- **Titre**: Spécifique à chaque article
- **Description**: Utilise `petit_description` de l'article
- **Open Graph**: Facebook, LinkedIn, WhatsApp
- **Twitter Cards**: Partage optimisé sur Twitter

### ✅ Données Structurées (JSON-LD)
- **Schema.org Article**: Pour chaque article
- **Informations complètes**: Auteur, éditeur, dates, images
- **WebSite Schema**: Pour la page d'accueil

### ✅ Sitemap Dynamique
- **Génération automatique**: Depuis l'API des articles
- **URLs avec slugs**: SEO-friendly
- **Mise à jour**: Avant chaque build de production

### ✅ Fichiers SEO Essentiels
- **robots.txt**: Optimisé pour l'indexation
- **manifest.json**: PWA ready
- **Favicon**: Optimisé pour tous les appareils

## 🛠️ Scripts Disponibles

### Développement
```bash
npm run serve                 # Serveur de développement
npm run generate-sitemap      # Générer le sitemap uniquement
npm run test:seo             # Tester le système SEO
```

### Production
```bash
npm run build:production      # Build complet avec SEO optimisé
npm run serve:dist           # Tester le build localement
```

### SEO Spécifique
```bash
npm run build:seo           # Build avec sitemap + pre-rendering
npm run prerender           # Alias pour build:seo
```

## 📁 Structure des Fichiers SEO

```
MeetVoice_frontend/
├── public/
│   ├── robots.txt           # Directives pour les robots
│   ├── sitemap.xml          # Sitemap généré automatiquement
│   └── manifest.json        # Manifest PWA
├── scripts/
│   ├── generate-sitemap.js  # Génération du sitemap
│   └── build-production.js  # Build optimisé
├── src/
│   └── utils/
│       ├── seo.js          # Gestionnaire SEO centralisé
│       └── slug.js         # Utilitaires pour les slugs
└── vue.config.js           # Configuration pre-rendering
```

## 🔧 Configuration

### Pre-rendering (vue.config.js)
Le pre-rendering est configuré pour générer des pages statiques des articles les plus populaires. La liste est mise à jour automatiquement via le script `generate-sitemap.js`.

### Meta Tags (src/utils/seo.js)
Le système SEO centralise la gestion des meta tags :
- Titre dynamique par page
- Meta description optimisée
- Open Graph complet
- Données structurées Schema.org

### URLs (src/utils/slug.js)
Les URLs sont optimisées avec des slugs :
- **Priorité aux slugs backend** : Utilise `article.slug` de l'API
- **Fallback frontend** : Génération depuis les titres si nécessaire
- **Validation** : Vérification de la cohérence slug/article
- **Redirections** : Vers l'URL canonique automatiquement
- **Caractères normalisés** : Accents supprimés, caractères spéciaux nettoyés

## 📊 Vérifications SEO

### Outils Recommandés
1. **Google Search Console**: Indexation et erreurs
2. **Test des données structurées**: https://search.google.com/test/rich-results
3. **PageSpeed Insights**: Performance et Core Web Vitals
4. **Open Graph Debugger**: https://developers.facebook.com/tools/debug/

### Checklist SEO
- [ ] Sitemap généré et accessible
- [ ] Meta tags présents sur toutes les pages
- [ ] URLs avec slugs fonctionnelles
- [ ] Données structurées valides
- [ ] Images avec attributs alt
- [ ] Temps de chargement < 3s

## 🚀 Déploiement

### Étapes de Déploiement
1. **Build de production**:
   ```bash
   npm run build:production
   ```

2. **Vérification locale**:
   ```bash
   npm run serve:dist
   ```

3. **Upload sur le serveur**:
   - Copier le contenu du dossier `dist/`
   - Configurer les redirections serveur si nécessaire

4. **Post-déploiement**:
   - Soumettre le sitemap à Google Search Console
   - Vérifier l'indexation des nouvelles pages
   - Tester les partages sociaux

### Configuration Serveur
Pour un SEO optimal, configurez votre serveur web :

**Apache (.htaccess)**:
```apache
# Redirection vers HTTPS
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Compression GZIP
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css text/javascript application/javascript application/json
</IfModule>
```

**Nginx**:
```nginx
# Redirection vers HTTPS
server {
    listen 80;
    return 301 https://$server_name$request_uri;
}

# Compression
gzip on;
gzip_types text/css application/javascript application/json;
```

## 🔄 Maintenance

### Mise à Jour du Sitemap
Le sitemap se met à jour automatiquement à chaque build de production. Pour une mise à jour manuelle :
```bash
npm run generate-sitemap
```

### Ajout de Nouvelles Pages
1. Ajouter la route dans `src/router/index.js`
2. Implémenter les meta tags avec `SEOManager`
3. Ajouter la page dans `scripts/generate-sitemap.js` si nécessaire

### Monitoring SEO
- Vérifier Google Search Console hebdomadairement
- Surveiller les Core Web Vitals
- Tester les nouvelles fonctionnalités avec les outils Google

## 📈 Résultats Attendus

Avec cette implémentation, vous devriez observer :
- **Indexation rapide** des nouveaux articles (24-48h)
- **Meilleur classement** sur les requêtes longue traîne
- **Partages sociaux optimisés** avec aperçus riches
- **Core Web Vitals** améliorés grâce au pre-rendering
- **Taux de clic** amélioré depuis les SERP

## 🆘 Dépannage

### Problèmes Courants

**Le pre-rendering échoue**:
- Vérifier que l'API est accessible
- Augmenter le timeout dans `vue.config.js`
- Vérifier les logs de Puppeteer

**Sitemap vide**:
- Vérifier la connexion à l'API
- Contrôler les URLs dans `scripts/generate-sitemap.js`

**Meta tags non mis à jour**:
- Vérifier l'import de `SEOManager`
- S'assurer que `updateSEOTags()` est appelé

Pour plus d'aide, consultez les logs de build ou contactez l'équipe de développement.
