#!/usr/bin/env python3
"""
Test de l'intégration des paramètres enhanced dans l'interface
"""

import requests
import json

def test_enhanced_parameters():
    """Test des paramètres enhanced via l'API"""
    print("🔬 Test des paramètres enhanced intégrés...")
    print("=" * 60)
    
    # Test avec différentes combinaisons de paramètres enhanced
    test_cases = [
        {
            "name": "Standard (sans enhanced)",
            "params": {
                'text': 'Test qualité standard.',
                'voice_id': 1,
                'language': 'fr',
                'speed': 1.0,
                'pitch': 1.0
            }
        },
        {
            "name": "Enhanced (avec enhanced=true)",
            "params": {
                'text': 'Test qualité enhanced.',
                'voice_id': 1,
                'language': 'fr',
                'speed': 1.0,
                'pitch': 1.0,
                'enhanced': True
            }
        },
        {
            "name": "Enhanced (avec quality='enhanced')",
            "params": {
                'text': 'Test qualité enhanced.',
                'voice_id': 1,
                'language': 'fr',
                'speed': 1.0,
                'pitch': 1.0,
                'quality': 'enhanced'
            }
        },
        {
            "name": "Enhanced (combiné)",
            "params": {
                'text': 'Test qualité enhanced combiné.',
                'voice_id': 1,
                'language': 'fr',
                'speed': 1.0,
                'pitch': 1.0,
                'enhanced': True,
                'quality': 'enhanced'
            }
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n🧪 {test_case['name']}:")
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', 
                                   json=test_case['params'])
            
            if response.status_code == 200:
                data = response.json()
                audio_url = data.get('audio_url', 'N/A')
                
                # Analyser l'URL pour voir si elle contient des indices de qualité
                url_analysis = {
                    'contains_enhanced': 'enhanced' in audio_url.lower(),
                    'contains_quality': 'quality' in audio_url.lower(),
                    'file_path': audio_url.split('/')[-1] if '/' in audio_url else audio_url
                }
                
                results.append({
                    'test': test_case['name'],
                    'status': 'SUCCESS',
                    'audio_url': audio_url,
                    'analysis': url_analysis
                })
                
                print(f"  ✅ Succès")
                print(f"  📄 URL: {audio_url}")
                print(f"  🔍 Contient 'enhanced': {url_analysis['contains_enhanced']}")
                print(f"  📁 Fichier: {url_analysis['file_path']}")
                
            else:
                results.append({
                    'test': test_case['name'],
                    'status': 'ERROR',
                    'error': f"HTTP {response.status_code}"
                })
                print(f"  ❌ Erreur HTTP {response.status_code}")
                
        except Exception as e:
            results.append({
                'test': test_case['name'],
                'status': 'ERROR',
                'error': str(e)
            })
            print(f"  ❌ Erreur: {e}")
    
    return results

def test_different_voices_enhanced():
    """Test enhanced avec différentes voix"""
    print("\n🎤 Test enhanced avec différentes voix:")
    print("-" * 40)
    
    voices_to_test = [
        (1, "Sophie"),
        (6, "Élise"),
        (3, "Lucas"),
        (4, "Antoine")
    ]
    
    for voice_id, voice_name in voices_to_test:
        params = {
            'text': f'Test enhanced avec {voice_name}.',
            'voice_id': voice_id,
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0,
            'enhanced': True,
            'quality': 'enhanced'
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=params)
            if response.status_code == 200:
                data = response.json()
                audio_url = data.get('audio_url', 'N/A')
                print(f"  ✅ {voice_name}: {audio_url.split('/')[-1]}")
            else:
                print(f"  ❌ {voice_name}: Erreur {response.status_code}")
        except Exception as e:
            print(f"  ❌ {voice_name}: Erreur {e}")

def analyze_results(results):
    """Analyser les résultats des tests"""
    print("\n" + "=" * 60)
    print("📊 ANALYSE DES RÉSULTATS")
    print("=" * 60)
    
    success_count = len([r for r in results if r['status'] == 'SUCCESS'])
    total_count = len(results)
    
    print(f"✅ Tests réussis: {success_count}/{total_count}")
    
    if success_count > 0:
        print("\n🔍 Analyse des URLs:")
        
        # Vérifier si les paramètres enhanced changent quelque chose
        standard_urls = [r['audio_url'] for r in results if r['status'] == 'SUCCESS' and 'Standard' in r['test']]
        enhanced_urls = [r['audio_url'] for r in results if r['status'] == 'SUCCESS' and 'Enhanced' in r['test']]
        
        if standard_urls and enhanced_urls:
            standard_files = [url.split('/')[-1] for url in standard_urls]
            enhanced_files = [url.split('/')[-1] for url in enhanced_urls]
            
            if set(standard_files) != set(enhanced_files):
                print("  🎯 Les paramètres enhanced génèrent des fichiers différents !")
                print(f"  📁 Standard: {standard_files}")
                print(f"  ⭐ Enhanced: {enhanced_files}")
            else:
                print("  ⚠️  Les paramètres enhanced génèrent les mêmes fichiers")
        
        # Vérifier les patterns dans les URLs
        enhanced_indicators = []
        for result in results:
            if result['status'] == 'SUCCESS' and 'Enhanced' in result['test']:
                if result['analysis']['contains_enhanced']:
                    enhanced_indicators.append("URL contient 'enhanced'")
                if 'enhanced' in result['audio_url']:
                    enhanced_indicators.append("Chemin contient 'enhanced'")
        
        if enhanced_indicators:
            print(f"  ✅ Indicateurs enhanced trouvés: {set(enhanced_indicators)}")
        else:
            print("  ⚠️  Aucun indicateur enhanced visible dans les URLs")

def main():
    """Fonction principale"""
    print("🎯 Test de l'intégration des paramètres enhanced")
    print("=" * 60)
    
    # Test des paramètres enhanced
    results = test_enhanced_parameters()
    
    # Test avec différentes voix
    test_different_voices_enhanced()
    
    # Analyser les résultats
    analyze_results(results)
    
    print("\n💡 RECOMMANDATIONS:")
    print("1. Testez l'interface web pour voir le sélecteur de qualité")
    print("2. Comparez l'audio standard vs enhanced")
    print("3. Vérifiez les logs de la console du navigateur")
    print("4. Écoutez les différences de qualité audio")

if __name__ == "__main__":
    main()
