#!/usr/bin/env python3
"""
Test avec les IDs spécifiques des voix
"""

import requests
import json

def test_voice_ids():
    """Test avec les IDs spécifiques"""
    print("🔍 Test avec IDs spécifiques des voix...")
    
    # Récupérer les voix avec leurs IDs
    response = requests.get('http://127.0.0.1:8000/tts/public/voices/')
    voices = response.json()
    
    # Trouver <PERSON> et <PERSON>lis<PERSON> (toutes deux female_young)
    sophie = next((v for v in voices if '<PERSON>' in v['name']), None)
    elise = next((v for v in voices if 'Élise' in v['name']), None)
    
    print(f"Sophie ID: {sophie['id'] if sophie else 'Non trouvée'}")
    print(f"Élise ID: {elise['id'] if elise else 'Non trouvée'}")
    
    # Test avec voice_id au lieu de voice_type
    if sophie:
        print(f"\n🧪 Test avec Sophie (ID {sophie['id']}):")
        payload = {
            'text': 'Je suis Sophie.',
            'voice_id': sophie['id'],  # Utiliser voice_id
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            if response.status_code == 200:
                print(f"✅ Succès: {len(response.content)} bytes")
            else:
                print(f"❌ Erreur {response.status_code}")
                print(f"Réponse: {response.text[:200]}")
        except Exception as e:
            print(f"❌ Erreur: {e}")
    
    if elise:
        print(f"\n🧪 Test avec Élise (ID {elise['id']}):")
        payload = {
            'text': 'Je suis Élise.',
            'voice_id': elise['id'],  # Utiliser voice_id
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            if response.status_code == 200:
                print(f"✅ Succès: {len(response.content)} bytes")
            else:
                print(f"❌ Erreur {response.status_code}")
                print(f"Réponse: {response.text[:200]}")
        except Exception as e:
            print(f"❌ Erreur: {e}")

def test_voice_type_vs_id():
    """Comparer voice_type vs voice_id"""
    print("\n🔄 Comparaison voice_type vs voice_id:")
    
    # Test avec voice_type (méthode actuelle)
    print("\n1. Avec voice_type (méthode actuelle):")
    payload1 = {
        'text': 'Test voice_type.',
        'voice_type': 'female_young',
        'language': 'fr',
        'speed': 1.0,
        'pitch': 1.0
    }
    
    try:
        response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload1)
        if response.status_code == 200:
            print(f"✅ voice_type: {len(response.content)} bytes")
        else:
            print(f"❌ voice_type: Erreur {response.status_code}")
    except Exception as e:
        print(f"❌ voice_type: Erreur {e}")
    
    # Test avec voice_id (nouvelle méthode)
    print("\n2. Avec voice_id (nouvelle méthode):")
    response = requests.get('http://127.0.0.1:8000/tts/public/voices/')
    voices = response.json()
    sophie = next((v for v in voices if 'Sophie' in v['name']), None)
    
    if sophie:
        payload2 = {
            'text': 'Test voice_id.',
            'voice_id': sophie['id'],
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload2)
            if response.status_code == 200:
                print(f"✅ voice_id: {len(response.content)} bytes")
            else:
                print(f"❌ voice_id: Erreur {response.status_code}")
                print(f"Réponse: {response.text[:200]}")
        except Exception as e:
            print(f"❌ voice_id: Erreur {e}")

if __name__ == "__main__":
    test_voice_ids()
    test_voice_type_vs_id()
