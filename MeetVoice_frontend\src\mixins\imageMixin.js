/**
 * Mixin pour la gestion des images optimisées
 * Simplifie l'utilisation des images WebP avec fallback
 */

export const imageMixin = {
  methods: {
    /**
     * Obtenir l'URL d'une image avec support WebP
     */
    getImageUrl(imagePath, useWebP = true) {
      if (!imagePath) return '';
      
      // Si WebP n'est pas supporté ou forcé à false, retourner l'original
      if (!useWebP || (typeof window !== 'undefined' && window.webpSupported === false)) {
        return imagePath;
      }
      
      // Si l'image a déjà l'extension .webp, la retourner
      if (imagePath.includes('.webp')) {
        return imagePath;
      }
      
      // Convertir l'extension en .webp
      return imagePath.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
    },
    
    /**
     * Obtenir les URLs WebP et fallback pour un élément picture
     */
    getImageSources(imagePath) {
      return {
        webp: this.getImageUrl(imagePath, true),
        fallback: imagePath
      };
    },
    
    /**
     * Précharger une image
     */
    preloadImage(imagePath, useWebP = true) {
      const url = this.getImageUrl(imagePath, useWebP);
      
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      link.href = url;
      
      if (useWebP && url.includes('.webp')) {
        link.type = 'image/webp';
      }
      
      document.head.appendChild(link);
    }
  }
};

export default imageMixin;
