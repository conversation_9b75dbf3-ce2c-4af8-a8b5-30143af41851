import stripeService from '@/services/stripe'
import paypalService from '@/services/paypal'
import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000'

export default {
  namespaced: true,
  
  state: {
    // État du paiement
    isProcessing: false,
    paymentError: null,
    paymentSuccess: false,
    
    // Méthodes de paiement sauvegardées
    paymentMethods: [],
    defaultPaymentMethod: null,
    
    // Historique des paiements
    paymentHistory: [],
    
    // Session de checkout en cours
    currentCheckoutSession: null,
    
    // Setup Intent pour sauvegarder une carte
    currentSetupIntent: null,
    
    // État de chargement
    loading: false,
    error: null
  },
  
  mutations: {
    setProcessing(state, processing) {
      state.isProcessing = processing
    },
    
    setPaymentError(state, error) {
      state.paymentError = error
    },
    
    setPaymentSuccess(state, success) {
      state.paymentSuccess = success
    },
    
    clearPaymentStatus(state) {
      state.paymentError = null
      state.paymentSuccess = false
    },
    
    setPaymentMethods(state, methods) {
      state.paymentMethods = methods
    },
    
    addPaymentMethod(state, method) {
      state.paymentMethods.push(method)
    },
    
    removePaymentMethod(state, methodId) {
      state.paymentMethods = state.paymentMethods.filter(m => m.id !== methodId)
    },
    
    setDefaultPaymentMethod(state, method) {
      state.defaultPaymentMethod = method
    },
    
    setPaymentHistory(state, history) {
      state.paymentHistory = history
    },
    
    addPaymentToHistory(state, payment) {
      state.paymentHistory.unshift(payment)
    },
    
    setCurrentCheckoutSession(state, session) {
      state.currentCheckoutSession = session
    },
    
    setCurrentSetupIntent(state, setupIntent) {
      state.currentSetupIntent = setupIntent
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    }
  },
  
  actions: {
    // Initialiser Stripe
    async initializeStripe({ commit }) {
      try {
        await stripeService.initialize()
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de l\'initialisation du système de paiement')
        throw error
      }
    },

    // Initialiser PayPal
    async initializePayPal({ commit }) {
      try {
        await paypalService.initialize()
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de l\'initialisation de PayPal')
        throw error
      }
    },
    
    // Créer un paiement direct avec carte
    async processCardPayment({ commit }, { amount, currency = 'eur', metadata = {}, billingDetails = {} }) {
      try {
        commit('setProcessing', true)
        commit('clearPaymentStatus')
        
        // Créer le Payment Intent
        const paymentIntent = await stripeService.createPaymentIntent(amount, currency, metadata)
        
        // Confirmer le paiement
        const result = await stripeService.confirmCardPayment(
          paymentIntent.client_secret,
          null,
          billingDetails
        )
        
        if (result.status === 'succeeded') {
          commit('setPaymentSuccess', true)
          commit('addPaymentToHistory', {
            id: result.id,
            amount: result.amount / 100,
            currency: result.currency,
            status: result.status,
            created: new Date(result.created * 1000)
          })
          
          return result
        } else {
          throw new Error('Le paiement n\'a pas pu être traité')
        }
        
      } catch (error) {
        const errorMessage = stripeService.formatError(error)
        commit('setPaymentError', errorMessage)
        throw error
      } finally {
        commit('setProcessing', false)
      }
    },
    
    // Créer une session de checkout pour un abonnement
    async createSubscriptionCheckout({ commit }, { planId, successUrl, cancelUrl }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/api/abonnement/checkout/`, {
          plan_id: planId,
          success_url: successUrl || `${window.location.origin}/payment/success`,
          cancel_url: cancelUrl || `${window.location.origin}/payment/cancel`
        })
        
        const session = response.data
        commit('setCurrentCheckoutSession', session)
        
        // Rediriger vers Stripe Checkout
        if (session.checkout_url) {
          window.location.href = session.checkout_url
        } else if (session.session_id) {
          await stripeService.redirectToCheckout(session.session_id)
        }
        
        return session
      } catch (error) {
        commit('setError', 'Erreur lors de la création du checkout')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Sauvegarder une méthode de paiement
    async savePaymentMethod({ commit }, { billingDetails = {} }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        // Créer un Setup Intent
        const setupIntent = await stripeService.createSetupIntent()
        commit('setCurrentSetupIntent', setupIntent)
        
        // Confirmer le Setup Intent
        const result = await stripeService.confirmCardSetup(
          setupIntent.client_secret,
          null,
          billingDetails
        )
        
        if (result.status === 'succeeded') {
          // Recharger les méthodes de paiement
          await this.dispatch('payment/loadPaymentMethods')
          
          return result
        } else {
          throw new Error('La méthode de paiement n\'a pas pu être sauvegardée')
        }
        
      } catch (error) {
        const errorMessage = stripeService.formatError(error)
        commit('setError', errorMessage)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Charger les méthodes de paiement sauvegardées
    async loadPaymentMethods({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const methods = await stripeService.getPaymentMethods()
        commit('setPaymentMethods', methods)
        
        // Définir la méthode par défaut
        const defaultMethod = methods.find(m => m.is_default) || methods[0]
        if (defaultMethod) {
          commit('setDefaultPaymentMethod', defaultMethod)
        }
        
        return methods
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des méthodes de paiement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Supprimer une méthode de paiement
    async deletePaymentMethod({ commit }, methodId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        await stripeService.deletePaymentMethod(methodId)
        commit('removePaymentMethod', methodId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de la suppression de la méthode de paiement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Charger l'historique des paiements
    async loadPaymentHistory({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/api/abonnement/factures/`)
        const payments = response.data.results || response.data
        
        commit('setPaymentHistory', payments)
        
        return payments
      } catch (error) {
        commit('setError', 'Erreur lors du chargement de l\'historique des paiements')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Payer avec une méthode sauvegardée
    async payWithSavedMethod({ commit }, { paymentMethodId, amount, currency = 'eur', metadata = {} }) {
      try {
        commit('setProcessing', true)
        commit('clearPaymentStatus')
        
        const response = await axios.post(`${API_BASE_URL}/api/abonnement/pay-with-saved-method/`, {
          payment_method_id: paymentMethodId,
          amount: Math.round(amount * 100), // Convertir en centimes
          currency,
          metadata
        })
        
        const paymentIntent = response.data
        
        if (paymentIntent.status === 'succeeded') {
          commit('setPaymentSuccess', true)
          commit('addPaymentToHistory', {
            id: paymentIntent.id,
            amount: paymentIntent.amount / 100,
            currency: paymentIntent.currency,
            status: paymentIntent.status,
            created: new Date()
          })
        } else if (paymentIntent.status === 'requires_action') {
          // Gérer l'authentification 3D Secure
          const result = await stripeService.stripe.confirmCardPayment(paymentIntent.client_secret)
          
          if (result.error) {
            throw new Error(result.error.message)
          }
          
          if (result.paymentIntent.status === 'succeeded') {
            commit('setPaymentSuccess', true)
          }
        }
        
        return paymentIntent
      } catch (error) {
        const errorMessage = stripeService.formatError(error)
        commit('setPaymentError', errorMessage)
        throw error
      } finally {
        commit('setProcessing', false)
      }
    },
    
    // Rembourser un paiement (admin)
    async refundPayment({ commit }, { paymentIntentId, amount = null, reason = 'requested_by_customer' }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/api/abonnement/refund/`, {
          payment_intent_id: paymentIntentId,
          amount: amount ? Math.round(amount * 100) : null,
          reason
        })
        
        // Recharger l'historique des paiements
        await this.dispatch('payment/loadPaymentHistory')
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du remboursement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Nettoyer l'état du paiement
    clearPaymentStatus({ commit }) {
      commit('clearPaymentStatus')
    },
    
    // Traiter un paiement PayPal
    async processPayPalPayment({ commit }, { orderId, amount, currency = 'eur', metadata = {} }) {
      try {
        commit('setProcessing', true)
        commit('clearPaymentStatus')

        const response = await axios.post(`${API_BASE_URL}/api/abonnement/paypal/capture-order/`, {
          order_id: orderId,
          metadata
        })

        const result = response.data

        if (result.status === 'COMPLETED') {
          commit('setPaymentSuccess', true)
          commit('addPaymentToHistory', {
            id: result.id,
            amount: amount,
            currency: currency,
            status: 'completed',
            method: 'paypal',
            created: new Date()
          })

          return result
        } else {
          throw new Error('Le paiement PayPal n\'a pas pu être finalisé')
        }

      } catch (error) {
        const errorMessage = paypalService.formatError(error)
        commit('setPaymentError', errorMessage)
        throw error
      } finally {
        commit('setProcessing', false)
      }
    },

    // Créer un abonnement PayPal
    async createPayPalSubscription({ commit }, { planId, subscriberInfo = {} }) {
      try {
        commit('setLoading', true)
        commit('clearError')

        const subscription = await paypalService.createSubscription(planId, subscriberInfo)

        return subscription
      } catch (error) {
        commit('setError', 'Erreur lors de la création de l\'abonnement PayPal')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    // Annuler un abonnement PayPal
    async cancelPayPalSubscription({ commit }, { subscriptionId, reason }) {
      try {
        commit('setLoading', true)
        commit('clearError')

        const result = await paypalService.cancelSubscription(subscriptionId, reason)

        // Recharger l'historique des paiements
        await this.dispatch('payment/loadPaymentHistory')

        return result
      } catch (error) {
        commit('setError', 'Erreur lors de l\'annulation de l\'abonnement PayPal')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    isProcessing: state => state.isProcessing,
    paymentError: state => state.paymentError,
    paymentSuccess: state => state.paymentSuccess,
    paymentMethods: state => state.paymentMethods,
    defaultPaymentMethod: state => state.defaultPaymentMethod,
    paymentHistory: state => state.paymentHistory,
    currentCheckoutSession: state => state.currentCheckoutSession,
    currentSetupIntent: state => state.currentSetupIntent,
    loading: state => state.loading,
    error: state => state.error,
    
    // Vérifier si l'utilisateur a des méthodes de paiement sauvegardées
    hasPaymentMethods: state => state.paymentMethods.length > 0,
    
    // Obtenir les paiements réussis
    successfulPayments: state => {
      return state.paymentHistory.filter(p => p.status === 'succeeded' || p.status === 'paid')
    },
    
    // Calculer le total des paiements
    totalPaid: state => {
      return state.paymentHistory
        .filter(p => p.status === 'succeeded' || p.status === 'paid')
        .reduce((total, payment) => total + (payment.amount || 0), 0)
    },
    
    // Obtenir les paiements en attente
    pendingPayments: state => {
      return state.paymentHistory.filter(p => p.status === 'pending' || p.status === 'processing')
    }
  }
}
