<template>
  <div class="amoureu-quick-actions">
    <h3>Actions rapides - Amoureux</h3>
    <div class="actions-grid">
      <router-link to="/amoureu/discover" class="action-card">
        <div class="action-icon">💕</div>
        <h4>Découvrir</h4>
        <p>Rencontrer de nouvelles personnes</p>
      </router-link>
      
      <router-link to="/amoureu/matches" class="action-card">
        <div class="action-icon">⭐</div>
        <h4>Matchs</h4>
        <p>Voir vos compatibilités</p>
      </router-link>
      
      <router-link to="/amoureu/dates" class="action-card">
        <div class="action-icon">🌹</div>
        <h4>Rendez-vous</h4>
        <p>Planifier un rendez-vous</p>
      </router-link>
      
      <router-link to="/voice-interview" class="action-card">
        <div class="action-icon">🎤</div>
        <h4><PERSON> <PERSON>e</h4>
        <p><PERSON><PERSON><PERSON> votre profil vocal</p>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AmoureuQuickActions'
}
</script>

<style scoped>
.amoureu-quick-actions {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amoureu-quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.action-card {
  display: block;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.action-card:hover {
  background: #fce4ec;
  border-color: #e91e63;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.2);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.action-card p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-card {
    padding: 16px;
  }
  
  .action-icon {
    font-size: 24px;
  }
}
</style>
