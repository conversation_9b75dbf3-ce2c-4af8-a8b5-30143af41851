#!/usr/bin/env python3
"""
Test des modifications des voix (<PERSON> et <PERSON> uniquement) et icônes navbar
"""

def test_voice_filtering():
    """Test du filtrage des voix pour ne garder que Camille et <PERSON>"""
    print("🎤 Test du filtrage des voix...")
    print("=" * 50)
    
    # Simulation des voix de l'API
    mock_api_voices = [
        {"id": 1, "name": "<PERSON>", "is_active": True, "voice_type": "female_young", "language": "fr"},
        {"id": 2, "name": "<PERSON>", "is_active": True, "voice_type": "male_young", "language": "fr"},
        {"id": 3, "name": "<PERSON>", "is_active": True, "voice_type": "male_adult", "language": "fr"},
        {"id": 4, "name": "<PERSON>", "is_active": True, "voice_type": "female_adult", "language": "fr"},
        {"id": 5, "name": "<PERSON>", "is_active": True, "voice_type": "male_adult", "language": "fr"},
        {"id": 6, "name": "<PERSON>", "is_active": True, "voice_type": "female_adult", "language": "fr"},
        {"id": 7, "name": "Élise", "is_active": True, "voice_type": "female_young", "language": "fr"},
    ]
    
    # Simulation de la fonction formatBackendVoices
    def format_backend_voices(voices):
        allowed_voices = ['Camille', 'Sophie']
        
        return [
            {
                "id": voice["id"],
                "name": voice["name"],
                "gender": "female",
                "language": voice["language"],
                "description": f"Voix {voice['name']}"
            }
            for voice in voices
            if voice["is_active"] and voice["name"] in allowed_voices
        ]
    
    # Test du filtrage
    filtered_voices = format_backend_voices(mock_api_voices)
    
    print("Voix disponibles avant filtrage:")
    for voice in mock_api_voices:
        print(f"  - {voice['name']} (ID: {voice['id']})")
    
    print(f"\nVoix disponibles après filtrage:")
    for voice in filtered_voices:
        print(f"  - {voice['name']} (ID: {voice['id']})")
    
    # Vérifications
    expected_count = 2
    expected_names = ['Camille', 'Sophie']
    
    actual_count = len(filtered_voices)
    actual_names = [voice['name'] for voice in filtered_voices]
    
    print(f"\n📊 Résultats:")
    print(f"   Nombre de voix: {actual_count}/{expected_count}")
    print(f"   Voix attendues: {expected_names}")
    print(f"   Voix obtenues: {actual_names}")
    
    success = (actual_count == expected_count and 
               set(actual_names) == set(expected_names))
    
    print(f"   Statut: {'✅ Succès' if success else '❌ Échec'}")
    
    return success

def test_default_voices():
    """Test des voix par défaut"""
    print("\n🎵 Test des voix par défaut...")
    print("=" * 50)
    
    # Simulation de getDefaultVoices
    def get_default_voices():
        return [
            {
                "id": 1,
                "name": "Camille",
                "gender": "female",
                "age": "young",
                "language": "fr",
                "description": "Voix féminine claire et dynamique"
            },
            {
                "id": 6,
                "name": "Sophie",
                "gender": "female",
                "age": "adult",
                "language": "fr",
                "description": "Voix féminine mature et professionnelle"
            }
        ]
    
    default_voices = get_default_voices()
    
    print("Voix par défaut:")
    for voice in default_voices:
        print(f"  - {voice['name']} (ID: {voice['id']}) - {voice['description']}")
    
    # Vérifications
    expected_count = 2
    expected_names = ['Camille', 'Sophie']
    expected_ids = [1, 6]
    
    actual_count = len(default_voices)
    actual_names = [voice['name'] for voice in default_voices]
    actual_ids = [voice['id'] for voice in default_voices]
    
    print(f"\n📊 Résultats:")
    print(f"   Nombre de voix: {actual_count}/{expected_count}")
    print(f"   Noms corrects: {set(actual_names) == set(expected_names)}")
    print(f"   IDs corrects: {set(actual_ids) == set(expected_ids)}")
    
    success = (actual_count == expected_count and 
               set(actual_names) == set(expected_names) and
               set(actual_ids) == set(expected_ids))
    
    print(f"   Statut: {'✅ Succès' if success else '❌ Échec'}")
    
    return success

def test_navbar_icons():
    """Test de la remise des icônes dans la navbar"""
    print("\n🧭 Test des icônes de navigation...")
    print("=" * 50)
    
    # Simulation du contenu de la navbar
    navbar_links = [
        {"path": "/", "text": "Accueil"},
        {"path": "/actualite", "text": "Actualité"},
        {"path": "/simple-voice-test", "text": "🎤 Vocal"},
        {"path": "/media-config", "text": "🔧 Config"},
        {"path": "/register", "text": "Inscription"},
        {"path": "/login", "text": "Connexion"}
    ]
    
    print("Liens de navigation:")
    for link in navbar_links:
        has_icon = '🎤' in link['text'] or '🔧' in link['text']
        icon_status = "✅ Avec icône" if has_icon else "❌ Sans icône"
        print(f"   {link['path']}: '{link['text']}' - {icon_status}")
    
    # Vérifications
    vocal_link = next((link for link in navbar_links if link['path'] == '/simple-voice-test'), None)
    config_link = next((link for link in navbar_links if link['path'] == '/media-config'), None)
    
    vocal_has_icon = vocal_link and '🎤' in vocal_link['text']
    config_has_icon = config_link and '🔧' in config_link['text']
    
    print(f"\n📊 Résultats:")
    print(f"   Lien Vocal avec icône 🎤: {'✅ Oui' if vocal_has_icon else '❌ Non'}")
    print(f"   Lien Config avec icône 🔧: {'✅ Oui' if config_has_icon else '❌ Non'}")
    
    success = vocal_has_icon and config_has_icon
    print(f"   Statut: {'✅ Succès' if success else '❌ Échec'}")
    
    return success

def main():
    """Fonction principale"""
    print("🔧 Test des modifications voix et navigation")
    print("=" * 60)
    
    # Tests
    voice_filter_ok = test_voice_filtering()
    default_voices_ok = test_default_voices()
    navbar_icons_ok = test_navbar_icons()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    print(f"🎤 Filtrage voix (Camille/Sophie): {'✅ OK' if voice_filter_ok else '❌ ERREUR'}")
    print(f"🎵 Voix par défaut: {'✅ OK' if default_voices_ok else '❌ ERREUR'}")
    print(f"🧭 Icônes navbar: {'✅ OK' if navbar_icons_ok else '❌ ERREUR'}")
    
    if voice_filter_ok and default_voices_ok and navbar_icons_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Seules Camille et Sophie sont disponibles")
        print("✅ Les voix par défaut sont correctes")
        print("✅ Les icônes 🎤 et 🔧 sont remises dans la navbar")
        print("\n💡 PROCHAINES ÉTAPES:")
        print("1. Testez l'interface pour voir les 2 voix uniquement")
        print("2. Vérifiez que la navbar affiche les icônes")
        print("3. Testez la synthèse vocale avec Camille et Sophie")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("💡 Vérifiez les modifications dans les fichiers")

if __name__ == "__main__":
    main()
