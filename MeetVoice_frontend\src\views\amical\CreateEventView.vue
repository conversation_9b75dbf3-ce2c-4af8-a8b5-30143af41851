<template>
  <main class="create-event-container">
    <header class="page-header">
      <h1>Créer un nouvel événement</h1>
      <p class="subtitle">Organisez une sortie et invitez la communauté à vous rejoindre</p>
    </header>

    <form @submit.prevent="createEvent" class="event-form" novalidate>
      <!-- Informations de base -->
      <section class="form-section">
        <h2>Informations générales</h2>
        
        <div class="form-group">
          <label for="event-title" class="required">Titre de l'événement</label>
          <input
            id="event-title"
            v-model="eventData.title"
            type="text"
            required
            maxlength="100"
            placeholder="Ex: Soirée jeux de société au café"
            class="form-input"
            :class="{ 'error': errors.title }"
          >
          <span v-if="errors.title" class="error-message">{{ errors.title }}</span>
        </div>

        <div class="form-group">
          <label for="event-category" class="required">Catégorie</label>
          <select
            id="event-category"
            v-model="eventData.category"
            required
            class="form-select"
            :class="{ 'error': errors.category }"
          >
            <option value="">Choisir une catégorie</option>
            <option 
              v-for="category in eventCategories" 
              :key="category"
              :value="category"
            >
              {{ getCategoryIcon(category) }} {{ getCategoryLabel(category) }}
            </option>
          </select>
          <span v-if="errors.category" class="error-message">{{ errors.category }}</span>
        </div>

        <div class="form-group">
          <label for="event-description" class="required">Description</label>
          <textarea
            id="event-description"
            v-model="eventData.description"
            required
            maxlength="500"
            rows="4"
            placeholder="Décrivez votre événement, l'ambiance, ce qui est prévu..."
            class="form-textarea"
            :class="{ 'error': errors.description }"
          ></textarea>
          <div class="char-count">{{ eventData.description.length }}/500</div>
          <span v-if="errors.description" class="error-message">{{ errors.description }}</span>
        </div>
      </section>

      <!-- Date et heure -->
      <section class="form-section">
        <h2>Date et heure</h2>
        
        <div class="form-row">
          <div class="form-group">
            <label for="event-date" class="required">Date</label>
            <input
              id="event-date"
              v-model="eventData.date"
              type="date"
              required
              :min="minDate"
              class="form-input"
              :class="{ 'error': errors.date }"
            >
            <span v-if="errors.date" class="error-message">{{ errors.date }}</span>
          </div>

          <div class="form-group">
            <label for="event-time" class="required">Heure</label>
            <input
              id="event-time"
              v-model="eventData.time"
              type="time"
              required
              class="form-input"
              :class="{ 'error': errors.time }"
            >
            <span v-if="errors.time" class="error-message">{{ errors.time }}</span>
          </div>
        </div>

        <div class="form-group">
          <label for="event-duration">Durée estimée</label>
          <select
            id="event-duration"
            v-model="eventData.duration"
            class="form-select"
          >
            <option value="">Non spécifiée</option>
            <option value="1h">1 heure</option>
            <option value="2h">2 heures</option>
            <option value="3h">3 heures</option>
            <option value="4h">4 heures</option>
            <option value="journee">Toute la journée</option>
            <option value="soiree">Toute la soirée</option>
          </select>
        </div>
      </section>

      <!-- Lieu -->
      <section class="form-section">
        <h2>Lieu</h2>
        
        <div class="form-group">
          <label for="event-location" class="required">Adresse ou lieu</label>
          <input
            id="event-location"
            v-model="eventData.location"
            type="text"
            required
            placeholder="Ex: Café des Amis, 123 rue de la Paix, Paris"
            class="form-input"
            :class="{ 'error': errors.location }"
          >
          <span v-if="errors.location" class="error-message">{{ errors.location }}</span>
        </div>

        <div class="form-group">
          <label for="event-meeting-point">Point de rendez-vous</label>
          <input
            id="event-meeting-point"
            v-model="eventData.meetingPoint"
            type="text"
            placeholder="Ex: Devant l'entrée principale"
            class="form-input"
          >
        </div>
      </section>

      <!-- Participants -->
      <section class="form-section">
        <h2>Participants</h2>
        
        <div class="form-row">
          <div class="form-group">
            <label for="max-participants" class="required">Nombre maximum de participants</label>
            <input
              id="max-participants"
              v-model.number="eventData.maxParticipants"
              type="number"
              required
              min="2"
              max="100"
              class="form-input"
              :class="{ 'error': errors.maxParticipants }"
            >
            <span v-if="errors.maxParticipants" class="error-message">{{ errors.maxParticipants }}</span>
          </div>

          <div class="form-group">
            <label for="min-participants">Nombre minimum (optionnel)</label>
            <input
              id="min-participants"
              v-model.number="eventData.minParticipants"
              type="number"
              min="1"
              :max="eventData.maxParticipants"
              class="form-input"
            >
          </div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="eventData.requiresApproval"
              type="checkbox"
              class="form-checkbox"
            >
            <span class="checkbox-text">Nécessite une approbation pour participer</span>
          </label>
        </div>
      </section>

      <!-- Informations complémentaires -->
      <section class="form-section">
        <h2>Informations complémentaires</h2>
        
        <div class="form-group">
          <label for="event-price">Prix (optionnel)</label>
          <div class="price-input">
            <input
              id="event-price"
              v-model.number="eventData.price"
              type="number"
              min="0"
              step="0.01"
              placeholder="0.00"
              class="form-input"
            >
            <span class="currency">€</span>
          </div>
        </div>

        <div class="form-group">
          <label for="event-notes">Notes pour les participants</label>
          <textarea
            id="event-notes"
            v-model="eventData.notes"
            maxlength="300"
            rows="3"
            placeholder="Informations importantes, ce qu'il faut apporter, code vestimentaire..."
            class="form-textarea"
          ></textarea>
          <div class="char-count">{{ eventData.notes.length }}/300</div>
        </div>

        <div class="form-group">
          <label class="checkbox-label">
            <input
              v-model="eventData.isPublic"
              type="checkbox"
              class="form-checkbox"
            >
            <span class="checkbox-text">Événement public (visible par tous)</span>
          </label>
        </div>
      </section>

      <!-- Actions -->
      <footer class="form-actions">
        <button
          type="button"
          @click="$router.go(-1)"
          class="btn-cancel"
        >
          Annuler
        </button>
        
        <button
          type="button"
          @click="saveDraft"
          class="btn-draft"
          :disabled="loading"
        >
          Sauvegarder en brouillon
        </button>
        
        <button
          type="submit"
          class="btn-create"
          :disabled="loading || !isFormValid"
        >
          {{ loading ? 'Création...' : 'Créer l\'événement' }}
        </button>
      </footer>
    </form>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'CreateEventView',
  
  data() {
    return {
      loading: false,
      eventData: {
        title: '',
        category: '',
        description: '',
        date: '',
        time: '',
        duration: '',
        location: '',
        meetingPoint: '',
        maxParticipants: 10,
        minParticipants: null,
        requiresApproval: false,
        price: null,
        notes: '',
        isPublic: true
      },
      errors: {}
    }
  },
  
  computed: {
    ...mapState(['eventCategories']),
    
    minDate() {
      return new Date().toISOString().split('T')[0];
    },
    
    isFormValid() {
      return this.eventData.title &&
             this.eventData.category &&
             this.eventData.description &&
             this.eventData.date &&
             this.eventData.time &&
             this.eventData.location &&
             this.eventData.maxParticipants >= 2;
    }
  },
  
  methods: {
    ...mapActions(['createEvent']),
    
    getCategoryLabel(category) {
      const labels = {
        sport: 'Sport',
        jeux: 'Jeux',
        danse: 'Danse',
        restaurant: 'Restaurant',
        cinéma: 'Cinéma'
      };
      return labels[category] || category;
    },
    
    getCategoryIcon(category) {
      const icons = {
        sport: '⚽',
        jeux: '🎮',
        danse: '💃',
        restaurant: '🍽️',
        cinéma: '🎬'
      };
      return icons[category] || '📅';
    },
    
    validateForm() {
      this.errors = {};
      
      if (!this.eventData.title.trim()) {
        this.errors.title = 'Le titre est obligatoire';
      }
      
      if (!this.eventData.category) {
        this.errors.category = 'La catégorie est obligatoire';
      }
      
      if (!this.eventData.description.trim()) {
        this.errors.description = 'La description est obligatoire';
      }
      
      if (!this.eventData.date) {
        this.errors.date = 'La date est obligatoire';
      } else if (new Date(this.eventData.date) < new Date()) {
        this.errors.date = 'La date ne peut pas être dans le passé';
      }
      
      if (!this.eventData.time) {
        this.errors.time = 'L\'heure est obligatoire';
      }
      
      if (!this.eventData.location.trim()) {
        this.errors.location = 'Le lieu est obligatoire';
      }
      
      if (this.eventData.maxParticipants < 2) {
        this.errors.maxParticipants = 'Minimum 2 participants requis';
      }
      
      return Object.keys(this.errors).length === 0;
    },
    
    async createEvent() {
      if (!this.validateForm()) {
        return;
      }
      
      this.loading = true;
      
      try {
        const eventPayload = {
          ...this.eventData,
          organizer: this.$store.state.user,
          createdAt: new Date().toISOString(),
          status: 'active'
        };
        
        await this.createEvent(eventPayload);
        
        this.$router.push('/events');
        
        // Notification de succès
        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Événement créé avec succès !',
          timestamp: new Date().toISOString()
        });
        
      } catch (error) {
        console.error('Erreur lors de la création de l\'événement:', error);
        this.$store.commit('addNotification', {
          type: 'error',
          message: 'Erreur lors de la création de l\'événement',
          timestamp: new Date().toISOString()
        });
      } finally {
        this.loading = false;
      }
    },
    
    saveDraft() {
      // Sauvegarder en localStorage
      localStorage.setItem('eventDraft', JSON.stringify(this.eventData));
      
      this.$store.commit('addNotification', {
        type: 'info',
        message: 'Brouillon sauvegardé',
        timestamp: new Date().toISOString()
      });
    },
    
    loadDraft() {
      const draft = localStorage.getItem('eventDraft');
      if (draft) {
        this.eventData = { ...this.eventData, ...JSON.parse(draft) };
      }
    }
  },
  
  mounted() {
    this.loadDraft();
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}

.create-event-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-blue);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.event-form {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.form-section {
  margin-bottom: 40px;
}

.form-section h2 {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--accent-purple);
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: var(--slogan-gray);
}

.required::after {
  content: ' *';
  color: #ff6b6b;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(0, 207, 255, 0.2);
}

.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #ff6b6b;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.char-count {
  text-align: right;
  font-size: 0.8rem;
  color: var(--icon-color);
  margin-top: 4px;
}

.error-message {
  color: #ff6b6b;
  font-size: 0.8rem;
  margin-top: 4px;
  display: block;
}

.price-input {
  position: relative;
  display: inline-block;
  width: 150px;
}

.currency {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--slogan-gray);
  pointer-events: none;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  margin-bottom: 0;
}

.form-checkbox {
  width: auto;
  margin: 0;
}

.checkbox-text {
  color: var(--slogan-gray);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-cancel,
.btn-draft,
.btn-create {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.btn-cancel {
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-draft {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-draft:hover:not(:disabled) {
  background: var(--sweater-purple);
}

.btn-create {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-create:hover:not(:disabled) {
  background: var(--accent-purple);
  transform: translateY(-2px);
}

.btn-create:disabled,
.btn-draft:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
  transform: none;
}

/* Responsive */
@media (max-width: 768px) {
  .create-event-container {
    padding: 16px;
  }
  
  .event-form {
    padding: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .page-header h1 {
    font-size: 1.5rem;
  }
}
</style>
