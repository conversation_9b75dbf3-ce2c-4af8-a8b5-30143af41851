/** @format */

import Axios from "./caller.service";
import store from "@/store";

let login = (credentials) => {
  return Axios.post("/api/login/", credentials);
};

let logout = () => {
  // Supprimer toutes les données utilisateur du localStorage
  localStorage.removeItem("token");
  localStorage.removeItem("user");
  localStorage.removeItem("userProfile");
  localStorage.removeItem("userPreferences");

  // Mettre à jour le store Vuex
  store.commit("setLoggedIn", false);
  store.commit("setUser", null);
};

let saveToken = (token) => {
  localStorage.setItem("token", token);
};

let getToken = () => {
  return localStorage.getItem("token");
};

let saveUser = (user) => {
  localStorage.setItem("user", JSON.stringify(user));
  store.commit("setUser", user);
};

let getUser = () => {
  const userStr = localStorage.getItem("user");
  return userStr ? JSON.parse(userStr) : null;
};

let saveUserProfile = (profile) => {
  localStorage.setItem("userProfile", JSON.stringify(profile));
};

let getUserProfile = () => {
  const profileStr = localStorage.getItem("userProfile");
  return profileStr ? JSON.parse(profileStr) : null;
};

let saveUserPreferences = (preferences) => {
  localStorage.setItem("userPreferences", JSON.stringify(preferences));
};

let getUserPreferences = () => {
  const preferencesStr = localStorage.getItem("userPreferences");
  return preferencesStr ? JSON.parse(preferencesStr) : {};
};

let isLogged = () => {
  let token = localStorage.getItem("token");
  return !!token;
};

let initializeAuth = () => {
  // Initialiser l'état d'authentification au démarrage de l'app
  const token = getToken();
  const user = getUser();

  if (token && user) {
    store.commit("setLoggedIn", true);
    store.commit("setUser", user);
    return true;
  } else {
    // Nettoyer si les données sont incohérentes
    logout();
    return false;
  }
};

let refreshUserData = async () => {
  // Récupérer les données utilisateur mises à jour depuis l'API
  try {
    const response = await Axios.get("/api/user/profile/");
    if (response.data) {
      saveUser(response.data);
      return response.data;
    }
  } catch (error) {
    console.error(
      "Erreur lors de la récupération des données utilisateur:",
      error
    );
    // Si l'API retourne une erreur d'authentification, déconnecter l'utilisateur
    if (error.response && error.response.status === 401) {
      logout();
    }
    throw error;
  }
};

export const accountService = {
  login,
  logout,
  saveToken,
  getToken,
  saveUser,
  getUser,
  saveUserProfile,
  getUserProfile,
  saveUserPreferences,
  getUserPreferences,
  isLogged,
  initializeAuth,
  refreshUserData,
};
