use anyhow::Result;
use axum::extract::ws::Message;
use serde_json;
use tokio::sync::mpsc;
use tracing::{error, debug};

use crate::models::WebSocketMessage;

#[derive(Clone)]
pub struct WebSocketConnection {
    sender: mpsc::UnboundedSender<WebSocketMessage>,
}

impl WebSocketConnection {
    pub fn new(sender: mpsc::UnboundedSender<WebSocketMessage>) -> Self {
        Self { sender }
    }
    
    pub async fn send_message(&self, message: WebSocketMessage) -> Result<()> {
        debug!("Envoi du message WebSocket: {:?}", message);
        
        if let Err(e) = self.sender.send(message) {
            error!("Erreur lors de l'envoi du message WebSocket: {}", e);
            return Err(anyhow::anyhow!("Connexion fermée"));
        }
        
        Ok(())
    }
    
    pub async fn send_raw_message(&self, message: Message) -> Result<()> {
        // Pour les messages raw comme Ping/Pong, nous devons les convertir
        // Cette méthode est utilisée pour les réponses directes aux pings
        match message {
            Message::Pong(data) => {
                debug!("Envoi d'un Pong");
                // Note: Dans une implémentation complète, nous aurions besoin
                // d'un canal séparé pour les messages raw
                Ok(())
            }
            _ => {
                debug!("Message raw non supporté: {:?}", message);
                Ok(())
            }
        }
    }
    
    pub fn is_connected(&self) -> bool {
        !self.sender.is_closed()
    }
}
