<template>
  <div class="credit-card-form">
    <div class="card-container">
      <!-- Aperçu de la carte -->
      <div class="card-preview" :class="{ 'flipped': showBack }">
        <div class="card-front">
          <div class="card-chip"></div>
          <div class="card-number">
            {{ formattedCardNumber }}
          </div>
          <div class="card-info">
            <div class="card-holder">
              <label>Titulaire</label>
              <div>{{ cardHolder || 'VOTRE NOM' }}</div>
            </div>
            <div class="card-expiry">
              <label>Expire</label>
              <div>{{ cardExpiry || 'MM/AA' }}</div>
            </div>
          </div>
          <div class="card-brand">
            <img v-if="cardBrand" :src="getBrandIcon(cardBrand)" :alt="cardBrand" />
          </div>
        </div>
        
        <div class="card-back">
          <div class="magnetic-strip"></div>
          <div class="cvc-strip">
            <div class="cvc-label">CVC</div>
            <div class="cvc-value">{{ cardCvc || '***' }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Formulaire -->
    <form @submit.prevent="handleSubmit" class="payment-form">
      <!-- Informations de facturation -->
      <div class="billing-section">
        <h3>Informations de facturation</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="cardholder-name">Nom du titulaire *</label>
            <input
              id="cardholder-name"
              v-model="billingDetails.name"
              type="text"
              required
              placeholder="Jean Dupont"
              @input="updateCardHolder"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="email">Email *</label>
            <input
              id="email"
              v-model="billingDetails.email"
              type="email"
              required
              placeholder="<EMAIL>"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="phone">Téléphone</label>
            <input
              id="phone"
              v-model="billingDetails.phone"
              type="tel"
              placeholder="+33 1 23 45 67 89"
            />
          </div>
        </div>
      </div>

      <!-- Adresse de facturation -->
      <div class="address-section">
        <h3>Adresse de facturation</h3>
        
        <div class="form-row">
          <div class="form-group">
            <label for="address-line1">Adresse *</label>
            <input
              id="address-line1"
              v-model="billingDetails.address.line1"
              type="text"
              required
              placeholder="123 Rue de la Paix"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="address-line2">Complément d'adresse</label>
            <input
              id="address-line2"
              v-model="billingDetails.address.line2"
              type="text"
              placeholder="Appartement, étage, etc."
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group half">
            <label for="city">Ville *</label>
            <input
              id="city"
              v-model="billingDetails.address.city"
              type="text"
              required
              placeholder="Paris"
            />
          </div>
          
          <div class="form-group half">
            <label for="postal-code">Code postal *</label>
            <input
              id="postal-code"
              v-model="billingDetails.address.postal_code"
              type="text"
              required
              placeholder="75001"
            />
          </div>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="country">Pays *</label>
            <select id="country" v-model="billingDetails.address.country" required>
              <option value="FR">France</option>
              <option value="BE">Belgique</option>
              <option value="CH">Suisse</option>
              <option value="CA">Canada</option>
              <option value="US">États-Unis</option>
            </select>
          </div>
        </div>
      </div>

      <!-- Informations de carte -->
      <div class="card-section">
        <h3>Informations de carte</h3>
        
        <div class="form-group">
          <label for="card-element">Numéro de carte *</label>
          <div
            id="card-element"
            ref="cardElement"
            class="stripe-element"
            @focus="showBack = false"
          ></div>
          <div v-if="cardError" class="error-message">
            {{ cardError }}
          </div>
        </div>

        <!-- Option pour sauvegarder la carte -->
        <div class="form-group" v-if="allowSaveCard">
          <label class="checkbox-label">
            <input
              type="checkbox"
              v-model="saveCard"
            />
            <span class="checkmark"></span>
            Sauvegarder cette carte pour les futurs paiements
          </label>
        </div>
      </div>

      <!-- Résumé du paiement -->
      <div class="payment-summary" v-if="amount">
        <h3>Résumé</h3>
        <div class="summary-line">
          <span>{{ description || 'Paiement' }}</span>
          <span class="amount">{{ formatAmount(amount) }} €</span>
        </div>
        <div class="summary-line total">
          <span>Total</span>
          <span class="amount">{{ formatAmount(amount) }} €</span>
        </div>
      </div>

      <!-- Boutons d'action -->
      <div class="form-actions">
        <button
          type="button"
          @click="$emit('cancel')"
          class="btn-cancel"
          :disabled="processing"
        >
          Annuler
        </button>
        
        <button
          type="submit"
          class="btn-pay"
          :disabled="!isFormValid || processing"
        >
          <span v-if="processing" class="spinner"></span>
          <span v-else>
            {{ submitText || `Payer ${formatAmount(amount)} €` }}
          </span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import stripeService from '@/services/stripe'

export default {
  name: 'CreditCardForm',
  
  props: {
    amount: {
      type: Number,
      default: 0
    },
    
    description: {
      type: String,
      default: ''
    },
    
    submitText: {
      type: String,
      default: ''
    },
    
    allowSaveCard: {
      type: Boolean,
      default: true
    },
    
    processing: {
      type: Boolean,
      default: false
    }
  },
  
  emits: ['submit', 'cancel', 'card-change'],
  
  data() {
    return {
      cardElement: null,
      cardError: null,
      cardComplete: false,
      cardBrand: null,
      cardNumber: '',
      cardHolder: '',
      cardExpiry: '',
      cardCvc: '',
      showBack: false,
      saveCard: false,
      
      billingDetails: {
        name: '',
        email: '',
        phone: '',
        address: {
          line1: '',
          line2: '',
          city: '',
          postal_code: '',
          country: 'FR'
        }
      }
    }
  },
  
  computed: {
    formattedCardNumber() {
      if (!this.cardNumber) {
        return '**** **** **** ****'
      }
      return this.cardNumber.replace(/(.{4})/g, '$1 ').trim()
    },
    
    isFormValid() {
      return this.cardComplete && 
             this.billingDetails.name && 
             this.billingDetails.email &&
             this.billingDetails.address.line1 &&
             this.billingDetails.address.city &&
             this.billingDetails.address.postal_code
    }
  },
  
  async mounted() {
    await this.initializeStripe()
  },
  
  beforeUnmount() {
    if (this.cardElement) {
      this.cardElement.destroy()
    }
  },
  
  methods: {
    async initializeStripe() {
      try {
        await stripeService.initialize()
        this.createCardElement()
      } catch (error) {
        console.error('Erreur initialisation Stripe:', error)
        this.cardError = 'Erreur lors de l\'initialisation du système de paiement'
      }
    },
    
    createCardElement() {
      const elements = stripeService.createElements()
      
      this.cardElement = elements.create('card', {
        style: {
          base: {
            fontSize: '16px',
            color: '#424770',
            fontFamily: 'Inter, system-ui, sans-serif',
            '::placeholder': {
              color: '#aab7c4'
            }
          },
          invalid: {
            color: '#9e2146'
          }
        },
        hidePostalCode: true
      })
      
      this.cardElement.mount(this.$refs.cardElement)
      
      // Écouter les événements
      this.cardElement.on('change', this.handleCardChange)
      this.cardElement.on('focus', () => this.showBack = false)
    },
    
    handleCardChange(event) {
      this.cardError = event.error ? event.error.message : null
      this.cardComplete = event.complete
      this.cardBrand = event.brand
      
      // Simuler les données pour l'aperçu
      if (event.complete) {
        this.cardNumber = '**** **** **** ' + (event.brand === 'amex' ? '****' : '****')
      }
      
      this.$emit('card-change', {
        complete: event.complete,
        error: event.error,
        brand: event.brand
      })
    },
    
    updateCardHolder() {
      this.cardHolder = this.billingDetails.name.toUpperCase()
    },
    
    async handleSubmit() {
      if (!this.isFormValid) {
        return
      }
      
      try {
        const paymentData = {
          cardElement: this.cardElement,
          billingDetails: this.billingDetails,
          amount: this.amount,
          saveCard: this.saveCard
        }
        
        this.$emit('submit', paymentData)
      } catch (error) {
        this.cardError = stripeService.formatError(error)
      }
    },
    
    getBrandIcon(brand) {
      const icons = {
        visa: '/images/cards/visa.svg',
        mastercard: '/images/cards/mastercard.svg',
        amex: '/images/cards/amex.svg',
        discover: '/images/cards/discover.svg',
        diners: '/images/cards/diners.svg',
        jcb: '/images/cards/jcb.svg',
        unionpay: '/images/cards/unionpay.svg'
      }
      return icons[brand] || '/images/cards/generic.svg'
    },
    
    formatAmount(amount) {
      return new Intl.NumberFormat('fr-FR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(amount)
    }
  }
}
</script>

<style scoped>
/* Variables CSS */
:root {
  --primary-color: #007bff;
  --error-color: #dc3545;
  --success-color: #28a745;
  --border-color: #e1e5e9;
  --text-color: #495057;
  --bg-color: #f8f9fa;
}

.credit-card-form {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

/* Aperçu de la carte */
.card-container {
  perspective: 1000px;
  margin-bottom: 30px;
}

.card-preview {
  width: 350px;
  height: 220px;
  margin: 0 auto;
  position: relative;
  transform-style: preserve-3d;
  transition: transform 0.6s;
}

.card-preview.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.card-back {
  transform: rotateY(180deg);
}

.card-chip {
  width: 40px;
  height: 30px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  border-radius: 5px;
  margin-bottom: 20px;
}

.card-number {
  font-size: 22px;
  font-family: 'Courier New', monospace;
  letter-spacing: 2px;
  margin-bottom: 20px;
}

.card-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.card-holder,
.card-expiry {
  font-size: 12px;
}

.card-holder label,
.card-expiry label {
  display: block;
  opacity: 0.7;
  margin-bottom: 5px;
}

.card-brand {
  position: absolute;
  top: 20px;
  right: 20px;
}

.card-brand img {
  height: 30px;
}

.magnetic-strip {
  width: 100%;
  height: 40px;
  background: #333;
  margin: 20px 0;
}

.cvc-strip {
  background: white;
  color: #333;
  padding: 10px;
  border-radius: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Formulaire */
.payment-form {
  background: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.billing-section,
.address-section,
.card-section,
.payment-summary {
  margin-bottom: 30px;
}

.billing-section h3,
.address-section h3,
.card-section h3,
.payment-summary h3 {
  color: var(--text-color);
  margin-bottom: 20px;
  font-size: 18px;
  font-weight: 600;
}

.form-row {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-group.half {
  flex: 0.5;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-color);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: var(--primary-color);
}

.stripe-element {
  padding: 12px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: white;
  transition: border-color 0.3s ease;
}

.stripe-element:focus-within {
  border-color: var(--primary-color);
}

.error-message {
  color: var(--error-color);
  font-size: 14px;
  margin-top: 8px;
}

/* Checkbox personnalisé */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color);
  border-radius: 4px;
  margin-right: 10px;
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
}

/* Résumé du paiement */
.summary-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 10px 0;
}

.summary-line.total {
  border-top: 2px solid var(--border-color);
  font-weight: 600;
  font-size: 18px;
}

.amount {
  font-weight: 600;
  color: var(--primary-color);
}

/* Boutons d'action */
.form-actions {
  display: flex;
  gap: 15px;
  justify-content: flex-end;
  margin-top: 30px;
}

.btn-cancel,
.btn-pay {
  padding: 12px 30px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
}

.btn-cancel {
  background: var(--bg-color);
  color: var(--text-color);
  border: 2px solid var(--border-color);
}

.btn-cancel:hover {
  background: var(--border-color);
}

.btn-pay {
  background: var(--primary-color);
  color: white;
}

.btn-pay:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.btn-pay:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .credit-card-form {
    padding: 10px;
  }
  
  .card-preview {
    width: 300px;
    height: 190px;
  }
  
  .payment-form {
    padding: 20px;
  }
  
  .form-row {
    flex-direction: column;
  }
  
  .form-group.half {
    flex: 1;
  }
  
  .form-actions {
    flex-direction: column;
  }
  
  .btn-cancel,
  .btn-pay {
    width: 100%;
  }
}
</style>
