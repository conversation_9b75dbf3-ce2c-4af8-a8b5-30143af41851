/**
 * Service STT Simple et Indépendant
 * Reconnaissance vocale SANS dépendances externes
 * Fonctionne de manière autonome
 */

class SimpleSpeechToTextService {
  constructor() {
    this.recognition = null;
    this.isListening = false;
    this.isManualStop = false;
    this.restartTimeout = null;
    
    console.log('🎤 Service STT Simple initialisé');
  }

  /**
   * Démarrer l'écoute vocale simple
   * @param {Object} options - Options d'écoute
   */
  startListening(options = {}) {
    return new Promise((resolve, reject) => {
      try {
        // Vérifier le support
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
          throw new Error('API Speech Recognition non supportée');
        }

        // Arrêter toute écoute en cours
        this.stopListening();

        console.log('🎤 Démarrage STT simple...');

        // Créer l'instance de reconnaissance
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        // Configuration simple et stable
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = options.language || 'fr-FR';
        this.recognition.maxAlternatives = 1;

        // Reset des variables
        this.isManualStop = false;

        // Événement de démarrage
        this.recognition.onstart = () => {
          this.isListening = true;
          console.log('✅ STT simple démarré');
          if (options.onStart) options.onStart();
        };

        // Événement de résultat
        this.recognition.onresult = (event) => {
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;
            
            if (event.results[i].isFinal) {
              console.log('📝 STT résultat final:', transcript);
              if (options.onResult) {
                options.onResult(transcript.trim(), '');
              }
            } else {
              console.log('📝 STT résultat partiel:', transcript);
              if (options.onInterim) {
                options.onInterim(transcript);
              }
            }
          }
        };

        // Événement d'erreur
        this.recognition.onerror = (event) => {
          console.warn('⚠️ Erreur STT simple:', event.error);
          
          // Redémarrage automatique pour certaines erreurs
          if ((event.error === 'no-speech' || event.error === 'audio-capture') && !this.isManualStop) {
            console.log('🔄 Redémarrage automatique STT...');
            this.restartTimeout = setTimeout(() => {
              if (this.isListening && !this.isManualStop) {
                this.recognition.start();
              }
            }, 1000);
          } else {
            this.isListening = false;
            if (options.onError) options.onError(event.error);
          }
        };

        // Événement de fin
        this.recognition.onend = () => {
          console.log('🛑 STT simple terminé');
          
          // Redémarrage automatique si pas d'arrêt manuel
          if (!this.isManualStop && this.isListening) {
            console.log('🔄 Redémarrage automatique STT...');
            this.restartTimeout = setTimeout(() => {
              if (this.isListening && !this.isManualStop) {
                this.recognition.start();
              }
            }, 500);
          } else {
            this.isListening = false;
            if (options.onEnd) options.onEnd();
          }
        };

        // Démarrer la reconnaissance
        this.recognition.start();
        resolve('STT simple démarré');

      } catch (error) {
        console.error('❌ Erreur STT simple:', error);
        this.isListening = false;
        reject(error);
      }
    });
  }

  /**
   * Arrêter l'écoute vocale
   */
  stopListening() {
    console.log('🛑 Arrêt STT simple...');
    
    this.isManualStop = true;
    this.isListening = false;
    
    // Nettoyer le timeout
    if (this.restartTimeout) {
      clearTimeout(this.restartTimeout);
      this.restartTimeout = null;
    }
    
    // Arrêter la reconnaissance
    if (this.recognition) {
      try {
        this.recognition.stop();
      } catch (error) {
        console.warn('⚠️ Erreur arrêt STT:', error.message);
      }
    }
    
    console.log('✅ STT simple arrêté');
  }

  /**
   * Vérifier si l'écoute est active
   */
  isListeningActive() {
    return this.isListening;
  }

  /**
   * Vérifier le support de l'API
   */
  isSupported() {
    return !!(('webkitSpeechRecognition' in window) || ('SpeechRecognition' in window));
  }

  /**
   * Obtenir le statut du service
   */
  getStatus() {
    return {
      isListening: this.isListening,
      isSupported: this.isSupported(),
      hasRecognition: !!this.recognition
    };
  }

  /**
   * Réinitialiser le service
   */
  reset() {
    console.log('🔄 Reset STT simple...');
    this.stopListening();
    this.recognition = null;
  }
}

// Export de l'instance unique
export const simpleSttService = new SimpleSpeechToTextService();
export default simpleSttService;
