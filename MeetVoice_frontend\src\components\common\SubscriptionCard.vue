<template>
  <div class="subscription-card">
    <div class="subscription-header">
      <h3>Votre abonnement</h3>
      <span class="subscription-status" :class="statusClass">{{ statusText }}</span>
    </div>
    
    <div class="subscription-content">
      <div v-if="currentSubscription" class="current-plan">
        <div class="plan-info">
          <h4>{{ currentSubscription.name }}</h4>
          <p class="plan-price">{{ currentSubscription.price }}€/mois</p>
          <p class="plan-description">{{ currentSubscription.description }}</p>
        </div>
        
        <div class="plan-features">
          <h5>Fonctionnalités incluses :</h5>
          <ul>
            <li v-for="feature in currentSubscription.features" :key="feature">
              <span class="feature-icon">✓</span>
              {{ feature }}
            </li>
          </ul>
        </div>
        
        <div class="subscription-actions">
          <button v-if="canUpgrade" @click="showUpgradeOptions" class="btn-upgrade">
            Améliorer
          </button>
          <button @click="manageSubscription" class="btn-manage">
            Gérer l'abonnement
          </button>
        </div>
        
        <div class="subscription-details">
          <p><strong>Prochaine facturation :</strong> {{ formatDate(currentSubscription.nextBilling) }}</p>
          <p><strong>Méthode de paiement :</strong> {{ currentSubscription.paymentMethod }}</p>
        </div>
      </div>
      
      <div v-else class="no-subscription">
        <div class="no-subscription-icon">💳</div>
        <h4>Aucun abonnement actif</h4>
        <p>Découvrez nos plans premium pour accéder à toutes les fonctionnalités</p>
        <button @click="showPlans" class="btn-subscribe">
          Voir les plans
        </button>
      </div>
    </div>
    
    <!-- Modal des plans d'abonnement -->
    <div v-if="showPlansModal" class="plans-modal" @click="closePlansModal">
      <div class="plans-modal-content" @click.stop>
        <div class="plans-modal-header">
          <h3>Choisissez votre plan</h3>
          <button @click="closePlansModal" class="btn-close">×</button>
        </div>
        
        <div class="plans-grid">
          <div 
            v-for="plan in availablePlans" 
            :key="plan.id" 
            class="plan-card"
            :class="{ 'recommended': plan.recommended }"
          >
            <div v-if="plan.recommended" class="recommended-badge">Recommandé</div>
            <h4>{{ plan.name }}</h4>
            <div class="plan-price">
              <span class="price">{{ plan.price }}€</span>
              <span class="period">/mois</span>
            </div>
            <p class="plan-description">{{ plan.description }}</p>
            
            <ul class="plan-features">
              <li v-for="feature in plan.features" :key="feature">
                <span class="feature-icon">✓</span>
                {{ feature }}
              </li>
            </ul>
            
            <button @click="selectPlan(plan)" class="btn-select-plan">
              {{ currentSubscription?.id === plan.id ? 'Plan actuel' : 'Choisir ce plan' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'SubscriptionCard',
  
  data() {
    return {
      showPlansModal: false,
      availablePlans: [
        {
          id: 'basic',
          name: 'Basic',
          price: 9.99,
          description: 'Parfait pour commencer',
          features: [
            'Profil complet',
            'Messages illimités',
            'Recherche avancée',
            'Support email'
          ]
        },
        {
          id: 'premium',
          name: 'Premium',
          price: 19.99,
          description: 'Le plus populaire',
          recommended: true,
          features: [
            'Toutes les fonctionnalités Basic',
            'Appels vidéo illimités',
            'Profil mis en avant',
            'Statistiques détaillées',
            'Support prioritaire'
          ]
        },
        {
          id: 'vip',
          name: 'VIP',
          price: 39.99,
          description: 'L\'expérience ultime',
          features: [
            'Toutes les fonctionnalités Premium',
            'Accès aux événements exclusifs',
            'Concierge personnel',
            'Vérification prioritaire',
            'Support 24/7'
          ]
        }
      ]
    }
  },
  
  computed: {
    ...mapState('payment', ['currentSubscription']),
    
    statusClass() {
      if (!this.currentSubscription) return 'inactive'
      
      switch (this.currentSubscription.status) {
        case 'active': return 'active'
        case 'cancelled': return 'cancelled'
        case 'expired': return 'expired'
        default: return 'inactive'
      }
    },
    
    statusText() {
      if (!this.currentSubscription) return 'Inactif'
      
      switch (this.currentSubscription.status) {
        case 'active': return 'Actif'
        case 'cancelled': return 'Annulé'
        case 'expired': return 'Expiré'
        default: return 'Inactif'
      }
    },
    
    canUpgrade() {
      if (!this.currentSubscription) return true
      return this.currentSubscription.id !== 'vip'
    }
  },
  
  methods: {
    ...mapActions('payment', ['loadCurrentSubscription', 'cancelSubscription']),
    
    showPlans() {
      this.showPlansModal = true
    },
    
    closePlansModal() {
      this.showPlansModal = false
    },
    
    showUpgradeOptions() {
      this.showPlansModal = true
    },
    
    async selectPlan(plan) {
      try {
        // Rediriger vers la page de checkout avec le plan sélectionné
        this.$router.push({
          name: 'Checkout',
          params: { planId: plan.id }
        })
      } catch (error) {
        console.error('Erreur lors de la sélection du plan:', error)
      }
    },
    
    manageSubscription() {
      // Rediriger vers la page de gestion d'abonnement
      this.$router.push({ name: 'ManageSubscription' })
    },
    
    formatDate(dateString) {
      if (!dateString) return 'Non défini'
      
      const date = new Date(dateString)
      return date.toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    }
  },
  
  mounted() {
    // Charger l'abonnement actuel
    this.loadCurrentSubscription()
  }
}
</script>

<style scoped>
.subscription-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.subscription-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.subscription-status {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
}

.subscription-status.active {
  background: #d4edda;
  color: #155724;
}

.subscription-status.cancelled {
  background: #f8d7da;
  color: #721c24;
}

.subscription-status.expired {
  background: #fff3cd;
  color: #856404;
}

.subscription-status.inactive {
  background: #e2e3e5;
  color: #383d41;
}

.subscription-content {
  padding: 24px;
}

.current-plan {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.plan-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 20px;
  font-weight: 700;
}

.plan-price {
  margin: 0 0 8px 0;
  color: #007bff;
  font-size: 24px;
  font-weight: 700;
}

.plan-description {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.plan-features h5 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.feature-icon {
  color: #28a745;
  font-weight: bold;
}

.subscription-actions {
  display: flex;
  gap: 12px;
}

.btn-upgrade,
.btn-manage {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-upgrade {
  background: #007bff;
  color: white;
}

.btn-upgrade:hover {
  background: #0056b3;
}

.btn-manage {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
}

.btn-manage:hover {
  background: #e9ecef;
}

.subscription-details {
  padding-top: 16px;
  border-top: 1px solid #eee;
}

.subscription-details p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #666;
}

.no-subscription {
  text-align: center;
  padding: 20px;
}

.no-subscription-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.no-subscription h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.no-subscription p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 14px;
}

.btn-subscribe {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-subscribe:hover {
  background: #0056b3;
}

/* Modal des plans */
.plans-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.plans-modal-content {
  background: white;
  border-radius: 12px;
  max-width: 900px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.plans-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #eee;
}

.plans-modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #666;
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  padding: 24px;
}

.plan-card {
  border: 2px solid #eee;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  position: relative;
  transition: all 0.3s ease;
}

.plan-card:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.2);
}

.plan-card.recommended {
  border-color: #007bff;
  background: #f8f9fa;
}

.recommended-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  background: #007bff;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.plan-card h4 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.plan-card .plan-price {
  margin-bottom: 16px;
}

.plan-card .price {
  font-size: 32px;
  font-weight: 700;
  color: #007bff;
}

.plan-card .period {
  font-size: 16px;
  color: #666;
}

.plan-card .plan-description {
  margin-bottom: 20px;
  color: #666;
  font-size: 14px;
}

.plan-card .plan-features {
  text-align: left;
  margin-bottom: 24px;
}

.plan-card .plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-card .plan-features li {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
  color: #666;
}

.btn-select-plan {
  width: 100%;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-select-plan:hover {
  background: #0056b3;
}

.btn-select-plan:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

@media (max-width: 768px) {
  .plans-grid {
    grid-template-columns: 1fr;
  }
  
  .subscription-actions {
    flex-direction: column;
  }
}
</style>
