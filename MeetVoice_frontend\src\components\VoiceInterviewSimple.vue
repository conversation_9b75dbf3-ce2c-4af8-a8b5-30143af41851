<template>
  <div class="voice-interview">
    <div class="interview-container">
      <!-- En-tête -->
      <div class="interview-header">
        <h2>Interview Vocale avec <PERSON></h2>
        <p>Répondez aux questions de <PERSON> pour créer votre profil</p>
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>
        <span class="progress-text">{{ currentQuestionIndex + 1 }} / {{ questions.length }}</span>
      </div>

      <!-- Zone de conversation -->
      <div class="conversation-area">
        <!-- Question de Sophie -->
        <div class="sophie-message">
          <div class="sophie-avatar">🎤</div>
          <div class="message-content">
            <h3>Sophie</h3>
            <p>{{ currentQuestion.text }}</p>
            <button @click="speakQuestion" class="repeat-button" title="Répéter la question">
              🔊 Répéter
            </button>
          </div>
        </div>

        <!-- Réponse utilisateur -->
        <div v-if="currentAnswer" class="user-message">
          <div class="message-content">
            <h3>Vous</h3>
            <p>{{ currentAnswer }}</p>
          </div>
          <div class="user-avatar">👤</div>
        </div>

        <!-- Zone de transcription en temps réel -->
        <div v-if="isListening" class="transcription-area">
          <div class="listening-indicator">
            <div class="pulse"></div>
            <span>Sophie vous écoute...</span>
          </div>
          <p v-if="currentTranscript" class="transcript">{{ currentTranscript }}</p>
        </div>
      </div>

      <!-- Contrôles -->
      <div class="controls">
        <button 
          @click="toggleListening" 
          class="mic-button"
          :class="{ 'listening': isListening, 'disabled': !isSupported }"
          :disabled="!isSupported"
        >
          <span class="mic-icon">{{ isListening ? '🛑' : '🎤' }}</span>
          <span class="mic-text">{{ isListening ? 'Arrêter' : 'Répondre' }}</span>
        </button>

        <button 
          v-if="currentAnswer" 
          @click="nextQuestion" 
          class="next-button"
        >
          Question suivante
        </button>

        <button 
          @click="skipQuestion" 
          class="skip-button"
          v-if="!currentQuestion.required"
        >
          Passer
        </button>
      </div>

      <!-- Erreurs -->
      <div v-if="error" class="error-message">
        <span class="error-icon">⚠️</span>
        {{ error }}
      </div>

      <!-- Fin de l'interview -->
      <div v-if="isCompleted" class="completion-message">
        <h3>🎉 Interview terminée !</h3>
        <p>Merci d'avoir complété votre profil vocal avec Sophie.</p>
        <button @click="saveAndFinish" class="finish-button">
          Terminer l'inscription
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VoiceInterviewSimple',
  
  props: {
    userEmail: {
      type: String,
      required: true
    }
  },
  
  emits: ['answers-saved', 'interview-completed'],
  
  data() {
    return {
      currentQuestionIndex: 0,
      currentAnswer: '',
      currentTranscript: '',
      isListening: false,
      isCompleted: false,
      isSophieSpeaking: false,
      error: '',
      recognition: null,
      answers: [],
      
      questions: [
        { id: 'username', text: 'Quel est votre nom d\'utilisateur ?', required: true },
        { id: 'lastname', text: 'Quel est votre nom de famille ?', required: true },
        { id: 'firstname', text: 'Quel est votre prénom ?', required: true },
        { id: 'birthdate', text: 'Quelle est votre date de naissance ?', required: true },
        { id: 'gender', text: 'Quel est votre genre ?', required: true },
        { id: 'children', text: 'Avez-vous des enfants ?', required: false },
        { id: 'relationship_type', text: 'Quel type de relation recherchez-vous ? Amical, amoureux ou libertin ?', required: true },
        { id: 'height', text: 'Quelle est votre taille en centimètres ?', required: false },
        { id: 'weight', text: 'Quel est votre poids en kilogrammes ?', required: false },
        { id: 'profession', text: 'Quelle est votre profession ?', required: false },
        { id: 'religion', text: 'Quelle est votre religion ou vos croyances ?', required: false },
        { id: 'languages', text: 'Quelles langues parlez-vous ?', required: false },
        { id: 'movie_genre', text: 'Quel est votre genre de film préféré ?', required: false },
        { id: 'music_genre', text: 'Quel genre de musique aimez-vous ?', required: false },
        { id: 'personality', text: 'Comment décririez-vous votre personnalité ?', required: false },
        { id: 'hobbies', text: 'Quels sont vos loisirs et centres d\'intérêt ?', required: false },
        { id: 'clothing_style', text: 'Comment décririez-vous votre style vestimentaire ?', required: false },
        { id: 'meeting_preference', text: 'Préférez-vous les rencontres en ligne ou en personne ?', required: false },
        { id: 'education', text: 'Quel est votre niveau d\'éducation ?', required: false },
        { id: 'bio', text: 'Parlez-nous un peu de vous, ce qui vous rend unique.', required: false }
      ]
    }
  },
  
  computed: {
    currentQuestion() {
      return this.questions[this.currentQuestionIndex] || {}
    },
    
    progressPercentage() {
      return (this.currentQuestionIndex / this.questions.length) * 100
    },
    
    isSupported() {
      return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window
    }
  },
  
  mounted() {
    if (!this.isSupported) {
      this.error = 'La reconnaissance vocale n\'est pas supportée par votre navigateur'
    }

    // Attendre un peu que les voix se chargent, puis dire la première question
    setTimeout(() => {
      this.speakQuestion()
    }, 1000)
  },
  
  methods: {
    async speakQuestion() {
      // Essayer d'abord l'API Sophie TTS, puis fallback sur la synthèse navigateur
      try {
        // Tentative avec l'API Sophie TTS (backend MeetVoice)
        const response = await fetch('http://127.0.0.1:8000/api/synthesize/', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            text: this.currentQuestion.text,
            voice_id: 'sophie'
          })
        })

        if (response.ok) {
          const audioBlob = await response.blob()
          const audioUrl = URL.createObjectURL(audioBlob)
          const audio = new Audio(audioUrl)

          // Configuration audio
          audio.volume = 0.8
          audio.preload = 'auto'

          // Gestion des événements
          audio.onloadstart = () => {
            console.log('🔊 Chargement audio Sophie...')
          }

          audio.oncanplay = () => {
            console.log('🔊 Audio Sophie prêt')
          }

          audio.onended = () => {
            console.log('🔊 Sophie a fini de parler')
            // Nettoyer l'URL blob
            URL.revokeObjectURL(audioUrl)
          }

          audio.onerror = (error) => {
            console.error('❌ Erreur lecture audio Sophie:', error)
            // Fallback sur synthèse navigateur en cas d'erreur
            throw new Error('Erreur lecture audio Sophie')
          }

          // Jouer l'audio
          await audio.play()
          console.log('🔊 Sophie TTS backend utilisée avec succès!')
          return
        } else {
          console.log('⚠️ API Sophie TTS réponse non-OK:', response.status, response.statusText)
        }
      } catch (error) {
        console.log('⚠️ API Sophie TTS non disponible, utilisation de la synthèse navigateur')
      }

      // Fallback : Utiliser la synthèse vocale du navigateur
      try {
        if ('speechSynthesis' in window) {
          // Arrêter toute synthèse en cours
          window.speechSynthesis.cancel()

          const utterance = new SpeechSynthesisUtterance(this.currentQuestion.text)

          // Configuration de la voix
          utterance.lang = 'fr-FR'
          utterance.rate = 0.9 // Vitesse légèrement ralentie
          utterance.pitch = 1.1 // Ton légèrement plus aigu pour Sophie
          utterance.volume = 0.8

          // Essayer de trouver une voix française féminine
          const voices = window.speechSynthesis.getVoices()
          const frenchVoice = voices.find(voice =>
            voice.lang.startsWith('fr') && voice.name.toLowerCase().includes('female')
          ) || voices.find(voice => voice.lang.startsWith('fr'))

          if (frenchVoice) {
            utterance.voice = frenchVoice
            console.log('🔊 Voix française trouvée:', frenchVoice.name)
          }

          utterance.onstart = () => {
            console.log('🔊 Sophie commence à parler')
          }

          utterance.onend = () => {
            console.log('🔊 Sophie a fini de parler')
          }

          utterance.onerror = (event) => {
            console.error('❌ Erreur synthèse vocale:', event.error)
          }

          // Démarrer la synthèse
          window.speechSynthesis.speak(utterance)
          console.log('🔊 Synthèse vocale navigateur utilisée')
        } else {
          console.log('⚠️ Synthèse vocale non supportée par le navigateur')
        }
      } catch (error) {
        console.error('❌ Erreur synthèse vocale navigateur:', error)
      }
    },
    
    toggleListening() {
      if (this.isListening) {
        this.stopListening()
      } else {
        this.startListening()
      }
    },
    
    startListening() {
      if (!this.isSupported) {
        this.error = 'Reconnaissance vocale non supportée'
        return
      }
      
      try {
        // Créer une nouvelle instance de reconnaissance vocale
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
        this.recognition = new SpeechRecognition()
        
        // Configuration
        this.recognition.lang = 'fr-FR'
        this.recognition.continuous = false
        this.recognition.interimResults = true
        this.recognition.maxAlternatives = 1
        
        // Événements
        this.recognition.onstart = () => {
          console.log('🎤 Reconnaissance vocale démarrée')
          this.isListening = true
          this.error = ''
          this.currentTranscript = ''
        }
        
        this.recognition.onresult = (event) => {
          let finalText = ''
          let interimText = ''
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const result = event.results[i]
            const transcript = result[0].transcript
            
            if (result.isFinal) {
              finalText += transcript
            } else {
              interimText += transcript
            }
          }
          
          // Afficher le texte interim
          if (interimText) {
            this.currentTranscript = interimText
          }
          
          // Si on a un texte final, l'utiliser comme réponse
          if (finalText && finalText.trim()) {
            this.currentAnswer = finalText.trim()
            this.currentTranscript = finalText.trim()
            this.recognition.stop()
          }
        }
        
        this.recognition.onerror = (event) => {
          console.error('❌ Erreur reconnaissance vocale:', event.error)
          this.isListening = false
          
          switch (event.error) {
            case 'not-allowed':
              this.error = 'Permission microphone refusée. Autorisez l\'accès au microphone.'
              break
            case 'no-speech':
              this.error = 'Aucune parole détectée. Parlez plus fort.'
              break
            case 'audio-capture':
              this.error = 'Impossible d\'accéder au microphone.'
              break
            case 'network':
              this.error = 'Erreur réseau. Vérifiez votre connexion.'
              break
            default:
              this.error = `Erreur: ${event.error}`
          }
        }
        
        this.recognition.onend = () => {
          console.log('🛑 Reconnaissance vocale terminée')
          this.isListening = false
          
          // Si on a du texte transcrit mais pas encore de réponse
          if (this.currentTranscript && !this.currentAnswer) {
            this.currentAnswer = this.currentTranscript.trim()
          }
        }
        
        // Démarrer la reconnaissance
        this.recognition.start()
        
      } catch (error) {
        console.error('❌ Erreur lors du démarrage:', error)
        this.error = `Erreur: ${error.message}`
        this.isListening = false
      }
    },
    
    stopListening() {
      if (this.recognition) {
        this.recognition.stop()
      }
      this.isListening = false
    },
    
    nextQuestion() {
      // Sauvegarder la réponse
      this.answers.push({
        questionId: this.currentQuestion.id,
        question: this.currentQuestion.text,
        answer: this.currentAnswer
      })
      
      // Passer à la question suivante
      this.currentQuestionIndex++
      this.currentAnswer = ''
      this.currentTranscript = ''
      this.error = ''
      
      if (this.currentQuestionIndex >= this.questions.length) {
        this.completeInterview()
      } else {
        // Dire la nouvelle question
        this.speakQuestion()
      }
    },
    
    skipQuestion() {
      if (!this.currentQuestion.required) {
        this.answers.push({
          questionId: this.currentQuestion.id,
          question: this.currentQuestion.text,
          answer: '' // Réponse vide pour question sautée
        })
        
        this.currentQuestionIndex++
        this.currentAnswer = ''
        this.currentTranscript = ''
        this.error = ''
        
        if (this.currentQuestionIndex >= this.questions.length) {
          this.completeInterview()
        } else {
          this.speakQuestion()
        }
      }
    },
    
    completeInterview() {
      this.isCompleted = true
      console.log('🎉 Interview terminée!', this.answers)
      this.$emit('interview-completed', this.answers)
    },
    
    saveAndFinish() {
      this.$emit('answers-saved', this.answers)
    }
  },
  
  beforeUnmount() {
    if (this.isListening) {
      this.stopListening()
    }
  }
}
</script>

<style scoped>
.voice-interview {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.interview-container {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.interview-header {
  text-align: center;
  margin-bottom: 32px;
}

.interview-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
}

.interview-header p {
  margin: 0 0 20px 0;
  color: #666;
  font-size: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.conversation-area {
  margin-bottom: 32px;
  min-height: 200px;
}

.sophie-message,
.user-message {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 20px;
}

.user-message {
  flex-direction: row-reverse;
}

.sophie-avatar,
.user-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: #007bff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  flex-shrink: 0;
}

.user-avatar {
  background: #28a745;
}

.message-content {
  background: #f8f9fa;
  padding: 16px 20px;
  border-radius: 12px;
  max-width: 70%;
}

.user-message .message-content {
  background: #e3f2fd;
}

.message-content h3 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.message-content p {
  margin: 0 0 12px 0;
  color: #666;
  line-height: 1.5;
}

.repeat-button {
  background: #17a2b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 6px 12px;
  font-size: 12px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.repeat-button:hover {
  background: #138496;
}

.transcription-area {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 16px;
  margin-top: 20px;
}

.listening-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.pulse {
  width: 12px;
  height: 12px;
  background: #dc3545;
  border-radius: 50%;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.7; }
  100% { transform: scale(1); opacity: 1; }
}

.transcript {
  margin: 0;
  font-style: italic;
  color: #856404;
}

.controls {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.mic-button {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 50px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
}

.mic-button:hover:not(:disabled) {
  background: #0056b3;
  transform: translateY(-2px);
}

.mic-button.listening {
  background: #dc3545;
  animation: pulse-button 1.5s infinite;
}

.mic-button.disabled {
  background: #6c757d;
  cursor: not-allowed;
}

@keyframes pulse-button {
  0% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(220, 53, 69, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 53, 69, 0); }
}

.next-button,
.skip-button {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.skip-button {
  background: #6c757d;
}

.next-button:hover {
  background: #218838;
}

.skip-button:hover {
  background: #5a6268;
}

.error-message {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.completion-message {
  text-align: center;
  padding: 32px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 8px;
}

.completion-message h3 {
  margin: 0 0 16px 0;
  color: #155724;
  font-size: 24px;
}

.completion-message p {
  margin: 0 0 24px 0;
  color: #155724;
  font-size: 16px;
}

.finish-button {
  background: #28a745;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.finish-button:hover {
  background: #218838;
}

@media (max-width: 768px) {
  .voice-interview {
    padding: 10px;
  }
  
  .interview-container {
    padding: 20px;
  }
  
  .message-content {
    max-width: 85%;
  }
  
  .controls {
    flex-direction: column;
    align-items: center;
  }
}
</style>
