import { accountService } from '@/_services'

export default {
  namespaced: true,
  
  state: {
    token: null,
    isLoggedIn: false,
    user: null,
    error: null,
    loading: false,
    userType: localStorage.getItem('userType') || 'amical'
  },
  
  mutations: {
    setToken(state, token) {
      state.token = token
    },
    
    setLoggedIn(state, value) {
      state.isLoggedIn = value
    },
    
    setUser(state, user) {
      state.user = user
    },
    
    setError(state, error) {
      state.error = error
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    clearError(state) {
      state.error = null
    },
    
    setUserType(state, userType) {
      state.userType = userType
      localStorage.setItem('userType', userType)
    }
  },
  
  actions: {
    async login({ commit }, credentials) {
      try {
        commit('setLoading', true)
        commit('clearError')

        const response = await accountService.login(credentials)

        if (response.data.access) {
          commit('setToken', response.data.access)
          accountService.saveToken(response.data.access)

          if (response.data.user) {
            commit('setUser', response.data.user)
            accountService.saveUser(response.data.user)
          }

          commit('setLoggedIn', true)
          localStorage.setItem('isLoggedIn', 'true')
          
          return response.data
        } else {
          throw new Error('Token d\'accès non reçu')
        }
      } catch (error) {
        const errorMessage = error.response?.data?.message || error.message || 'Erreur de connexion'
        commit('setError', errorMessage)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async register({ commit }, userData) {
      try {
        commit('setLoading', true)
        commit('clearError')

        const response = await accountService.register(userData)
        
        if (response.data.access) {
          commit('setToken', response.data.access)
          accountService.saveToken(response.data.access)

          if (response.data.user) {
            commit('setUser', response.data.user)
            accountService.saveUser(response.data.user)
          }

          commit('setLoggedIn', true)
          localStorage.setItem('isLoggedIn', 'true')
          
          return response.data
        }
        
        return response.data
      } catch (error) {
        const errorMessage = error.response?.data?.message || error.message || 'Erreur d\'inscription'
        commit('setError', errorMessage)
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    logout({ commit }) {
      commit('setLoggedIn', false)
      commit('setUser', null)
      commit('setToken', null)
      commit('clearError')
      localStorage.removeItem('isLoggedIn')
      accountService.logout()
    },

    async initializeAuth({ commit }) {
      try {
        const isAuthenticated = accountService.initializeAuth()
        if (isAuthenticated) {
          const user = accountService.getUser()
          const token = accountService.getToken()

          commit('setLoggedIn', true)
          commit('setUser', user)
          commit('setToken', token)

          try {
            await accountService.refreshUserData()
          } catch (error) {
            console.warn('Impossible de rafraîchir les données utilisateur:', error)
          }
        }
        return isAuthenticated
      } catch (error) {
        console.error('Erreur lors de l\'initialisation de l\'authentification:', error)
        commit('setError', 'Erreur d\'initialisation')
        return false
      }
    },

    async refreshUserData({ commit }) {
      try {
        const userData = await accountService.refreshUserData()
        commit('setUser', userData)
        return userData
      } catch (error) {
        commit('setError', 'Impossible de mettre à jour les données utilisateur')
        throw error
      }
    },

    async updateProfile({ commit }, profileData) {
      try {
        commit('setLoading', true)
        const response = await accountService.updateProfile(profileData)
        commit('setUser', response.data)
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la mise à jour du profil')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    async changePassword({ commit }, passwordData) {
      try {
        commit('setLoading', true)
        await accountService.changePassword(passwordData)
        return true
      } catch (error) {
        commit('setError', 'Erreur lors du changement de mot de passe')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },

    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    token: state => state.token,
    user: state => state.user,
    isAuthenticated: state => state.isLoggedIn,
    error: state => state.error,
    loading: state => state.loading,
    userType: state => state.userType,
    
    userFullName: state => {
      if (state.user) {
        if (state.user.first_name && state.user.last_name) {
          return `${state.user.first_name} ${state.user.last_name}`
        }
        return state.user.username || state.user.email
      }
      return null
    }
  }
}
