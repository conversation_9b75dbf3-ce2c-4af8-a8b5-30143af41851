<template>
  <main class="community-container">
    <!-- Header -->
    <header class="community-header">
      <section class="header-content">
        <h1>Communauté</h1>
        <p class="subtitle">Explorez une communauté ouverte et bienveillante</p>
      </section>
      
      <nav class="community-actions" aria-label="Actions communauté">
        <button 
          @click="$router.push('/parties')"
          class="btn-parties"
        >
          <span aria-hidden="true">🎉</span>
          Soirées ({{ upcomingParties }})
        </button>
        
        <button 
          @click="showCreatePost = true"
          class="btn-create-post"
        >
          <span aria-hidden="true">✍️</span>
          Créer un post
        </button>
      </nav>
    </header>

    <!-- Statistiques de la communauté -->
    <section class="community-stats" aria-label="Statistiques">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-number">{{ totalMembers }}</div>
          <div class="stat-label">Membres actifs</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🌟</div>
          <div class="stat-number">{{ onlineMembers }}</div>
          <div class="stat-label">En ligne</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🎉</div>
          <div class="stat-number">{{ upcomingParties }}</div>
          <div class="stat-label">Soirées à venir</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💬</div>
          <div class="stat-number">{{ todayPosts }}</div>
          <div class="stat-label">Posts aujourd'hui</div>
        </div>
      </div>
    </section>

    <!-- Filtres et navigation -->
    <section class="content-navigation">
      <div class="nav-tabs">
        <button 
          @click="activeTab = 'feed'"
          :class="['nav-tab', { active: activeTab === 'feed' }]"
        >
          <span aria-hidden="true">📰</span>
          Fil d'actualité
        </button>
        
        <button 
          @click="activeTab = 'members'"
          :class="['nav-tab', { active: activeTab === 'members' }]"
        >
          <span aria-hidden="true">👥</span>
          Membres
        </button>
        
        <button 
          @click="activeTab = 'groups'"
          :class="['nav-tab', { active: activeTab === 'groups' }]"
        >
          <span aria-hidden="true">🔥</span>
          Groupes
        </button>
        
        <button 
          @click="activeTab = 'events'"
          :class="['nav-tab', { active: activeTab === 'events' }]"
        >
          <span aria-hidden="true">🎭</span>
          Événements
        </button>
      </div>
      
      <div class="content-filters" v-if="activeTab === 'feed'">
        <select v-model="feedFilter" class="filter-select">
          <option value="all">Tous les posts</option>
          <option value="photos">Photos</option>
          <option value="stories">Histoires</option>
          <option value="questions">Questions</option>
          <option value="experiences">Expériences</option>
        </select>
        
        <select v-model="sortBy" class="filter-select">
          <option value="recent">Plus récents</option>
          <option value="popular">Plus populaires</option>
          <option value="trending">Tendances</option>
        </select>
      </div>
    </section>

    <!-- Contenu principal -->
    <div class="community-content">
      <!-- Fil d'actualité -->
      <section v-if="activeTab === 'feed'" class="feed-section">
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>Chargement du fil d'actualité...</p>
        </div>
        
        <div v-else class="posts-container">
          <article 
            v-for="post in filteredPosts" 
            :key="post.id"
            class="post-card"
          >
            <header class="post-header">
              <div class="author-info">
                <img 
                  :src="post.author.avatar || '/default-avatar.jpg'"
                  :alt="`Avatar de ${post.author.username}`"
                  class="author-avatar"
                >
                <div class="author-details">
                  <h3 class="author-name">{{ post.author.username }}</h3>
                  <div class="post-meta">
                    <time class="post-time">{{ formatPostTime(post.createdAt) }}</time>
                    <span class="post-type">{{ getPostTypeLabel(post.type) }}</span>
                    <span v-if="post.isVerified" class="verified-badge">✓</span>
                  </div>
                </div>
              </div>
              
              <button class="btn-post-options" @click="showPostOptions(post.id)">
                <span aria-hidden="true">⋮</span>
              </button>
            </header>
            
            <div class="post-content">
              <p v-if="post.content" class="post-text">{{ post.content }}</p>
              
              <div v-if="post.media && post.media.length > 0" class="post-media">
                <div 
                  v-for="(media, index) in post.media" 
                  :key="index"
                  class="media-item"
                  @click="openMediaViewer(post.media, index)"
                >
                  <img 
                    v-if="media.type === 'image'"
                    :src="media.url"
                    :alt="media.alt || 'Image du post'"
                    class="post-image"
                    loading="lazy"
                  >
                  <video 
                    v-else-if="media.type === 'video'"
                    :src="media.url"
                    class="post-video"
                    controls
                    preload="metadata"
                  ></video>
                </div>
                
                <div v-if="post.media.length > 3" class="media-overlay">
                  +{{ post.media.length - 3 }} autres
                </div>
              </div>
              
              <div v-if="post.tags && post.tags.length > 0" class="post-tags">
                <span 
                  v-for="tag in post.tags" 
                  :key="tag"
                  class="post-tag"
                  @click="filterByTag(tag)"
                >
                  #{{ tag }}
                </span>
              </div>
            </div>
            
            <footer class="post-footer">
              <div class="post-stats">
                <span class="likes-count">{{ post.likesCount || 0 }} ❤️</span>
                <span class="comments-count">{{ post.commentsCount || 0 }} 💬</span>
                <span class="shares-count">{{ post.sharesCount || 0 }} 🔄</span>
              </div>
              
              <div class="post-actions">
                <button 
                  @click="toggleLike(post.id)"
                  :class="['btn-like', { liked: post.isLiked }]"
                >
                  <span aria-hidden="true">{{ post.isLiked ? '❤️' : '🤍' }}</span>
                  J'aime
                </button>
                
                <button 
                  @click="toggleComments(post.id)"
                  class="btn-comment"
                >
                  <span aria-hidden="true">💬</span>
                  Commenter
                </button>
                
                <button 
                  @click="sharePost(post.id)"
                  class="btn-share"
                >
                  <span aria-hidden="true">🔄</span>
                  Partager
                </button>
                
                <button 
                  @click="sendPrivateMessage(post.author.id)"
                  class="btn-message"
                >
                  <span aria-hidden="true">📩</span>
                  Message
                </button>
              </div>
              
              <!-- Commentaires -->
              <div v-if="post.showComments" class="comments-section">
                <div class="comments-list">
                  <div 
                    v-for="comment in post.comments?.slice(0, 3)" 
                    :key="comment.id"
                    class="comment-item"
                  >
                    <img 
                      :src="comment.author.avatar || '/default-avatar.jpg'"
                      :alt="`Avatar de ${comment.author.username}`"
                      class="comment-avatar"
                    >
                    <div class="comment-content">
                      <span class="comment-author">{{ comment.author.username }}</span>
                      <p class="comment-text">{{ comment.content }}</p>
                      <time class="comment-time">{{ formatPostTime(comment.createdAt) }}</time>
                    </div>
                  </div>
                  
                  <button 
                    v-if="post.commentsCount > 3"
                    @click="loadMoreComments(post.id)"
                    class="btn-load-comments"
                  >
                    Voir les {{ post.commentsCount - 3 }} autres commentaires
                  </button>
                </div>
                
                <div class="comment-form">
                  <img 
                    :src="user.avatar || '/default-avatar.jpg'"
                    :alt="`Votre avatar`"
                    class="comment-avatar"
                  >
                  <input 
                    v-model="newComments[post.id]"
                    @keydown.enter="addComment(post.id)"
                    type="text"
                    placeholder="Écrivez un commentaire..."
                    class="comment-input"
                  >
                  <button 
                    @click="addComment(post.id)"
                    :disabled="!newComments[post.id]?.trim()"
                    class="btn-send-comment"
                  >
                    Envoyer
                  </button>
                </div>
              </div>
            </footer>
          </article>
        </div>
      </section>

      <!-- Membres -->
      <section v-else-if="activeTab === 'members'" class="members-section">
        <div class="members-filters">
          <input 
            v-model="memberSearch"
            type="text"
            placeholder="Rechercher un membre..."
            class="search-input"
          >
          
          <select v-model="memberFilter" class="filter-select">
            <option value="all">Tous les membres</option>
            <option value="online">En ligne</option>
            <option value="verified">Vérifiés</option>
            <option value="new">Nouveaux</option>
          </select>
        </div>
        
        <div class="members-grid">
          <article 
            v-for="member in filteredMembers" 
            :key="member.id"
            class="member-card"
            @click="viewMemberProfile(member.id)"
          >
            <div class="member-image-container">
              <img 
                :src="member.avatar || '/default-avatar.jpg'"
                :alt="`Photo de ${member.username}`"
                class="member-image"
              >
              <div v-if="member.isOnline" class="online-indicator"></div>
              <div v-if="member.isVerified" class="verified-indicator">✓</div>
            </div>
            
            <div class="member-info">
              <h3 class="member-name">{{ member.username }}</h3>
              <p class="member-location">{{ member.location || 'Non spécifiée' }}</p>
              <div class="member-stats">
                <span>{{ member.age }} ans</span>
                <span>{{ member.postsCount || 0 }} posts</span>
              </div>
            </div>
            
            <div class="member-actions">
              <button 
                @click.stop="followMember(member.id)"
                :class="['btn-follow', { following: member.isFollowing }]"
              >
                {{ member.isFollowing ? 'Suivi' : 'Suivre' }}
              </button>
              
              <button 
                @click.stop="sendPrivateMessage(member.id)"
                class="btn-message-member"
              >
                Message
              </button>
            </div>
          </article>
        </div>
      </section>

      <!-- Groupes -->
      <section v-else-if="activeTab === 'groups'" class="groups-section">
        <div class="groups-header">
          <h2>Groupes thématiques</h2>
          <button @click="showCreateGroup = true" class="btn-create-group">
            Créer un groupe
          </button>
        </div>
        
        <div class="groups-grid">
          <article 
            v-for="group in communityGroups" 
            :key="group.id"
            class="group-card"
            @click="joinGroup(group.id)"
          >
            <div class="group-image">
              <img 
                :src="group.image || '/default-group.jpg'"
                :alt="`Image du groupe ${group.name}`"
                class="group-cover"
              >
              <div class="group-privacy">
                <span :class="['privacy-badge', group.privacy]">
                  {{ group.privacy === 'public' ? '🌍 Public' : '🔒 Privé' }}
                </span>
              </div>
            </div>
            
            <div class="group-info">
              <h3 class="group-name">{{ group.name }}</h3>
              <p class="group-description">{{ group.description }}</p>
              <div class="group-stats">
                <span class="members-count">{{ group.membersCount }} membres</span>
                <span class="activity-level">{{ group.activityLevel }}</span>
              </div>
            </div>
            
            <div class="group-actions">
              <button 
                v-if="!group.isMember"
                @click.stop="joinGroup(group.id)"
                class="btn-join-group"
              >
                Rejoindre
              </button>
              <button 
                v-else
                @click.stop="viewGroup(group.id)"
                class="btn-view-group"
              >
                Voir le groupe
              </button>
            </div>
          </article>
        </div>
      </section>

      <!-- Événements -->
      <section v-else-if="activeTab === 'events'" class="events-section">
        <div class="events-header">
          <h2>Événements de la communauté</h2>
          <button @click="$router.push('/parties')" class="btn-view-all-events">
            Voir tous les événements
          </button>
        </div>
        
        <div class="events-grid">
          <article 
            v-for="event in communityEvents" 
            :key="event.id"
            class="event-card"
            @click="viewEvent(event.id)"
          >
            <div class="event-date">
              <span class="event-day">{{ formatEventDay(event.date) }}</span>
              <span class="event-month">{{ formatEventMonth(event.date) }}</span>
            </div>
            
            <div class="event-info">
              <h3 class="event-title">{{ event.title }}</h3>
              <p class="event-location">📍 {{ event.location }}</p>
              <p class="event-time">🕐 {{ formatEventTime(event.time) }}</p>
              <div class="event-participants">
                {{ event.participantsCount }}/{{ event.maxParticipants }} participants
              </div>
            </div>
            
            <div class="event-actions">
              <button 
                @click.stop="participateEvent(event.id)"
                :class="['btn-participate', { participating: event.isParticipating }]"
              >
                {{ event.isParticipating ? 'Inscrit' : 'Participer' }}
              </button>
            </div>
          </article>
        </div>
      </section>
    </div>

    <!-- Modal de création de post -->
    <div v-if="showCreatePost" class="modal-overlay" @click="showCreatePost = false">
      <div class="modal-content" @click.stop>
        <header class="modal-header">
          <h2>Créer un nouveau post</h2>
          <button @click="showCreatePost = false" class="btn-close-modal">×</button>
        </header>
        
        <form @submit.prevent="createPost" class="post-form">
          <div class="form-group">
            <label for="post-type">Type de post :</label>
            <select id="post-type" v-model="newPost.type" class="form-select">
              <option value="story">Histoire</option>
              <option value="photo">Photo</option>
              <option value="question">Question</option>
              <option value="experience">Expérience</option>
            </select>
          </div>
          
          <div class="form-group">
            <label for="post-content">Contenu :</label>
            <textarea
              id="post-content"
              v-model="newPost.content"
              placeholder="Partagez votre histoire, vos pensées..."
              class="form-textarea"
              rows="4"
            ></textarea>
          </div>
          
          <div class="form-group">
            <label for="post-tags">Tags (séparés par des virgules) :</label>
            <input
              id="post-tags"
              v-model="newPost.tags"
              type="text"
              placeholder="couple, expérience, conseil..."
              class="form-input"
            >
          </div>
          
          <div class="form-actions">
            <button type="button" @click="showCreatePost = false" class="btn-cancel">
              Annuler
            </button>
            <button type="submit" class="btn-create" :disabled="!newPost.content.trim()">
              Publier
            </button>
          </div>
        </form>
      </div>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'CommunityView',

  data() {
    return {
      loading: false,
      activeTab: 'feed',
      feedFilter: 'all',
      sortBy: 'recent',
      memberSearch: '',
      memberFilter: 'all',
      showCreatePost: false,
      showCreateGroup: false,
      newComments: {},

      newPost: {
        type: 'story',
        content: '',
        tags: '',
        media: []
      }
    }
  },

  computed: {
    ...mapState(['community', 'parties', 'user']),

    totalMembers() {
      return this.community?.members?.length || 1247; // Placeholder
    },

    onlineMembers() {
      return this.community?.members?.filter(m => m.isOnline).length || 89; // Placeholder
    },

    upcomingParties() {
      return this.parties?.filter(p => new Date(p.date) > new Date()).length || 12; // Placeholder
    },

    todayPosts() {
      const today = new Date().toDateString();
      return this.community?.posts?.filter(p =>
        new Date(p.createdAt).toDateString() === today
      ).length || 24; // Placeholder
    },

    filteredPosts() {
      let posts = this.community?.posts || [];

      // Filtrage par type
      if (this.feedFilter !== 'all') {
        posts = posts.filter(post => post.type === this.feedFilter);
      }

      // Tri
      posts.sort((a, b) => {
        switch (this.sortBy) {
          case 'popular':
            return (b.likesCount || 0) - (a.likesCount || 0);
          case 'trending':
            const aScore = (b.likesCount || 0) + (b.commentsCount || 0) + (b.sharesCount || 0);
            const bScore = (a.likesCount || 0) + (a.commentsCount || 0) + (a.sharesCount || 0);
            return bScore - aScore;
          default: // recent
            return new Date(b.createdAt) - new Date(a.createdAt);
        }
      });

      return posts;
    },

    filteredMembers() {
      let members = this.community?.members || [];

      // Recherche par nom
      if (this.memberSearch) {
        members = members.filter(member =>
          member.username.toLowerCase().includes(this.memberSearch.toLowerCase())
        );
      }

      // Filtrage par statut
      switch (this.memberFilter) {
        case 'online':
          members = members.filter(member => member.isOnline);
          break;
        case 'verified':
          members = members.filter(member => member.isVerified);
          break;
        case 'new':
          const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
          members = members.filter(member => new Date(member.joinedAt) > oneWeekAgo);
          break;
      }

      return members;
    },

    communityGroups() {
      return this.community?.groups || [];
    },

    communityEvents() {
      return this.community?.events || [];
    }
  },

  methods: {
    ...mapActions(['loadCommunity', 'createCommunityPost', 'likeCommunityPost']),

    formatPostTime(timestamp) {
      const now = new Date();
      const postTime = new Date(timestamp);
      const diffMs = now - postTime;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 1) return 'À l\'instant';
      if (diffMins < 60) return `Il y a ${diffMins} min`;
      if (diffHours < 24) return `Il y a ${diffHours}h`;
      if (diffDays < 7) return `Il y a ${diffDays}j`;
      return postTime.toLocaleDateString('fr-FR');
    },

    formatEventDay(date) {
      return new Date(date).getDate();
    },

    formatEventMonth(date) {
      return new Date(date).toLocaleDateString('fr-FR', { month: 'short' });
    },

    formatEventTime(time) {
      return new Date(`2000-01-01T${time}`).toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    getPostTypeLabel(type) {
      const labels = {
        story: '📖 Histoire',
        photo: '📸 Photo',
        question: '❓ Question',
        experience: '✨ Expérience'
      };
      return labels[type] || type;
    },

    async toggleLike(postId) {
      try {
        await this.likeCommunityPost(postId);
      } catch (error) {
        console.error('Erreur lors du like:', error);
      }
    },

    toggleComments(postId) {
      const post = this.community.posts.find(p => p.id === postId);
      if (post) {
        post.showComments = !post.showComments;
      }
    },

    async addComment(postId) {
      const content = this.newComments[postId];
      if (!content?.trim()) return;

      try {
        // Ici on ajouterait le commentaire via l'API
        console.log('Ajouter commentaire:', postId, content);
        this.newComments[postId] = '';
      } catch (error) {
        console.error('Erreur lors de l\'ajout du commentaire:', error);
      }
    },

    sharePost(postId) {
      // Implémenter le partage
      console.log('Partager post:', postId);
    },

    sendPrivateMessage(userId) {
      this.$router.push(`/messages/${userId}`);
    },

    viewMemberProfile(memberId) {
      this.$router.push(`/profile/${memberId}`);
    },

    followMember(memberId) {
      // Implémenter le suivi
      console.log('Suivre membre:', memberId);
    },

    joinGroup(groupId) {
      // Implémenter l'adhésion au groupe
      console.log('Rejoindre groupe:', groupId);
    },

    viewGroup(groupId) {
      this.$router.push(`/groups/${groupId}`);
    },

    viewEvent(eventId) {
      this.$router.push(`/events/${eventId}`);
    },

    participateEvent(eventId) {
      // Implémenter la participation à l'événement
      console.log('Participer événement:', eventId);
    },

    filterByTag(tag) {
      // Implémenter le filtrage par tag
      console.log('Filtrer par tag:', tag);
    },

    showPostOptions(postId) {
      // Afficher les options du post
      console.log('Options post:', postId);
    },

    openMediaViewer(media, index) {
      // Ouvrir la visionneuse de médias
      console.log('Ouvrir média:', media, index);
    },

    loadMoreComments(postId) {
      // Charger plus de commentaires
      console.log('Charger plus de commentaires:', postId);
    },

    async createPost() {
      if (!this.newPost.content.trim()) return;

      try {
        const postData = {
          ...this.newPost,
          tags: this.newPost.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          author: this.user,
          createdAt: new Date().toISOString()
        };

        await this.createCommunityPost(postData);

        // Réinitialiser le formulaire
        this.newPost = {
          type: 'story',
          content: '',
          tags: '',
          media: []
        };

        this.showCreatePost = false;

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Post publié avec succès !',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors de la création du post:', error);
        this.$store.commit('addNotification', {
          type: 'error',
          message: 'Erreur lors de la publication',
          timestamp: new Date().toISOString()
        });
      }
    }
  },

  async mounted() {
    this.loading = true;
    try {
      await this.loadCommunity();
    } catch (error) {
      console.error('Erreur lors du chargement de la communauté:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --sensual-red: #DC143C;
  --passion-orange: #FF6347;
  --desire-purple: #9932CC;
  --intimate-pink: #FF1493;
}

.community-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--desire-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.community-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--intimate-pink);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.community-actions {
  display: flex;
  gap: 12px;
}

.btn-parties,
.btn-create-post {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--sensual-red);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-parties:hover,
.btn-create-post:hover {
  background: var(--passion-orange);
  transform: translateY(-2px);
}

.community-stats {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 12px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--intimate-pink);
  margin-bottom: 8px;
}

.stat-label {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.content-navigation {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.nav-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.nav-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 12px 20px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-tab.active {
  background: var(--intimate-pink);
  color: var(--text-white);
  border-color: var(--intimate-pink);
}

.nav-tab:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.content-filters {
  display: flex;
  gap: 16px;
}

.filter-select,
.search-input {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.filter-select:focus,
.search-input:focus {
  outline: none;
  border-color: var(--intimate-pink);
  background: rgba(255, 255, 255, 0.15);
}

.community-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--intimate-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Posts */
.posts-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.post-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.post-card:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--intimate-pink);
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--intimate-pink);
}

.author-name {
  color: var(--intimate-pink);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.post-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.post-type {
  padding: 2px 6px;
  background: var(--sensual-red);
  border-radius: 10px;
  font-size: 0.7rem;
}

.verified-badge {
  color: var(--accent-blue);
  font-weight: bold;
}

.btn-post-options {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.2rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-post-options:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.post-content {
  margin-bottom: 16px;
}

.post-text {
  color: var(--text-white);
  line-height: 1.6;
  margin-bottom: 16px;
}

.post-media {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 16px;
  position: relative;
}

.media-item {
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.media-item:hover {
  transform: scale(1.02);
}

.post-image,
.post-video {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.media-overlay {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: var(--text-white);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
}

.post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.post-tag {
  padding: 4px 8px;
  background: rgba(255, 20, 147, 0.2);
  color: var(--intimate-pink);
  border-radius: 12px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.post-tag:hover {
  background: rgba(255, 20, 147, 0.3);
}

.post-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 16px;
}

.post-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 0.9rem;
  color: var(--slogan-gray);
}

.post-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.btn-like,
.btn-comment,
.btn-share,
.btn-message {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-like:hover,
.btn-comment:hover,
.btn-share:hover,
.btn-message:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-like.liked {
  color: var(--sensual-red);
  border-color: var(--sensual-red);
}

.comments-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.comments-list {
  margin-bottom: 16px;
}

.comment-item {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.comment-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid var(--intimate-pink);
}

.comment-content {
  flex: 1;
}

.comment-author {
  color: var(--intimate-pink);
  font-weight: 600;
  font-size: 0.9rem;
}

.comment-text {
  color: var(--text-white);
  margin: 4px 0;
  font-size: 0.9rem;
  line-height: 1.4;
}

.comment-time {
  color: var(--icon-color);
  font-size: 0.7rem;
}

.btn-load-comments {
  color: var(--intimate-pink);
  background: transparent;
  border: none;
  cursor: pointer;
  font-size: 0.8rem;
  margin-bottom: 12px;
}

.comment-form {
  display: flex;
  gap: 12px;
  align-items: center;
}

.comment-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.comment-input:focus {
  outline: none;
  border-color: var(--intimate-pink);
}

.btn-send-comment {
  padding: 8px 16px;
  background: var(--intimate-pink);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-send-comment:hover:not(:disabled) {
  background: var(--sensual-red);
}

.btn-send-comment:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Membres */
.members-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.members-filters {
  display: flex;
  gap: 16px;
}

.search-input {
  flex: 1;
  max-width: 300px;
}

.members-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
}

.member-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.member-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  border-color: var(--intimate-pink);
}

.member-image-container {
  position: relative;
  display: inline-block;
  margin-bottom: 16px;
}

.member-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--intimate-pink);
}

.online-indicator {
  position: absolute;
  bottom: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: #4ade80;
  border: 2px solid var(--text-white);
  border-radius: 50%;
}

.verified-indicator {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 24px;
  height: 24px;
  background: var(--accent-blue);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
}

.member-name {
  color: var(--intimate-pink);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.member-location {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.member-stats {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 16px;
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.member-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.btn-follow,
.btn-message-member {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.btn-follow {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-follow.following {
  background: var(--wall-color);
}

.btn-message-member {
  background: transparent;
  color: var(--intimate-pink);
  border: 1px solid var(--intimate-pink);
}

.btn-follow:hover,
.btn-message-member:hover {
  transform: translateY(-1px);
}

/* Groupes */
.groups-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.groups-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.groups-header h2 {
  color: var(--intimate-pink);
  font-size: 1.5rem;
  font-weight: 600;
}

.btn-create-group {
  padding: 10px 20px;
  background: var(--sensual-red);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-group:hover {
  background: var(--passion-orange);
}

.groups-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.group-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.group-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  border-color: var(--intimate-pink);
}

.group-image {
  position: relative;
  height: 150px;
}

.group-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.group-privacy {
  position: absolute;
  top: 12px;
  right: 12px;
}

.privacy-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.privacy-badge.public {
  background: var(--accent-blue);
  color: var(--text-white);
}

.privacy-badge.private {
  background: var(--sensual-red);
  color: var(--text-white);
}

.group-info {
  padding: 16px;
}

.group-name {
  color: var(--intimate-pink);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.group-description {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  line-height: 1.4;
  margin-bottom: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.group-stats {
  display: flex;
  justify-content: space-between;
  font-size: 0.8rem;
  color: var(--icon-color);
  margin-bottom: 16px;
}

.group-actions {
  display: flex;
  justify-content: center;
}

.btn-join-group,
.btn-view-group {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-join-group {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-view-group {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-join-group:hover,
.btn-view-group:hover {
  transform: translateY(-1px);
}

/* Événements */
.events-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.events-header h2 {
  color: var(--intimate-pink);
  font-size: 1.5rem;
  font-weight: 600;
}

.btn-view-all-events {
  padding: 10px 20px;
  background: var(--sensual-red);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view-all-events:hover {
  background: var(--passion-orange);
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.event-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.event-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  border-color: var(--intimate-pink);
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  padding: 12px;
  background: var(--intimate-pink);
  border-radius: 8px;
  text-align: center;
}

.event-day {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-white);
}

.event-month {
  font-size: 0.8rem;
  color: var(--text-white);
  text-transform: uppercase;
}

.event-info {
  flex: 1;
}

.event-title {
  color: var(--intimate-pink);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 8px 0;
}

.event-location,
.event-time {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.event-participants {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.event-actions {
  flex-shrink: 0;
}

.btn-participate {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.btn-participate {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-participate.participating {
  background: var(--wall-color);
}

.btn-participate:hover {
  transform: translateY(-1px);
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--header-bg);
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h2 {
  color: var(--intimate-pink);
  font-size: 1.3rem;
  font-weight: 600;
}

.btn-close-modal {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.post-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: var(--slogan-gray);
  font-weight: 500;
}

.form-select,
.form-input,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.form-select:focus,
.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--intimate-pink);
  background: rgba(255, 255, 255, 0.15);
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.btn-cancel,
.btn-create {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-cancel {
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-create {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-create:hover:not(:disabled) {
  background: var(--sensual-red);
}

.btn-create:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .nav-tabs {
    flex-wrap: wrap;
  }

  .members-grid,
  .groups-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .community-container {
    padding: 16px;
  }

  .community-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .content-filters {
    flex-direction: column;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .nav-tabs {
    justify-content: center;
  }

  .nav-tab {
    flex: 1;
    justify-content: center;
  }

  .members-grid,
  .groups-grid,
  .events-grid {
    grid-template-columns: 1fr;
  }

  .post-actions {
    flex-wrap: wrap;
  }

  .comment-form {
    flex-direction: column;
    align-items: stretch;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
  }
}
</style>
