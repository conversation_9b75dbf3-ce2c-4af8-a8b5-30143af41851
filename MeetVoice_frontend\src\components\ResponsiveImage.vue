<!-- @format -->

<template>
  <div class="responsive-image-container" :class="containerClass">
    <picture class="responsive-image">
      <!-- Source WebP pour les navigateurs compatibles -->
      <source 
        v-if="webpSrc && !forceFallback"
        :srcset="webpSrcSet" 
        :sizes="sizes"
        type="image/webp"
      />
      
      <!-- Source fallback -->
      <source 
        :srcset="fallbackSrcSet" 
        :sizes="sizes"
        :type="fallbackType"
      />
      
      <!-- Image de base -->
      <img
        :src="fallbackSrc"
        :alt="alt"
        :width="width"
        :height="height"
        :loading="loading"
        :class="['responsive-img', imageClass]"
        :style="imageStyle"
        @load="onLoad"
        @error="onError"
        ref="image"
      />
    </picture>
    
    <!-- Overlay de chargement -->
    <div v-if="isLoading" class="loading-overlay">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- Overlay d'erreur -->
    <div v-if="hasError" class="error-overlay">
      <div class="error-content">
        <span class="error-icon">⚠️</span>
        <span class="error-text">Image non disponible</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResponsiveImage',
  props: {
    // Source de l'image (peut être une URL ou un objet avec webp/fallback)
    src: {
      type: [String, Object],
      required: true
    },
    
    // Texte alternatif
    alt: {
      type: String,
      required: true
    },
    
    // Largeur de l'image
    width: {
      type: [String, Number],
      default: null
    },
    
    // Hauteur de l'image
    height: {
      type: [String, Number],
      default: null
    },
    
    // Attribut sizes pour le responsive
    sizes: {
      type: String,
      default: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
    },
    
    // Type de chargement
    loading: {
      type: String,
      default: 'lazy',
      validator: value => ['lazy', 'eager'].includes(value)
    },
    
    // Classes CSS pour le container
    containerClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Classes CSS pour l'image
    imageClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Styles CSS pour l'image
    imageStyle: {
      type: [String, Object],
      default: null
    },
    
    // Forcer l'utilisation du fallback
    forceFallback: {
      type: Boolean,
      default: false
    },
    
    // Tailles disponibles pour le srcset
    breakpoints: {
      type: Array,
      default: () => [400, 800, 1200, 1600]
    }
  },
  
  data() {
    return {
      isLoading: true,
      hasError: false
    };
  },
  
  computed: {
    // Source WebP
    webpSrc() {
      if (typeof this.src === 'object') {
        return this.src.webp;
      }
      
      // Convertir automatiquement l'extension
      return this.src.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
    },
    
    // Source de fallback
    fallbackSrc() {
      if (typeof this.src === 'object') {
        return this.src.fallback || this.src.webp;
      }
      
      return this.src;
    },
    
    // Type MIME du fallback
    fallbackType() {
      const ext = this.fallbackSrc.split('.').pop().toLowerCase();
      const mimeTypes = {
        jpg: 'image/jpeg',
        jpeg: 'image/jpeg',
        png: 'image/png',
        gif: 'image/gif',
        webp: 'image/webp'
      };
      
      return mimeTypes[ext] || 'image/jpeg';
    },
    
    // SrcSet WebP
    webpSrcSet() {
      if (!this.webpSrc) return '';
      
      return this.generateSrcSet(this.webpSrc);
    },
    
    // SrcSet fallback
    fallbackSrcSet() {
      return this.generateSrcSet(this.fallbackSrc);
    }
  },
  
  methods: {
    // Générer le srcset pour différentes tailles
    generateSrcSet(baseSrc) {
      // Pour cette démo, on retourne juste l'image de base
      // Dans un vrai projet, vous auriez différentes tailles générées
      return baseSrc;
    },
    
    onLoad(event) {
      this.isLoading = false;
      this.hasError = false;
      this.$emit('load', event);
      
      // Mesurer les performances
      this.measureLoadTime(event);
    },
    
    onError(event) {
      this.isLoading = false;
      this.hasError = true;
      this.$emit('error', event);
      
      console.warn(`Erreur de chargement d'image: ${event.target.src}`);
    },
    
    // Mesurer le temps de chargement
    measureLoadTime(event) {
      if ('performance' in window) {
        const entries = performance.getEntriesByName(event.target.currentSrc || event.target.src);
        if (entries.length > 0) {
          const entry = entries[entries.length - 1];
          const loadTime = entry.responseEnd - entry.startTime;
          
          // Émettre l'événement de performance
          this.$emit('performance', {
            url: event.target.src,
            loadTime: Math.round(loadTime),
            isWebP: (event.target.currentSrc || event.target.src).includes('.webp')
          });
        }
      }
    },
    
    // Précharger l'image
    preload() {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      
      if (this.webpSrc && !this.forceFallback) {
        link.href = this.webpSrc;
        link.type = 'image/webp';
      } else {
        link.href = this.fallbackSrc;
      }
      
      document.head.appendChild(link);
    }
  },
  
  mounted() {
    // Précharger si c'est une image critique
    if (this.loading === 'eager') {
      this.preload();
    }
  }
};
</script>

<style scoped>
.responsive-image-container {
  position: relative;
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
}

.responsive-image {
  display: block;
  width: 100%;
  height: auto;
}

.responsive-img {
  width: 100%;
  height: auto;
  display: block;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* États de chargement */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* État d'erreur */
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px dashed #ddd;
}

.error-content {
  text-align: center;
  color: #666;
  font-size: 14px;
}

.error-icon {
  display: block;
  font-size: 24px;
  margin-bottom: 8px;
}

/* Animations */
@keyframes shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .responsive-image-container {
    width: 100%;
  }
}

/* Optimisations pour les images critiques */
.responsive-img[loading="eager"] {
  opacity: 1;
}

/* Effet hover optionnel */
.responsive-image-container:hover .responsive-img {
  transform: scale(1.02);
}
</style>
