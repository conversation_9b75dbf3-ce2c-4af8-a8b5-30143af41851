import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000/api'

export default {
  namespaced: true,
  
  state: {
    posts: [],
    members: [],
    groups: [],
    parties: [],
    currentPost: null,
    currentGroup: null,
    loading: false,
    error: null
  },
  
  mutations: {
    setPosts(state, posts) {
      state.posts = posts
    },
    
    addPost(state, post) {
      state.posts.unshift(post)
    },
    
    updatePost(state, updatedPost) {
      const index = state.posts.findIndex(p => p.id === updatedPost.id)
      if (index !== -1) {
        state.posts.splice(index, 1, updatedPost)
      }
    },
    
    removePost(state, postId) {
      state.posts = state.posts.filter(p => p.id !== postId)
    },
    
    setMembers(state, members) {
      state.members = members
    },
    
    addMember(state, member) {
      state.members.push(member)
    },
    
    updateMember(state, updatedMember) {
      const index = state.members.findIndex(m => m.id === updatedMember.id)
      if (index !== -1) {
        state.members.splice(index, 1, updatedMember)
      }
    },
    
    setGroups(state, groups) {
      state.groups = groups
    },
    
    addGroup(state, group) {
      state.groups.unshift(group)
    },
    
    updateGroup(state, updatedGroup) {
      const index = state.groups.findIndex(g => g.id === updatedGroup.id)
      if (index !== -1) {
        state.groups.splice(index, 1, updatedGroup)
      }
    },
    
    setParties(state, parties) {
      state.parties = parties
    },
    
    addParty(state, party) {
      state.parties.unshift(party)
    },
    
    updateParty(state, updatedParty) {
      const index = state.parties.findIndex(p => p.id === updatedParty.id)
      if (index !== -1) {
        state.parties.splice(index, 1, updatedParty)
      }
    },
    
    removeParty(state, partyId) {
      state.parties = state.parties.filter(p => p.id !== partyId)
    },
    
    setCurrentPost(state, post) {
      state.currentPost = post
    },
    
    setCurrentGroup(state, group) {
      state.currentGroup = group
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    }
  },
  
  actions: {
    async loadMurPosts({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')

        // Charger les posts du mur selon votre API Django
        const postsResponse = await axios.get(`${API_BASE_URL}/mur/api/posts/`)
        commit('setPosts', postsResponse.data.results || postsResponse.data)

        return postsResponse.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement du mur')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async createMurPost({ commit }, postData) {
      try {
        commit('setLoading', true)
        commit('clearError')

        // Créer un FormData pour gérer les fichiers (images/vidéos)
        const formData = new FormData()
        formData.append('titre', postData.titre)
        formData.append('text', postData.text)

        if (postData.image) {
          formData.append('image', postData.image)
        }

        if (postData.video) {
          formData.append('video', postData.video)
        }

        if (postData.is_recruteur !== undefined) {
          formData.append('is_recruteur', postData.is_recruteur)
        }

        if (postData.is_applicant !== undefined) {
          formData.append('is_applicant', postData.is_applicant)
        }

        if (postData.is_taff !== undefined) {
          formData.append('is_taff', postData.is_taff)
        }

        const response = await axios.post(`${API_BASE_URL}/mur/api/posts/`, formData, {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        })

        commit('addPost', response.data)

        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la création du post')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async toggleLikeMurPost({ commit }, postId) {
      try {
        // Utiliser l'endpoint Django existant pour toggle like
        const response = await axios.post(`${API_BASE_URL}/mur/toggle-like/${postId}/`)
        commit('updatePost', response.data)

        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du like')
        throw error
      }
    },
    
    async addComment({ commit }, { postId, content }) {
      try {
        const response = await axios.post(`${API_BASE_URL}/community/posts/${postId}/comments/`, {
          content
        })
        
        // Recharger le post pour avoir les commentaires mis à jour
        const postResponse = await axios.get(`${API_BASE_URL}/community/posts/${postId}/`)
        commit('updatePost', postResponse.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'ajout du commentaire')
        throw error
      }
    },
    
    async sharePost({ commit }, postId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/community/posts/${postId}/share/`)
        commit('updatePost', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du partage')
        throw error
      }
    },
    
    async followMember({ commit }, memberId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/community/members/${memberId}/follow/`)
        commit('updateMember', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du suivi')
        throw error
      }
    },
    
    async joinGroup({ commit }, groupId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/community/groups/${groupId}/join/`)
        commit('updateGroup', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'adhésion au groupe')
        throw error
      }
    },
    
    async createGroup({ commit }, groupData) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/community/groups/`, groupData)
        commit('addGroup', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la création du groupe')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async loadParties({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/parties/`)
        commit('setParties', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des soirées')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async createParty({ commit }, partyData) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/parties/`, partyData)
        commit('addParty', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la création de la soirée')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async joinParty({ commit }, partyId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/parties/${partyId}/join/`)
        commit('updateParty', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'inscription à la soirée')
        throw error
      }
    },
    
    async leaveParty({ commit }, partyId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/parties/${partyId}/leave/`)
        commit('updateParty', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la désinscription de la soirée')
        throw error
      }
    },
    
    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    posts: state => state.posts,
    members: state => state.members,
    groups: state => state.groups,
    parties: state => state.parties,
    currentPost: state => state.currentPost,
    currentGroup: state => state.currentGroup,
    loading: state => state.loading,
    error: state => state.error,
    
    trendingPosts: state => {
      return state.posts
        .filter(post => post.isTrending)
        .sort((a, b) => (b.likesCount + b.commentsCount) - (a.likesCount + a.commentsCount))
    },
    
    onlineMembers: state => {
      return state.members.filter(member => member.isOnline)
    },
    
    publicGroups: state => {
      return state.groups.filter(group => group.privacy === 'public')
    },
    
    upcomingParties: state => {
      const now = new Date()
      return state.parties.filter(party => new Date(party.date) > now)
    }
  }
}
