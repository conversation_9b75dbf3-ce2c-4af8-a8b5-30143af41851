describe('Articles Management E2E Tests', () => {
  const baseUrl = 'http://localhost:8080'
  const apiUrl = 'http://127.0.0.1:8000'

  beforeEach(() => {
    // Intercepter les appels API
    cy.intercept('GET', `${apiUrl}/actualite/api/articles/published/`, {
      fixture: 'articles.json'
    }).as('getPublishedArticles')

    cy.intercept('GET', `${apiUrl}/actualite/api/articles/featured/`, {
      fixture: 'featured-articles.json'
    }).as('getFeaturedArticles')

    cy.intercept('POST', `${apiUrl}/api/login/`, {
      statusCode: 200,
      body: {
        access: 'fake-token',
        user: {
          id: 1,
          username: 'testuser',
          first_name: 'Test',
          last_name: 'User',
          email: '<EMAIL>'
        }
      }
    }).as('login')
  })

  describe('Articles Page - Public Access', () => {
    it('should display articles page correctly', () => {
      cy.visit(`${baseUrl}/articles`)
      
      // Vérifier que la page se charge
      cy.contains('Articles').should('be.visible')
      cy.contains('Découvrez nos derniers articles').should('be.visible')
      
      // Vérifier que les articles sont chargés
      cy.wait('@getPublishedArticles')
      
      // Vérifier la présence des éléments de l'interface
      cy.get('[data-cy=search-input]').should('be.visible')
      cy.get('[data-cy=category-filter]').should('be.visible')
      cy.get('[data-cy=theme-filter]').should('be.visible')
      cy.get('[data-cy=sort-filter]').should('be.visible')
    })

    it('should search articles', () => {
      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Effectuer une recherche
      cy.get('[data-cy=search-input]').type('Test Article')
      
      // Vérifier que les résultats sont filtrés
      cy.get('[data-cy=article-card]').should('have.length.at.least', 1)
      cy.contains('Test Article').should('be.visible')
    })

    it('should filter articles by category', () => {
      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Filtrer par catégorie
      cy.get('[data-cy=category-filter]').select('recruteur')
      
      // Vérifier que les articles sont filtrés
      cy.get('[data-cy=article-card]').should('exist')
      cy.get('[data-cy=category-badge]').should('contain', 'Recruteur')
    })

    it('should sort articles', () => {
      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Trier par titre
      cy.get('[data-cy=sort-filter]').select('title_asc')
      
      // Vérifier que l'ordre a changé
      cy.get('[data-cy=article-title]').first().should('be.visible')
    })

    it('should open article modal', () => {
      cy.intercept('GET', `${apiUrl}/actualite/api/articles/1/`, {
        fixture: 'article-detail.json'
      }).as('getArticleDetail')

      cy.intercept('POST', `${apiUrl}/actualite/api/articles/1/increment_views/`, {
        statusCode: 200,
        body: { access_count: 11 }
      }).as('incrementViews')

      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Cliquer sur un article
      cy.get('[data-cy=article-card]').first().click()
      
      // Vérifier que la modal s'ouvre
      cy.wait('@getArticleDetail')
      cy.wait('@incrementViews')
      
      cy.get('[data-cy=article-modal]').should('be.visible')
      cy.get('[data-cy=article-detail-title]').should('be.visible')
      cy.get('[data-cy=article-detail-content]').should('be.visible')
    })

    it('should close article modal', () => {
      cy.intercept('GET', `${apiUrl}/actualite/api/articles/1/`, {
        fixture: 'article-detail.json'
      }).as('getArticleDetail')

      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Ouvrir la modal
      cy.get('[data-cy=article-card]').first().click()
      cy.wait('@getArticleDetail')
      
      // Fermer la modal
      cy.get('[data-cy=close-modal]').click()
      
      // Vérifier que la modal est fermée
      cy.get('[data-cy=article-modal]').should('not.exist')
    })

    it('should handle pagination', () => {
      // Mock avec beaucoup d'articles pour tester la pagination
      cy.intercept('GET', `${apiUrl}/actualite/api/articles/published/`, {
        fixture: 'many-articles.json'
      }).as('getManyArticles')

      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getManyArticles')
      
      // Vérifier la pagination
      cy.get('[data-cy=pagination]').should('be.visible')
      cy.get('[data-cy=page-button]').should('have.length.at.least', 2)
      
      // Aller à la page suivante
      cy.get('[data-cy=next-page]').click()
      
      // Vérifier que la page a changé
      cy.url().should('include', 'page=2')
    })

    it('should display featured articles section', () => {
      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      cy.wait('@getFeaturedArticles')
      
      // Vérifier la section des articles mis en avant
      cy.get('[data-cy=featured-section]').should('be.visible')
      cy.contains('Articles mis en avant').should('be.visible')
      cy.get('[data-cy=featured-card]').should('exist')
    })

    it('should reset filters', () => {
      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getPublishedArticles')
      
      // Appliquer des filtres
      cy.get('[data-cy=search-input]').type('test')
      cy.get('[data-cy=category-filter]').select('recruteur')
      cy.get('[data-cy=theme-filter]').select('Décryptage')
      
      // Réinitialiser les filtres
      cy.get('[data-cy=reset-filters]').click()
      
      // Vérifier que les filtres sont réinitialisés
      cy.get('[data-cy=search-input]').should('have.value', '')
      cy.get('[data-cy=category-filter]').should('have.value', '')
      cy.get('[data-cy=theme-filter]').should('have.value', '')
    })
  })

  describe('Back-office Articles Management', () => {
    beforeEach(() => {
      // Se connecter en tant qu'administrateur
      cy.visit(`${baseUrl}/login`)
      cy.get('[data-cy=email-input]').type('<EMAIL>')
      cy.get('[data-cy=password-input]').type('password')
      cy.get('[data-cy=login-button]').click()
      cy.wait('@login')
    })

    it('should access back-office articles page', () => {
      cy.intercept('GET', `${apiUrl}/backoffice/articles/`, {
        fixture: 'backoffice-articles.json'
      }).as('getBackofficeArticles')

      cy.visit(`${baseUrl}/backoffice/articles`)
      cy.wait('@getBackofficeArticles')
      
      // Vérifier l'accès à la page
      cy.contains('Gestion des Articles').should('be.visible')
      cy.get('[data-cy=articles-table]').should('be.visible')
      cy.get('[data-cy=new-article-button]').should('be.visible')
    })

    it('should create new article', () => {
      cy.intercept('POST', `${apiUrl}/actualite/`, {
        statusCode: 201,
        body: { id: 1, titre: 'Nouvel Article' }
      }).as('createArticle')

      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Ouvrir la modal de création
      cy.get('[data-cy=new-article-button]').click()
      cy.get('[data-cy=article-modal]').should('be.visible')
      
      // Remplir le formulaire
      cy.get('[data-cy=title-input]').type('Nouvel Article')
      cy.get('[data-cy=content-input]').type('Contenu du nouvel article')
      cy.get('[data-cy=category-select]').select('recruteur')
      cy.get('[data-cy=theme-select]').select('Décryptage')
      cy.get('[data-cy=status-select]').select('published')
      
      // Soumettre le formulaire
      cy.get('[data-cy=save-button]').click()
      cy.wait('@createArticle')
      
      // Vérifier la création
      cy.contains('Nouvel Article').should('be.visible')
    })

    it('should edit existing article', () => {
      cy.intercept('GET', `${apiUrl}/actualite/get/1/`, {
        fixture: 'article-edit-data.json'
      }).as('getArticleEditData')

      cy.intercept('POST', `${apiUrl}/actualite/edit/1/`, {
        statusCode: 200
      }).as('updateArticle')

      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Cliquer sur modifier
      cy.get('[data-cy=edit-button]').first().click()
      cy.wait('@getArticleEditData')
      
      // Modifier le titre
      cy.get('[data-cy=title-input]').clear().type('Article Modifié')
      
      // Sauvegarder
      cy.get('[data-cy=save-button]').click()
      cy.wait('@updateArticle')
      
      // Vérifier la modification
      cy.contains('Article Modifié').should('be.visible')
    })

    it('should delete article', () => {
      cy.intercept('POST', `${apiUrl}/actualite/delete/1/`, {
        statusCode: 200
      }).as('deleteArticle')

      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Cliquer sur supprimer
      cy.get('[data-cy=delete-button]').first().click()
      
      // Confirmer la suppression
      cy.get('[data-cy=confirm-delete]').click()
      cy.wait('@deleteArticle')
      
      // Vérifier que l'article a été supprimé
      cy.contains('Article supprimé').should('not.exist')
    })

    it('should toggle featured status', () => {
      cy.intercept('POST', `${apiUrl}/actualite/highlight/1/`, {
        statusCode: 200
      }).as('toggleFeatured')

      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Basculer le statut mis en avant
      cy.get('[data-cy=toggle-featured]').first().click()
      cy.wait('@toggleFeatured')
      
      // Vérifier le changement de statut
      cy.get('[data-cy=featured-icon]').should('be.visible')
    })

    it('should filter articles in back-office', () => {
      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Filtrer par statut
      cy.get('[data-cy=status-filter]').select('published')
      cy.get('[data-cy=filter-button]').click()
      
      // Vérifier que les articles sont filtrés
      cy.get('[data-cy=status-badge]').should('contain', 'Publié')
    })

    it('should search articles in back-office', () => {
      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Effectuer une recherche
      cy.get('[data-cy=search-input]').type('Test')
      cy.get('[data-cy=filter-button]').click()
      
      // Vérifier les résultats
      cy.get('[data-cy=article-row]').should('contain', 'Test')
    })
  })

  describe('Authentication Integration', () => {
    it('should redirect to login when accessing protected routes', () => {
      cy.visit(`${baseUrl}/backoffice/articles`)
      
      // Vérifier la redirection vers la page de connexion
      cy.url().should('include', '/login')
    })

    it('should maintain authentication state', () => {
      // Se connecter
      cy.visit(`${baseUrl}/login`)
      cy.get('[data-cy=email-input]').type('<EMAIL>')
      cy.get('[data-cy=password-input]').type('password')
      cy.get('[data-cy=login-button]').click()
      cy.wait('@login')
      
      // Naviguer vers les articles
      cy.visit(`${baseUrl}/articles`)
      
      // Vérifier que l'utilisateur est toujours connecté
      cy.get('[data-cy=user-menu]').should('be.visible')
    })

    it('should logout correctly', () => {
      // Se connecter d'abord
      cy.visit(`${baseUrl}/login`)
      cy.get('[data-cy=email-input]').type('<EMAIL>')
      cy.get('[data-cy=password-input]').type('password')
      cy.get('[data-cy=login-button]').click()
      cy.wait('@login')
      
      // Se déconnecter
      cy.get('[data-cy=user-menu]').click()
      cy.get('[data-cy=logout-button]').click()
      
      // Vérifier la déconnexion
      cy.url().should('include', '/login')
      cy.get('[data-cy=login-form]').should('be.visible')
    })
  })

  describe('Error Handling', () => {
    it('should handle API errors gracefully', () => {
      cy.intercept('GET', `${apiUrl}/actualite/api/articles/published/`, {
        statusCode: 500,
        body: { error: 'Server Error' }
      }).as('getArticlesError')

      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getArticlesError')
      
      // Vérifier l'affichage de l'erreur
      cy.contains('Erreur lors du chargement').should('be.visible')
    })

    it('should handle network errors', () => {
      cy.intercept('GET', `${apiUrl}/actualite/api/articles/published/`, {
        forceNetworkError: true
      }).as('getArticlesNetworkError')

      cy.visit(`${baseUrl}/articles`)
      cy.wait('@getArticlesNetworkError')
      
      // Vérifier l'affichage de l'erreur réseau
      cy.contains('Erreur de connexion').should('be.visible')
    })
  })
})
