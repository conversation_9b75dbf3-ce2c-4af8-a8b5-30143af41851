<template>
  <main class="matches-container">
    <!-- Header -->
    <header class="matches-header">
      <section class="header-content">
        <h1>Mes matchs</h1>
        <p class="subtitle">Vos connexions mutuelles et conversations en cours</p>
      </section>
      
      <nav class="matches-actions" aria-label="Actions matchs">
        <button 
          @click="$router.push('/profiles')"
          class="btn-discover"
        >
          <span aria-hidden="true">🔍</span>
          Découvrir
        </button>
        
        <button 
          @click="$router.push('/messages')"
          class="btn-messages"
        >
          <span aria-hidden="true">💬</span>
          Messages
        </button>
      </nav>
    </header>

    <!-- Statistiques -->
    <section class="stats-section" aria-label="Statistiques">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-number">{{ totalMatches }}</div>
          <div class="stat-label">Matchs totaux</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-number">{{ newMatches }}</div>
          <div class="stat-label">Nouveaux matchs</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-number">{{ activeConversations }}</div>
          <div class="stat-label">Conversations actives</div>
        </div>
        
        <div class="stat-card">
          <div class="stat-number">{{ todayLikes }}</div>
          <div class="stat-label">Likes aujourd'hui</div>
        </div>
      </div>
    </section>

    <!-- Filtres -->
    <section class="filters-section" aria-label="Filtres">
      <div class="filters-container">
        <div class="filter-group">
          <label for="match-filter">Afficher :</label>
          <select 
            id="match-filter"
            v-model="selectedFilter"
            @change="filterMatches"
            class="filter-select"
          >
            <option value="all">Tous les matchs</option>
            <option value="new">Nouveaux matchs</option>
            <option value="active">Conversations actives</option>
            <option value="recent">Récemment connectés</option>
            <option value="favorites">Favoris</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="sort-filter">Trier par :</label>
          <select 
            id="sort-filter"
            v-model="sortBy"
            @change="sortMatches"
            class="filter-select"
          >
            <option value="recent">Plus récents</option>
            <option value="alphabetical">Alphabétique</option>
            <option value="lastMessage">Dernier message</option>
            <option value="compatibility">Compatibilité</option>
          </select>
        </div>
        
        <button 
          @click="refreshMatches"
          class="btn-refresh"
          :disabled="loading"
        >
          <span aria-hidden="true">🔄</span>
          Actualiser
        </button>
      </div>
    </section>

    <!-- Liste des matchs -->
    <section class="matches-content" aria-label="Liste des matchs">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Chargement de vos matchs...</p>
      </div>
      
      <div v-else-if="filteredMatches.length === 0" class="empty-state">
        <div class="empty-icon">💔</div>
        <h2>{{ getEmptyStateTitle() }}</h2>
        <p>{{ getEmptyStateMessage() }}</p>
        <button 
          @click="$router.push('/profiles')"
          class="btn-start-matching"
        >
          Commencer à matcher
        </button>
      </div>
      
      <div v-else class="matches-grid">
        <article 
          v-for="match in filteredMatches" 
          :key="match.id"
          class="match-card"
          @click="openConversation(match)"
          role="button"
          tabindex="0"
          @keydown.enter="openConversation(match)"
          @keydown.space.prevent="openConversation(match)"
        >
          <div class="match-image-container">
            <img 
              :src="match.profile.photos?.[0] || '/default-avatar.jpg'"
              :alt="`Photo de ${match.profile.prenom}`"
              class="match-image"
              loading="lazy"
            >
            
            <!-- Indicateurs de statut -->
            <div class="status-indicators">
              <div v-if="match.profile.isOnline" class="online-indicator"></div>
              <div v-if="match.isNew" class="new-match-badge">Nouveau</div>
              <div v-if="match.isFavorite" class="favorite-indicator">⭐</div>
            </div>
            
            <!-- Badge de compatibilité -->
            <div class="compatibility-badge">
              {{ match.compatibility }}%
            </div>
          </div>
          
          <div class="match-info">
            <header class="match-header">
              <h3 class="match-name">{{ match.profile.prenom }}</h3>
              <span class="match-age">{{ calculateAge(match.profile.dateNaissance) }} ans</span>
            </header>
            
            <div class="match-location">
              <span aria-hidden="true">📍</span>
              {{ match.profile.ville || 'Non spécifiée' }}
              <span v-if="match.profile.distance" class="distance">
                ({{ match.profile.distance }} km)
              </span>
            </div>
            
            <!-- Dernier message ou statut -->
            <div class="last-interaction">
              <div v-if="match.lastMessage" class="last-message">
                <span class="message-preview">{{ match.lastMessage.content }}</span>
                <time class="message-time">{{ formatMessageTime(match.lastMessage.timestamp) }}</time>
              </div>
              <div v-else class="no-message">
                <span class="match-time">Match du {{ formatMatchDate(match.matchedAt) }}</span>
                <span class="start-conversation">Commencez la conversation !</span>
              </div>
            </div>
            
            <!-- Indicateur de messages non lus -->
            <div v-if="match.unreadCount > 0" class="unread-indicator">
              {{ match.unreadCount }}
            </div>
          </div>
          
          <footer class="match-actions">
            <button 
              @click.stop="toggleFavorite(match.id)"
              :class="['btn-favorite', { active: match.isFavorite }]"
              :aria-label="match.isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'"
            >
              <span aria-hidden="true">{{ match.isFavorite ? '⭐' : '☆' }}</span>
            </button>
            
            <button 
              @click.stop="viewProfile(match.profile.id)"
              class="btn-view-profile"
              aria-label="Voir le profil"
            >
              <span aria-hidden="true">👤</span>
            </button>
            
            <button 
              @click.stop="openConversation(match)"
              class="btn-message"
              aria-label="Envoyer un message"
            >
              <span aria-hidden="true">💬</span>
            </button>
            
            <button 
              @click.stop="unmatch(match.id)"
              class="btn-unmatch"
              aria-label="Supprimer le match"
            >
              <span aria-hidden="true">🚫</span>
            </button>
          </footer>
        </article>
      </div>
    </section>

    <!-- Suggestions de profils similaires -->
    <aside v-if="suggestedProfiles.length > 0" class="suggestions-section">
      <h2>Profils similaires à vos matchs</h2>
      <div class="suggestions-grid">
        <article 
          v-for="profile in suggestedProfiles" 
          :key="profile.id"
          class="suggestion-card"
          @click="viewProfile(profile.id)"
        >
          <img 
            :src="profile.photos?.[0] || '/default-avatar.jpg'"
            :alt="`Photo de ${profile.prenom}`"
            class="suggestion-image"
          >
          <div class="suggestion-info">
            <h4>{{ profile.prenom }}</h4>
            <span>{{ calculateAge(profile.dateNaissance) }} ans</span>
          </div>
          <div class="suggestion-compatibility">
            {{ profile.compatibility }}%
          </div>
        </article>
      </div>
    </aside>

    <!-- Pagination -->
    <nav class="pagination" v-if="totalPages > 1" aria-label="Navigation pagination">
      <button 
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        class="btn-page"
      >
        Précédent
      </button>
      
      <span class="page-info">
        Page {{ currentPage }} sur {{ totalPages }}
      </span>
      
      <button 
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="btn-page"
      >
        Suivant
      </button>
    </nav>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'MatchesView',
  
  data() {
    return {
      loading: false,
      selectedFilter: 'all',
      sortBy: 'recent',
      currentPage: 1,
      matchesPerPage: 12
    }
  },
  
  computed: {
    ...mapState(['matches', 'profiles', 'conversations', 'user']),
    
    totalMatches() {
      return this.matches?.length || 0;
    },
    
    newMatches() {
      const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
      return this.matches?.filter(match => 
        new Date(match.matchedAt) > oneDayAgo
      ).length || 0;
    },
    
    activeConversations() {
      return this.matches?.filter(match => 
        match.lastMessage && match.lastMessage.content
      ).length || 0;
    },
    
    todayLikes() {
      // Cette valeur devrait venir du store/API
      return 5; // Placeholder
    },
    
    filteredMatches() {
      let filtered = [...(this.matches || [])];
      
      // Filtrage
      switch (this.selectedFilter) {
        case 'new':
          const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
          filtered = filtered.filter(match => new Date(match.matchedAt) > oneDayAgo);
          break;
        case 'active':
          filtered = filtered.filter(match => match.lastMessage);
          break;
        case 'recent':
          const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
          filtered = filtered.filter(match => 
            match.profile.lastConnection && new Date(match.profile.lastConnection) > oneHourAgo
          );
          break;
        case 'favorites':
          filtered = filtered.filter(match => match.isFavorite);
          break;
      }
      
      // Tri
      filtered.sort((a, b) => {
        switch (this.sortBy) {
          case 'recent':
            return new Date(b.matchedAt) - new Date(a.matchedAt);
          case 'alphabetical':
            return a.profile.prenom.localeCompare(b.profile.prenom);
          case 'lastMessage':
            const aTime = a.lastMessage?.timestamp || a.matchedAt;
            const bTime = b.lastMessage?.timestamp || b.matchedAt;
            return new Date(bTime) - new Date(aTime);
          case 'compatibility':
            return (b.compatibility || 0) - (a.compatibility || 0);
          default:
            return 0;
        }
      });
      
      // Pagination
      const start = (this.currentPage - 1) * this.matchesPerPage;
      const end = start + this.matchesPerPage;
      return filtered.slice(start, end);
    },
    
    totalPages() {
      return Math.ceil((this.matches?.length || 0) / this.matchesPerPage);
    },
    
    suggestedProfiles() {
      // Retourner des profils suggérés basés sur les matchs existants
      return this.profiles?.slice(0, 4) || [];
    }
  },
  
  methods: {
    ...mapActions(['loadMatches', 'toggleFavoriteMatch', 'unmatchUser']),
    
    calculateAge(birthDate) {
      if (!birthDate) return 'N/A';
      const today = new Date();
      const birth = new Date(birthDate);
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }
      
      return age;
    },
    
    formatMessageTime(timestamp) {
      if (!timestamp) return '';
      
      const now = new Date();
      const messageTime = new Date(timestamp);
      const diffMs = now - messageTime;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      if (diffMins < 1) return 'À l\'instant';
      if (diffMins < 60) return `Il y a ${diffMins} min`;
      if (diffHours < 24) return `Il y a ${diffHours}h`;
      if (diffDays < 7) return `Il y a ${diffDays}j`;
      return messageTime.toLocaleDateString('fr-FR');
    },
    
    formatMatchDate(timestamp) {
      return new Date(timestamp).toLocaleDateString('fr-FR');
    },
    
    getEmptyStateTitle() {
      switch (this.selectedFilter) {
        case 'new': return 'Aucun nouveau match';
        case 'active': return 'Aucune conversation active';
        case 'recent': return 'Aucun match récemment connecté';
        case 'favorites': return 'Aucun favori';
        default: return 'Aucun match pour le moment';
      }
    },
    
    getEmptyStateMessage() {
      switch (this.selectedFilter) {
        case 'new': return 'Continuez à liker des profils pour obtenir de nouveaux matchs !';
        case 'active': return 'Envoyez un message à vos matchs pour démarrer une conversation.';
        case 'recent': return 'Vos matchs ne se sont pas connectés récemment.';
        case 'favorites': return 'Ajoutez des matchs à vos favoris en cliquant sur l\'étoile.';
        default: return 'Découvrez des profils et commencez à matcher !';
      }
    },
    
    filterMatches() {
      this.currentPage = 1;
    },
    
    sortMatches() {
      this.currentPage = 1;
    },
    
    async refreshMatches() {
      this.loading = true;
      try {
        await this.loadMatches();
      } catch (error) {
        console.error('Erreur lors du rafraîchissement:', error);
      } finally {
        this.loading = false;
      }
    },
    
    openConversation(match) {
      this.$router.push(`/messages/${match.id}`);
    },
    
    viewProfile(profileId) {
      this.$router.push(`/profile/${profileId}`);
    },
    
    async toggleFavorite(matchId) {
      try {
        await this.toggleFavoriteMatch(matchId);
      } catch (error) {
        console.error('Erreur lors de la modification des favoris:', error);
      }
    },
    
    async unmatch(matchId) {
      if (confirm('Êtes-vous sûr de vouloir supprimer ce match ?')) {
        try {
          await this.unmatchUser(matchId);
          this.$store.commit('addNotification', {
            type: 'info',
            message: 'Match supprimé',
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Erreur lors de la suppression du match:', error);
        }
      }
    },
    
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    }
  },
  
  async mounted() {
    this.loading = true;
    try {
      await this.loadMatches();
    } catch (error) {
      console.error('Erreur lors du chargement des matchs:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --love-pink: #FF69B4;
  --heart-red: #FF1744;
  --match-gold: #FFD700;
}

.matches-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.matches-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--love-pink);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.matches-actions {
  display: flex;
  gap: 12px;
}

.btn-discover,
.btn-messages {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-discover:hover,
.btn-messages:hover {
  background: var(--heart-red);
  transform: translateY(-2px);
}

.stats-section {
  margin-bottom: 30px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.stat-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--love-pink);
  margin-bottom: 8px;
}

.stat-label {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  font-weight: 500;
}

.filters-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--slogan-gray);
  font-weight: 500;
}

.filter-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  min-width: 150px;
}

.filter-select:focus {
  outline: none;
  border-color: var(--love-pink);
  background: rgba(255, 255, 255, 0.15);
}

.btn-refresh {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: var(--wall-color);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-refresh:hover:not(:disabled) {
  background: var(--sweater-purple);
}

.btn-refresh:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.matches-content {
  margin-bottom: 40px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--love-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h2 {
  color: var(--love-pink);
  margin-bottom: 16px;
  font-size: 1.5rem;
}

.btn-start-matching {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-start-matching:hover {
  background: var(--heart-red);
}

.matches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
}

.match-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.match-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--love-pink);
  box-shadow: 0 20px 40px rgba(255, 105, 180, 0.2);
}

.match-image-container {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.match-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.match-card:hover .match-image {
  transform: scale(1.05);
}

.status-indicators {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: flex-end;
}

.online-indicator {
  width: 12px;
  height: 12px;
  background: #4ade80;
  border: 2px solid var(--text-white);
  border-radius: 50%;
}

.new-match-badge {
  padding: 4px 8px;
  background: var(--match-gold);
  color: var(--header-bg);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.favorite-indicator {
  font-size: 1.2rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.compatibility-badge {
  position: absolute;
  bottom: 12px;
  left: 12px;
  padding: 6px 12px;
  background: var(--love-pink);
  color: var(--text-white);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.match-info {
  padding: 20px;
  position: relative;
}

.match-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.match-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--love-pink);
  margin: 0;
}

.match-age {
  color: var(--slogan-gray);
  font-size: 1rem;
}

.match-location {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--slogan-gray);
  font-size: 0.9rem;
  margin-bottom: 16px;
}

.distance {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.last-interaction {
  margin-bottom: 16px;
}

.last-message {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.message-preview {
  color: var(--slogan-gray);
  font-size: 0.9rem;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.message-time {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.no-message {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.match-time {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.start-conversation {
  color: var(--love-pink);
  font-size: 0.9rem;
  font-weight: 500;
}

.unread-indicator {
  position: absolute;
  top: 16px;
  right: 16px;
  width: 24px;
  height: 24px;
  background: var(--heart-red);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
}

.match-actions {
  display: flex;
  justify-content: space-around;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.btn-favorite,
.btn-view-profile,
.btn-message,
.btn-unmatch {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-favorite {
  background: transparent;
  color: var(--icon-color);
}

.btn-favorite.active {
  color: var(--match-gold);
}

.btn-favorite:hover {
  background: rgba(255, 215, 0, 0.2);
  color: var(--match-gold);
}

.btn-view-profile {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-view-profile:hover {
  background: var(--accent-purple);
  transform: scale(1.1);
}

.btn-message {
  background: var(--love-pink);
  color: var(--text-white);
}

.btn-message:hover {
  background: var(--heart-red);
  transform: scale(1.1);
}

.btn-unmatch {
  background: #6b7280;
  color: var(--text-white);
}

.btn-unmatch:hover {
  background: #ef4444;
  transform: scale(1.1);
}

.suggestions-section {
  margin-bottom: 40px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.suggestions-section h2 {
  color: var(--love-pink);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 20px;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.suggestion-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.suggestion-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
  border-color: var(--love-pink);
}

.suggestion-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  object-fit: cover;
  margin: 0 auto 12px;
  border: 2px solid var(--love-pink);
}

.suggestion-info h4 {
  color: var(--love-pink);
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 4px;
}

.suggestion-info span {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.suggestion-compatibility {
  margin-top: 8px;
  padding: 4px 8px;
  background: var(--love-pink);
  color: var(--text-white);
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  display: inline-block;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}

.btn-page {
  padding: 10px 20px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-page:hover:not(:disabled) {
  background: var(--heart-red);
}

.btn-page:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.page-info {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .matches-grid {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .matches-container {
    padding: 16px;
  }

  .matches-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .matches-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .suggestions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .match-actions {
    padding: 12px 16px;
  }

  .btn-favorite,
  .btn-view-profile,
  .btn-message,
  .btn-unmatch {
    width: 36px;
    height: 36px;
    font-size: 1rem;
  }
}
</style>
