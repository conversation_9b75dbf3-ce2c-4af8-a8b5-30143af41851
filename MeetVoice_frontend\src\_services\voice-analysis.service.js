/**
 * Service d'analyse vocale pour MeetVoice
 * Détection du genre et autres caractéristiques vocales
 * @format
 */

class VoiceAnalysisService {
  constructor() {
    this.isSupported = this.checkSupport();
    this.audioContext = null;
    this.analyser = null;
    this.voiceData = {
      fundamentalFrequency: [],
      formants: [],
      spectralCentroid: [],
      samples: 0
    };
  }

  /**
   * Vérifier le support de l'analyse vocale
   * @returns {boolean}
   */
  checkSupport() {
    return !!(window.AudioContext || window.webkitAudioContext) && 
           !!navigator.mediaDevices && 
           !!navigator.mediaDevices.getUserMedia;
  }

  /**
   * Analyser un flux audio pour détecter le genre
   * @param {MediaStream} stream - Flux audio du microphone
   * @returns {Promise<Object>} Résultat de l'analyse
   */
  async analyzeVoiceGender(stream) {
    if (!this.isSupported) {
      throw new Error('Analyse vocale non supportée');
    }

    try {
      // Initialiser le contexte audio
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      this.analyser = this.audioContext.createAnalyser();
      
      // Configuration de l'analyseur
      this.analyser.fftSize = 2048;
      this.analyser.smoothingTimeConstant = 0.8;
      
      // Connecter le flux audio
      const source = this.audioContext.createMediaStreamSource(stream);
      source.connect(this.analyser);
      
      // Analyser pendant 3 secondes
      const analysisResult = await this.performAnalysis(3000);
      
      // Nettoyer
      this.cleanup();
      
      return analysisResult;
      
    } catch (error) {
      this.cleanup();
      throw new Error(`Erreur d'analyse vocale: ${error.message}`);
    }
  }

  /**
   * Effectuer l'analyse pendant une durée donnée
   * @param {number} duration - Durée en millisecondes
   * @returns {Promise<Object>}
   */
  performAnalysis(duration) {
    return new Promise((resolve) => {
      const startTime = Date.now();
      const bufferLength = this.analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);
      const frequencyData = new Float32Array(bufferLength);
      
      let sampleCount = 0;
      let totalF0 = 0;
      let f0Samples = [];
      let spectralCentroids = [];
      
      const analyze = () => {
        const currentTime = Date.now();
        
        if (currentTime - startTime < duration) {
          // Obtenir les données de fréquence
          this.analyser.getByteFrequencyData(dataArray);
          this.analyser.getFloatFrequencyData(frequencyData);
          
          // Calculer la fréquence fondamentale (F0)
          const f0 = this.calculateFundamentalFrequency(dataArray);
          if (f0 > 0) {
            f0Samples.push(f0);
            totalF0 += f0;
            sampleCount++;
          }
          
          // Calculer le centroïde spectral
          const spectralCentroid = this.calculateSpectralCentroid(frequencyData);
          if (spectralCentroid > 0) {
            spectralCentroids.push(spectralCentroid);
          }
          
          // Continuer l'analyse
          requestAnimationFrame(analyze);
        } else {
          // Analyse terminée
          const result = this.analyzeGenderFromFeatures(f0Samples, spectralCentroids);
          resolve(result);
        }
      };
      
      analyze();
    });
  }

  /**
   * Calculer la fréquence fondamentale
   * @param {Uint8Array} frequencyData
   * @returns {number}
   */
  calculateFundamentalFrequency(frequencyData) {
    const sampleRate = this.audioContext.sampleRate;
    const nyquist = sampleRate / 2;
    const binSize = nyquist / frequencyData.length;
    
    let maxAmplitude = 0;
    let maxIndex = 0;
    
    // Chercher le pic principal entre 80Hz et 400Hz (plage vocale humaine)
    const minBin = Math.floor(80 / binSize);
    const maxBin = Math.floor(400 / binSize);
    
    for (let i = minBin; i < maxBin && i < frequencyData.length; i++) {
      if (frequencyData[i] > maxAmplitude) {
        maxAmplitude = frequencyData[i];
        maxIndex = i;
      }
    }
    
    // Retourner la fréquence correspondante
    return maxIndex * binSize;
  }

  /**
   * Calculer le centroïde spectral
   * @param {Float32Array} frequencyData
   * @returns {number}
   */
  calculateSpectralCentroid(frequencyData) {
    let numerator = 0;
    let denominator = 0;
    
    for (let i = 0; i < frequencyData.length; i++) {
      const magnitude = Math.pow(10, frequencyData[i] / 20); // Convertir dB en magnitude
      numerator += i * magnitude;
      denominator += magnitude;
    }
    
    return denominator > 0 ? numerator / denominator : 0;
  }

  /**
   * Analyser le genre basé sur les caractéristiques vocales
   * @param {Array} f0Samples - Échantillons de fréquence fondamentale
   * @param {Array} spectralCentroids - Centroïdes spectraux
   * @returns {Object}
   */
  analyzeGenderFromFeatures(f0Samples, spectralCentroids) {
    if (f0Samples.length === 0) {
      return {
        gender: null,
        confidence: 0,
        reason: 'Pas assez de données vocales',
        features: {}
      };
    }

    // Calculer les statistiques
    const avgF0 = f0Samples.reduce((a, b) => a + b, 0) / f0Samples.length;
    const avgSpectralCentroid = spectralCentroids.length > 0 
      ? spectralCentroids.reduce((a, b) => a + b, 0) / spectralCentroids.length 
      : 0;

    // Seuils basés sur la recherche en acoustique vocale
    const MALE_F0_THRESHOLD = 165; // Hz
    const FEMALE_F0_THRESHOLD = 165; // Hz
    
    let genderScore = 0;
    let confidence = 0;
    let features = {
      averageF0: Math.round(avgF0),
      spectralCentroid: Math.round(avgSpectralCentroid),
      samples: f0Samples.length
    };

    // Analyse basée sur la fréquence fondamentale
    if (avgF0 < MALE_F0_THRESHOLD) {
      genderScore -= 1; // Plus probablement masculin
      confidence += 0.4;
    } else if (avgF0 > FEMALE_F0_THRESHOLD) {
      genderScore += 1; // Plus probablement féminin
      confidence += 0.4;
    }

    // Analyse basée sur le centroïde spectral (les voix féminines ont généralement un centroïde plus élevé)
    if (avgSpectralCentroid > 0) {
      if (avgSpectralCentroid > 1000) {
        genderScore += 0.5; // Plus probablement féminin
        confidence += 0.3;
      } else if (avgSpectralCentroid < 800) {
        genderScore -= 0.5; // Plus probablement masculin
        confidence += 0.3;
      }
    }

    // Déterminer le genre final
    let predictedGender = null;
    if (genderScore > 0.5) {
      predictedGender = 'F';
    } else if (genderScore < -0.5) {
      predictedGender = 'M';
    }

    // Ajuster la confiance
    confidence = Math.min(confidence, 0.8); // Maximum 80% de confiance
    
    return {
      gender: predictedGender,
      confidence: Math.round(confidence * 100),
      features: features,
      reason: this.getGenderReason(avgF0, avgSpectralCentroid, predictedGender)
    };
  }

  /**
   * Obtenir une explication du résultat
   * @param {number} f0
   * @param {number} spectralCentroid
   * @param {string} gender
   * @returns {string}
   */
  getGenderReason(f0, spectralCentroid, gender) {
    if (!gender) {
      return 'Caractéristiques vocales ambiguës ou insuffisantes';
    }

    const reasons = [];
    
    if (gender === 'F') {
      if (f0 > 165) reasons.push('fréquence vocale élevée');
      if (spectralCentroid > 1000) reasons.push('timbre aigu');
    } else if (gender === 'M') {
      if (f0 < 165) reasons.push('fréquence vocale grave');
      if (spectralCentroid < 800) reasons.push('timbre grave');
    }

    return reasons.length > 0 
      ? `Détection basée sur: ${reasons.join(', ')}`
      : 'Analyse des caractéristiques vocales';
  }

  /**
   * Nettoyer les ressources
   */
  cleanup() {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    this.analyser = null;
  }

  /**
   * Analyser rapidement le genre depuis un flux audio
   * @param {MediaStream} stream
   * @returns {Promise<Object>}
   */
  async quickGenderDetection(stream) {
    try {
      // Analyse rapide de 2 secondes
      const result = await this.analyzeVoiceGender(stream);
      
      // Retourner seulement si la confiance est suffisante
      if (result.confidence >= 60) {
        return {
          success: true,
          gender: result.gender,
          confidence: result.confidence,
          features: result.features
        };
      } else {
        return {
          success: false,
          reason: 'Confiance insuffisante',
          confidence: result.confidence
        };
      }
      
    } catch (error) {
      return {
        success: false,
        reason: error.message,
        confidence: 0
      };
    }
  }
}

// Instance singleton
export const voiceAnalysisService = new VoiceAnalysisService();
export default voiceAnalysisService;
