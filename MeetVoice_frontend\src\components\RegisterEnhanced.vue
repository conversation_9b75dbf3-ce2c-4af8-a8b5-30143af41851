<!-- @format -->

<template>
  <div class="register-enhanced">
    <div class="register-container">
      <div class="header">
        <h2>🎤 Inscription MeetVoice</h2>
        <p>Créez votre profil en parlant naturellement</p>
      </div>

      <!-- Choix du mode d'inscription -->
      <div v-if="!modeSelected" class="mode-selection">
        <h3>Comment souhaitez-vous vous inscrire ?</h3>
        
        <div class="mode-options">
          <button @click="selectMode('voice')" class="mode-btn voice-mode">
            <span class="mode-icon">🎤</span>
            <div class="mode-info">
              <h4>Interview Vocal</h4>
              <p>Répondez aux questions en parlant<br><span class="highlight">(recommandé)</span></p>
            </div>
          </button>
          
          <button @click="selectMode('form')" class="mode-btn form-mode">
            <span class="mode-icon">📝</span>
            <div class="mode-info">
              <h4>Formulaire Classique</h4>
              <p>Remplissez les champs manuellement<br><span class="traditional">(méthode traditionnelle)</span></p>
            </div>
          </button>
        </div>
      </div>

      <!-- Configuration vocale -->
      <div v-if="modeSelected && selectedMode === 'voice' && !configCompleted" class="config-section">
        <VoiceConfig
          @config-complete="handleConfigComplete"
        />
      </div>

      <!-- Interview vocale -->
      <div v-if="modeSelected && selectedMode === 'voice' && configCompleted" class="voice-section">
        <VoiceInterviewSimple
          :user-email="userEmail"
          @answers-saved="handleVoiceAnswers"
          @interview-completed="handleInterviewCompleted"
        />

        <div class="mode-switch">
          <button @click="switchToForm" class="switch-btn">
            📝 Passer au formulaire classique
          </button>
        </div>
      </div>

      <!-- Formulaire classique -->
      <div v-if="modeSelected && selectedMode === 'form'" class="form-section">
        <form @submit.prevent="submitForm" class="register-form">
          <!-- Informations de connexion -->
          <div class="form-group">
            <h3>Informations de connexion</h3>
            
            <div class="input-group">
              <label for="email">Email *</label>
              <input 
                type="email" 
                id="email" 
                v-model="formData.email" 
                required 
                placeholder="<EMAIL>"
              >
            </div>
            
            <div class="input-group">
              <label for="password">Mot de passe *</label>
              <input 
                type="password" 
                id="password" 
                v-model="formData.password" 
                required 
                placeholder="Minimum 8 caractères"
              >
            </div>
            
            <div class="input-group">
              <label for="username">Nom d'utilisateur *</label>
              <input 
                type="text" 
                id="username" 
                v-model="formData.username" 
                required 
                placeholder="Votre pseudo"
              >
            </div>
          </div>

          <!-- Informations personnelles -->
          <div class="form-group">
            <h3>Informations personnelles</h3>
            
            <div class="input-row">
              <div class="input-group">
                <label for="prenom">Prénom *</label>
                <input 
                  type="text" 
                  id="prenom" 
                  v-model="formData.prenom" 
                  required 
                  placeholder="Votre prénom"
                >
              </div>
              
              <div class="input-group">
                <label for="nom">Nom *</label>
                <input 
                  type="text" 
                  id="nom" 
                  v-model="formData.nom" 
                  required 
                  placeholder="Votre nom"
                >
              </div>
            </div>
            
            <div class="input-group">
              <label for="date_de_naissance">Date de naissance *</label>
              <input 
                type="date" 
                id="date_de_naissance" 
                v-model="formData.date_de_naissance" 
                required
              >
            </div>
            
            <div class="input-row">
              <div class="input-group">
                <label for="sexe">Sexe *</label>
                <select id="sexe" v-model="formData.sexe" required>
                  <option value="">Sélectionnez</option>
                  <option value="Homme">Homme</option>
                  <option value="Femme">Femme</option>
                  <option value="Homosexuelle">Homosexuelle</option>
                  <option value="Lesbienne">Lesbienne</option>
                  <option value="Bisexuelle">Bisexuelle</option>
                  <option value="Transgenre">Transgenre</option>
                </select>
              </div>
              
              <div class="input-group">
                <label for="recherche">Que recherchez-vous ? *</label>
                <select id="recherche" v-model="formData.recherche" required>
                  <option value="">Sélectionnez</option>
                  <option value="Amical">Amitié</option>
                  <option value="Amour">Relation sérieuse</option>
                  <option value="Libertin">Libertin</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Informations physiques -->
          <div class="form-group">
            <h3>Informations physiques (optionnel)</h3>
            
            <div class="input-row">
              <div class="input-group">
                <label for="taille">Taille (cm)</label>
                <input 
                  type="number" 
                  id="taille" 
                  v-model="formData.taille" 
                  placeholder="175"
                  min="100" 
                  max="250"
                >
              </div>
              
              <div class="input-group">
                <label for="poids">Poids (kg)</label>
                <input 
                  type="text" 
                  id="poids" 
                  v-model="formData.poids" 
                  placeholder="70"
                >
              </div>
            </div>
            
            <div class="input-row">
              <div class="input-group">
                <label for="yeux">Couleur des yeux</label>
                <select id="yeux" v-model="formData.yeux">
                  <option value="">Sélectionnez</option>
                  <option value="Noire">Noire</option>
                  <option value="Marron">Marron</option>
                  <option value="Bleu">Bleu</option>
                  <option value="Vert">Vert</option>
                  <option value="Noisette">Noisette</option>
                </select>
              </div>
              
              <div class="input-group">
                <label for="shilhouette">Silhouette</label>
                <select id="shilhouette" v-model="formData.shilhouette">
                  <option value="">Sélectionnez</option>
                  <option value="Normal">Normal</option>
                  <option value="Mince">Mince</option>
                  <option value="Athlétique">Athlétique</option>
                  <option value="Ronde">Ronde</option>
                  <option value="Forte">Forte</option>
                  <option value="Handicapé">Handicapé</option>
                  <option value="Chaise Roulante">Chaise Roulante</option>
                </select>
              </div>
            </div>
          </div>

          <!-- Informations culturelles -->
          <div class="form-group">
            <h3>Informations culturelles (optionnel)</h3>
            
            <div class="input-row">
              <div class="input-group">
                <label for="ethnique">Origine ethnique</label>
                <select id="ethnique" v-model="formData.ethnique">
                  <option value="">Sélectionnez</option>
                  <option value="Caucasien">Caucasien</option>
                  <option value="Métisse">Métisse</option>
                  <option value="Arabe">Arabe</option>
                  <option value="Africaine">Africaine</option>
                  <option value="Indienne">Indienne</option>
                  <option value="Latine">Latine</option>
                  <option value="Asiatique">Asiatique</option>
                </select>
              </div>
              
              <div class="input-group">
                <label for="religion">Religion</label>
                <select id="religion" v-model="formData.religion">
                  <option value="">Sélectionnez</option>
                  <option value="Athé">Athé</option>
                  <option value="Catholique">Catholique</option>
                  <option value="Musulman">Musulman</option>
                  <option value="Bouddhisme">Bouddhisme</option>
                  <option value="Indouisme">Indouisme</option>
                  <option value="Juive">Juive</option>
                  <option value="Protestante">Protestante</option>
                  <option value="Orthodoxe">Orthodoxe</option>
                  <option value="Agnosticisme">Agnosticisme</option>
                </select>
              </div>
            </div>
            
            <div class="input-group">
              <label for="metier">Métier</label>
              <input 
                type="text" 
                id="metier" 
                v-model="formData.metier" 
                placeholder="Votre profession"
              >
            </div>
          </div>

          <!-- Description -->
          <div class="form-group">
            <h3>À propos de vous</h3>
            <div class="input-group">
              <label for="description">Description personnelle</label>
              <textarea 
                id="description" 
                v-model="formData.description_personnelle" 
                placeholder="Décrivez-vous en quelques mots..."
                rows="4"
              ></textarea>
            </div>
          </div>

          <!-- Boutons -->
          <div class="form-actions">
            <button type="button" @click="switchToVoice" class="switch-btn">
              🎤 Passer à l'interview vocal
            </button>
            <button type="submit" class="submit-btn" :disabled="isSubmitting">
              {{ isSubmitting ? 'Inscription...' : '✅ S\'inscrire' }}
            </button>
          </div>
        </form>
      </div>

      <!-- Messages -->
      <div v-if="message" class="message" :class="messageType">
        {{ message }}
      </div>
    </div>
  </div>
</template>

<script>
import VoiceInterviewSimple from './VoiceInterviewSimple.vue';
import VoiceConfig from './VoiceConfig.vue';

export default {
  name: 'RegisterEnhanced',
  components: {
    VoiceInterviewSimple,
    VoiceConfig
  },
  data() {
    return {
      modeSelected: false,
      selectedMode: null, // 'voice' ou 'form'
      configCompleted: false,
      userEmail: '',
      isSubmitting: false,
      message: '',
      messageType: 'success', // 'success' ou 'error'
      
      // Données du formulaire
      formData: {
        // Champs obligatoires
        email: '',
        password: '',
        username: '',
        nom: '',
        prenom: '',
        date_de_naissance: '',
        sexe: '',
        recherche: '',
        
        // Champs optionnels
        taille: null,
        poids: '',
        ethnique: '',
        religion: '',
        yeux: '',
        shilhouette: '',
        metier: '',
        description_personnelle: ''
      }
    };
  },
  
  methods: {
    selectMode(mode) {
      this.selectedMode = mode;
      this.modeSelected = true;
      this.message = '';
    },
    
    switchToVoice() {
      this.selectedMode = 'voice';
      this.message = '';
    },
    
    switchToForm() {
      this.selectedMode = 'form';
      this.message = '';
    },

    handleConfigComplete(configData) {
      this.configCompleted = true;
      this.userEmail = configData.email;
      console.log('✅ Configuration terminée:', configData);
    },
    
    handleInterviewCompleted(answers) {
      console.log('Interview terminée:', answers);
      this.message = 'Interview terminée ! Vérifiez vos réponses et sauvegardez.';
      this.messageType = 'success';
    },
    
    handleVoiceAnswers(interviewData) {
      console.log('Données de l\'interview:', interviewData);
      
      // Mapper les données vocales vers le formulaire
      if (interviewData.backend_fields) {
        Object.assign(this.formData, interviewData.backend_fields);
      }
      
      // Soumettre automatiquement
      this.submitRegistration(interviewData.backend_fields);
    },
    
    async submitForm() {
      // Validation des champs obligatoires
      const requiredFields = ['email', 'password', 'username', 'nom', 'prenom', 'date_de_naissance', 'sexe', 'recherche'];
      const missingFields = requiredFields.filter(field => !this.formData[field]);
      
      if (missingFields.length > 0) {
        this.message = `Veuillez remplir les champs obligatoires : ${missingFields.join(', ')}`;
        this.messageType = 'error';
        return;
      }
      
      await this.submitRegistration(this.formData);
    },
    
    async submitRegistration(data) {
      try {
        this.isSubmitting = true;
        this.message = '';
        
        console.log('Données à envoyer au backend:', data);
        
        // TODO: Remplacer par l'appel API réel
        // const response = await fetch('/api/register', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify(data)
        // });
        
        // Simulation de l'inscription
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        this.message = '🎉 Inscription réussie ! Bienvenue sur MeetVoice !';
        this.messageType = 'success';
        
        // Redirection après succès
        setTimeout(() => {
          this.$router.push('/login');
        }, 2000);
        
      } catch (error) {
        console.error('Erreur inscription:', error);
        this.message = 'Erreur lors de l\'inscription. Veuillez réessayer.';
        this.messageType = 'error';
      } finally {
        this.isSubmitting = false;
      }
    }
  }
};
</script>

<style scoped>
.register-enhanced {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  padding: 20px;
}

.register-container {
  max-width: 800px;
  margin: 0 auto;
  background: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.08);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  text-align: center;
  padding: 50px 20px;
}

.header h2 {
  margin: 0 0 15px 0;
  font-size: 32px;
  font-weight: 700;
  background: linear-gradient(45deg, #fff, #f1f5f9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  margin: 0;
  opacity: 0.95;
  font-size: 18px;
  font-weight: 300;
}

/* Sélection du mode */
.mode-selection {
  padding: 50px 40px;
  text-align: center;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

.mode-selection h3 {
  color: #1e293b;
  margin-bottom: 40px;
  font-size: 26px;
  font-weight: 600;
}

.mode-options {
  display: flex;
  gap: 30px;
  justify-content: center;
  flex-wrap: wrap;
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 30px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 300px;
  min-height: 120px;
  text-align: left;
  position: relative;
  overflow: hidden;
}

.mode-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  transition: left 0.5s;
}

.mode-btn:hover::before {
  left: 100%;
}

.mode-btn:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.15);
}

.mode-btn.voice-mode:hover {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.mode-btn.form-mode:hover {
  background: linear-gradient(135deg, #764ba2, #667eea);
  color: white;
}

.mode-icon {
  font-size: 40px;
  flex-shrink: 0;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
}

.mode-info h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: inherit;
}

.mode-info p {
  margin: 0;
  font-size: 15px;
  opacity: 0.8;
  color: inherit;
  line-height: 1.5;
}

.mode-info .highlight {
  color: #22c55e;
  font-weight: 600;
  font-size: 13px;
  opacity: 1;
}

.mode-info .traditional {
  color: #6366f1;
  font-weight: 500;
  font-size: 13px;
  opacity: 1;
}

/* Sections */
.voice-section,
.form-section {
  padding: 50px 40px;
  background: linear-gradient(135deg, #fafbfc 0%, #f8fafc 100%);
}

/* Formulaire */
.register-form {
  max-width: 650px;
  margin: 0 auto;
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.05);
}

.form-group {
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e2e8f0;
}

.form-group:last-child {
  border-bottom: none;
}

.form-group h3 {
  color: #1e293b;
  margin-bottom: 25px;
  font-size: 20px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
}

.input-group {
  margin-bottom: 20px;
}

.input-row {
  display: flex;
  gap: 15px;
}

.input-row .input-group {
  flex: 1;
}

.input-group label {
  display: block;
  margin-bottom: 10px;
  color: #374151;
  font-weight: 600;
  font-size: 15px;
}

.input-group input,
.input-group select,
.input-group textarea {
  width: 100%;
  padding: 16px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 15px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-sizing: border-box;
  background: #fafbfc;
}

.input-group input:focus,
.input-group select:focus,
.input-group textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
  background: white;
  transform: translateY(-1px);
}

.input-group textarea {
  resize: vertical;
  min-height: 100px;
}

/* Boutons */
.form-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-top: 40px;
  flex-wrap: wrap;
}

.switch-btn,
.submit-btn {
  padding: 16px 32px;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 200px;
  position: relative;
  overflow: hidden;
}

.switch-btn {
  background: #f1f5f9;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.switch-btn:hover {
  background: #e2e8f0;
  border-color: #cbd5e1;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0,0,0,0.1);
}

.submit-btn {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.mode-switch {
  text-align: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* Messages */
.message {
  margin: 20px 40px;
  padding: 20px 25px;
  border-radius: 12px;
  text-align: center;
  font-weight: 500;
  border-left: 4px solid;
}

.message.success {
  background: linear-gradient(135deg, #ecfdf5, #d1fae5);
  color: #065f46;
  border-left-color: #10b981;
  box-shadow: 0 4px 15px rgba(16, 185, 129, 0.1);
}

.message.error {
  background: linear-gradient(135deg, #fef2f2, #fee2e2);
  color: #991b1b;
  border-left-color: #ef4444;
  box-shadow: 0 4px 15px rgba(239, 68, 68, 0.1);
}

/* Responsive */
@media (max-width: 768px) {
  .register-enhanced {
    padding: 10px;
  }

  .header {
    padding: 30px 20px;
  }

  .header h2 {
    font-size: 24px;
  }

  .mode-selection,
  .voice-section,
  .form-section {
    padding: 20px;
  }

  .mode-options {
    flex-direction: column;
    align-items: center;
  }

  .mode-btn {
    min-width: 100%;
    max-width: 350px;
  }

  .input-row {
    flex-direction: column;
    gap: 0;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
  }

  .switch-btn,
  .submit-btn {
    width: 100%;
    max-width: 300px;
  }
}

@media (max-width: 480px) {
  .header h2 {
    font-size: 20px;
  }

  .mode-selection h3 {
    font-size: 18px;
  }

  .mode-btn {
    padding: 20px;
    min-width: 100%;
    min-height: 100px;
  }

  .mode-icon {
    font-size: 24px;
  }

  .mode-info h4 {
    font-size: 16px;
  }

  .mode-info p {
    font-size: 13px;
  }
}
</style>
