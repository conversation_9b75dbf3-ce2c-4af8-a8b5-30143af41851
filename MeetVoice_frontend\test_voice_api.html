<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API TTS MeetVoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎤 Test API TTS MeetVoice</h1>
        <p>Ce test vérifie que l'API TTS backend fonctionne correctement avec les voix sélectionnées.</p>

        <div class="test-section">
            <h3>Test 1: Voix Jenny (ID: 4)</h3>
            <button onclick="testVoice(4, 'Jenny')">Tester Jenny</button>
            <div id="result-jenny" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 2: Voix Stephanie (ID: 5)</h3>
            <button onclick="testVoice(5, 'Stephanie')">Tester Stephanie</button>
            <div id="result-stephanie" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 3: Vitesse et tonalité personnalisées</h3>
            <button onclick="testCustomVoice()">Tester voix personnalisée</button>
            <div id="result-custom" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>Test 4: Simulation interview</h3>
            <button onclick="testInterviewQuestion()">Tester question d'interview</button>
            <div id="result-interview" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        async function testVoice(voiceId, voiceName) {
            const resultDiv = document.getElementById(`result-${voiceName.toLowerCase()}`);
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = `🔄 Test de la voix ${voiceName} en cours...`;

            try {
                const response = await fetch('http://localhost:8000/tts/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: `Bonjour ! Je suis ${voiceName}, votre voix d'interview MeetVoice. Cette voix vous convient-elle ?`,
                        voice_id: voiceId,
                        language: 'fr',
                        speed: 1.0,
                        pitch: 1.0
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.audio_url) {
                        // Jouer l'audio
                        const audio = new Audio(data.audio_url);
                        audio.play();
                        
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = `✅ Voix ${voiceName} testée avec succès !<br>URL: ${data.audio_url}`;
                    } else {
                        throw new Error('Pas d\'URL audio dans la réponse');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur test voix ${voiceName}: ${error.message}`;
            }
        }

        async function testCustomVoice() {
            const resultDiv = document.getElementById('result-custom');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 Test voix personnalisée en cours...';

            try {
                const response = await fetch('http://localhost:8000/tts/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: 'Test de vitesse et tonalité personnalisées. Cette voix est plus rapide et plus aiguë.',
                        voice_id: 4, // Jenny
                        language: 'fr',
                        speed: 1.3, // Plus rapide
                        pitch: 1.2  // Plus aigu
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.audio_url) {
                        const audio = new Audio(data.audio_url);
                        audio.play();
                        
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Voix personnalisée testée avec succès !<br>Vitesse: 1.3, Tonalité: 1.2';
                    } else {
                        throw new Error('Pas d\'URL audio dans la réponse');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur test voix personnalisée: ${error.message}`;
            }
        }

        async function testInterviewQuestion() {
            const resultDiv = document.getElementById('result-interview');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.innerHTML = '🔄 Test question d\'interview en cours...';

            try {
                const response = await fetch('http://localhost:8000/tts/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text: 'Question 1. Pour votre adresse email, veuillez l\'écrire dans le champ de saisie manuelle ci-dessous. Conseil : Utilisez la saisie manuelle pour votre email.',
                        voice_id: 4, // Jenny
                        language: 'fr',
                        speed: 1.0,
                        pitch: 1.0
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.audio_url) {
                        const audio = new Audio(data.audio_url);
                        audio.play();
                        
                        resultDiv.className = 'result success';
                        resultDiv.innerHTML = '✅ Question d\'interview testée avec succès !<br>Simulation de l\'interview vocal MeetVoice';
                    } else {
                        throw new Error('Pas d\'URL audio dans la réponse');
                    }
                } else {
                    throw new Error(`Erreur HTTP: ${response.status}`);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ Erreur test interview: ${error.message}`;
            }
        }
    </script>
</body>
</html>
