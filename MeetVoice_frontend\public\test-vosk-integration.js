/**
 * Script de Test Automatique - Intégration Vosk
 * À exécuter dans la console du navigateur
 */

console.log('🚀 === DÉBUT DES TESTS AUTOMATIQUES VOSK ===');

// Configuration
const BACKEND_URL = 'http://127.0.0.1:8000';

// Fonction principale de test
async function runVoskIntegrationTests() {
  try {
    console.log('🔄 Chargement des services...');

    // Test 1: Vérification de l'API Vosk
    console.log('\n🎯 TEST 1: API Vosk Backend');
    try {
      const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API Vosk disponible:', data);
      } else {
        console.log('❌ API Vosk indisponible - Status:', response.status);
      }
    } catch (error) {
      console.log('❌ Erreur API Vosk:', error.message);
    }

    // Test 2: Test des endpoints
    console.log('\n🔗 TEST 2: Endpoints Vosk');
    const endpoints = [
      `${BACKEND_URL}/api/vosk/status/`,
      `${BACKEND_URL}/api/vosk/speech-to-text/`,
      `${BACKEND_URL}/api/vosk/languages/`
    ];

    for (const endpoint of endpoints) {
      try {
        const response = await fetch(endpoint, { method: 'HEAD' });
        console.log(`${response.ok ? '✅' : '❌'} ${endpoint} - Status: ${response.status}`);
      } catch (error) {
        console.log(`❌ ${endpoint} - Erreur: ${error.message}`);
      }
    }

    // Test 3: Test de connectivité générale
    console.log('\n📡 TEST 3: Connectivité Backend');
    try {
      const response = await fetch(`${BACKEND_URL}/api/`);
      console.log(`${response.ok ? '✅' : '❌'} Backend accessible - Status: ${response.status}`);
    } catch (error) {
      console.log('❌ Backend inaccessible:', error.message);
    }

    // Test 4: Test des services frontend (si disponibles)
    console.log('\n🎤 TEST 4: Services Frontend');
    
    // Vérifier si les services sont chargés
    if (typeof window !== 'undefined') {
      console.log('✅ Environnement navigateur détecté');
      
      // Test MediaRecorder
      if (window.MediaRecorder) {
        console.log('✅ MediaRecorder disponible');
      } else {
        console.log('❌ MediaRecorder non disponible');
      }
      
      // Test getUserMedia
      if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
        console.log('✅ getUserMedia disponible');
      } else {
        console.log('❌ getUserMedia non disponible');
      }
      
      // Test Speech Recognition
      if (window.SpeechRecognition || window.webkitSpeechRecognition) {
        console.log('✅ Speech Recognition disponible');
      } else {
        console.log('❌ Speech Recognition non disponible');
      }
    }

    // Test 5: Test d'upload audio simulé
    console.log('\n🎵 TEST 5: Upload Audio Simulé');
    try {
      // Créer un blob audio de test
      const audioBlob = new Blob(['test audio data'], { type: 'audio/webm' });
      const formData = new FormData();
      formData.append('audio', audioBlob, 'test.webm');
      formData.append('language', 'fr-FR');

      const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Upload audio réussi:', result);
      } else {
        console.log('❌ Upload audio échec - Status:', response.status);
      }
    } catch (error) {
      console.log('❌ Erreur upload audio:', error.message);
    }

    console.log('\n🎉 === TESTS TERMINÉS ===');
    console.log('📊 Vérifiez les résultats ci-dessus pour le statut de chaque test.');

  } catch (error) {
    console.error('❌ Erreur générale des tests:', error);
  }
}

// Test rapide
async function quickVoskTest() {
  console.log('⚡ Test rapide API Vosk...');
  try {
    const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API Vosk OK:', data);
      return true;
    } else {
      console.log('❌ API Vosk KO - Status:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ API Vosk Erreur:', error.message);
    return false;
  }
}

// Test de performance
async function performanceTest() {
  console.log('⏱️ Test de performance...');
  
  const startTime = performance.now();
  
  try {
    const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    console.log(`⏱️ Temps de réponse API: ${duration.toFixed(2)}ms`);
    
    if (duration < 100) {
      console.log('🚀 Performance excellente !');
    } else if (duration < 500) {
      console.log('✅ Performance bonne');
    } else {
      console.log('⚠️ Performance lente');
    }
    
    return duration;
  } catch (error) {
    console.log('❌ Erreur test performance:', error.message);
    return null;
  }
}

// Fonctions disponibles globalement
window.runVoskIntegrationTests = runVoskIntegrationTests;
window.quickVoskTest = quickVoskTest;
window.performanceTest = performanceTest;

console.log('🧪 Tests automatiques chargés !');
console.log('📋 Commandes disponibles:');
console.log('  - runVoskIntegrationTests() : Tests complets');
console.log('  - quickVoskTest() : Test rapide');
console.log('  - performanceTest() : Test de performance');
console.log('');
console.log('🚀 Lancez: runVoskIntegrationTests()');
