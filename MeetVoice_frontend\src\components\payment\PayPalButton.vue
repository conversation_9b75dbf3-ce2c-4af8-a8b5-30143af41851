<template>
  <div class="paypal-button-container">
    <!-- Bouton PayPal -->
    <div
      ref="paypalButton"
      class="paypal-button"
      :class="{ 'loading': loading }"
    ></div>

    <!-- État de chargement -->
    <div v-if="loading" class="loading-overlay">
      <div class="spinner"></div>
      <p>Initialisation de PayPal...</p>
    </div>

    <!-- Messages d'erreur -->
    <div v-if="error" class="error-message">
      <span class="error-icon">⚠️</span>
      {{ error }}
      <button @click="retry" class="retry-button">Réessayer</button>
    </div>

    <!-- Informations de sécurité -->
    <div class="security-info">
      <div class="security-badges">
        <span class="badge">🔒 Paiement sécurisé</span>
        <span class="badge">✅ Protection acheteur</span>
        <span class="badge">💳 Pas de frais supplémentaires</span>
      </div>
      <p class="security-text">
        Vos informations de paiement sont protégées par le chiffrement SSL de PayPal.
        Vous pouvez payer avec votre compte PayPal ou votre carte bancaire.
      </p>
    </div>
  </div>
</template>

<script>
import paypalService from '@/services/paypal'

export default {
  name: 'PayPalButton',
  
  props: {
    amount: {
      type: Number,
      required: true
    },
    
    currency: {
      type: String,
      default: 'EUR'
    },
    
    description: {
      type: String,
      default: 'Paiement MeetVoice'
    },
    
    planId: {
      type: String,
      default: null
    },
    
    metadata: {
      type: Object,
      default: () => ({})
    },
    
    style: {
      type: Object,
      default: () => ({
        layout: 'vertical',
        color: 'gold',
        shape: 'rect',
        label: 'paypal',
        height: 40
      })
    },
    
    funding: {
      type: Object,
      default: () => ({
        allowed: ['paypal', 'card'],
        disallowed: []
      })
    }
  },
  
  emits: ['success', 'error', 'cancel', 'loading'],
  
  data() {
    return {
      loading: true,
      error: null,
      buttonRendered: false
    }
  },
  
  async mounted() {
    await this.initializePayPal()
  },
  
  beforeUnmount() {
    this.cleanup()
  },
  
  watch: {
    amount() {
      this.recreateButton()
    },
    
    planId() {
      this.recreateButton()
    }
  },
  
  methods: {
    async initializePayPal() {
      try {
        this.loading = true
        this.error = null
        this.$emit('loading', true)
        
        await paypalService.initialize()
        await this.createButton()
        
      } catch (error) {
        console.error('Erreur initialisation PayPal:', error)
        this.error = 'Impossible de charger PayPal. Vérifiez votre connexion.'
        this.$emit('error', error)
      } finally {
        this.loading = false
        this.$emit('loading', false)
      }
    },
    
    async createButton() {
      if (!this.$refs.paypalButton) {
        return
      }
      
      // Nettoyer le conteneur
      this.$refs.paypalButton.innerHTML = ''
      
      const orderData = {
        amount: this.amount,
        currency: this.currency,
        description: this.description,
        planId: this.planId,
        metadata: this.metadata
      }
      
      const options = {
        orderData,
        style: this.style,
        funding: this.funding,
        onSuccess: this.handleSuccess,
        onError: this.handleError,
        onCancel: this.handleCancel
      }
      
      try {
        await paypalService.createButton(this.$refs.paypalButton, options)
        this.buttonRendered = true
      } catch (error) {
        console.error('Erreur création bouton PayPal:', error)
        this.error = 'Erreur lors de la création du bouton PayPal'
        throw error
      }
    },
    
    handleSuccess(result) {
      console.log('Paiement PayPal réussi:', result)
      this.$emit('success', {
        orderId: result.id,
        payerId: result.payer?.payer_id,
        amount: this.amount,
        currency: this.currency,
        status: result.status,
        details: result
      })
    },
    
    handleError(error) {
      console.error('Erreur paiement PayPal:', error)
      this.error = paypalService.formatError(error)
      this.$emit('error', {
        message: this.error,
        details: error
      })
    },
    
    handleCancel(data) {
      console.log('Paiement PayPal annulé:', data)
      this.$emit('cancel', {
        orderId: data.orderID,
        reason: 'user_cancelled'
      })
    },
    
    async retry() {
      this.error = null
      await this.initializePayPal()
    },
    
    async recreateButton() {
      if (this.buttonRendered) {
        this.buttonRendered = false
        await this.createButton()
      }
    },
    
    cleanup() {
      if (this.$refs.paypalButton) {
        this.$refs.paypalButton.innerHTML = ''
      }
      this.buttonRendered = false
    },
    
    // Méthodes publiques pour contrôler le bouton
    disable() {
      if (this.$refs.paypalButton) {
        this.$refs.paypalButton.style.pointerEvents = 'none'
        this.$refs.paypalButton.style.opacity = '0.6'
      }
    },
    
    enable() {
      if (this.$refs.paypalButton) {
        this.$refs.paypalButton.style.pointerEvents = 'auto'
        this.$refs.paypalButton.style.opacity = '1'
      }
    }
  }
}
</script>

<style scoped>
.paypal-button-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
}

.paypal-button {
  min-height: 40px;
  position: relative;
}

.paypal-button.loading {
  opacity: 0.6;
  pointer-events: none;
}

/* Overlay de chargement */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: 8px;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #0070ba;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-overlay p {
  color: #666;
  font-size: 14px;
  margin: 0;
}

/* Messages d'erreur */
.error-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  color: #856404;
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.error-icon {
  font-size: 18px;
}

.retry-button {
  background: #0070ba;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  margin-left: auto;
}

.retry-button:hover {
  background: #005ea6;
}

/* Informations de sécurité */
.security-info {
  margin-top: 20px;
  text-align: center;
}

.security-badges {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
  flex-wrap: wrap;
}

.badge {
  background: #e8f5e8;
  color: #28a745;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.security-text {
  color: #666;
  font-size: 13px;
  line-height: 1.4;
  margin: 0;
  max-width: 350px;
  margin: 0 auto;
}

/* Responsive */
@media (max-width: 480px) {
  .paypal-button-container {
    max-width: 100%;
  }
  
  .security-badges {
    flex-direction: column;
    align-items: center;
  }
  
  .error-message {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .retry-button {
    margin-left: 0;
    align-self: flex-end;
  }
}

/* Styles pour les différentes tailles de bouton */
.paypal-button-container.small .paypal-button {
  min-height: 35px;
}

.paypal-button-container.large .paypal-button {
  min-height: 50px;
}

/* Animation d'apparition */
.paypal-button-container {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .loading-overlay {
    background: rgba(0, 0, 0, 0.9);
  }
  
  .loading-overlay p {
    color: #ccc;
  }
  
  .security-text {
    color: #ccc;
  }
}
</style>
