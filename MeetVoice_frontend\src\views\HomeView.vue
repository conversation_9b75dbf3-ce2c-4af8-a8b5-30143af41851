<template>
<body>
  <div class="top">
    <h1><span class="rose">Meet</span><span class="bleu"> Voice</span></h1>
    <p> <PERSON><PERSON><PERSON>, vibrez, rencontrez </p>
  </div>
</body>

</template>

<script>


export default {
  name: 'HomeView',
  components: {
  
  }
}
</script>

<style scoped>
body {
  display: flex;
  height: 100vh;
  background-image: url('@/assets/pseudo-site-rencontre-Pr.webp');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat ;
}

/* Fallback pour navigateurs non compatibles WebP */
.no-webp body {
  background-image: url('@/assets/pseudo-site-rencontre-Pr.jpg');
}
.top{
    margin-top: 20px; /* Réduit car .main-content a déjà padding-top: 80px */
    margin-left: 120px;
}
h1{
    font-style: italic!important;
    font-size: 175px!important; 
    color: #D477EB!important; 
    background-color: #42bfe500;
}
.rose {
    color: #42BEE5
}
p{
    font-style: italic!important;;
    font-size: 30px;    
}
</style>