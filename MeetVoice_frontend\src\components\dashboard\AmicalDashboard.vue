<template>
  <div class="amical-dashboard">
    <div class="dashboard-header">
      <h2>Tableau de bord Amical</h2>
      <p>Découvrez de nouveaux amis et participez à des activités</p>
    </div>
    
    <div class="dashboard-content">
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">👥</div>
          <div class="stat-info">
            <h3>{{ friendsCount }}</h3>
            <p>Amis</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🎉</div>
          <div class="stat-info">
            <h3>{{ eventsCount }}</h3>
            <p>Événements</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">💬</div>
          <div class="stat-info">
            <h3>{{ messagesCount }}</h3>
            <p>Messages</p>
          </div>
        </div>
      </div>
      
      <div class="recent-activities">
        <h3>Activités récentes</h3>
        <div class="activity-list">
          <div v-for="activity in recentActivities" :key="activity.id" class="activity-item">
            <div class="activity-icon">{{ activity.icon }}</div>
            <div class="activity-content">
              <h4>{{ activity.title }}</h4>
              <p>{{ activity.description }}</p>
              <span class="activity-time">{{ formatTime(activity.time) }}</span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="upcoming-events">
        <h3>Événements à venir</h3>
        <div class="events-list">
          <div v-for="event in upcomingEvents" :key="event.id" class="event-item">
            <div class="event-date">
              <span class="day">{{ formatDay(event.date) }}</span>
              <span class="month">{{ formatMonth(event.date) }}</span>
            </div>
            <div class="event-content">
              <h4>{{ event.title }}</h4>
              <p>{{ event.location }}</p>
              <span class="event-participants">{{ event.participants }} participants</span>
            </div>
            <button class="btn-join">Rejoindre</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AmicalDashboard',
  
  data() {
    return {
      friendsCount: 42,
      eventsCount: 8,
      messagesCount: 156,
      recentActivities: [
        {
          id: 1,
          icon: '👋',
          title: 'Nouvelle demande d\'ami',
          description: 'Marie souhaite devenir votre amie',
          time: new Date(Date.now() - 2 * 60 * 60 * 1000)
        },
        {
          id: 2,
          icon: '🎉',
          title: 'Événement confirmé',
          description: 'Soirée jeux de société ce samedi',
          time: new Date(Date.now() - 4 * 60 * 60 * 1000)
        },
        {
          id: 3,
          icon: '💬',
          title: 'Nouveau message',
          description: 'Dans le groupe "Randonneurs du dimanche"',
          time: new Date(Date.now() - 6 * 60 * 60 * 1000)
        }
      ],
      upcomingEvents: [
        {
          id: 1,
          title: 'Soirée jeux de société',
          location: 'Café des Amis, Paris',
          date: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
          participants: 12
        },
        {
          id: 2,
          title: 'Randonnée en forêt',
          location: 'Forêt de Fontainebleau',
          date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
          participants: 8
        }
      ]
    }
  },
  
  methods: {
    formatTime(date) {
      const now = new Date()
      const diff = now - date
      const hours = Math.floor(diff / (1000 * 60 * 60))
      
      if (hours < 1) {
        const minutes = Math.floor(diff / (1000 * 60))
        return `Il y a ${minutes} min`
      } else if (hours < 24) {
        return `Il y a ${hours}h`
      } else {
        const days = Math.floor(hours / 24)
        return `Il y a ${days}j`
      }
    },
    
    formatDay(date) {
      return date.getDate().toString().padStart(2, '0')
    },
    
    formatMonth(date) {
      const months = ['JAN', 'FÉV', 'MAR', 'AVR', 'MAI', 'JUN', 'JUL', 'AOÛ', 'SEP', 'OCT', 'NOV', 'DÉC']
      return months[date.getMonth()]
    }
  }
}
</script>

<style scoped>
.amical-dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: #e3f2fd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.recent-activities,
.upcoming-events {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recent-activities h3,
.upcoming-events h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.activity-list,
.events-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  background: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.activity-content p {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.activity-time {
  font-size: 12px;
  color: #999;
}

.event-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: #2196f3;
  color: white;
  border-radius: 8px;
  padding: 12px;
  min-width: 60px;
}

.event-date .day {
  font-size: 20px;
  font-weight: 700;
}

.event-date .month {
  font-size: 12px;
  font-weight: 500;
}

.event-content {
  flex: 1;
}

.event-content h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.event-content p {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.event-participants {
  font-size: 12px;
  color: #999;
}

.btn-join {
  background: #2196f3;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-join:hover {
  background: #1976d2;
}

@media (max-width: 768px) {
  .amical-dashboard {
    padding: 16px;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .activity-item,
  .event-item {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
}
</style>
