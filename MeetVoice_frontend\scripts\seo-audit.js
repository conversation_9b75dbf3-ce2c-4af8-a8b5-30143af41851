#!/usr/bin/env node

/**
 * Script d'audit SEO automatique pour MeetVoice
 * Usage: node scripts/seo-audit.js
 */

const fs = require('fs');
const path = require('path');
const https = require('https');

class SEOAuditor {
  constructor() {
    this.results = {
      score: 0,
      issues: [],
      suggestions: [],
      passed: []
    };
  }

  async runAudit() {
    console.log('🔍 Démarrage de l\'audit SEO...\n');

    // Tests de base
    await this.checkRobotsTxt();
    await this.checkSitemap();
    await this.checkMetaTags();
    await this.checkStructuredData();
    await this.checkImages();
    await this.checkPerformance();
    await this.checkAccessibility();

    this.generateReport();
  }

  async checkRobotsTxt() {
    console.log('📋 Vérification du robots.txt...');
    
    const robotsPath = path.join(__dirname, '../public/robots.txt');
    
    if (fs.existsSync(robotsPath)) {
      const content = fs.readFileSync(robotsPath, 'utf8');
      
      if (content.includes('Sitemap:')) {
        this.results.passed.push('✅ robots.txt contient une référence au sitemap');
        this.results.score += 10;
      } else {
        this.results.issues.push('❌ robots.txt ne référence pas le sitemap');
      }
      
      if (content.includes('Allow: /article')) {
        this.results.passed.push('✅ robots.txt autorise l\'indexation des articles');
        this.results.score += 5;
      }
    } else {
      this.results.issues.push('❌ Fichier robots.txt manquant');
    }
  }

  async checkSitemap() {
    console.log('🗺️ Vérification du sitemap...');
    
    const sitemapPath = path.join(__dirname, '../public/sitemap.xml');
    
    if (fs.existsSync(sitemapPath)) {
      const content = fs.readFileSync(sitemapPath, 'utf8');
      
      if (content.includes('<urlset')) {
        this.results.passed.push('✅ Sitemap XML valide trouvé');
        this.results.score += 15;
        
        // Compter les URLs
        const urlCount = (content.match(/<url>/g) || []).length;
        if (urlCount > 0) {
          this.results.passed.push(`✅ ${urlCount} URLs dans le sitemap`);
          this.results.score += 5;
        }
      }
    } else {
      this.results.issues.push('❌ Fichier sitemap.xml manquant');
      this.results.suggestions.push('💡 Générer le sitemap avec: npm run generate-sitemap');
    }
  }

  async checkMetaTags() {
    console.log('🏷️ Vérification des meta tags...');
    
    // Vérifier le fichier SEO
    const seoPath = path.join(__dirname, '../src/utils/seo.js');
    
    if (fs.existsSync(seoPath)) {
      const content = fs.readFileSync(seoPath, 'utf8');
      
      if (content.includes('og:title')) {
        this.results.passed.push('✅ Open Graph tags implémentés');
        this.results.score += 10;
      }
      
      if (content.includes('twitter:card')) {
        this.results.passed.push('✅ Twitter Cards implémentées');
        this.results.score += 10;
      }
      
      if (content.includes('application/ld+json')) {
        this.results.passed.push('✅ Données structurées JSON-LD');
        this.results.score += 15;
      }
    }
  }

  async checkStructuredData() {
    console.log('🏗️ Vérification des données structurées...');
    
    const seoPath = path.join(__dirname, '../src/utils/seo.js');
    
    if (fs.existsSync(seoPath)) {
      const content = fs.readFileSync(seoPath, 'utf8');
      
      if (content.includes('"@type": "Article"')) {
        this.results.passed.push('✅ Schema.org Article implémenté');
        this.results.score += 10;
      }
      
      if (content.includes('"@type": "Organization"')) {
        this.results.passed.push('✅ Schema.org Organization implémenté');
        this.results.score += 5;
      }
      
      if (content.includes('datePublished')) {
        this.results.passed.push('✅ Dates de publication dans les données structurées');
        this.results.score += 5;
      }
    }
  }

  async checkImages() {
    console.log('🖼️ Vérification des images...');
    
    // Vérifier les composants Vue pour les attributs alt
    const componentsDir = path.join(__dirname, '../src/views');
    const files = fs.readdirSync(componentsDir);
    
    let hasAltAttributes = false;
    let hasLazyLoading = false;
    
    files.forEach(file => {
      if (file.endsWith('.vue')) {
        const content = fs.readFileSync(path.join(componentsDir, file), 'utf8');
        
        if (content.includes(':alt=') || content.includes('alt=')) {
          hasAltAttributes = true;
        }
        
        if (content.includes('loading="lazy"')) {
          hasLazyLoading = true;
        }
      }
    });
    
    if (hasAltAttributes) {
      this.results.passed.push('✅ Attributs alt présents sur les images');
      this.results.score += 10;
    } else {
      this.results.issues.push('❌ Attributs alt manquants sur certaines images');
    }
    
    if (hasLazyLoading) {
      this.results.passed.push('✅ Lazy loading implémenté');
      this.results.score += 5;
    } else {
      this.results.suggestions.push('💡 Implémenter le lazy loading pour les images');
    }
  }

  async checkPerformance() {
    console.log('⚡ Vérification des optimisations de performance...');
    
    // Vérifier la configuration webpack
    const configPath = path.join(__dirname, '../vue.config.js');
    
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      
      if (content.includes('splitChunks')) {
        this.results.passed.push('✅ Code splitting configuré');
        this.results.score += 10;
      }
      
      if (content.includes('prerender-spa-plugin')) {
        this.results.passed.push('✅ Pre-rendering SPA activé');
        this.results.score += 15;
      }
    }
    
    // Vérifier le package.json pour les optimisations
    const packagePath = path.join(__dirname, '../package.json');
    if (fs.existsSync(packagePath)) {
      const pkg = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
      
      if (pkg.dependencies && pkg.dependencies['web-vitals']) {
        this.results.passed.push('✅ Web Vitals tracking disponible');
        this.results.score += 5;
      } else {
        this.results.suggestions.push('💡 Ajouter web-vitals pour le monitoring des performances');
      }
    }
  }

  async checkAccessibility() {
    console.log('♿ Vérification de l\'accessibilité...');
    
    // Vérifier les composants pour l'accessibilité
    const componentsDir = path.join(__dirname, '../src');
    
    let hasAriaLabels = false;
    let hasSemanticHTML = false;
    
    const checkDirectory = (dir) => {
      const files = fs.readdirSync(dir);
      
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stat = fs.statSync(filePath);
        
        if (stat.isDirectory()) {
          checkDirectory(filePath);
        } else if (file.endsWith('.vue')) {
          const content = fs.readFileSync(filePath, 'utf8');
          
          if (content.includes('aria-label') || content.includes('aria-')) {
            hasAriaLabels = true;
          }
          
          if (content.includes('<nav') || content.includes('<main') || content.includes('<section')) {
            hasSemanticHTML = true;
          }
        }
      });
    };
    
    checkDirectory(componentsDir);
    
    if (hasAriaLabels) {
      this.results.passed.push('✅ Attributs ARIA présents');
      this.results.score += 5;
    }
    
    if (hasSemanticHTML) {
      this.results.passed.push('✅ HTML sémantique utilisé');
      this.results.score += 5;
    }
  }

  generateReport() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 RAPPORT D\'AUDIT SEO - MEETVOICE');
    console.log('='.repeat(60));
    
    // Score global
    const maxScore = 100;
    const percentage = Math.round((this.results.score / maxScore) * 100);
    
    console.log(`\n🎯 SCORE GLOBAL: ${this.results.score}/${maxScore} (${percentage}%)`);
    
    // Niveau de performance
    let level = '';
    let emoji = '';
    if (percentage >= 90) {
      level = 'EXCELLENT';
      emoji = '🏆';
    } else if (percentage >= 75) {
      level = 'TRÈS BON';
      emoji = '🥇';
    } else if (percentage >= 60) {
      level = 'BON';
      emoji = '🥈';
    } else if (percentage >= 40) {
      level = 'MOYEN';
      emoji = '🥉';
    } else {
      level = 'À AMÉLIORER';
      emoji = '⚠️';
    }
    
    console.log(`${emoji} NIVEAU: ${level}\n`);
    
    // Points positifs
    if (this.results.passed.length > 0) {
      console.log('✅ POINTS POSITIFS:');
      this.results.passed.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    // Problèmes
    if (this.results.issues.length > 0) {
      console.log('❌ PROBLÈMES À CORRIGER:');
      this.results.issues.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    // Suggestions
    if (this.results.suggestions.length > 0) {
      console.log('💡 SUGGESTIONS D\'AMÉLIORATION:');
      this.results.suggestions.forEach(item => console.log(`   ${item}`));
      console.log('');
    }
    
    // Prochaines étapes
    console.log('🚀 PROCHAINES ÉTAPES RECOMMANDÉES:');
    if (percentage < 90) {
      console.log('   1. Corriger les problèmes identifiés ci-dessus');
      console.log('   2. Tester avec Google PageSpeed Insights');
      console.log('   3. Valider les données structurées avec Google Rich Results Test');
      console.log('   4. Vérifier l\'indexation dans Google Search Console');
    } else {
      console.log('   1. Maintenir le niveau d\'excellence actuel');
      console.log('   2. Surveiller les Core Web Vitals régulièrement');
      console.log('   3. Continuer à optimiser le contenu');
    }
    
    console.log('\n' + '='.repeat(60));
    
    // Sauvegarder le rapport
    const reportPath = path.join(__dirname, '../seo-audit-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      date: new Date().toISOString(),
      score: this.results.score,
      percentage,
      level,
      ...this.results
    }, null, 2));
    
    console.log(`📄 Rapport détaillé sauvegardé: ${reportPath}`);
  }
}

// Exécuter l'audit
if (require.main === module) {
  const auditor = new SEOAuditor();
  auditor.runAudit().catch(console.error);
}

module.exports = SEOAuditor;
