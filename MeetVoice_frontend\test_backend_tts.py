#!/usr/bin/env python3
"""
Script de test pour l'API TTS backend Django
Teste les endpoints TTS et affiche les voix disponibles
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/tts/public"

def test_status():
    """Test de l'endpoint de statut"""
    print("🔍 Test de l'endpoint de statut...")
    try:
        response = requests.get(f"{BASE_URL}/status/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Service TTS en ligne")
            print(f"📊 Statut: {data}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def test_voices():
    """Test de l'endpoint des voix"""
    print("\n🔍 Test de l'endpoint des voix...")
    try:
        response = requests.get(f"{BASE_URL}/voices/")
        if response.status_code == 200:
            data = response.json()
            
            if 'voices' in data:
                voices = data['voices']
                print(f"✅ {len(voices)} voix trouvées")
                
                print("\n🎤 Voix disponibles:")
                for voice in voices:
                    if isinstance(voice, str):
                        print(f"  • {voice}")
                    else:
                        print(f"  • {voice.get('name', voice.get('id', 'Voix inconnue'))}")
                        if 'description' in voice:
                            print(f"    📝 {voice['description']}")
                
                return voices
            else:
                print(f"❌ Format de réponse inattendu: {data}")
                return []
        else:
            print(f"❌ Erreur: {response.status_code}")
            # Essayer l'endpoint status comme fallback
            return test_status_voices()
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_status_voices():
    """Test des voix via l'endpoint status"""
    print("\n🔍 Test des voix via l'endpoint status...")
    try:
        response = requests.get(f"{BASE_URL}/status/")
        if response.status_code == 200:
            data = response.json()
            
            if 'available_voices' in data:
                voices = data['available_voices']
                print(f"✅ {len(voices)} voix trouvées via status")
                
                print("\n🎤 Voix disponibles:")
                for voice in voices:
                    print(f"  • {voice}")
                
                return voices
            else:
                print(f"📊 Données status: {data}")
                return []
        else:
            print(f"❌ Erreur: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_synthesis(voice_type="female_young", text="Bonjour ! Ceci est un test de synthèse vocale."):
    """Test de synthèse vocale"""
    print(f"\n🔍 Test de synthèse avec la voix {voice_type}...")
    try:
        payload = {
            "text": text,
            "voice_type": voice_type,
            "language": "fr",
            "speed": 1.0,
            "pitch": 1.0
        }
        
        response = requests.post(f"{BASE_URL}/synthesize/", json=payload)
        if response.status_code == 200:
            print(f"✅ Synthèse réussie ({len(response.content)} bytes)")
            print(f"📄 Content-Type: {response.headers.get('content-type', 'unknown')}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            if response.headers.get('content-type') == 'application/json':
                try:
                    error_data = response.json()
                    print(f"💬 Message: {error_data.get('error', 'Erreur inconnue')}")
                except:
                    pass
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🎤 Test de l'API TTS Backend Django pour MeetVoice")
    print("=" * 60)
    
    # Test de statut
    if not test_status():
        print("❌ Le service TTS n'est pas accessible.")
        print("💡 Assurez-vous que le serveur Django fonctionne sur http://127.0.0.1:8000")
        sys.exit(1)
    
    # Test des voix
    voices = test_voices()
    if not voices:
        print("⚠️  Aucune voix trouvée ou endpoint non disponible.")
    
    # Test de synthèse avec la première voix disponible
    if voices:
        test_voice = voices[0] if isinstance(voices[0], str) else voices[0].get('id', 'female_young')
        test_synthesis(test_voice)
    else:
        # Test avec une voix par défaut
        test_synthesis()
    
    print("\n🎉 Tests terminés !")
    print("🌐 Interface web: http://localhost:8081")
    print("🔧 API Backend: http://127.0.0.1:8000/tts/public")

if __name__ == "__main__":
    main()
