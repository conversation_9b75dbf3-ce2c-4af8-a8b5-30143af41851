<!-- @format -->

<template>
  <footer class="app-footer">
    <!-- Section principale du footer -->
    <div class="footer-main">
      <div class="footer-container">
        <!-- Logo et description -->
        <div class="footer-section footer-brand">
          <div class="footer-logo">
            <h3>Meet Voice</h3>
          </div>
          <p class="footer-description">
            La plateforme de rencontres qui donne la parole à votre personnalité.
            Rencontrez des personnes authentiques grâce à notre système d'interview vocal innovant.
          </p>
          
          <!-- Réseaux sociaux -->
          <div class="social-links">
            <a href="#" class="social-link tiktok" aria-label="TikTok">
              <i class="fab fa-tiktok"></i>
            </a>
            <a href="#" class="social-link facebook" aria-label="Facebook">
              <i class="fab fa-facebook-f"></i>
            </a>
            <a href="#" class="social-link instagram" aria-label="Instagram">
              <i class="fab fa-instagram"></i>
            </a>
            <a href="#" class="social-link youtube" aria-label="YouTube">
              <i class="fab fa-youtube"></i>
            </a>
          </div>
        </div>

        <!-- Liens légaux -->
        <div class="footer-section footer-legal">
          <h4>Informations légales</h4>
          <ul class="footer-links">
            <li><router-link to="/cgu">Conditions Générales d'Utilisation</router-link></li>
            <li><router-link to="/donnees-personnelles">Données personnelles (RGPD)</router-link></li>
            <li><router-link to="/responsabilite">Responsabilité</router-link></li>
            <li><router-link to="/propriete-intellectuelle">Propriété intellectuelle</router-link></li>
            <li><router-link to="/mediation">Médiation</router-link></li>
            <li><router-link to="/mentions-legales">Mentions légales</router-link></li>
          </ul>
        </div>

        <!-- Contact -->
        <div class="footer-section footer-contact">
          <h4>Contact</h4>
          <div class="contact-info">
            <p>
              <i class="fas fa-envelope"></i>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </p>
            <p>
              <i class="fas fa-headset"></i>
              <router-link to="/contact">Centre d'aide</router-link>
            </p>
            <p>
              <i class="fas fa-building"></i>
              <span>SIRET : 92363926400012</span>
            </p>
          </div>
        </div>

        <!-- Liens rapides -->
        <div class="footer-section footer-quick">
          <h4>Liens rapides</h4>
          <ul class="footer-links">
            <li><router-link to="/about">À propos</router-link></li>
            <li><router-link to="/how-it-works">Comment ça marche</router-link></li>
            <li><router-link to="/safety">Sécurité</router-link></li>
            <li><router-link to="/support">Support</router-link></li>
            <li><router-link to="/blog">Blog</router-link></li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Section copyright -->
    <div class="footer-bottom">
      <div class="footer-container">
        <div class="footer-bottom-content">
          <p class="copyright">
            © {{ currentYear }} Meet Voice. Tous droits réservés.
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script>
export default {
  name: 'AppFooter',
  computed: {
    currentYear() {
      return new Date().getFullYear();
    }
  }
};
</script>

<style scoped>
.app-footer {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  color: #ffffff;
  margin-top: auto;
}

.footer-main {
  padding: 60px 0 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-main .footer-container {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 40px;
}

.footer-section h3,
.footer-section h4 {
  margin-bottom: 20px;
  color: #ffffff;
  font-weight: 600;
}

.footer-section h3 {
  font-size: 28px;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.footer-section h4 {
  font-size: 18px;
}

/* Section marque */
.footer-brand {
  max-width: 350px;
}

.footer-description {
  color: #b8c5d6;
  line-height: 1.6;
  margin-bottom: 25px;
  font-size: 14px;
}

/* Réseaux sociaux */
.social-links {
  display: flex;
  gap: 15px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px;
  height: 45px;
  border-radius: 50%;
  color: #ffffff;
  font-size: 20px;
  transition: all 0.3s ease;
  text-decoration: none;
}

.social-link:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.social-link.tiktok {
  background: linear-gradient(45deg, #ff0050, #000000);
}

.social-link.facebook {
  background: linear-gradient(45deg, #1877f2, #42a5f5);
}

.social-link.instagram {
  background: linear-gradient(45deg, #e4405f, #f77737, #fccc63);
}

.social-link.youtube {
  background: linear-gradient(45deg, #ff0000, #cc0000);
}

/* Liens du footer */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 12px;
}

.footer-links a {
  color: #b8c5d6;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #667eea;
}

/* Section contact */
.contact-info p {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  color: #b8c5d6;
  font-size: 14px;
}

.contact-info i {
  width: 20px;
  margin-right: 10px;
  color: #667eea;
}

.contact-info a {
  color: #b8c5d6;
  text-decoration: none;
  transition: color 0.3s ease;
}

.contact-info a:hover {
  color: #667eea;
  text-decoration: underline;
}

/* Footer bottom */
.footer-bottom {
  padding: 25px 0;
  background: rgba(0, 0, 0, 0.2);
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.footer-bottom p {
  margin: 0;
  font-size: 13px;
  color: #8a9bb3;
}

.footer-bottom a {
  color: #667eea;
  text-decoration: none;
}

.footer-bottom a:hover {
  text-decoration: underline;
}

/* Responsive */
@media (max-width: 1024px) {
  .footer-main .footer-container {
    grid-template-columns: 1fr 1fr;
    gap: 30px;
  }
}

@media (max-width: 768px) {
  .footer-main {
    padding: 40px 0 30px;
  }
  
  .footer-main .footer-container {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }
  
  .footer-brand {
    max-width: none;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
  
  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .footer-container {
    padding: 0 15px;
  }
  
  .footer-main {
    padding: 30px 0 20px;
  }
  
  .footer-bottom {
    padding: 20px 0;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }
}
</style>
