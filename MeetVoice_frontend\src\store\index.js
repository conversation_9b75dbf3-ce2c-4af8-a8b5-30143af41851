/** @format */

import { createStore } from "vuex";
import { accountService } from "@/_services";

// Import des modules
import auth from './modules/auth'
import profiles from './modules/profiles'
import events from './modules/events'
import community from './modules/community'
import notifications from './modules/notifications'
import payment from './modules/payment'
import websocket from './modules/websocket'
import subscription from './modules/subscription'

export default createStore({
  state: {
    // État global de l'application
    appVersion: '1.0.0',
    isOnline: navigator.onLine,
    currentTheme: localStorage.getItem('theme') || 'dark',
    sidebarOpen: false,

    // Données communes
    wall: {
      posts: [],
      stories: []
    },

    subscription: {
      current: null,
      history: []
    },

    currentProfile: null,

    // Configuration voix et microphone (conservée de l'ancien store)
    voiceConfig: {
      selectedVoiceId: 4, // Julie par défaut
      speed: 1.2, // VITESSE FIXE À 1.2
      pitch: 1.0,
      volume: 1.0,
      availableVoices: []
    },
    microphoneConfig: {
      selectedMicrophoneId: '',
      availableMicrophones: [],
      audioLevel: 0,
      lastTestResult: null
    },

    // Configuration de l'application
    config: {
      maxFileSize: 10 * 1024 * 1024, // 10MB
      supportedImageTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      supportedVideoTypes: ['video/mp4', 'video/webm'],
      supportedAudioTypes: ['audio/mp3', 'audio/wav', 'audio/ogg'],
      maxVoiceMessageDuration: 300, // 5 minutes
      apiTimeout: 30000 // 30 secondes
    }
  },
  mutations: {
    setToken(state, token) {
      state.token = token;
    },
    setLoggedIn(state, value) {
      state.isLoggedIn = value;
    },
    setUser(state, user) {
      state.user = user;
    },
    setError(state, error) {
      state.error = error;
    },
    setLoading(state, loading) {
      state.loading = loading;
    },
    clearError(state) {
      state.error = null;
    },

    // Mutations pour la configuration voix
    setVoiceConfig(state, config) {
      state.voiceConfig = { ...state.voiceConfig, ...config };
    },
    setSelectedVoice(state, voiceId) {
      state.voiceConfig.selectedVoiceId = voiceId;
    },
    setVoiceSpeed(state, speed) {
      // VITESSE FIXE À 1.2 - PAS DE MODIFICATION
      state.voiceConfig.speed = 1.2;
    },
    setVoicePitch(state, pitch) {
      state.voiceConfig.pitch = pitch;
    },
    setVoiceVolume(state, volume) {
      state.voiceConfig.volume = volume;
    },
    setAvailableVoices(state, voices) {
      state.voiceConfig.availableVoices = voices;
    },

    // Mutations pour la configuration microphone
    setMicrophoneConfig(state, config) {
      state.microphoneConfig = { ...state.microphoneConfig, ...config };
    },
    setSelectedMicrophone(state, microphoneId) {
      state.microphoneConfig.selectedMicrophoneId = microphoneId;
    },
    setAvailableMicrophones(state, microphones) {
      state.microphoneConfig.availableMicrophones = microphones;
    },
    setAudioLevel(state, level) {
      state.microphoneConfig.audioLevel = level;
    },
    setMicTestResult(state, result) {
      state.microphoneConfig.lastTestResult = result;
    },

    // Mutations pour les types d'utilisateurs
    setUserType(state, userType) {
      state.userType = userType;
      localStorage.setItem('userType', userType);
    },

    // Mutations globales ajoutées
    setOnlineStatus(state, isOnline) {
      state.isOnline = isOnline;
    },

    setTheme(state, theme) {
      state.currentTheme = theme;
      localStorage.setItem('theme', theme);
    },

    toggleSidebar(state) {
      state.sidebarOpen = !state.sidebarOpen;
    },

    setSidebarOpen(state, open) {
      state.sidebarOpen = open;
    },

    // Mutations pour le mur
    setWallPosts(state, posts) {
      state.wall.posts = posts;
    },

    addWallPost(state, post) {
      state.wall.posts.unshift(post);
    },

    updateWallPost(state, updatedPost) {
      const index = state.wall.posts.findIndex(p => p.id === updatedPost.id);
      if (index !== -1) {
        state.wall.posts.splice(index, 1, updatedPost);
      }
    },

    setWallStories(state, stories) {
      state.wall.stories = stories;
    },

    addWallStory(state, story) {
      state.wall.stories.unshift(story);
    },

    // Mutations pour l'abonnement
    setSubscription(state, subscription) {
      state.subscription.current = subscription;
    },

    setSubscriptionHistory(state, history) {
      state.subscription.history = history;
    },

    // Mutation pour le profil actuel
    setCurrentProfile(state, profile) {
      state.currentProfile = profile;
    },

    // Mutations pour les événements (interface amical)
    setEvents(state, events) {
      state.events = events;
    },
    addEvent(state, event) {
      state.events.push(event);
    },
    updateEvent(state, updatedEvent) {
      const index = state.events.findIndex(e => e.id === updatedEvent.id);
      if (index !== -1) {
        state.events.splice(index, 1, updatedEvent);
      }
    },
    setUserEvents(state, events) {
      state.userEvents = events;
    },

    // Mutations pour les profils (interface amoureux)
    setProfiles(state, profiles) {
      state.profiles = profiles;
    },
    setMatches(state, matches) {
      state.matches = matches;
    },
    addMatch(state, match) {
      state.matches.push(match);
    },
    setConversations(state, conversations) {
      state.conversations = conversations;
    },

    // Mutations pour la communauté (interface libertin)
    setCommunity(state, community) {
      state.community = community;
    },
    setParties(state, parties) {
      state.parties = parties;
    },

    // Mutations communes
    setNotifications(state, notifications) {
      state.notifications = notifications;
    },
    addNotification(state, notification) {
      state.notifications.unshift(notification);
    },
    setSubscription(state, subscription) {
      state.subscription = subscription;
    },
    setWall(state, wall) {
      state.wall = wall;
    },
    addWallPost(state, post) {
      state.wall.unshift(post);
    },
  },
  actions: {
    saveToken({ commit }, token) {
      commit("setToken", token);
      accountService.saveToken(token);
    },

    async login({ commit }, credentials) {
      try {
        commit("setLoading", true);
        commit("clearError");

        const response = await accountService.login(credentials);

        if (response.data.access) {
          // Sauvegarder le token
          commit("setToken", response.data.access);
          accountService.saveToken(response.data.access);

          // Sauvegarder les informations utilisateur si disponibles
          if (response.data.user) {
            commit("setUser", response.data.user);
            accountService.saveUser(response.data.user);
          }

          commit("setLoggedIn", true);
          return response.data;
        } else {
          throw new Error("Token d'accès non reçu");
        }
      } catch (error) {
        const errorMessage =
          error.response?.data?.message ||
          error.message ||
          "Erreur de connexion";
        commit("setError", errorMessage);
        throw error;
      } finally {
        commit("setLoading", false);
      }
    },

    logout({ commit }) {
      commit("setLoggedIn", false);
      commit("setUser", null);
      commit("setToken", null);
      commit("clearError");
      accountService.logout();
    },

    async initializeAuth({ commit }) {
      try {
        const isAuthenticated = accountService.initializeAuth();
        if (isAuthenticated) {
          const user = accountService.getUser();
          const token = accountService.getToken();

          commit("setLoggedIn", true);
          commit("setUser", user);
          commit("setToken", token);

          // Optionnel : rafraîchir les données utilisateur
          try {
            await accountService.refreshUserData();
          } catch (error) {
            // Si le rafraîchissement échoue, on garde les données en cache
            console.warn(
              "Impossible de rafraîchir les données utilisateur:",
              error
            );
          }
        }
        return isAuthenticated;
      } catch (error) {
        console.error(
          "Erreur lors de l'initialisation de l'authentification:",
          error
        );
        commit("setError", "Erreur d'initialisation");
        return false;
      }
    },

    async refreshUserData({ commit }) {
      try {
        const userData = await accountService.refreshUserData();
        commit("setUser", userData);
        return userData;
      } catch (error) {
        commit(
          "setError",
          "Impossible de mettre à jour les données utilisateur"
        );
        throw error;
      }
    },

    clearError({ commit }) {
      commit("clearError");
    },

    // Actions globales ajoutées
    initializeApp({ dispatch, commit }) {
      // Initialiser l'authentification
      dispatch('auth/initializeAuth');

      // Écouter les changements de statut en ligne
      window.addEventListener('online', () => commit('setOnlineStatus', true));
      window.addEventListener('offline', () => commit('setOnlineStatus', false));

      // Charger les notifications si connecté
      if (this.getters.isAuthenticated) {
        dispatch('notifications/loadNotifications');
      }
    },

    // Actions pour le mur
    async loadWall({ commit }) {
      try {
        // Simuler le chargement du mur (à remplacer par l'API réelle)
        const posts = [
          {
            id: 1,
            author: { id: 1, username: 'Alice', avatar: '/avatars/alice.jpg' },
            content: 'Belle journée pour une sortie !',
            createdAt: new Date().toISOString(),
            likesCount: 5,
            commentsCount: 2,
            isLiked: false,
            media: []
          }
        ];

        const stories = [
          {
            id: 1,
            author: { id: 2, username: 'Bob', avatar: '/avatars/bob.jpg' },
            media: '/stories/story1.jpg',
            createdAt: new Date().toISOString(),
            viewed: false
          }
        ];

        commit('setWallPosts', posts);
        commit('setWallStories', stories);

        return { posts, stories };
      } catch (error) {
        console.error('Erreur lors du chargement du mur:', error);
        throw error;
      }
    },

    async createWallPost({ commit }, postData) {
      try {
        // Simuler la création d'un post (à remplacer par l'API réelle)
        const newPost = {
          id: Date.now(),
          ...postData,
          createdAt: new Date().toISOString(),
          likesCount: 0,
          commentsCount: 0,
          isLiked: false
        };

        commit('addWallPost', newPost);

        return newPost;
      } catch (error) {
        console.error('Erreur lors de la création du post:', error);
        throw error;
      }
    },

    async likeWallPost({ commit, state }, postId) {
      try {
        // Simuler le like d'un post (à remplacer par l'API réelle)
        const post = state.wall.posts.find(p => p.id === postId);
        if (post) {
          const updatedPost = {
            ...post,
            isLiked: !post.isLiked,
            likesCount: post.isLiked ? post.likesCount - 1 : post.likesCount + 1
          };
          commit('updateWallPost', updatedPost);
        }

        return true;
      } catch (error) {
        console.error('Erreur lors du like:', error);
        throw error;
      }
    },

    // Actions pour l'abonnement
    async loadSubscription({ commit }) {
      try {
        // Simuler le chargement de l'abonnement (à remplacer par l'API réelle)
        const subscription = {
          planId: 'premium',
          planName: 'Premium',
          price: 19.99,
          cycle: 'mois',
          status: 'active',
          nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        };

        const history = [
          {
            id: 1,
            date: new Date().toISOString(),
            planName: 'Premium',
            amount: 19.99,
            status: 'success'
          }
        ];

        commit('setSubscription', subscription);
        commit('setSubscriptionHistory', history);

        return { subscription, history };
      } catch (error) {
        console.error('Erreur lors du chargement de l\'abonnement:', error);
        throw error;
      }
    },

    async subscribeToPlan({ commit }, planData) {
      try {
        // Simuler l'abonnement à un plan (à remplacer par l'API réelle)
        const subscription = {
          planId: planData.planId,
          planName: planData.planName || 'Premium',
          price: planData.price || 19.99,
          cycle: planData.cycle || 'monthly',
          status: 'active',
          nextBilling: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        };

        commit('setSubscription', subscription);

        return subscription;
      } catch (error) {
        console.error('Erreur lors de l\'abonnement:', error);
        throw error;
      }
    },

    async cancelSubscription({ commit, state }) {
      try {
        // Simuler l'annulation de l'abonnement (à remplacer par l'API réelle)
        const subscription = {
          ...state.subscription.current,
          status: 'cancelled'
        };

        commit('setSubscription', subscription);

        return subscription;
      } catch (error) {
        console.error('Erreur lors de l\'annulation:', error);
        throw error;
      }
    },

    // Action pour charger un profil
    async loadProfile({ commit }, profileId) {
      try {
        // Simuler le chargement d'un profil (à remplacer par l'API réelle)
        const profile = {
          id: profileId,
          prenom: 'Alice',
          age: 28,
          ville: 'Paris',
          photos: ['/avatars/alice.jpg'],
          description: 'Passionnée de voyages et de photographie',
          isOnline: true,
          isVerified: true,
          stats: {
            profileViews: 150,
            likes: 45,
            matches: 12,
            events: 8
          }
        };

        commit('setCurrentProfile', profile);

        return profile;
      } catch (error) {
        console.error('Erreur lors du chargement du profil:', error);
        throw error;
      }
    },

    // Actions pour la configuration voix
    async loadAvailableVoices({ commit }) {
      try {
        const response = await fetch('http://127.0.0.1:8000/tts/voices/');
        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }
        const voices = await response.json();
        commit('setAvailableVoices', voices);
        console.log('✅ Voix chargées depuis l\'API:', voices);
        return voices;
      } catch (error) {
        console.error('❌ Erreur chargement voix:', error);
        // Voix par défaut en cas d'erreur
        const defaultVoices = [
          { id: 4, name: 'Julie', language: 'fr', gender: 'female' },
          { id: 1, name: 'Pierre', language: 'fr', gender: 'male' }
        ];
        commit('setAvailableVoices', defaultVoices);
        return defaultVoices;
      }
    },

    updateVoiceConfig({ commit }, config) {
      commit('setVoiceConfig', config);
      // Sauvegarder dans localStorage
      localStorage.setItem('meetvoice_voice_config', JSON.stringify(config));
      console.log('💾 Configuration voix sauvegardée:', config);
    },

    updateMicrophoneConfig({ commit }, config) {
      commit('setMicrophoneConfig', config);
      // Sauvegarder dans localStorage
      localStorage.setItem('meetvoice_microphone_config', JSON.stringify(config));
      console.log('💾 Configuration microphone sauvegardée:', config);
    },

    loadSavedConfigs({ commit }) {
      try {
        // Charger la config voix
        const savedVoiceConfig = localStorage.getItem('meetvoice_voice_config');
        if (savedVoiceConfig) {
          const voiceConfig = JSON.parse(savedVoiceConfig);
          commit('setVoiceConfig', voiceConfig);
          console.log('📂 Configuration voix chargée:', voiceConfig);
        }

        // Charger la config microphone
        const savedMicConfig = localStorage.getItem('meetvoice_microphone_config');
        if (savedMicConfig) {
          const micConfig = JSON.parse(savedMicConfig);
          commit('setMicrophoneConfig', micConfig);
          console.log('📂 Configuration microphone chargée:', micConfig);
        }
      } catch (error) {
        console.error('❌ Erreur chargement configurations:', error);
      }
    },
  },
  getters: {
    getToken: (state) => state.token,
    getUser: (state) => state.user,
    isAuthenticated: (state) => state.isLoggedIn,
    getError: (state) => state.error,
    isLoading: (state) => state.loading,
    getUserFullName: (state) => {
      if (state.user) {
        if (state.user.first_name && state.user.last_name) {
          return `${state.user.first_name} ${state.user.last_name}`;
        }
        return state.user.username || state.user.email;
      }
      return null;
    },

    // Getters pour la configuration voix et microphone
    getVoiceConfig: (state) => state.voiceConfig,
    getSelectedVoice: (state) => state.voiceConfig.selectedVoiceId,
    getVoiceSpeed: (state) => state.voiceConfig.speed,
    getVoicePitch: (state) => state.voiceConfig.pitch,
    getVoiceVolume: (state) => state.voiceConfig.volume,
    getAvailableVoices: (state) => state.voiceConfig.availableVoices,

    getMicrophoneConfig: (state) => state.microphoneConfig,
    getSelectedMicrophone: (state) => state.microphoneConfig.selectedMicrophoneId,
    getAvailableMicrophones: (state) => state.microphoneConfig.availableMicrophones,
    getAudioLevel: (state) => state.microphoneConfig.audioLevel,
    getMicTestResult: (state) => state.microphoneConfig.lastTestResult,

    // Getters globaux ajoutés
    isOnline: state => state.isOnline,
    currentTheme: state => state.currentTheme,
    sidebarOpen: state => state.sidebarOpen,
    appVersion: state => state.appVersion,
    config: state => state.config,

    // Getters délégués aux modules
    userType: (state, getters) => getters['auth/userType'],
    unreadNotificationsCount: (state, getters) => getters['notifications/unreadCount'] || 0,
    hasUnreadNotifications: (state, getters) => getters['notifications/hasUnreadNotifications'] || false,
  },

  modules: {
    auth,
    profiles,
    events,
    community,
    notifications,
    payment,
    websocket
  }
});
