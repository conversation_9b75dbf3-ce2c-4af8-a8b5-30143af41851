const fs = require('fs');
const path = require('path');
const fetch = require('node-fetch');

/**
 * Script pour générer automatiquement le sitemap.xml
 * avec tous les articles publiés depuis l'API
 */

const API_BASE_URL = 'http://127.0.0.1:8000';
const SITE_URL = 'https://meetvoice.fr';
const OUTPUT_PATH = path.join(__dirname, '../public/sitemap.xml');

// Pages statiques à inclure dans le sitemap
const STATIC_PAGES = [
  {
    url: '/',
    changefreq: 'daily',
    priority: '1.0'
  },
  {
    url: '/actualite',
    changefreq: 'daily',
    priority: '0.8'
  }
];

/**
 * Récupère tous les articles publiés depuis l'API
 */
async function fetchPublishedArticles() {
  try {
    console.log('Récupération des articles depuis l\'API...');
    const response = await fetch(`${API_BASE_URL}/actualite/api/articles/published/`);
    
    if (!response.ok) {
      throw new Error(`Erreur API: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    const articles = data.results || data;
    
    console.log(`${articles.length} articles trouvés`);
    return articles;
  } catch (error) {
    console.error('Erreur lors de la récupération des articles:', error);
    return [];
  }
}

/**
 * Génère une URL slug à partir du titre
 */
function generateSlug(title) {
  return title
    .toLowerCase()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '') // Supprimer les accents
    .replace(/[^a-z0-9\s-]/g, '') // Supprimer les caractères spéciaux
    .replace(/\s+/g, '-') // Remplacer les espaces par des tirets
    .replace(/-+/g, '-') // Supprimer les tirets multiples
    .trim('-'); // Supprimer les tirets en début/fin
}

/**
 * Formate une date au format ISO pour le sitemap
 */
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toISOString().split('T')[0]; // Format YYYY-MM-DD
}

/**
 * Génère le contenu XML du sitemap
 */
function generateSitemapXML(articles) {
  let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">`;

  // Ajouter les pages statiques
  STATIC_PAGES.forEach(page => {
    xml += `
  <url>
    <loc>${SITE_URL}${page.url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
  });

  // Ajouter les articles
  articles.forEach(article => {
    // Utiliser le slug du backend s'il existe, sinon générer un slug
    const slug = article.slug || generateSlug(article.titre);
    const lastmod = formatDate(article.date_modification || article.date_creation || article.date_publication);

    // URL avec slug SEO-friendly (sans ID)
    const articleUrl = slug ? `/article/${slug}` : `/article/${article.id}`;

    xml += `
  <url>
    <loc>${SITE_URL}${articleUrl}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.7</priority>
  </url>`;
  });

  xml += `
</urlset>`;

  return xml;
}

/**
 * Sauvegarde le sitemap dans le dossier public
 */
function saveSitemap(xmlContent) {
  try {
    // Créer le dossier s'il n'existe pas
    const dir = path.dirname(OUTPUT_PATH);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // Écrire le fichier
    fs.writeFileSync(OUTPUT_PATH, xmlContent, 'utf8');
    console.log(`Sitemap généré avec succès: ${OUTPUT_PATH}`);
  } catch (error) {
    console.error('Erreur lors de la sauvegarde du sitemap:', error);
  }
}

/**
 * Met à jour la configuration webpack avec les routes d'articles
 */
function updateWebpackConfig(articles) {
  try {
    const configPath = path.join(__dirname, '../vue.config.js');
    let configContent = fs.readFileSync(configPath, 'utf8');
    
    // Générer la liste des routes d'articles avec slugs du backend
    const articleRoutes = articles.slice(0, 20).map(article => {
      // Utiliser le slug du backend s'il existe, sinon générer un slug
      const slug = article.slug || generateSlug(article.titre);
      return slug ? `'/article/${slug}'` : `'/article/${article.id}'`;
    });
    const routesArray = [
      "'/'",
      "'/actualite'",
      ...articleRoutes
    ];
    
    // Remplacer la section routes dans la config
    const routesRegex = /routes:\s*\[([\s\S]*?)\]/;
    const newRoutes = `routes: [
            ${routesArray.join(',\n            ')}
          ]`;
    
    configContent = configContent.replace(routesRegex, newRoutes);
    
    fs.writeFileSync(configPath, configContent, 'utf8');
    console.log('Configuration webpack mise à jour avec les routes d\'articles');
  } catch (error) {
    console.error('Erreur lors de la mise à jour de la configuration webpack:', error);
  }
}

/**
 * Fonction principale
 */
async function main() {
  console.log('🚀 Génération du sitemap...');
  
  // Récupérer les articles
  const articles = await fetchPublishedArticles();
  
  if (articles.length === 0) {
    console.warn('Aucun article trouvé, génération du sitemap avec les pages statiques uniquement');
  }
  
  // Générer le XML
  const sitemapXML = generateSitemapXML(articles);
  
  // Sauvegarder le sitemap
  saveSitemap(sitemapXML);
  
  // Mettre à jour la config webpack
  updateWebpackConfig(articles);
  
  console.log('✅ Génération terminée !');
}

// Exécuter le script si appelé directement
if (require.main === module) {
  main().catch(console.error);
}

module.exports = {
  main,
  fetchPublishedArticles,
  generateSitemapXML,
  generateSlug
};
