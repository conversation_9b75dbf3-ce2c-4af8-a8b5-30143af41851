/**
 * Utilitaires SEO pour la gestion des meta tags
 */

export class SEOManager {
  static updateTitle(title) {
    document.title = title;
  }

  static updateMetaTag(name, content, attribute = 'name') {
    let element = document.querySelector(`meta[${attribute}="${name}"]`);
    if (element) {
      element.setAttribute('content', content);
    } else {
      element = document.createElement('meta');
      element.setAttribute(attribute, name);
      element.setAttribute('content', content);
      element.setAttribute('data-dynamic', 'true');
      document.getElementsByTagName('head')[0].appendChild(element);
    }
  }

  static updateCanonical(url) {
    let element = document.querySelector('link[rel="canonical"]');
    if (element) {
      element.setAttribute('href', url);
    } else {
      element = document.createElement('link');
      element.setAttribute('rel', 'canonical');
      element.setAttribute('href', url);
      element.setAttribute('data-dynamic', 'true');
      document.getElementsByTagName('head')[0].appendChild(element);
    }
  }

  static addStructuredData(data) {
    // Supprimer l'ancien script JSON-LD s'il existe
    const existingScript = document.querySelector('script[type="application/ld+json"][data-dynamic="true"]');
    if (existingScript) {
      existingScript.remove();
    }

    // Créer le nouveau script JSON-LD
    const script = document.createElement('script');
    script.type = 'application/ld+json';
    script.setAttribute('data-dynamic', 'true');
    script.textContent = JSON.stringify(data);
    document.getElementsByTagName('head')[0].appendChild(script);
  }

  static cleanupDynamicTags() {
    // Nettoyer tous les tags dynamiques
    const dynamicElements = document.querySelectorAll('[data-dynamic="true"]');
    dynamicElements.forEach(element => element.remove());
  }

  static setArticleSEO(article) {
    if (!article) return;

    const title = `${article.titre} | MeetVoice`;
    const description = article.petit_description || article.titre;
    const url = window.location.href;
    const image = article.photo || article.image || `${window.location.origin}/favicon.png`;

    // Titre de la page
    this.updateTitle(title);

    // Meta description
    this.updateMetaTag('description', description);

    // URL canonique
    this.updateCanonical(url);

    // Open Graph
    this.updateMetaTag('og:title', title, 'property');
    this.updateMetaTag('og:description', description, 'property');
    this.updateMetaTag('og:type', 'article', 'property');
    this.updateMetaTag('og:url', url, 'property');
    this.updateMetaTag('og:image', image, 'property');

    // Twitter Cards
    this.updateMetaTag('twitter:card', 'summary_large_image', 'name');
    this.updateMetaTag('twitter:title', title, 'name');
    this.updateMetaTag('twitter:description', description, 'name');
    this.updateMetaTag('twitter:image', image, 'name');

    // Données structurées enrichies
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "Article",
      "headline": article.titre,
      "description": description,
      "image": {
        "@type": "ImageObject",
        "url": image,
        "width": 1200,
        "height": 630
      },
      "author": {
        "@type": "Person",
        "name": article.auteur?.full_name || "MeetVoice"
      },
      "publisher": {
        "@type": "Organization",
        "name": "MeetVoice",
        "url": window.location.origin,
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/favicon.png`,
          "width": 192,
          "height": 192
        }
      },
      "datePublished": article.date_creation,
      "dateModified": article.date_modification || article.date_creation,
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": url
      },
      "articleSection": article.theme,
      "wordCount": article.contenu ? article.contenu.length : 0,
      "inLanguage": "fr-FR",
      "isAccessibleForFree": true,
      "articleSection": article.theme || "Actualités",
      "wordCount": article.contenu ? article.contenu.replace(/<[^>]*>/g, '').split(' ').length : 0,
      "inLanguage": "fr-FR"
    };

    this.addStructuredData(structuredData);
  }

  static setPageSEO(title, description, type = 'website') {
    const fullTitle = title.includes('MeetVoice') ? title : `${title} | MeetVoice`;
    const url = window.location.href;

    // Titre de la page
    this.updateTitle(fullTitle);

    // Meta description
    this.updateMetaTag('description', description);

    // URL canonique
    this.updateCanonical(url);

    // Open Graph
    this.updateMetaTag('og:title', fullTitle, 'property');
    this.updateMetaTag('og:description', description, 'property');
    this.updateMetaTag('og:type', type, 'property');
    this.updateMetaTag('og:url', url, 'property');

    // Twitter Cards
    this.updateMetaTag('twitter:title', fullTitle, 'name');
    this.updateMetaTag('twitter:description', description, 'name');
  }
}

export default SEOManager;
