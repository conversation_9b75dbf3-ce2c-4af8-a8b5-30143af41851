[package]
name = "meetvoice-websocket"
version = "0.1.0"
edition = "2021"

[[bin]]
name = "simple-server"
path = "src/simple_main.rs"

[dependencies]
# Async runtime
tokio = { version = "1.0", features = ["full"] }

# Web framework et WebSocket
axum = { version = "0.7", features = ["ws", "macros"] }
tower = "0.4"
tower-http = { version = "0.5", features = ["cors", "trace"] }

# Sérialisation
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"

# Base de données (désactivées temporairement pour les tests)
# sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "postgres", "uuid", "chrono"] }
# mongodb = "2.8"

# P2P (désactivé temporairement pour les tests)
# libp2p = { version = "0.53", features = [
#     "tcp",
#     "mdns",
#     "noise",
#     "yamux",
#     "websocket",
#     "ping",
#     "identify",
#     "relay",
#     "dcutr",
#     "kad",
#     "gossipsub",
#     "request-response",
#     "tokio"
# ] }

# Utilitaires
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter"] }
anyhow = "1.0"
thiserror = "1.0"
futures = "0.3"
dashmap = "5.5"

# Configuration
config = "0.14"
dotenvy = "0.15"
async-trait = "0.1"
tokio-stream = "0.1"

[dev-dependencies]
tokio-test = "0.4"
tokio-tungstenite = "0.21"
futures-util = "0.3"
reqwest = { version = "0.11", features = ["json"] }
tokio-stream = "0.1"
async-trait = "0.1"
