/**
 * Service Vosk STT - Reconnaissance vocale avancée
 * Utilise l'API Vosk backend pour une reconnaissance vocale précise
 * Complètement séparé du système TTS
 */

import Axios from './caller.service.js';

class VoskSTTService {
  constructor() {
    this.baseUrl = '/api'; // Utilise le baseURL d'Axios
    this.isListening = false;
    this.isInitialized = true; // API backend toujours prête
    this.mediaRecorder = null;
    this.mediaStream = null;
    this.audioChunks = [];
    this.recordingInterval = null;

    console.log('🎯 Service Vosk STT API initialisé');
  }

  /**
   * Vérifier la disponibilité de l'API Vosk backend
   */
  async initialize() {
    try {
      console.log('🔄 Vérification API Vosk backend...');

      // Tester la connexion à l'API Vosk
      const response = await Axios.get(`${this.baseUrl}/vosk/status/`);

      if (response.data && response.data.status === 'ready') {
        this.isInitialized = true;
        console.log('✅ API Vosk backend disponible');
        return true;
      } else {
        throw new Error('API Vosk backend non disponible');
      }
    } catch (error) {
      console.warn('⚠️ API Vosk backend indisponible:', error.message);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Démarrer l'écoute avec l'API Vosk backend
   */
  async startListening(options = {}) {
    try {
      if (!this.isInitialized) {
        console.log('🔄 Vérification API Vosk...');
        const available = await this.initialize();
        if (!available) {
          throw new Error('API Vosk backend non disponible');
        }
      }

      console.log('🎤 Démarrage écoute Vosk API...');

      // Obtenir l'accès au microphone
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      // Configurer MediaRecorder pour envoyer l'audio à l'API
      // Utiliser WAV si possible, sinon WebM
      const supportedMimeType = this.getSupportedMimeType();
      this.mediaRecorder = new MediaRecorder(this.mediaStream, {
        mimeType: supportedMimeType
      });

      this.audioChunks = [];

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = async () => {
        if (this.audioChunks.length > 0) {
          await this.processAudioChunks(options);
        }
      };

      // Démarrer l'enregistrement
      this.mediaRecorder.start();

      // Envoyer des chunks régulièrement pour la reconnaissance en temps réel
      this.recordingInterval = setInterval(() => {
        if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          this.mediaRecorder.stop();
          this.mediaRecorder.start();
        }
      }, 2000); // Envoyer toutes les 2 secondes

      this.isListening = true;
      console.log('✅ Écoute Vosk API démarrée');

      if (options.onStart) options.onStart();

      return 'Vosk API STT démarré';

    } catch (error) {
      console.error('❌ Erreur démarrage Vosk API:', error);
      this.isListening = false;
      if (options.onError) options.onError(error.message);
      throw error;
    }
  }

  /**
   * Traiter les chunks audio avec l'API Vosk
   */
  async processAudioChunks(options) {
    try {
      if (this.audioChunks.length === 0) return;

      // Créer un blob audio avec le bon type MIME
      const mimeType = this.getSupportedMimeType();
      const audioBlob = new Blob(this.audioChunks, { type: mimeType });

      // Convertir en WAV si nécessaire
      const wavBlob = await this.convertToWav(audioBlob);

      // Préparer FormData pour l'API
      const formData = new FormData();
      formData.append('audio', wavBlob, 'audio.wav');
      formData.append('language', options.language || 'fr-FR');

      // Envoyer à l'API Vosk
      const response = await Axios.post(`${this.baseUrl}/vosk/speech-to-text/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data && response.data.text) {
        const text = response.data.text.trim();
        console.log('📝 Vosk API résultat:', text);

        if (text && options.onResult) {
          options.onResult(text, '');
        }
      }

      // Réinitialiser les chunks
      this.audioChunks = [];

    } catch (error) {
      console.warn('⚠️ Erreur traitement audio Vosk:', error.message);
      this.audioChunks = [];
    }
  }

  /**
   * Arrêter l'écoute Vosk API
   */
  stopListening() {
    console.log('🛑 Arrêt écoute Vosk API...');

    this.isListening = false;

    // Arrêter l'intervalle d'enregistrement
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
      this.recordingInterval = null;
    }

    // Arrêter MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    // Arrêter le stream audio
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    // Nettoyer
    this.mediaRecorder = null;
    this.audioChunks = [];

    console.log('✅ Écoute Vosk API arrêtée');
  }

  /**
   * Vérifier si Vosk est en écoute
   */
  isListeningActive() {
    return this.isListening;
  }

  /**
   * Vérifier si Vosk est initialisé
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * Obtenir le statut de Vosk API
   */
  getStatus() {
    return {
      isListening: this.isListening,
      isInitialized: this.isInitialized,
      isSupported: this.isSupported(),
      apiUrl: `${this.baseUrl}/vosk/`,
      hasMediaRecorder: !!this.mediaRecorder
    };
  }

  /**
   * Vérifier le support de Vosk API
   */
  isSupported() {
    return !!(navigator.mediaDevices &&
              navigator.mediaDevices.getUserMedia &&
              window.MediaRecorder);
  }

  /**
   * Réinitialiser Vosk API
   */
  reset() {
    console.log('🔄 Reset Vosk API...');
    this.stopListening();
    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isInitialized = true; // API backend toujours prête
  }

  /**
   * Finaliser la reconnaissance (traiter les derniers chunks)
   */
  async finalize(options = {}) {
    if (this.audioChunks.length > 0 && this.isListening) {
      console.log('🏁 Finalisation Vosk API...');
      await this.processAudioChunks(options);
      return 'Finalisation terminée';
    }
    return '';
  }
}

// Export de l'instance unique
export const voskSttService = new VoskSTTService();
export default voskSttService;
