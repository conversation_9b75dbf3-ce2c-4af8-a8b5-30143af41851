import { mount, createLocalVue } from '@vue/test-utils'
import ActualiteView from '@/views/ActualiteView.vue'
import Vuex from 'vuex'

// Mock fetch
global.fetch = jest.fn()

const localVue = createLocalVue()
localVue.use(Vuex)

describe('ActualiteView.vue', () => {
  let wrapper
  let store
  let actions
  let getters

  beforeEach(() => {
    // Mock store
    actions = {
      setError: jest.fn()
    }
    
    getters = {
      isAuthenticated: () => false,
      getUser: () => null
    }

    store = new Vuex.Store({
      actions,
      getters
    })

    // Mock fetch response
    fetch.mockResolvedValue({
      ok: true,
      json: async () => ([
        {
          id: 1,
          titre: 'Test Article',
          sous_titre: 'Test Subtitle',
          contenu: 'Test content',
          petit_description: 'Test description',
          auteur: {
            id: 1,
            username: 'testuser',
            full_name: 'Test User'
          },
          categorie: 'recruteur',
          theme: 'Décryptage',
          status: 'published',
          access_count: 10,
          mis_en_avant: false,
          photo: null,
          tags_list: ['test', 'article'],
          reading_time: 2,
          date_publication: '2023-01-01T00:00:00Z'
        }
      ])
    })

    wrapper = mount(ActualiteView, {
      localVue,
      store,
      mocks: {
        $router: {
          push: jest.fn()
        }
      }
    })
  })

  afterEach(() => {
    jest.clearAllMocks()
  })

  it('renders correctly', () => {
    expect(wrapper.exists()).toBe(true)
    expect(wrapper.find('.articles-container').exists()).toBe(true)
  })

  it('displays hero section with title', () => {
    expect(wrapper.find('.hero-title').text()).toContain('Articles')
    expect(wrapper.find('.hero-subtitle').text()).toContain('Découvrez nos derniers articles')
  })

  it('has search functionality', () => {
    const searchInput = wrapper.find('.search-input')
    expect(searchInput.exists()).toBe(true)
    expect(searchInput.attributes('placeholder')).toBe('Rechercher un article...')
  })

  it('has filter controls', () => {
    const categoryFilter = wrapper.find('select[name="categorie"]')
    const themeFilter = wrapper.find('select[name="theme"]')
    const sortFilter = wrapper.find('select[name="sort"]')
    
    expect(categoryFilter.exists()).toBe(true)
    expect(themeFilter.exists()).toBe(true)
    expect(sortFilter.exists()).toBe(true)
  })

  it('loads articles on mount', async () => {
    await wrapper.vm.$nextTick()
    
    expect(fetch).toHaveBeenCalledWith('http://127.0.0.1:8000/actualite/api/articles/published/')
    expect(wrapper.vm.articles).toHaveLength(1)
    expect(wrapper.vm.articles[0].titre).toBe('Test Article')
  })

  it('filters articles by search query', async () => {
    await wrapper.vm.$nextTick()
    
    // Set search query
    wrapper.setData({ searchQuery: 'Test' })
    wrapper.vm.filterArticles()
    
    expect(wrapper.vm.filteredArticles).toHaveLength(1)
    
    // Search for non-existent term
    wrapper.setData({ searchQuery: 'NonExistent' })
    wrapper.vm.filterArticles()
    
    expect(wrapper.vm.filteredArticles).toHaveLength(0)
  })

  it('filters articles by category', async () => {
    await wrapper.vm.$nextTick()
    
    wrapper.setData({ selectedCategory: 'recruteur' })
    wrapper.vm.filterArticles()
    
    expect(wrapper.vm.filteredArticles).toHaveLength(1)
    
    wrapper.setData({ selectedCategory: 'candidats' })
    wrapper.vm.filterArticles()
    
    expect(wrapper.vm.filteredArticles).toHaveLength(0)
  })

  it('sorts articles correctly', async () => {
    // Add more test data
    wrapper.setData({
      articles: [
        {
          id: 1,
          titre: 'Article A',
          date_publication: '2023-01-01T00:00:00Z',
          access_count: 5
        },
        {
          id: 2,
          titre: 'Article B',
          date_publication: '2023-01-02T00:00:00Z',
          access_count: 10
        }
      ],
      filteredArticles: [
        {
          id: 1,
          titre: 'Article A',
          date_publication: '2023-01-01T00:00:00Z',
          access_count: 5
        },
        {
          id: 2,
          titre: 'Article B',
          date_publication: '2023-01-02T00:00:00Z',
          access_count: 10
        }
      ]
    })

    // Test date descending (default)
    wrapper.setData({ sortBy: 'date_desc' })
    wrapper.vm.sortArticles()
    expect(wrapper.vm.filteredArticles[0].titre).toBe('Article B')

    // Test views descending
    wrapper.setData({ sortBy: 'views_desc' })
    wrapper.vm.sortArticles()
    expect(wrapper.vm.filteredArticles[0].access_count).toBe(10)

    // Test title ascending
    wrapper.setData({ sortBy: 'title_asc' })
    wrapper.vm.sortArticles()
    expect(wrapper.vm.filteredArticles[0].titre).toBe('Article A')
  })

  it('handles pagination correctly', () => {
    // Set up test data for pagination
    const articles = Array.from({ length: 20 }, (_, i) => ({
      id: i + 1,
      titre: `Article ${i + 1}`
    }))
    
    wrapper.setData({ 
      filteredArticles: articles,
      articlesPerPage: 9,
      currentPage: 1
    })

    expect(wrapper.vm.totalPages).toBe(3)
    expect(wrapper.vm.paginatedArticles).toHaveLength(9)

    // Test page change
    wrapper.vm.changePage(2)
    expect(wrapper.vm.currentPage).toBe(2)
    expect(wrapper.vm.paginatedArticles).toHaveLength(9)

    // Test last page
    wrapper.vm.changePage(3)
    expect(wrapper.vm.currentPage).toBe(3)
    expect(wrapper.vm.paginatedArticles).toHaveLength(2)
  })

  it('resets filters correctly', () => {
    wrapper.setData({
      searchQuery: 'test',
      selectedCategory: 'recruteur',
      selectedTheme: 'Décryptage',
      sortBy: 'views_desc',
      currentPage: 2
    })

    wrapper.vm.resetFilters()

    expect(wrapper.vm.searchQuery).toBe('')
    expect(wrapper.vm.selectedCategory).toBe('')
    expect(wrapper.vm.selectedTheme).toBe('')
    expect(wrapper.vm.sortBy).toBe('date_desc')
    expect(wrapper.vm.currentPage).toBe(1)
  })

  it('opens article modal correctly', async () => {
    const article = {
      id: 1,
      titre: 'Test Article'
    }

    // Mock detailed article fetch
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        ...article,
        contenu: 'Detailed content'
      })
    })

    await wrapper.vm.openArticle(article)

    expect(fetch).toHaveBeenCalledWith('http://127.0.0.1:8000/actualite/api/articles/1/')
    expect(wrapper.vm.selectedArticle).toBeTruthy()
    expect(wrapper.vm.selectedArticle.titre).toBe('Test Article')
  })

  it('closes article modal correctly', () => {
    wrapper.setData({ selectedArticle: { id: 1, titre: 'Test' } })
    
    wrapper.vm.closeArticle()
    
    expect(wrapper.vm.selectedArticle).toBeNull()
  })

  it('increments article views', async () => {
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ access_count: 11 })
    })

    await wrapper.vm.incrementViews(1)

    expect(fetch).toHaveBeenCalledWith(
      'http://127.0.0.1:8000/actualite/api/articles/1/increment_views/',
      expect.objectContaining({
        method: 'POST'
      })
    )
  })

  it('formats date correctly', () => {
    const dateString = '2023-01-15T10:30:00Z'
    const formatted = wrapper.vm.formatDate(dateString)
    
    expect(formatted).toMatch(/15 janvier 2023/)
  })

  it('gets category class correctly', () => {
    expect(wrapper.vm.getCategoryClass('recruteur')).toEqual({
      'category-recruteur': true,
      'category-candidats': false
    })
    
    expect(wrapper.vm.getCategoryClass('candidats')).toEqual({
      'category-recruteur': false,
      'category-candidats': true
    })
  })

  it('gets category display correctly', () => {
    expect(wrapper.vm.getCategoryDisplay('recruteur')).toBe('Recruteur')
    expect(wrapper.vm.getCategoryDisplay('candidats')).toBe('Candidats')
  })

  it('handles loading state', () => {
    wrapper.setData({ loading: true })
    
    expect(wrapper.find('.loading-section').exists()).toBe(true)
    expect(wrapper.find('.articles-section').exists()).toBe(false)
  })

  it('displays no articles message when empty', () => {
    wrapper.setData({ 
      loading: false,
      filteredArticles: []
    })
    
    expect(wrapper.find('.no-articles').exists()).toBe(true)
  })

  it('separates featured articles correctly', async () => {
    const articles = [
      { id: 1, titre: 'Regular Article', mis_en_avant: false },
      { id: 2, titre: 'Featured Article', mis_en_avant: true }
    ]
    
    wrapper.setData({ articles })
    
    // Simulate the separation logic
    const featured = articles.filter(article => article.mis_en_avant)
    
    expect(featured).toHaveLength(1)
    expect(featured[0].titre).toBe('Featured Article')
  })
})
