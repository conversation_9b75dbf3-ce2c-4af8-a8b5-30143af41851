#!/usr/bin/env node

const { execSync } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🧪 Démarrage de la suite de tests MeetVoice\n')

// Configuration
const config = {
  testTimeout: 30000,
  coverageThreshold: 70,
  maxWorkers: '50%'
}

// Couleurs pour la console
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
}

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

function logSection(title) {
  console.log('\n' + '='.repeat(50))
  log(title, 'cyan')
  console.log('='.repeat(50))
}

function runCommand(command, description) {
  log(`\n📋 ${description}...`, 'blue')
  try {
    const output = execSync(command, { 
      encoding: 'utf8',
      stdio: 'pipe',
      maxBuffer: 1024 * 1024 * 10 // 10MB buffer
    })
    log(`✅ ${description} - Succès`, 'green')
    return { success: true, output }
  } catch (error) {
    log(`❌ ${description} - Échec`, 'red')
    console.error(error.stdout || error.message)
    return { success: false, error: error.message }
  }
}

function checkDependencies() {
  logSection('Vérification des dépendances')
  
  const dependencies = [
    'jest',
    '@vue/test-utils',
    '@vue/vue3-jest',
    'babel-jest'
  ]
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
  const allDeps = { 
    ...packageJson.dependencies, 
    ...packageJson.devDependencies 
  }
  
  let allPresent = true
  
  dependencies.forEach(dep => {
    if (allDeps[dep]) {
      log(`✅ ${dep} - ${allDeps[dep]}`, 'green')
    } else {
      log(`❌ ${dep} - Manquant`, 'red')
      allPresent = false
    }
  })
  
  return allPresent
}

function runLinting() {
  logSection('Analyse du code (ESLint)')
  
  const result = runCommand(
    'npx eslint src tests --ext .js,.vue --format=compact',
    'Analyse ESLint'
  )
  
  if (!result.success) {
    log('⚠️  Des problèmes de linting ont été détectés', 'yellow')
    return false
  }
  
  return true
}

function runUnitTests() {
  logSection('Tests unitaires')
  
  const jestCommand = [
    'npx jest',
    '--coverage',
    '--verbose',
    `--maxWorkers=${config.maxWorkers}`,
    '--detectOpenHandles',
    '--forceExit'
  ].join(' ')
  
  const result = runCommand(jestCommand, 'Exécution des tests unitaires')
  
  if (result.success) {
    log('📊 Génération du rapport de couverture...', 'blue')
    generateCoverageReport()
  }
  
  return result.success
}

function runE2ETests() {
  logSection('Tests end-to-end (optionnel)')
  
  // Vérifier si Cypress est installé
  try {
    execSync('npx cypress version', { stdio: 'pipe' })
  } catch (error) {
    log('⚠️  Cypress non installé - Tests E2E ignorés', 'yellow')
    return true
  }
  
  const result = runCommand(
    'npx cypress run --headless',
    'Exécution des tests E2E'
  )
  
  return result.success
}

function generateCoverageReport() {
  const coveragePath = path.join(process.cwd(), 'coverage')
  
  if (!fs.existsSync(coveragePath)) {
    log('❌ Répertoire de couverture non trouvé', 'red')
    return
  }
  
  // Lire le rapport de couverture JSON
  const coverageJsonPath = path.join(coveragePath, 'coverage-final.json')
  if (fs.existsSync(coverageJsonPath)) {
    try {
      const coverage = JSON.parse(fs.readFileSync(coverageJsonPath, 'utf8'))
      
      let totalLines = 0
      let coveredLines = 0
      let totalFunctions = 0
      let coveredFunctions = 0
      let totalBranches = 0
      let coveredBranches = 0
      
      Object.values(coverage).forEach(file => {
        if (file.s) {
          totalLines += Object.keys(file.s).length
          coveredLines += Object.values(file.s).filter(count => count > 0).length
        }
        
        if (file.f) {
          totalFunctions += Object.keys(file.f).length
          coveredFunctions += Object.values(file.f).filter(count => count > 0).length
        }
        
        if (file.b) {
          Object.values(file.b).forEach(branch => {
            totalBranches += branch.length
            coveredBranches += branch.filter(count => count > 0).length
          })
        }
      })
      
      const linesCoverage = totalLines > 0 ? (coveredLines / totalLines * 100).toFixed(2) : 0
      const functionsCoverage = totalFunctions > 0 ? (coveredFunctions / totalFunctions * 100).toFixed(2) : 0
      const branchesCoverage = totalBranches > 0 ? (coveredBranches / totalBranches * 100).toFixed(2) : 0
      
      console.log('\n📊 Résumé de la couverture:')
      console.log(`   Lignes: ${linesCoverage}% (${coveredLines}/${totalLines})`)
      console.log(`   Fonctions: ${functionsCoverage}% (${coveredFunctions}/${totalFunctions})`)
      console.log(`   Branches: ${branchesCoverage}% (${coveredBranches}/${totalBranches})`)
      
      // Vérifier les seuils
      const thresholds = [
        { name: 'Lignes', value: parseFloat(linesCoverage) },
        { name: 'Fonctions', value: parseFloat(functionsCoverage) },
        { name: 'Branches', value: parseFloat(branchesCoverage) }
      ]
      
      const failedThresholds = thresholds.filter(t => t.value < config.coverageThreshold)
      
      if (failedThresholds.length > 0) {
        log(`\n⚠️  Seuils de couverture non atteints (${config.coverageThreshold}%):`, 'yellow')
        failedThresholds.forEach(t => {
          log(`   ${t.name}: ${t.value}%`, 'yellow')
        })
      } else {
        log(`\n✅ Tous les seuils de couverture atteints (${config.coverageThreshold}%)`, 'green')
      }
      
    } catch (error) {
      log('❌ Erreur lors de la lecture du rapport de couverture', 'red')
      console.error(error.message)
    }
  }
  
  // Afficher le chemin vers le rapport HTML
  const htmlReportPath = path.join(coveragePath, 'lcov-report', 'index.html')
  if (fs.existsSync(htmlReportPath)) {
    log(`\n📄 Rapport HTML disponible: file://${htmlReportPath}`, 'cyan')
  }
}

function generateTestReport(results) {
  logSection('Rapport final')
  
  const totalTests = results.filter(r => r.type === 'test').length
  const passedTests = results.filter(r => r.type === 'test' && r.success).length
  const failedTests = totalTests - passedTests
  
  console.log(`\n📈 Résumé des tests:`)
  console.log(`   Total: ${totalTests}`)
  console.log(`   Réussis: ${passedTests}`)
  console.log(`   Échoués: ${failedTests}`)
  
  if (failedTests === 0) {
    log('\n🎉 Tous les tests sont passés avec succès!', 'green')
  } else {
    log(`\n❌ ${failedTests} test(s) ont échoué`, 'red')
  }
  
  // Afficher les détails des échecs
  const failures = results.filter(r => !r.success)
  if (failures.length > 0) {
    log('\n💥 Détails des échecs:', 'red')
    failures.forEach(failure => {
      console.log(`   - ${failure.description}: ${failure.error}`)
    })
  }
  
  return failedTests === 0
}

// Fonction principale
async function main() {
  const startTime = Date.now()
  const results = []
  
  try {
    // Vérifier les dépendances
    if (!checkDependencies()) {
      log('\n❌ Dépendances manquantes. Exécutez: npm install', 'red')
      process.exit(1)
    }
    
    // Linting
    const lintResult = runLinting()
    results.push({
      type: 'lint',
      description: 'Analyse du code',
      success: lintResult
    })
    
    // Tests unitaires
    const unitTestResult = runUnitTests()
    results.push({
      type: 'test',
      description: 'Tests unitaires',
      success: unitTestResult
    })
    
    // Tests E2E (optionnel)
    const e2eTestResult = runE2ETests()
    results.push({
      type: 'test',
      description: 'Tests E2E',
      success: e2eTestResult
    })
    
    // Générer le rapport final
    const allPassed = generateTestReport(results)
    
    const endTime = Date.now()
    const duration = ((endTime - startTime) / 1000).toFixed(2)
    
    log(`\n⏱️  Durée totale: ${duration}s`, 'cyan')
    
    if (allPassed) {
      log('\n🚀 Suite de tests terminée avec succès!', 'green')
      process.exit(0)
    } else {
      log('\n💥 Suite de tests terminée avec des erreurs', 'red')
      process.exit(1)
    }
    
  } catch (error) {
    log('\n💥 Erreur fatale lors de l\'exécution des tests', 'red')
    console.error(error)
    process.exit(1)
  }
}

// Gestion des signaux
process.on('SIGINT', () => {
  log('\n\n⏹️  Tests interrompus par l\'utilisateur', 'yellow')
  process.exit(1)
})

process.on('SIGTERM', () => {
  log('\n\n⏹️  Tests interrompus', 'yellow')
  process.exit(1)
})

// Exécuter si appelé directement
if (require.main === module) {
  main()
}

module.exports = { main, runCommand, generateCoverageReport }
