#!/usr/bin/env python3
"""
Test de la modification pour la saisie manuelle de l'email
"""

def test_email_question_modification():
    """Test de la modification de la question email"""
    print("📧 Test de la modification de la question email...")
    print("=" * 60)
    
    # Simulation de la question email modifiée
    email_question = {
        "text": "Pour votre adresse email, veuillez l'écrire dans le champ de saisie manuelle ci-dessous.",
        "hint": "💡 Utilisez la saisie manuelle pour votre email",
        "examples": ["<EMAIL>", "<EMAIL>"],
        "fields": ['email'],
        "type": 'email',
        "requiresManualInput": True
    }
    
    print("✅ Question email modifiée:")
    print(f"   Texte: '{email_question['text']}'")
    print(f"   Hint: '{email_question['hint']}'")
    print(f"   Saisie manuelle requise: {email_question.get('requiresManualInput', False)}")
    print(f"   Exemples: {email_question['examples']}")
    
    return True

def test_visual_hint_detection():
    """Test de la détection du hint visuel pour l'email"""
    print("\n🔍 Test de détection du hint visuel...")
    print("=" * 50)
    
    # Simulation de la fonction isVisualHint
    def is_visual_hint(hint):
        import re
        visual_patterns = [
            r'^💡',  # Commence par l'emoji ampoule
            r'^🔍',  # Commence par l'emoji loupe
            r'^ℹ️',  # Commence par l'emoji info
            r'^⚠️',  # Commence par l'emoji warning
            r'^📝',  # Commence par l'emoji note
            r'^👆',  # Commence par l'emoji pointeur
            r'^Dites\s+votre\s+prénom$',  # "Dites votre prénom" exactement
            r'^Dites\s+votre\s+nom\s+de\s+famille$',  # "Dites votre nom de famille" exactement
            r'^Dites\s+simplement\s+(oui|non)$',  # "Dites simplement oui" ou "Dites simplement non"
        ]
        
        return any(re.search(pattern, hint.strip(), re.IGNORECASE) for pattern in visual_patterns)
    
    # Test du hint email
    email_hint = "💡 Utilisez la saisie manuelle pour votre email"
    is_visual = is_visual_hint(email_hint)
    
    print(f"Hint email: '{email_hint}'")
    print(f"Est visuel (ne sera pas lu): {'✅ Oui' if is_visual else '❌ Non'}")
    
    if is_visual:
        print("✅ Le hint email ne sera pas lu à voix haute")
    else:
        print("❌ Le hint email sera lu à voix haute (problème)")
    
    return is_visual

def test_question_text_construction():
    """Test de construction du texte de la question"""
    print("\n🎤 Test de construction du texte de la question...")
    print("=" * 50)
    
    # Simulation de la logique de construction
    def build_question_text(question_index, question_text, hint):
        import re
        
        def is_visual_hint(hint):
            visual_patterns = [
                r'^💡',  # Commence par l'emoji ampoule
                r'^🔍',  # Commence par l'emoji loupe
                r'^ℹ️',  # Commence par l'emoji info
                r'^⚠️',  # Commence par l'emoji warning
                r'^📝',  # Commence par l'emoji note
                r'^👆',  # Commence par l'emoji pointeur
                r'^Dites\s+votre\s+prénom$',  # "Dites votre prénom" exactement
                r'^Dites\s+votre\s+nom\s+de\s+famille$',  # "Dites votre nom de famille" exactement
                r'^Dites\s+simplement\s+(oui|non)$',  # "Dites simplement oui" ou "Dites simplement non"
            ]
            
            return any(re.search(pattern, hint.strip(), re.IGNORECASE) for pattern in visual_patterns)
        
        # Construire le texte à lire
        text_to_read = f"Question {question_index + 1}. {question_text}"
        
        # Ajouter le hint seulement s'il ne commence pas par un emoji ou des instructions visuelles
        if hint and not is_visual_hint(hint):
            text_to_read += f" Conseil : {hint}"
        
        return text_to_read
    
    # Test avec la question email
    question_text = "Pour votre adresse email, veuillez l'écrire dans le champ de saisie manuelle ci-dessous."
    hint = "💡 Utilisez la saisie manuelle pour votre email"
    
    result = build_question_text(2, question_text, hint)  # Question 3 (index 2)
    expected = "Question 3. Pour votre adresse email, veuillez l'écrire dans le champ de saisie manuelle ci-dessous."
    
    print(f"Texte lu: '{result}'")
    print(f"Attendu:  '{expected}'")
    
    success = result == expected
    print(f"Résultat: {'✅ Correct' if success else '❌ Incorrect'}")
    
    if success:
        print("✅ La question email s'arrête bien sans lire le hint")
    else:
        print("❌ Problème dans la construction du texte")
    
    return success

def test_manual_input_activation():
    """Test de l'activation automatique de la saisie manuelle"""
    print("\n📝 Test de l'activation de la saisie manuelle...")
    print("=" * 50)
    
    # Simulation de la logique d'activation
    def should_activate_manual_input(question):
        return question.get('requiresManualInput', False)
    
    # Questions de test
    questions = [
        {
            "text": "Bonjour ! Pouvez-vous me dire votre prénom ?",
            "type": "first_name"
        },
        {
            "text": "Et votre nom de famille ?",
            "type": "last_name"
        },
        {
            "text": "Pour votre adresse email, veuillez l'écrire dans le champ de saisie manuelle ci-dessous.",
            "type": "email",
            "requiresManualInput": True
        },
        {
            "text": "Quel âge avez-vous ?",
            "type": "age"
        }
    ]
    
    success_count = 0
    
    for i, question in enumerate(questions):
        should_activate = should_activate_manual_input(question)
        expected = question['type'] == 'email'
        
        status = "✅" if should_activate == expected else "❌"
        action = "Activée" if should_activate else "Non activée"
        
        print(f"{status} Question {i + 1} ({question['type']}): Saisie manuelle {action}")
        
        if should_activate == expected:
            success_count += 1
    
    print(f"\n📊 Résultat: {success_count}/{len(questions)} questions correctes")
    return success_count == len(questions)

def main():
    """Fonction principale"""
    print("📧 Test de la modification pour la saisie manuelle de l'email")
    print("=" * 70)
    
    # Test de la modification de la question
    question_ok = test_email_question_modification()
    
    # Test de détection du hint visuel
    hint_ok = test_visual_hint_detection()
    
    # Test de construction du texte
    text_ok = test_question_text_construction()
    
    # Test d'activation de la saisie manuelle
    manual_ok = test_manual_input_activation()
    
    print("\n" + "=" * 70)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 70)
    
    print(f"📧 Question email modifiée: {'✅ OK' if question_ok else '❌ ERREUR'}")
    print(f"🔍 Hint visuel détecté: {'✅ OK' if hint_ok else '❌ ERREUR'}")
    print(f"🎤 Texte de question: {'✅ OK' if text_ok else '❌ ERREUR'}")
    print(f"📝 Saisie manuelle: {'✅ OK' if manual_ok else '❌ ERREUR'}")
    
    if question_ok and hint_ok and text_ok and manual_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ La question email demande maintenant d'écrire l'email")
        print("✅ Le hint visuel ne sera pas lu à voix haute")
        print("✅ La saisie manuelle s'active automatiquement")
        print("✅ L'expérience utilisateur est améliorée")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("💡 Vérifiez la configuration de la question email")

if __name__ == "__main__":
    main()
