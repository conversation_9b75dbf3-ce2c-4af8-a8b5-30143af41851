<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔍 Diagnostic Vosk - Détection des Problèmes</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .diagnostic-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #c0392b;
            transform: translateY(-2px);
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
        }
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
        }
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
        }
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔍 Diagnostic Vosk</h1>
        <p>Détection et analyse des problèmes d'intégration</p>
        <div id="overall-status">
            <span class="status warning">🟡 Prêt pour diagnostic</span>
        </div>
    </div>

    <div class="diagnostic-section">
        <h3>🚨 Diagnostic Complet</h3>
        <button class="btn" onclick="runFullDiagnostic()">
            🔍 Lancer Diagnostic Complet
        </button>
        <button class="btn" onclick="testSpecificEndpoints()">
            🎯 Tester Endpoints Spécifiques
        </button>
        <button class="btn" onclick="analyzeError500()">
            🚨 Analyser Erreur 500
        </button>
        
        <div id="diagnostic-results" class="result" style="display: none;"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://127.0.0.1:8000';

        // Diagnostic complet
        async function runFullDiagnostic() {
            const resultDiv = document.getElementById('diagnostic-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔍 === DIAGNOSTIC COMPLET VOSK ===\n\n';
            
            // Test 1: Endpoints de base
            appendResult('🔍 Test 1: Vérification des endpoints de base...');
            await testBaseEndpoints();
            
            // Test 2: Endpoints Vosk spécifiques
            appendResult('\n🔍 Test 2: Endpoints Vosk spécifiques...');
            await testVoskEndpoints();
            
            // Test 3: Analyse de l'erreur 500
            appendResult('\n🔍 Test 3: Analyse erreur 500...');
            await analyzeUploadError();
            
            // Test 4: Test avec différents formats
            appendResult('\n🔍 Test 4: Test formats audio...');
            await testDifferentFormats();
            
            appendResult('\n🔍 === FIN DU DIAGNOSTIC ===');
        }

        // Test des endpoints de base
        async function testBaseEndpoints() {
            const baseEndpoints = [
                '/',
                '/api/',
                '/admin/',
                '/docs/',
                '/health/'
            ];
            
            for (const endpoint of baseEndpoints) {
                try {
                    const response = await fetch(`${BACKEND_URL}${endpoint}`);
                    appendResult(`${response.ok ? '✅' : '❌'} ${endpoint} - Status: ${response.status}`);
                } catch (error) {
                    appendResult(`❌ ${endpoint} - Erreur: ${error.message}`);
                }
            }
        }

        // Test des endpoints Vosk
        async function testVoskEndpoints() {
            const voskEndpoints = [
                { path: '/api/vosk/', method: 'GET' },
                { path: '/api/vosk/status/', method: 'GET' },
                { path: '/api/vosk/languages/', method: 'GET' },
                { path: '/api/vosk/speech-to-text/', method: 'POST' },
                { path: '/api/vosk/speech-to-text/', method: 'GET' }
            ];
            
            for (const endpoint of voskEndpoints) {
                try {
                    const options = endpoint.method === 'POST' ? { method: 'HEAD' } : {};
                    const response = await fetch(`${BACKEND_URL}${endpoint.path}`, options);
                    appendResult(`${response.status !== 404 ? '✅' : '❌'} ${endpoint.method} ${endpoint.path} - Status: ${response.status}`);
                    
                    // Si GET et succès, afficher les données
                    if (endpoint.method === 'GET' && response.ok) {
                        try {
                            const data = await response.json();
                            appendResult(`   📄 Data: ${JSON.stringify(data, null, 2)}`);
                        } catch (e) {
                            appendResult(`   📄 Data: Non-JSON`);
                        }
                    }
                } catch (error) {
                    appendResult(`❌ ${endpoint.method} ${endpoint.path} - Erreur: ${error.message}`);
                }
            }
        }

        // Analyser l'erreur d'upload
        async function analyzeUploadError() {
            appendResult('🚨 Analyse de l\'erreur 500 d\'upload...');
            
            // Test 1: POST sans données
            try {
                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST'
                });
                appendResult(`📤 POST sans données: ${response.status}`);
                
                const text = await response.text();
                appendResult(`   📄 Réponse: ${text.substring(0, 200)}...`);
            } catch (error) {
                appendResult(`❌ POST sans données: ${error.message}`);
            }
            
            // Test 2: POST avec FormData vide
            try {
                const formData = new FormData();
                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST',
                    body: formData
                });
                appendResult(`📤 POST FormData vide: ${response.status}`);
                
                const text = await response.text();
                appendResult(`   📄 Réponse: ${text.substring(0, 200)}...`);
            } catch (error) {
                appendResult(`❌ POST FormData vide: ${error.message}`);
            }
            
            // Test 3: POST avec données invalides
            try {
                const formData = new FormData();
                formData.append('audio', 'invalid data');
                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST',
                    body: formData
                });
                appendResult(`📤 POST données invalides: ${response.status}`);
                
                const text = await response.text();
                appendResult(`   📄 Réponse: ${text.substring(0, 200)}...`);
            } catch (error) {
                appendResult(`❌ POST données invalides: ${error.message}`);
            }
        }

        // Test avec différents formats
        async function testDifferentFormats() {
            appendResult('🎵 Test avec vrai fichier WAV...');

            // Test avec un vrai fichier WAV
            try {
                const audioBlob = createMinimalWavFile();
                const formData = new FormData();
                formData.append('audio', audioBlob, 'test.wav');
                formData.append('language', 'fr-FR');

                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST',
                    body: formData
                });

                appendResult(`🎵 Vrai WAV: ${response.status}`);

                if (response.ok) {
                    const data = await response.json();
                    appendResult(`   📄 Succès: ${JSON.stringify(data)}`);
                } else {
                    const text = await response.text();
                    appendResult(`   📄 Erreur: ${text.substring(0, 200)}...`);
                }
            } catch (error) {
                appendResult(`❌ Vrai WAV: ${error.message}`);
            }

            // Test formats non supportés
            const formats = [
                { type: 'audio/webm', data: 'webm test data' },
                { type: 'audio/mp3', data: 'mp3 test data' },
                { type: 'audio/ogg', data: 'ogg test data' }
            ];
            
            for (const format of formats) {
                try {
                    const audioBlob = new Blob([format.data], { type: format.type });
                    const formData = new FormData();
                    formData.append('audio', audioBlob, `test.${format.type.split('/')[1]}`);
                    formData.append('language', 'fr-FR');
                    
                    const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                        method: 'POST',
                        body: formData
                    });
                    
                    appendResult(`🎵 Format ${format.type}: ${response.status}`);
                    
                    if (!response.ok) {
                        const text = await response.text();
                        appendResult(`   📄 Erreur: ${text.substring(0, 100)}...`);
                    }
                } catch (error) {
                    appendResult(`❌ Format ${format.type}: ${error.message}`);
                }
            }
        }

        // Test d'endpoints spécifiques
        async function testSpecificEndpoints() {
            const resultDiv = document.getElementById('diagnostic-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🎯 === TEST ENDPOINTS SPÉCIFIQUES ===\n\n';
            
            // Endpoints que vous avez confirmés fonctionner
            const workingEndpoints = [
                'GET /api/vosk/status/',
                'GET /api/vosk/languages/',
                'POST /api/vosk/',
                'POST /api/vosk/speech-to-text/'
            ];
            
            appendResult('✅ Endpoints confirmés par l\'utilisateur:');
            workingEndpoints.forEach(endpoint => {
                appendResult(`   ${endpoint}`);
            });
            
            appendResult('\n🔍 Test en direct:');
            
            // Test status
            try {
                const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
                const data = await response.json();
                appendResult(`✅ Status: ${response.status} - ${JSON.stringify(data)}`);
            } catch (error) {
                appendResult(`❌ Status: ${error.message}`);
            }
            
            // Test languages
            try {
                const response = await fetch(`${BACKEND_URL}/api/vosk/languages/`);
                const data = await response.json();
                appendResult(`✅ Languages: ${response.status} - ${JSON.stringify(data)}`);
            } catch (error) {
                appendResult(`❌ Languages: ${error.message}`);
            }
        }

        // Analyser spécifiquement l'erreur 500
        async function analyzeError500() {
            const resultDiv = document.getElementById('diagnostic-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🚨 === ANALYSE ERREUR 500 ===\n\n';
            
            appendResult('🚨 Reproduction de l\'erreur 500...');
            
            try {
                const audioBlob = new Blob(['test audio data'], { type: 'audio/webm' });
                const formData = new FormData();
                formData.append('audio', audioBlob, 'test.webm');
                formData.append('language', 'fr-FR');

                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST',
                    body: formData
                });

                appendResult(`📤 Status: ${response.status}`);
                appendResult(`📤 Headers: ${JSON.stringify([...response.headers.entries()])}`);
                
                const text = await response.text();
                appendResult(`📄 Réponse complète:\n${text}`);
                
                if (response.status === 500) {
                    appendResult('\n🔍 ANALYSE:');
                    appendResult('- Erreur 500 = Erreur serveur interne');
                    appendResult('- Le endpoint existe mais le traitement échoue');
                    appendResult('- Possible: problème avec le modèle Vosk');
                    appendResult('- Possible: format audio non supporté');
                    appendResult('- Possible: configuration serveur');
                }
                
            } catch (error) {
                appendResult(`❌ Erreur réseau: ${error.message}`);
            }
        }

        // Créer un fichier WAV minimal valide
        function createMinimalWavFile() {
            const sampleRate = 16000;
            const duration = 1; // 1 seconde
            const numSamples = sampleRate * duration;
            const numChannels = 1;
            const bitsPerSample = 16;

            const buffer = new ArrayBuffer(44 + numSamples * 2);
            const view = new DataView(buffer);

            // Header WAV
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + numSamples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
            view.setUint16(32, numChannels * bitsPerSample / 8, true);
            view.setUint16(34, bitsPerSample, true);
            writeString(36, 'data');
            view.setUint32(40, numSamples * 2, true);

            // Données audio (silence)
            for (let i = 0; i < numSamples; i++) {
                view.setInt16(44 + i * 2, 0, true);
            }

            return new Blob([buffer], { type: 'audio/wav' });
        }

        // Fonction utilitaire
        function appendResult(text) {
            const resultDiv = document.getElementById('diagnostic-results');
            resultDiv.textContent += text + '\n';
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        console.log('🔍 Diagnostic Vosk chargé !');
        console.log('🚨 Cliquez sur "Lancer Diagnostic Complet" pour analyser les problèmes.');
    </script>
</body>
</html>
