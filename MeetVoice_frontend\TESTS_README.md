# 🧪 Tests Fonctionnels MeetVoice

Suite de tests complète pour vérifier que l'interview vocal s'enregistre correctement en backend.

## 📋 Tests Inclus

### 🔧 Tests Backend Integration
- ✅ **Connexion Backend** - Vérification de l'accessibilité du serveur Django
- ✅ **Validation des Données** - Contrôle de tous les champs obligatoires/optionnels
- ✅ **Mapping des Champs** - Vérification du formatage des données
- ✅ **Inscription Backend** - Test complet d'enregistrement utilisateur

### 🎤 Tests API TTS Backend
- ✅ **Endpoint TTS** - Vérification de l'accessibilité de l'API de synthèse vocale
- ✅ **Types de Voix** - Test de toutes les voix disponibles (female_young, male_young, etc.)
- ✅ **Questions Interview** - Test de la synthèse des questions réelles
- ✅ **Performance TTS** - Mesure des temps de réponse

## 🚀 Comment Lancer les Tests

### Prérequis
1. **Backend Django démarré** sur `http://127.0.0.1:8000`
2. **Node.js installé** (pour les tests en ligne de commande)
3. **Navigateur moderne** (pour le test HTML)

### Option 1: Test HTML Interactif (Recommandé)
```bash
# Ouvrir dans votre navigateur
open test-interview-backend.html
```

**Avantages :**
- Interface graphique intuitive
- Visualisation en temps réel
- Détails des erreurs
- Simulation complète de l'interview

### Option 2: Tests en Ligne de Commande

#### Tous les tests
```bash
node run-all-tests.js
```

#### Tests backend uniquement
```bash
node run-all-tests.js --backend-only
# ou
node test-backend-integration.js
```

#### Tests TTS uniquement
```bash
node run-all-tests.js --tts-only
# ou
node test-tts-backend.js
```

#### Aide
```bash
node run-all-tests.js --help
```

## 📊 Données de Test

### Utilisateur de Test Créé
```javascript
{
  // Champs obligatoires
  first_name: 'TestMarie',
  last_name: 'TestDupont',
  username: 'test_user_[timestamp]',
  email: 'test.[timestamp]@example.com',
  password1: 'TestPassword123!',
  password2: 'TestPassword123!',
  birth_date: '1995-03-15',
  gender: 'F',
  looking_for: 'M',
  city: 'Paris',
  
  // Champs optionnels
  profession: 'Ingénieur informatique',
  education: 'Master en informatique',
  height: '1m65',
  smoking: 'Non',
  drinking: 'Socialement',
  children: 'Aucun',
  pets: 'Un chat',
  languages: ['Français', 'Anglais'],
  interests: ['Sport', 'Lecture', 'Voyages'],
  bio: 'Je suis quelqu\'un de joyeux et spontané.',
  
  // Métadonnées interview
  registration_mode: 'voice_interview',
  voice_interview_completed: true,
  voice_answers: [/* 17 réponses simulées */]
}
```

## 🔍 Interprétation des Résultats

### ✅ Tests Réussis
- **Status 200** : Inscription réussie
- **Tous les champs obligatoires** présents
- **API TTS** accessible et fonctionnelle
- **Voix multiples** disponibles

### ⚠️ Avertissements
- **Status 400** : Erreur de validation (champs manquants/incorrects)
- **TTS lent** : Temps de réponse > 3 secondes
- **Voix limitées** : Certaines voix indisponibles

### ❌ Erreurs Critiques
- **Connexion refusée** : Backend non démarré
- **Status 500** : Erreur serveur interne
- **TTS inaccessible** : API de synthèse vocale non configurée

## 🔧 Résolution des Problèmes

### Backend Non Accessible
```bash
# Vérifier que Django est démarré
python manage.py runserver 127.0.0.1:8000

# Vérifier les logs Django
tail -f logs/django.log
```

### Erreurs de Validation (400)
- Vérifier les champs obligatoires dans le modèle User
- Contrôler les contraintes de validation
- Examiner les logs de validation Django

### API TTS Non Fonctionnelle
```bash
# Tester manuellement l'endpoint TTS
curl -X POST http://127.0.0.1:8000/tts/public/synthesize/ \
  -H "Content-Type: application/json" \
  -d '{"text":"test","voice_id":"female_young","language":"fr"}'
```

### Performance TTS Lente
- Vérifier les ressources serveur (CPU, RAM)
- Optimiser la configuration TTS
- Considérer un cache pour les réponses fréquentes

## 📈 Métriques de Performance

### Temps de Réponse Acceptables
- **Inscription Backend** : < 2 secondes
- **Synthèse TTS** : < 3 secondes
- **Questions courtes** : < 1 seconde
- **Questions longues** : < 5 secondes

### Taux de Réussite Attendus
- **Tests Backend** : 100% (4/4)
- **Tests TTS** : ≥ 75% (3/4)
- **Score Global** : ≥ 90%

## 🐛 Debug et Logs

### Logs Backend Django
```bash
# Activer les logs détaillés
DEBUG = True
LOGGING = {
    'version': 1,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}
```

### Logs Frontend (Console Navigateur)
```javascript
// Activer les logs détaillés
localStorage.setItem('debug', 'true');

// Observer les requêtes réseau
// Onglet Network dans DevTools
```

### Logs Tests
```bash
# Rediriger la sortie vers un fichier
node run-all-tests.js > test-results.log 2>&1

# Analyser les résultats
grep "❌\|⚠️\|✅" test-results.log
```

## 📚 Fichiers de Test

| Fichier | Description | Usage |
|---------|-------------|-------|
| `test-interview-backend.html` | Test HTML interactif | Interface graphique |
| `test-backend-integration.js` | Tests backend Node.js | Ligne de commande |
| `test-tts-backend.js` | Tests TTS Node.js | Ligne de commande |
| `run-all-tests.js` | Lanceur de tous les tests | Ligne de commande |

## 🎯 Objectifs des Tests

### Validation Fonctionnelle
- ✅ Toutes les données de l'interview sont sauvegardées
- ✅ Les champs obligatoires sont présents
- ✅ Les champs optionnels sont gérés correctement
- ✅ Les formats de données sont respectés

### Validation Technique
- ✅ L'API d'inscription fonctionne
- ✅ L'API TTS est accessible
- ✅ Les performances sont acceptables
- ✅ La gestion d'erreurs est robuste

### Validation UX
- ✅ L'interview vocal est fluide
- ✅ La synthèse vocale est claire
- ✅ Les erreurs sont bien gérées
- ✅ L'expérience utilisateur est optimale

## 🚀 Prochaines Étapes

Après avoir validé les tests :

1. **Déploiement** : Les tests confirment que le système est prêt
2. **Monitoring** : Surveiller les métriques en production
3. **Optimisation** : Améliorer les performances si nécessaire
4. **Tests Utilisateurs** : Valider l'expérience réelle

---

**🎉 Bonne chance avec vos tests ! L'interview vocal MeetVoice est maintenant prêt pour la production.**
