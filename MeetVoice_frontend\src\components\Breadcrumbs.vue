<template>
  <nav aria-label="breadcrumb" class="breadcrumb-nav">
    <div class="container">
      <ol class="breadcrumb">
        <li class="breadcrumb-item">
          <router-link to="/" class="breadcrumb-link">
            <i class="fas fa-home me-1"></i>
            Accueil
          </router-link>
        </li>
        <li 
          v-for="(crumb, index) in breadcrumbs" 
          :key="index"
          class="breadcrumb-item"
          :class="{ active: index === breadcrumbs.length - 1 }"
        >
          <router-link 
            v-if="crumb.to && index !== breadcrumbs.length - 1" 
            :to="crumb.to"
            class="breadcrumb-link"
          >
            {{ crumb.text }}
          </router-link>
          <span v-else class="breadcrumb-current">
            {{ crumb.text }}
          </span>
        </li>
      </ol>
    </div>
  </nav>
</template>

<script>
export default {
  name: 'Breadcrumbs',
  props: {
    breadcrumbs: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style scoped>
.breadcrumb-nav {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  padding: 20px 0;
  margin-top: 20px;
  margin-bottom: 30px;
}

.breadcrumb {
  background: none;
  margin-bottom: 0;
  padding: 0;
  font-size: 1.1rem;
}

.breadcrumb-item {
  color: #6c757d;
}

.breadcrumb-link {
  color: #007bff;
  text-decoration: none;
  transition: color 0.2s ease;
  font-size: 1.1rem;
  font-weight: 500;
}

.breadcrumb-link:hover {
  color: #0056b3;
  text-decoration: underline;
}

.breadcrumb-current {
  color: #495057;
  font-weight: 600;
  font-size: 1.1rem;
}

.breadcrumb-item.active {
  color: #495057;
}

.breadcrumb-item + .breadcrumb-item::before {
  content: "›";
  color: #6c757d;
  margin: 0 8px;
}
</style>
