<template>
  <main class="profile-container">
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Chargement du profil...</p>
    </div>
    
    <div v-else-if="!profile" class="error-state">
      <h2>Profil introuvable</h2>
      <p>Ce profil n'existe pas ou a été supprimé.</p>
      <button @click="$router.go(-1)" class="btn-back">
        Retour
      </button>
    </div>
    
    <div v-else class="profile-content">
      <!-- Header du profil -->
      <header class="profile-header">
        <div class="profile-cover">
          <img 
            v-if="profile.coverPhoto"
            :src="profile.coverPhoto"
            :alt="`Photo de couverture de ${profile.prenom}`"
            class="cover-image"
          >
          <div class="cover-overlay"></div>
        </div>
        
        <div class="profile-main-info">
          <div class="profile-avatar-section">
            <div class="avatar-container">
              <img 
                :src="profile.photos?.[0] || '/default-avatar.jpg'"
                :alt="`Photo de ${profile.prenom}`"
                class="profile-avatar"
              >
              <div v-if="profile.isOnline" class="online-indicator"></div>
              <div v-if="profile.isVerified" class="verified-badge">✓</div>
            </div>
            
            <div class="profile-basic-info">
              <h1 class="profile-name">
                {{ profile.prenom }}
                <span v-if="profile.age" class="profile-age">, {{ profile.age }}</span>
              </h1>
              
              <div class="profile-location" v-if="profile.ville">
                <span aria-hidden="true">📍</span>
                {{ profile.ville }}
                <span v-if="profile.distance" class="distance">
                  ({{ profile.distance }} km)
                </span>
              </div>
              
              <div class="profile-status">
                {{ getProfileStatus() }}
              </div>
            </div>
          </div>
          
          <div class="profile-actions" v-if="!isOwnProfile">
            <button 
              @click="likeProfile"
              :class="['btn-like', { liked: profile.isLiked }]"
            >
              <span aria-hidden="true">{{ profile.isLiked ? '❤️' : '🤍' }}</span>
              {{ profile.isLiked ? 'Aimé' : 'Aimer' }}
            </button>
            
            <button 
              @click="sendMessage"
              class="btn-message"
            >
              <span aria-hidden="true">💬</span>
              Message
            </button>
            
            <button 
              @click="superLike"
              class="btn-super-like"
            >
              <span aria-hidden="true">⭐</span>
              Super Like
            </button>
            
            <button 
              @click="blockUser"
              class="btn-block"
            >
              <span aria-hidden="true">🚫</span>
              Bloquer
            </button>
          </div>
          
          <div class="profile-actions" v-else>
            <button 
              @click="editProfile"
              class="btn-edit"
            >
              <span aria-hidden="true">✏️</span>
              Modifier le profil
            </button>
            
            <button 
              @click="viewSettings"
              class="btn-settings"
            >
              <span aria-hidden="true">⚙️</span>
              Paramètres
            </button>
          </div>
        </div>
      </header>

      <!-- Navigation des onglets -->
      <nav class="profile-tabs">
        <button 
          @click="activeTab = 'about'"
          :class="['tab-button', { active: activeTab === 'about' }]"
        >
          À propos
        </button>
        
        <button 
          @click="activeTab = 'photos'"
          :class="['tab-button', { active: activeTab === 'photos' }]"
        >
          Photos ({{ profile.photos?.length || 0 }})
        </button>
        
        <button 
          @click="activeTab = 'posts'"
          :class="['tab-button', { active: activeTab === 'posts' }]"
        >
          Posts ({{ profile.postsCount || 0 }})
        </button>
        
        <button 
          v-if="userType === 'amical'"
          @click="activeTab = 'events'"
          :class="['tab-button', { active: activeTab === 'events' }]"
        >
          Événements
        </button>
        
        <button 
          v-if="userType === 'libertin'"
          @click="activeTab = 'parties'"
          :class="['tab-button', { active: activeTab === 'parties' }]"
        >
          Soirées
        </button>
      </nav>

      <!-- Contenu des onglets -->
      <section class="tab-content">
        <!-- À propos -->
        <div v-if="activeTab === 'about'" class="about-section">
          <div class="about-grid">
            <div class="info-card">
              <h3>Informations personnelles</h3>
              <div class="info-list">
                <div v-if="profile.age" class="info-item">
                  <span class="info-label">Âge :</span>
                  <span class="info-value">{{ profile.age }} ans</span>
                </div>
                
                <div v-if="profile.ville" class="info-item">
                  <span class="info-label">Ville :</span>
                  <span class="info-value">{{ profile.ville }}</span>
                </div>
                
                <div v-if="profile.profession" class="info-item">
                  <span class="info-label">Profession :</span>
                  <span class="info-value">{{ profile.profession }}</span>
                </div>
                
                <div v-if="profile.situation" class="info-item">
                  <span class="info-label">Situation :</span>
                  <span class="info-value">{{ profile.situation }}</span>
                </div>
                
                <div class="info-item">
                  <span class="info-label">Membre depuis :</span>
                  <span class="info-value">{{ formatJoinDate(profile.createdAt) }}</span>
                </div>
              </div>
            </div>
            
            <div class="info-card" v-if="profile.description">
              <h3>Description</h3>
              <p class="description-text">{{ profile.description }}</p>
            </div>
            
            <div class="info-card" v-if="profile.hobbies">
              <h3>Centres d'intérêt</h3>
              <div class="hobbies-list">
                <span 
                  v-for="hobby in profile.hobbies.split(',')" 
                  :key="hobby.trim()"
                  class="hobby-tag"
                >
                  {{ hobby.trim() }}
                </span>
              </div>
            </div>
            
            <div class="info-card" v-if="userType === 'amoureux' && profile.preferences">
              <h3>Recherche</h3>
              <div class="preferences-list">
                <div v-if="profile.preferences.ageRange" class="preference-item">
                  <span class="pref-label">Âge :</span>
                  <span class="pref-value">{{ profile.preferences.ageRange.min }}-{{ profile.preferences.ageRange.max }} ans</span>
                </div>
                
                <div v-if="profile.preferences.distance" class="preference-item">
                  <span class="pref-label">Distance :</span>
                  <span class="pref-value">{{ profile.preferences.distance }} km</span>
                </div>
                
                <div v-if="profile.preferences.relationshipType" class="preference-item">
                  <span class="pref-label">Type de relation :</span>
                  <span class="pref-value">{{ profile.preferences.relationshipType }}</span>
                </div>
              </div>
            </div>
            
            <div class="info-card" v-if="profile.stats">
              <h3>Statistiques</h3>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-number">{{ profile.stats.profileViews || 0 }}</span>
                  <span class="stat-label">Vues du profil</span>
                </div>
                
                <div class="stat-item">
                  <span class="stat-number">{{ profile.stats.likes || 0 }}</span>
                  <span class="stat-label">Likes reçus</span>
                </div>
                
                <div class="stat-item">
                  <span class="stat-number">{{ profile.stats.matches || 0 }}</span>
                  <span class="stat-label">Matchs</span>
                </div>
                
                <div class="stat-item">
                  <span class="stat-number">{{ profile.stats.events || 0 }}</span>
                  <span class="stat-label">Événements</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Photos -->
        <div v-if="activeTab === 'photos'" class="photos-section">
          <div v-if="!profile.photos || profile.photos.length === 0" class="empty-photos">
            <p>Aucune photo disponible</p>
          </div>
          
          <div v-else class="photos-grid">
            <div 
              v-for="(photo, index) in profile.photos" 
              :key="index"
              class="photo-item"
              @click="openPhotoViewer(index)"
            >
              <img 
                :src="photo"
                :alt="`Photo ${index + 1} de ${profile.prenom}`"
                class="photo-image"
                loading="lazy"
              >
            </div>
          </div>
        </div>

        <!-- Posts -->
        <div v-if="activeTab === 'posts'" class="posts-section">
          <div v-if="!profile.posts || profile.posts.length === 0" class="empty-posts">
            <p>Aucun post publié</p>
          </div>
          
          <div v-else class="posts-list">
            <article 
              v-for="post in profile.posts" 
              :key="post.id"
              class="post-card"
            >
              <header class="post-header">
                <time class="post-date">{{ formatPostDate(post.createdAt) }}</time>
              </header>
              
              <div class="post-content">
                <p v-if="post.content" class="post-text">{{ post.content }}</p>
                
                <div v-if="post.media && post.media.length > 0" class="post-media">
                  <img 
                    v-for="(media, index) in post.media.slice(0, 3)" 
                    :key="index"
                    :src="media.url"
                    :alt="`Média ${index + 1}`"
                    class="media-image"
                    @click="openMediaViewer(post.media, index)"
                  >
                  <div v-if="post.media.length > 3" class="media-overlay">
                    +{{ post.media.length - 3 }}
                  </div>
                </div>
              </div>
              
              <footer class="post-footer">
                <div class="post-stats">
                  <span>{{ post.likesCount || 0 }} ❤️</span>
                  <span>{{ post.commentsCount || 0 }} 💬</span>
                </div>
              </footer>
            </article>
          </div>
        </div>

        <!-- Événements (pour amical) -->
        <div v-if="activeTab === 'events' && userType === 'amical'" class="events-section">
          <div v-if="!profile.events || profile.events.length === 0" class="empty-events">
            <p>Aucun événement organisé</p>
          </div>
          
          <div v-else class="events-list">
            <article 
              v-for="event in profile.events" 
              :key="event.id"
              class="event-card"
              @click="viewEvent(event.id)"
            >
              <div class="event-date">
                <span class="event-day">{{ formatEventDay(event.date) }}</span>
                <span class="event-month">{{ formatEventMonth(event.date) }}</span>
              </div>
              
              <div class="event-info">
                <h4 class="event-title">{{ event.title }}</h4>
                <p class="event-location">📍 {{ event.location }}</p>
                <p class="event-participants">{{ event.participantsCount }} participants</p>
              </div>
            </article>
          </div>
        </div>

        <!-- Soirées (pour libertin) -->
        <div v-if="activeTab === 'parties' && userType === 'libertin'" class="parties-section">
          <div v-if="!profile.parties || profile.parties.length === 0" class="empty-parties">
            <p>Aucune soirée organisée</p>
          </div>
          
          <div v-else class="parties-list">
            <article 
              v-for="party in profile.parties" 
              :key="party.id"
              class="party-card"
              @click="viewParty(party.id)"
            >
              <div class="party-image">
                <img 
                  :src="party.image || '/default-party.jpg'"
                  :alt="`Image de ${party.title}`"
                  class="party-thumbnail"
                >
              </div>
              
              <div class="party-info">
                <h4 class="party-title">{{ party.title }}</h4>
                <p class="party-date">{{ formatPartyDate(party.date) }}</p>
                <p class="party-participants">{{ party.participantsCount }}/{{ party.maxParticipants }} participants</p>
              </div>
            </article>
          </div>
        </div>
      </section>
    </div>

    <!-- Modal de visualisation de photos -->
    <div v-if="photoViewerOpen" class="modal-overlay" @click="closePhotoViewer">
      <div class="photo-viewer" @click.stop>
        <button @click="closePhotoViewer" class="btn-close-viewer">×</button>
        
        <div class="photo-navigation">
          <button 
            @click="previousPhoto"
            :disabled="currentPhotoIndex === 0"
            class="btn-nav prev"
          >
            ‹
          </button>
          
          <img 
            :src="profile.photos[currentPhotoIndex]"
            :alt="`Photo ${currentPhotoIndex + 1} de ${profile.prenom}`"
            class="viewer-image"
          >
          
          <button 
            @click="nextPhoto"
            :disabled="currentPhotoIndex === profile.photos.length - 1"
            class="btn-nav next"
          >
            ›
          </button>
        </div>
        
        <div class="photo-counter">
          {{ currentPhotoIndex + 1 }} / {{ profile.photos.length }}
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ProfileView',

  data() {
    return {
      loading: false,
      activeTab: 'about',
      photoViewerOpen: false,
      currentPhotoIndex: 0
    }
  },

  computed: {
    ...mapState(['user', 'userType']),

    profileId() {
      return this.$route.params.id;
    },

    profile() {
      // Récupérer le profil depuis le store ou les données locales
      return this.$store.state.currentProfile || null;
    },

    isOwnProfile() {
      return this.profile?.id === this.user?.id;
    }
  },

  methods: {
    ...mapActions(['loadProfile', 'likeProfile', 'superLikeProfile']),

    getProfileStatus() {
      if (this.profile.isOnline) return 'En ligne';
      if (this.profile.lastConnection) {
        return `Vu ${this.formatLastConnection(this.profile.lastConnection)}`;
      }
      return 'Hors ligne';
    },

    formatJoinDate(dateString) {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        year: 'numeric',
        month: 'long'
      });
    },

    formatLastConnection(dateString) {
      const now = new Date();
      const lastConnection = new Date(dateString);
      const diffMs = now - lastConnection;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 5) return 'à l\'instant';
      if (diffMins < 60) return `il y a ${diffMins} min`;
      if (diffHours < 24) return `il y a ${diffHours}h`;
      if (diffDays < 7) return `il y a ${diffDays}j`;
      return lastConnection.toLocaleDateString('fr-FR');
    },

    formatPostDate(dateString) {
      return new Date(dateString).toLocaleDateString('fr-FR');
    },

    formatEventDay(dateString) {
      return new Date(dateString).getDate();
    },

    formatEventMonth(dateString) {
      return new Date(dateString).toLocaleDateString('fr-FR', { month: 'short' });
    },

    formatPartyDate(dateString) {
      return new Date(dateString).toLocaleDateString('fr-FR', {
        day: 'numeric',
        month: 'short',
        year: 'numeric'
      });
    },

    async likeProfile() {
      try {
        await this.likeProfile(this.profile.id);

        this.$store.commit('addNotification', {
          type: 'success',
          message: this.profile.isLiked ? 'Like retiré' : 'Profil liké !',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors du like:', error);
      }
    },

    sendMessage() {
      this.$router.push(`/messages/${this.profile.id}`);
    },

    async superLike() {
      try {
        await this.superLikeProfile(this.profile.id);

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Super Like envoyé ! ⭐',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors du super like:', error);
      }
    },

    blockUser() {
      if (confirm(`Êtes-vous sûr de vouloir bloquer ${this.profile.prenom} ?`)) {
        // Implémenter le blocage
        console.log('Bloquer utilisateur:', this.profile.id);
      }
    },

    editProfile() {
      this.$router.push('/profile/edit');
    },

    viewSettings() {
      this.$router.push('/settings');
    },

    openPhotoViewer(index) {
      this.currentPhotoIndex = index;
      this.photoViewerOpen = true;
    },

    closePhotoViewer() {
      this.photoViewerOpen = false;
    },

    previousPhoto() {
      if (this.currentPhotoIndex > 0) {
        this.currentPhotoIndex--;
      }
    },

    nextPhoto() {
      if (this.currentPhotoIndex < this.profile.photos.length - 1) {
        this.currentPhotoIndex++;
      }
    },

    openMediaViewer(media, index) {
      // Implémenter la visionneuse de médias
      console.log('Ouvrir média:', media, index);
    },

    viewEvent(eventId) {
      this.$router.push(`/events/${eventId}`);
    },

    viewParty(partyId) {
      this.$router.push(`/parties/${partyId}`);
    }
  },

  async mounted() {
    this.loading = true;
    try {
      await this.loadProfile(this.profileId);
    } catch (error) {
      console.error('Erreur lors du chargement du profil:', error);
    } finally {
      this.loading = false;
    }
  },

  watch: {
    '$route.params.id': {
      handler(newId) {
        if (newId) {
          this.loadProfile(newId);
        }
      },
      immediate: true
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --love-pink: #FF69B4;
  --heart-red: #FF1744;
  --verified-blue: #1DA1F2;
}

.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.loading-state,
.error-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--accent-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-state h2 {
  color: var(--accent-blue);
  margin-bottom: 16px;
}

.btn-back {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--accent-blue);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-back:hover {
  background: var(--accent-purple);
}

.profile-content {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 16px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.profile-header {
  position: relative;
}

.profile-cover {
  height: 200px;
  position: relative;
  overflow: hidden;
}

.cover-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, transparent 0%, rgba(28, 15, 46, 0.8) 100%);
}

.profile-main-info {
  display: flex;
  justify-content: space-between;
  align-items: end;
  padding: 20px 24px;
  margin-top: -80px;
  position: relative;
  z-index: 2;
}

.profile-avatar-section {
  display: flex;
  align-items: end;
  gap: 20px;
}

.avatar-container {
  position: relative;
}

.profile-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--text-white);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.online-indicator {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  background: #4ade80;
  border: 3px solid var(--text-white);
  border-radius: 50%;
}

.verified-badge {
  position: absolute;
  top: 0;
  right: 0;
  width: 32px;
  height: 32px;
  background: var(--verified-blue);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
  border: 3px solid var(--text-white);
}

.profile-basic-info {
  margin-bottom: 20px;
}

.profile-name {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-white);
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.profile-age {
  font-weight: 400;
  opacity: 0.9;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--slogan-gray);
  font-size: 1rem;
  margin-bottom: 4px;
}

.distance {
  color: var(--icon-color);
  font-size: 0.9rem;
}

.profile-status {
  color: var(--accent-blue);
  font-size: 0.9rem;
  font-weight: 500;
}

.profile-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.btn-like,
.btn-message,
.btn-super-like,
.btn-block,
.btn-edit,
.btn-settings {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: 0.9rem;
}

.btn-like {
  background: var(--love-pink);
  color: var(--text-white);
}

.btn-like.liked {
  background: var(--heart-red);
}

.btn-message {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-super-like {
  background: #fbbf24;
  color: var(--text-white);
}

.btn-block {
  background: #6b7280;
  color: var(--text-white);
}

.btn-edit {
  background: var(--accent-purple);
  color: var(--text-white);
}

.btn-settings {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-like:hover,
.btn-message:hover,
.btn-super-like:hover,
.btn-block:hover,
.btn-edit:hover,
.btn-settings:hover {
  transform: translateY(-2px);
}

.profile-tabs {
  display: flex;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.tab-button {
  flex: 1;
  padding: 16px 20px;
  background: transparent;
  color: var(--slogan-gray);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  border-bottom: 3px solid transparent;
}

.tab-button.active {
  color: var(--accent-blue);
  border-bottom-color: var(--accent-blue);
  background: rgba(0, 207, 255, 0.1);
}

.tab-button:hover:not(.active) {
  color: var(--text-white);
  background: rgba(255, 255, 255, 0.05);
}

.tab-content {
  padding: 24px;
}

.about-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.about-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.info-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-card h3 {
  color: var(--accent-blue);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  color: var(--slogan-gray);
  font-weight: 500;
}

.info-value {
  color: var(--text-white);
  font-weight: 500;
}

.description-text {
  color: var(--text-white);
  line-height: 1.6;
  margin: 0;
}

.hobbies-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hobby-tag {
  padding: 6px 12px;
  background: rgba(0, 207, 255, 0.2);
  color: var(--accent-blue);
  border-radius: 16px;
  font-size: 0.9rem;
  font-weight: 500;
}

.preferences-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.preference-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.pref-label {
  color: var(--slogan-gray);
  font-weight: 500;
}

.pref-value {
  color: var(--text-white);
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.stat-number {
  display: block;
  font-size: 1.8rem;
  font-weight: 700;
  color: var(--accent-blue);
  margin-bottom: 4px;
}

.stat-label {
  color: var(--slogan-gray);
  font-size: 0.8rem;
}

.photos-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.empty-photos,
.empty-posts,
.empty-events,
.empty-parties {
  text-align: center;
  padding: 40px 20px;
  color: var(--slogan-gray);
}

.photos-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.photo-item {
  aspect-ratio: 1;
  border-radius: 8px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
}

.photo-item:hover {
  transform: scale(1.05);
}

.photo-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.posts-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.posts-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.post-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.post-header {
  margin-bottom: 12px;
}

.post-date {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.post-text {
  color: var(--text-white);
  line-height: 1.5;
  margin-bottom: 12px;
}

.post-media {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  position: relative;
}

.media-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 6px;
  cursor: pointer;
}

.media-overlay {
  position: absolute;
  top: 0;
  right: 0;
  width: 80px;
  height: 80px;
  background: rgba(0, 0, 0, 0.7);
  color: var(--text-white);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: bold;
}

.post-footer {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 12px;
}

.post-stats {
  display: flex;
  gap: 16px;
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.events-section,
.parties-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.events-list,
.parties-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.event-card,
.party-card {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.event-card:hover,
.party-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.event-date {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 50px;
  padding: 8px;
  background: var(--accent-blue);
  border-radius: 8px;
  text-align: center;
}

.event-day {
  font-size: 1.2rem;
  font-weight: 700;
  color: var(--text-white);
}

.event-month {
  font-size: 0.7rem;
  color: var(--text-white);
  text-transform: uppercase;
}

.event-info,
.party-info {
  flex: 1;
}

.event-title,
.party-title {
  color: var(--accent-blue);
  font-size: 1rem;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.event-location,
.party-date,
.event-participants,
.party-participants {
  color: var(--slogan-gray);
  font-size: 0.8rem;
  margin-bottom: 2px;
}

.party-image {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  overflow: hidden;
}

.party-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Modal de visualisation de photos */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.photo-viewer {
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-close-viewer {
  position: absolute;
  top: -50px;
  right: 0;
  background: transparent;
  border: none;
  color: var(--text-white);
  font-size: 2rem;
  cursor: pointer;
  z-index: 1001;
}

.photo-navigation {
  position: relative;
  display: flex;
  align-items: center;
  gap: 20px;
}

.btn-nav {
  background: rgba(0, 0, 0, 0.5);
  border: none;
  color: var(--text-white);
  font-size: 2rem;
  padding: 10px 15px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-nav:hover:not(:disabled) {
  background: rgba(0, 0, 0, 0.7);
}

.btn-nav:disabled {
  opacity: 0.3;
  cursor: not-allowed;
}

.viewer-image {
  max-width: 80vw;
  max-height: 80vh;
  object-fit: contain;
  border-radius: 8px;
}

.photo-counter {
  margin-top: 20px;
  color: var(--text-white);
  font-size: 1rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .about-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .profile-container {
    padding: 16px;
  }

  .profile-main-info {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }

  .profile-avatar-section {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .profile-actions {
    justify-content: center;
  }

  .profile-tabs {
    flex-wrap: wrap;
  }

  .tab-button {
    flex: none;
    min-width: 120px;
  }

  .photos-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .events-list,
  .parties-list {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
