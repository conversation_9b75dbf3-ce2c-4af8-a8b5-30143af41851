<template>
  <div class="notifications-view">
    <div class="container">
      <h1>Notifications</h1>
      
      <div class="notifications-content">
        <NotificationsList />
      </div>
    </div>
  </div>
</template>

<script>
import NotificationsList from '@/components/common/NotificationsList.vue'

export default {
  name: 'NotificationsView',
  
  components: {
    NotificationsList
  }
}
</script>

<style scoped>
.notifications-view {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

.notifications-view h1 {
  margin: 0 0 32px 0;
  color: #333;
  font-size: 32px;
  font-weight: 700;
  text-align: center;
}

.notifications-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
