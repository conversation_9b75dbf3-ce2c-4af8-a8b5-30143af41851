<!-- @format -->

<template>
  <div class="legal-page">
    <div class="legal-container">
      <div class="legal-header">
        <h1>Responsabilité</h1>
        <p class="last-updated">Dernière mise à jour : {{ lastUpdated }}</p>
      </div>

      <div class="legal-content">
        <!-- Limitation de responsabilité -->
        <section class="legal-section">
          <h2>Limitation de responsabilité de Meet Voice</h2>
          <p>L'éditeur du site décline toute responsabilité en cas de dommages directs ou indirects liés à l'utilisation du site ou à l'indisponibilité temporaire du service.</p>
          
          <div class="responsibility-grid">
            <div class="responsibility-item">
              <h3>Rencontres physiques</h3>
              <p>MeetVoice n'est pas responsable des rencontres organisées entre utilisateurs en dehors de la plateforme.</p>
            </div>
            
            <div class="responsibility-item">
              <h3>Comportement des utilisateurs</h3>
              <p>Chaque utilisateur est responsable de ses actes et propos sur et en dehors de la plateforme.</p>
            </div>
            
            <div class="responsibility-item">
              <h3>Contenu publié</h3>
              <p>Les utilisateurs sont seuls responsables du contenu qu'ils publient (photos, messages, informations).</p>
            </div>
            
            <div class="responsibility-item">
              <h3>Sécurité des données</h3>
              <p>Malgré nos mesures de sécurité, aucun système n'est infaillible à 100%.</p>
            </div>
          </div>
        </section>

        <!-- Responsabilité des utilisateurs -->
        <section class="legal-section">
          <h2>Responsabilité des utilisateurs</h2>
          <p>L'utilisateur est seul responsable de l'exactitude des informations fournies et de son comportement sur la plateforme.</p>
          
          <div class="user-responsibilities">
            <div class="responsibility-category">
              <h3>Informations personnelles</h3>
              <ul>
                <li>Exactitude des données fournies</li>
                <li>Mise à jour des informations</li>
                <li>Authenticité des photos</li>
                <li>Respect de l'âge minimum (18 ans)</li>
              </ul>
            </div>
            
            <div class="responsibility-category">
              <h3>Comportement en ligne</h3>
              <ul>
                <li>Respect des autres utilisateurs</li>
                <li>Conformité aux CGU</li>
                <li>Signalement des comportements inappropriés</li>
                <li>Non-divulgation d'informations personnelles d'autrui</li>
              </ul>
            </div>
            
            <div class="responsibility-category">
              <h3>Sécurité du compte</h3>
              <ul>
                <li>Protection des identifiants de connexion</li>
                <li>Signalement d'utilisation non autorisée</li>
                <li>Déconnexion sur les appareils partagés</li>
                <li>Mise à jour régulière du mot de passe</li>
              </ul>
            </div>
          </div>
        </section>

        <!-- Exclusions de responsabilité -->
        <section class="legal-section">
          <h2>Exclusions de responsabilité</h2>
          
          <div class="exclusion-warning">
            <h3>MeetVoice ne peut être tenu responsable de :</h3>
            <ul>
              <li>Dommages résultant d'une utilisation inappropriée du service</li>
              <li>Pertes de données dues à des problèmes techniques</li>
              <li>Interruptions temporaires du service pour maintenance</li>
              <li>Actes de piratage ou d'intrusion malveillante</li>
              <li>Virus ou logiciels malveillants</li>
              <li>Dysfonctionnements liés à l'équipement de l'utilisateur</li>
              <li>Problèmes de connexion internet</li>
            </ul>
          </div>
        </section>

        <!-- Force majeure -->
        <section class="legal-section">
          <h2>Force majeure</h2>
          <p>MeetVoice ne saurait être tenu responsable de tout retard ou inexécution consécutif à la survenance d'un cas de force majeure habituellement reconnu par la jurisprudence française.</p>
          
          <div class="force-majeure-examples">
            <h3>Événements de force majeure :</h3>
            <ul>
              <li>Catastrophes naturelles</li>
              <li>Guerres, troubles civils</li>
              <li>Grèves générales</li>
              <li>Pannes généralisées d'internet</li>
              <li>Décisions gouvernementales</li>
              <li>Cyberattaques majeures</li>
            </ul>
          </div>
        </section>

        <!-- Assurance et garanties -->
        <section class="legal-section">
          <h2>Assurance et garanties</h2>
          
          <div class="insurance-info">
            <h3>Assurance responsabilité civile professionnelle</h3>
            <p>MeetVoice dispose d'une assurance responsabilité civile professionnelle couvrant les activités liées à l'exploitation de la plateforme.</p>
            
            <h3>Garanties limitées</h3>
            <p>Le service est fourni "en l'état" sans garantie expresse ou implicite quant à :</p>
            <ul>
              <li>La disponibilité continue du service</li>
              <li>L'absence d'erreurs ou de bugs</li>
              <li>La compatibilité avec tous les appareils</li>
              <li>Les résultats obtenus par l'utilisation</li>
            </ul>
          </div>
        </section>

        <!-- Indemnisation -->
        <section class="legal-section">
          <h2>Indemnisation</h2>
          <p>L'utilisateur s'engage à indemniser MeetVoice contre toute réclamation, demande, action ou dommage résultant de :</p>
          
          <div class="indemnification-list">
            <ul>
              <li>Violation des présentes conditions d'utilisation</li>
              <li>Violation des droits d'un tiers</li>
              <li>Utilisation inappropriée du service</li>
              <li>Contenu publié par l'utilisateur</li>
              <li>Comportement frauduleux ou illégal</li>
            </ul>
          </div>
        </section>

        <!-- Contact en cas de problème -->
        <section class="legal-section">
          <h2>Contact en cas de problème</h2>
          <p>En cas de problème ou de réclamation :</p>
          
          <div class="contact-procedure">
            <div class="contact-step">
              <h3>1. Contact direct</h3>
              <p>Contactez notre support : <a href="mailto:<EMAIL>"><EMAIL></a></p>
            </div>
            
            <div class="contact-step">
              <h3>2. Médiation</h3>
              <p>En cas de litige persistant, recours possible à la médiation</p>
            </div>
            
            <div class="contact-step">
              <h3>3. Juridiction compétente</h3>
              <p>Les tribunaux français restent compétents en dernier recours</p>
            </div>
          </div>
        </section>
      </div>

      <!-- Navigation -->
      <div class="legal-navigation">
        <router-link to="/mentions-legales" class="nav-link">
          <i class="fas fa-info-circle"></i>
          Mentions légales
        </router-link>
        <router-link to="/cgu" class="nav-link">
          <i class="fas fa-file-contract"></i>
          Conditions d'utilisation
        </router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ResponsabiliteView',
  data() {
    return {
      lastUpdated: '15 janvier 2025'
    };
  },
  mounted() {
    window.scrollTo(0, 0);
  }
};
</script>

<style scoped>
.legal-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0 40px;
}

.legal-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.legal-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.legal-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.last-updated {
  color: #7f8c8d;
  font-style: italic;
  margin: 0;
}

.legal-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.legal-section {
  margin-bottom: 40px;
}

.legal-section h2 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
  font-weight: 600;
}

.legal-section h3 {
  color: #34495e;
  font-size: 1.2rem;
  margin: 20px 0 10px;
  font-weight: 600;
}

.legal-section p {
  color: #2c3e50;
  line-height: 1.6;
  margin-bottom: 15px;
}

.legal-section ul {
  color: #2c3e50;
  line-height: 1.6;
  padding-left: 20px;
}

.legal-section li {
  margin-bottom: 8px;
}

.responsibility-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.responsibility-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #dc3545;
}

.responsibility-item h3 {
  margin-top: 0;
  color: #dc3545;
}

.user-responsibilities {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.responsibility-category {
  background: #e8f5e8;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.responsibility-category h3 {
  margin-top: 0;
  color: #28a745;
}

.exclusion-warning {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 20px;
  border-left: 4px solid #f39c12;
}

.exclusion-warning h3 {
  color: #856404;
  margin-top: 0;
}

.force-majeure-examples {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #6c757d;
  margin-top: 20px;
}

.force-majeure-examples h3 {
  color: #495057;
  margin-top: 0;
}

.insurance-info {
  background: #e3f2fd;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.insurance-info h3 {
  color: #1976d2;
  margin-top: 0;
}

.indemnification-list {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.contact-procedure {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.contact-step {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  text-align: center;
}

.contact-step h3 {
  margin-top: 0;
  color: #667eea;
}

.legal-section a {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.legal-section a:hover {
  text-decoration: underline;
}

.legal-navigation {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(255, 255, 255, 0.95);
  color: #2c3e50;
  text-decoration: none;
  padding: 15px 25px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.nav-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  background: #ffffff;
}

.nav-link i {
  color: #667eea;
}

/* Responsive */
@media (max-width: 768px) {
  .legal-page {
    padding: 60px 0 20px;
  }
  
  .legal-container {
    padding: 0 15px;
  }
  
  .legal-header,
  .legal-content {
    padding: 25px;
  }
  
  .legal-header h1 {
    font-size: 2rem;
  }
  
  .responsibility-grid,
  .user-responsibilities,
  .contact-procedure {
    grid-template-columns: 1fr;
  }
  
  .legal-navigation {
    flex-direction: column;
    align-items: center;
  }
  
  .nav-link {
    width: 100%;
    max-width: 300px;
    justify-content: center;
  }
}
</style>
