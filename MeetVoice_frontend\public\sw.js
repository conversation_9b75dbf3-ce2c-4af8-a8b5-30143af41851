/**
 * Service Worker pour MeetVoice
 * Met en cache les ressources pour un chargement ultra-rapide
 */

const CACHE_NAME = 'meetvoice-v1.0.0';
const STATIC_CACHE = 'meetvoice-static-v1';
const DYNAMIC_CACHE = 'meetvoice-dynamic-v1';

// Ressources à mettre en cache immédiatement
const STATIC_ASSETS = [
  '/',
  '/actualite',
  '/static/css/app.css',
  '/static/js/app.js',
  '/static/js/chunk-vendors.js',
  '/favicon.ico',
  '/manifest.json'
];

// URLs de l'API à mettre en cache
const API_CACHE_PATTERNS = [
  /\/actualite\/api\/articles/,
  /\/api\/static\//
];

// Installation du Service Worker
self.addEventListener('install', event => {
  console.log('🔧 Service Worker: Installation...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then(cache => {
        console.log('📦 Service Worker: Mise en cache des ressources statiques');
        return cache.addAll(STATIC_ASSETS);
      })
      .then(() => {
        console.log('✅ Service Worker: Installation terminée');
        return self.skipWaiting();
      })
      .catch(err => {
        console.error('❌ Service Worker: Erreur d\'installation', err);
      })
  );
});

// Activation du Service Worker
self.addEventListener('activate', event => {
  console.log('🚀 Service Worker: Activation...');
  
  event.waitUntil(
    caches.keys()
      .then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => {
            // Supprimer les anciens caches
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('🗑️ Service Worker: Suppression ancien cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('✅ Service Worker: Activation terminée');
        return self.clients.claim();
      })
  );
});

// Interception des requêtes
self.addEventListener('fetch', event => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Ignorer les requêtes non-HTTP
  if (!request.url.startsWith('http')) return;
  
  // Stratégie Cache First pour les ressources statiques
  if (isStaticAsset(request)) {
    event.respondWith(cacheFirst(request));
    return;
  }
  
  // Stratégie Network First pour l'API
  if (isAPIRequest(request)) {
    event.respondWith(networkFirst(request));
    return;
  }
  
  // Stratégie Stale While Revalidate pour les pages
  if (isPageRequest(request)) {
    event.respondWith(staleWhileRevalidate(request));
    return;
  }
  
  // Par défaut: Network First
  event.respondWith(networkFirst(request));
});

// Vérifier si c'est une ressource statique
function isStaticAsset(request) {
  return request.url.includes('/static/') || 
         request.url.includes('.css') || 
         request.url.includes('.js') ||
         request.url.includes('.png') ||
         request.url.includes('.jpg') ||
         request.url.includes('.ico');
}

// Vérifier si c'est une requête API
function isAPIRequest(request) {
  return API_CACHE_PATTERNS.some(pattern => pattern.test(request.url));
}

// Vérifier si c'est une page
function isPageRequest(request) {
  return request.method === 'GET' && 
         request.headers.get('accept').includes('text/html');
}

// Stratégie Cache First (ressources statiques)
async function cacheFirst(request) {
  try {
    const cachedResponse = await caches.match(request);
    if (cachedResponse) {
      return cachedResponse;
    }
    
    const networkResponse = await fetch(request);
    if (networkResponse.ok) {
      const cache = await caches.open(STATIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.error('Cache First error:', error);
    return new Response('Offline', { status: 503 });
  }
}

// Stratégie Network First (API)
async function networkFirst(request) {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok && request.method === 'GET') {
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
  } catch (error) {
    console.log('Network failed, trying cache:', request.url);
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    return new Response(JSON.stringify({
      error: 'Offline',
      message: 'Contenu non disponible hors ligne'
    }), {
      status: 503,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

// Stratégie Stale While Revalidate (pages)
async function staleWhileRevalidate(request) {
  const cache = await caches.open(DYNAMIC_CACHE);
  const cachedResponse = await cache.match(request);
  
  const fetchPromise = fetch(request).then(networkResponse => {
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone());
    }
    return networkResponse;
  });
  
  return cachedResponse || fetchPromise;
}

// Nettoyage périodique du cache
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'CLEAN_CACHE') {
    cleanOldCache();
  }
});

async function cleanOldCache() {
  const cache = await caches.open(DYNAMIC_CACHE);
  const requests = await cache.keys();

  // Garder seulement les 50 dernières entrées
  if (requests.length > 50) {
    const toDelete = requests.slice(0, requests.length - 50);
    await Promise.all(toDelete.map(request => cache.delete(request)));
    console.log(`🧹 Service Worker: ${toDelete.length} entrées supprimées du cache`);
  }
}

// ===== NOTIFICATIONS PUSH =====

// Gestion des notifications push
self.addEventListener('push', event => {
  console.log('📱 Service Worker: Notification push reçue');

  let data = {};

  if (event.data) {
    try {
      data = event.data.json();
    } catch (error) {
      console.error('Service Worker: Erreur lors du parsing des données push:', error);
      data = { title: 'MeetVoice', body: event.data.text() };
    }
  }

  const title = data.title || 'MeetVoice';
  const options = {
    body: data.body || 'Vous avez une nouvelle notification',
    icon: data.icon || '/icons/icon-192x192.png',
    badge: '/icons/badge-72x72.png',
    image: data.image,
    vibrate: data.vibrate || [200, 100, 200],
    tag: data.tag || 'meetvoice-notification',
    data: data.data || {},
    actions: data.actions || [],
    requireInteraction: data.requireInteraction || false,
    silent: data.silent || false,
    timestamp: Date.now()
  };

  event.waitUntil(
    self.registration.showNotification(title, options)
  );
});

// Gestion des clics sur les notifications
self.addEventListener('notificationclick', event => {
  console.log('👆 Service Worker: Clic sur notification');

  event.notification.close();

  const data = event.notification.data;
  const action = event.action;

  let url = '/';

  // Déterminer l'URL à ouvrir selon le type de notification et l'action
  if (data && data.type) {
    switch (data.type) {
      case 'message':
        if (action === 'reply') {
          url = `/messages/${data.conversationId}`;
        } else if (action === 'view' || !action) {
          url = `/messages/${data.conversationId}`;
        }
        break;

      case 'match':
        if (action === 'message') {
          url = `/messages/${data.matchId}`;
        } else if (action === 'view' || !action) {
          url = `/profile/${data.userId}`;
        }
        break;

      case 'like':
        url = `/profile/${data.userId}`;
        break;

      case 'event':
        url = `/events/${data.eventId}`;
        break;

      case 'party':
        url = `/parties/${data.partyId}`;
        break;

      default:
        url = '/';
    }
  }

  // Ouvrir ou focuser la fenêtre de l'application
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true })
      .then(clientList => {
        // Chercher une fenêtre existante avec l'URL cible
        for (let client of clientList) {
          if (client.url.includes(url.split('?')[0]) && 'focus' in client) {
            return client.focus();
          }
        }

        // Si aucune fenêtre n'est trouvée, en ouvrir une nouvelle
        if (clients.openWindow) {
          return clients.openWindow(url);
        }
      })
      .catch(error => {
        console.error('Service Worker: Erreur lors de l\'ouverture de la fenêtre:', error);
      })
  );
});

// Gestion de la fermeture des notifications
self.addEventListener('notificationclose', event => {
  console.log('❌ Service Worker: Notification fermée');

  // Optionnel: envoyer des analytics sur la fermeture de notification
  const data = event.notification.data;
  if (data && data.trackingId) {
    // Envoyer les données de tracking
    fetch('/api/notifications/track/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        trackingId: data.trackingId,
        action: 'close',
        timestamp: Date.now()
      })
    }).catch(error => {
      console.error('Service Worker: Erreur lors du tracking:', error);
    });
  }
});

// Gestion de la synchronisation en arrière-plan
self.addEventListener('sync', event => {
  console.log('🔄 Service Worker: Synchronisation en arrière-plan');

  if (event.tag === 'background-sync') {
    event.waitUntil(
      syncPendingData()
    );
  }
});

// Fonction pour synchroniser les données en attente
async function syncPendingData() {
  try {
    // Récupérer les données en attente depuis IndexedDB ou localStorage
    const pendingData = await getPendingData();

    if (pendingData && pendingData.length > 0) {
      for (let data of pendingData) {
        try {
          await fetch(data.url, {
            method: data.method,
            headers: data.headers,
            body: data.body
          });

          // Supprimer les données synchronisées
          await removePendingData(data.id);
        } catch (error) {
          console.error('Service Worker: Erreur lors de la synchronisation:', error);
        }
      }
    }
  } catch (error) {
    console.error('Service Worker: Erreur lors de la synchronisation des données:', error);
  }
}

// Fonctions helper pour gérer les données en attente
async function getPendingData() {
  // Implémentation simplifiée avec localStorage
  // Dans une vraie application, utiliser IndexedDB
  try {
    const data = localStorage.getItem('pendingSync');
    return data ? JSON.parse(data) : [];
  } catch (error) {
    console.error('Service Worker: Erreur lors de la récupération des données en attente:', error);
    return [];
  }
}

async function removePendingData(id) {
  try {
    const data = await getPendingData();
    const filteredData = data.filter(item => item.id !== id);
    localStorage.setItem('pendingSync', JSON.stringify(filteredData));
  } catch (error) {
    console.error('Service Worker: Erreur lors de la suppression des données en attente:', error);
  }
}
