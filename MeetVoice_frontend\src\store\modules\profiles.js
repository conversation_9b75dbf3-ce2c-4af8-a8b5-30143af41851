import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000/api'

export default {
  namespaced: true,
  
  state: {
    profiles: [],
    currentProfile: null,
    matches: [],
    conversations: [],
    loading: false,
    error: null,
    filters: {
      ageRange: { min: 18, max: 65 },
      distance: 50,
      interests: [],
      relationshipType: 'all'
    },
    viewMode: 'cards' // cards, swipe, list
  },
  
  mutations: {
    setProfiles(state, profiles) {
      state.profiles = profiles
    },
    
    setCurrentProfile(state, profile) {
      state.currentProfile = profile
    },
    
    addProfile(state, profile) {
      state.profiles.push(profile)
    },
    
    updateProfile(state, updatedProfile) {
      const index = state.profiles.findIndex(p => p.id === updatedProfile.id)
      if (index !== -1) {
        state.profiles.splice(index, 1, updatedProfile)
      }
    },
    
    removeProfile(state, profileId) {
      state.profiles = state.profiles.filter(p => p.id !== profileId)
    },
    
    setMatches(state, matches) {
      state.matches = matches
    },
    
    addMatch(state, match) {
      state.matches.unshift(match)
    },
    
    removeMatch(state, matchId) {
      state.matches = state.matches.filter(m => m.id !== matchId)
    },
    
    setConversations(state, conversations) {
      state.conversations = conversations
    },
    
    addConversation(state, conversation) {
      state.conversations.unshift(conversation)
    },
    
    updateConversation(state, updatedConversation) {
      const index = state.conversations.findIndex(c => c.id === updatedConversation.id)
      if (index !== -1) {
        state.conversations.splice(index, 1, updatedConversation)
      }
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    },
    
    setFilters(state, filters) {
      state.filters = { ...state.filters, ...filters }
    },
    
    setViewMode(state, mode) {
      state.viewMode = mode
    }
  },
  
  actions: {
    async loadProfiles({ commit, state }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const params = new URLSearchParams()
        params.append('age_min', state.filters.ageRange.min)
        params.append('age_max', state.filters.ageRange.max)
        params.append('distance', state.filters.distance)
        
        if (state.filters.interests.length > 0) {
          params.append('interests', state.filters.interests.join(','))
        }
        
        if (state.filters.relationshipType !== 'all') {
          params.append('relationship_type', state.filters.relationshipType)
        }
        
        const response = await axios.get(`${API_BASE_URL}/profiles/?${params}`)
        commit('setProfiles', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des profils')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async loadProfile({ commit }, profileId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/profiles/${profileId}/`)
        commit('setCurrentProfile', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement du profil')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async likeProfile({ commit }, profileId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/profiles/${profileId}/like/`)
        
        // Mettre à jour le profil dans la liste
        commit('updateProfile', response.data.profile)
        
        // Si c'est un match, l'ajouter aux matchs
        if (response.data.isMatch) {
          commit('addMatch', response.data.match)
        }
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du like')
        throw error
      }
    },
    
    async superLikeProfile({ commit }, profileId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/profiles/${profileId}/super-like/`)
        
        commit('updateProfile', response.data.profile)
        
        if (response.data.isMatch) {
          commit('addMatch', response.data.match)
        }
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du super like')
        throw error
      }
    },
    
    async passProfile({ commit }, profileId) {
      try {
        await axios.post(`${API_BASE_URL}/profiles/${profileId}/pass/`)
        commit('removeProfile', profileId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors du passage')
        throw error
      }
    },
    
    async loadMatches({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/matches/`)
        commit('setMatches', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des matchs')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async unmatch({ commit }, matchId) {
      try {
        await axios.delete(`${API_BASE_URL}/matches/${matchId}/`)
        commit('removeMatch', matchId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de la suppression du match')
        throw error
      }
    },
    
    async loadConversations({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/conversations/`)
        commit('setConversations', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des conversations')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async sendMessage({ commit }, { conversationId, content, isVoiceMessage = false }) {
      try {
        const response = await axios.post(`${API_BASE_URL}/conversations/${conversationId}/messages/`, {
          content,
          is_voice_message: isVoiceMessage
        })
        
        // Mettre à jour la conversation
        commit('updateConversation', response.data.conversation)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'envoi du message')
        throw error
      }
    },
    
    async markAsRead({ commit }, conversationId) {
      try {
        const response = await axios.post(`${API_BASE_URL}/conversations/${conversationId}/mark-read/`)
        commit('updateConversation', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du marquage comme lu')
        throw error
      }
    },
    
    updateFilters({ commit }, filters) {
      commit('setFilters', filters)
    },
    
    setViewMode({ commit }, mode) {
      commit('setViewMode', mode)
    },
    
    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    profiles: state => state.profiles,
    currentProfile: state => state.currentProfile,
    matches: state => state.matches,
    conversations: state => state.conversations,
    loading: state => state.loading,
    error: state => state.error,
    filters: state => state.filters,
    viewMode: state => state.viewMode,
    
    unreadConversations: state => {
      return state.conversations.filter(c => c.unreadCount > 0)
    },
    
    totalUnreadMessages: state => {
      return state.conversations.reduce((total, c) => total + (c.unreadCount || 0), 0)
    },
    
    recentMatches: state => {
      return state.matches.slice(0, 10)
    }
  }
}
