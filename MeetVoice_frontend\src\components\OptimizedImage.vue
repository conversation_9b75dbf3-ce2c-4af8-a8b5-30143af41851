<!-- @format -->

<template>
  <picture class="optimized-image">
    <!-- Source WebP pour les navigateurs compatibles -->
    <source 
      :srcset="webpSrc" 
      type="image/webp"
      v-if="webpSrc"
    />
    
    <!-- Fallback pour les navigateurs non compatibles -->
    <img
      :src="fallbackSrc"
      :alt="alt"
      :width="width"
      :height="height"
      :loading="loading"
      :class="imageClass"
      :style="imageStyle"
      @load="onLoad"
      @error="onError"
      ref="image"
    />
  </picture>
</template>

<script>
export default {
  name: 'OptimizedImage',
  props: {
    // Source de l'image (sans extension)
    src: {
      type: String,
      required: true
    },
    
    // Texte alternatif
    alt: {
      type: String,
      required: true
    },
    
    // Largeur de l'image
    width: {
      type: [String, Number],
      default: null
    },
    
    // Hauteur de l'image
    height: {
      type: [String, Number],
      default: null
    },
    
    // Type de chargement (lazy, eager)
    loading: {
      type: String,
      default: 'lazy',
      validator: value => ['lazy', 'eager'].includes(value)
    },
    
    // Classes CSS personnalisées
    imageClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Styles CSS personnalisés
    imageStyle: {
      type: [String, Object],
      default: null
    },
    
    // Qualité de l'image (pour les optimisations futures)
    quality: {
      type: Number,
      default: 85,
      validator: value => value >= 1 && value <= 100
    },
    
    // Forcer l'utilisation du fallback
    forceFallback: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    // Source WebP optimisée
    webpSrc() {
      if (this.forceFallback) return null;
      
      // Si l'image a déjà l'extension .webp
      if (this.src.endsWith('.webp')) {
        return this.src;
      }
      
      // Remplacer l'extension par .webp
      const basePath = this.src.replace(/\.[^/.]+$/, '');
      return `${basePath}.webp`;
    },
    
    // Source de fallback (image originale)
    fallbackSrc() {
      return this.src;
    }
  },
  
  methods: {
    onLoad(event) {
      this.$emit('load', event);
      
      // Analytics pour mesurer l'adoption du WebP
      if (this.webpSrc && event.target.currentSrc?.includes('.webp')) {
        this.trackWebPUsage('webp_loaded');
      } else {
        this.trackWebPUsage('fallback_loaded');
      }
    },
    
    onError(event) {
      this.$emit('error', event);
      console.warn(`Erreur de chargement d'image: ${event.target.src}`);
    },
    
    // Suivi de l'utilisation du WebP (optionnel)
    trackWebPUsage(event) {
      if (typeof gtag !== 'undefined') {
        gtag('event', event, {
          event_category: 'image_optimization',
          event_label: this.src
        });
      }
    },
    
    // Méthode pour précharger l'image
    preload() {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      
      // Précharger la version WebP si supportée
      if (this.supportsWebP() && this.webpSrc) {
        link.href = this.webpSrc;
        link.type = 'image/webp';
      } else {
        link.href = this.fallbackSrc;
      }
      
      document.head.appendChild(link);
    },
    
    // Détecter le support WebP
    supportsWebP() {
      if (typeof window === 'undefined') return false;
      
      // Cache du résultat
      if (this.$root._webpSupport !== undefined) {
        return this.$root._webpSupport;
      }
      
      // Test de support WebP
      const canvas = document.createElement('canvas');
      canvas.width = 1;
      canvas.height = 1;
      
      const support = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      this.$root._webpSupport = support;
      
      return support;
    }
  },
  
  mounted() {
    // Précharger si c'est une image critique
    if (this.loading === 'eager') {
      this.preload();
    }
  }
};
</script>

<style scoped>
.optimized-image {
  display: inline-block;
  max-width: 100%;
}

.optimized-image img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Animation de chargement optionnelle */
.optimized-image img {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.optimized-image img[src] {
  opacity: 1;
}

/* Placeholder pendant le chargement */
.optimized-image::before {
  content: '';
  display: block;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive */
@media (max-width: 768px) {
  .optimized-image img {
    width: 100%;
    height: auto;
  }
}
</style>
