const fetch = require('node-fetch');

/**
 * Script de test pour vérifier le système SEO
 */

const API_BASE_URL = 'http://127.0.0.1:8000';
const FRONTEND_URL = 'http://localhost:8081';

async function testAPI() {
  console.log('🔍 Test de l\'API...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/actualite/api/articles/published/`);
    const data = await response.json();
    const articles = data.results || data;
    
    console.log(`✅ API accessible - ${articles.length} articles trouvés`);
    
    // Vérifier que les articles ont des slugs
    const articlesWithSlugs = articles.filter(article => article.slug);
    console.log(`✅ Articles avec slugs: ${articlesWithSlugs.length}/${articles.length}`);
    
    // Afficher quelques exemples
    console.log('\n📋 Exemples d\'articles:');
    articles.slice(0, 3).forEach(article => {
      console.log(`- ID: ${article.id}`);
      console.log(`  Titre: ${article.titre}`);
      console.log(`  Slug: ${article.slug || 'AUCUN'}`);
      console.log(`  URL: /article/${article.slug || 'no-slug'}`);
      console.log('');
    });
    
    return articles;
  } catch (error) {
    console.error('❌ Erreur API:', error.message);
    return [];
  }
}

async function testSitemap() {
  console.log('🗺️  Test du sitemap...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    const sitemapPath = path.join(__dirname, '../public/sitemap.xml');
    
    if (fs.existsSync(sitemapPath)) {
      const content = fs.readFileSync(sitemapPath, 'utf8');
      const urlCount = (content.match(/<loc>/g) || []).length;
      console.log(`✅ Sitemap trouvé avec ${urlCount} URLs`);
      
      // Vérifier que les URLs contiennent des slugs
      const hasSlugUrls = content.includes('/article/') && content.includes('/article/') && !content.includes('/article/1');
      console.log(`✅ URLs avec slugs: ${hasSlugUrls ? 'OUI' : 'NON'}`);
    } else {
      console.log('❌ Sitemap non trouvé');
    }
  } catch (error) {
    console.error('❌ Erreur sitemap:', error.message);
  }
}

async function testWebpackConfig() {
  console.log('⚙️  Test de la configuration webpack...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    const configPath = path.join(__dirname, '../vue.config.js');
    
    if (fs.existsSync(configPath)) {
      const content = fs.readFileSync(configPath, 'utf8');
      const hasRoutes = content.includes('routes:');
      const hasSlugRoutes = content.includes('/article/') && content.includes('/7');
      
      console.log(`✅ Configuration webpack trouvée`);
      console.log(`✅ Routes configurées: ${hasRoutes ? 'OUI' : 'NON'}`);
      console.log(`✅ Routes avec slugs: ${hasSlugRoutes ? 'OUI' : 'NON'}`);
    } else {
      console.log('❌ Configuration webpack non trouvée');
    }
  } catch (error) {
    console.error('❌ Erreur configuration:', error.message);
  }
}

async function testFrontendUrls(articles) {
  console.log('🌐 Test des URLs frontend...');
  
  if (articles.length === 0) {
    console.log('⚠️  Aucun article à tester');
    return;
  }
  
  const testArticle = articles[0];
  const testUrl = `${FRONTEND_URL}/article/${testArticle.slug || 'no-slug'}`;
  
  console.log(`🔗 Test de l'URL: ${testUrl}`);
  console.log('💡 Ouvrez cette URL dans votre navigateur pour tester');
}

async function main() {
  console.log('🚀 Test du système SEO MeetVoice\n');
  
  const articles = await testAPI();
  console.log('');
  
  await testSitemap();
  console.log('');
  
  await testWebpackConfig();
  console.log('');
  
  await testFrontendUrls(articles);
  
  console.log('\n✅ Tests terminés !');
  console.log('\n📋 Prochaines étapes:');
  console.log('1. Tester les URLs dans le navigateur');
  console.log('2. Vérifier les meta tags avec les outils de développement');
  console.log('3. Tester le partage social');
  console.log('4. Valider les données structurées avec Google');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { testAPI, testSitemap, testWebpackConfig };
