# Configuration de l'API
VUE_APP_API_URL=http://127.0.0.1:8000
VUE_APP_WS_URL=ws://127.0.0.1:8000

# Configuration WebSocket MeetVoice (Rust)
VUE_APP_MEETVOICE_WS_URL=ws://localhost:8080/ws

# Configuration Stripe
VUE_APP_STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key_here
VUE_APP_STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here

# Configuration PayPal
VUE_APP_PAYPAL_CLIENT_ID=your_paypal_client_id_here

# Configuration VAPID pour les notifications push
VUE_APP_VAPID_PUBLIC_KEY=your_vapid_public_key_here

# Configuration de l'environnement
NODE_ENV=development
VUE_APP_ENV=development

# Configuration de debug
VUE_APP_DEBUG=true
VUE_APP_LOG_LEVEL=debug
