use anyhow::Result;
use axum::{
    extract::{
        ws::{Message, WebSocket, WebSocketUpgrade},
        State,
    },
    response::Response,
};
use dashmap::DashMap;
use futures::{sink::SinkExt, stream::StreamExt};
use tokio_stream;
use serde_json;
use std::sync::Arc;
use tokio::sync::mpsc;
use tracing::{info, warn, error, debug};
use uuid::Uuid;

use crate::models::*;
use crate::AppState;

pub mod handlers;
pub mod connection;

use connection::WebSocketConnection;
use handlers::MessageH<PERSON><PERSON>;

#[derive(Clone)]
pub struct WebSocketManager {
    // Connexions actives: user_id -> WebSocketConnection
    connections: Arc<DashMap<Uuid, WebSocketConnection>>,
    // Salles de chat: room_id -> Vec<user_id>
    rooms: Arc<DashMap<String, Vec<Uuid>>>,
    // Sessions vocales: session_id -> Vec<user_id>
    voice_sessions: Arc<DashMap<String, Vec<Uuid>>>,
}

impl WebSocketManager {
    pub fn new() -> Self {
        Self {
            connections: Arc::new(DashMap::new()),
            rooms: Arc::new(DashMap::new()),
            voice_sessions: Arc::new(DashMap::new()),
        }
    }
    
    pub async fn add_connection(&self, user_id: Uuid, connection: WebSocketConnection) {
        info!("👤 Nouvelle connexion WebSocket pour l'utilisateur: {}", user_id);
        self.connections.insert(user_id, connection);
    }
    
    pub async fn remove_connection(&self, user_id: Uuid) {
        info!("👋 Déconnexion WebSocket pour l'utilisateur: {}", user_id);
        self.connections.remove(&user_id);
        
        // Nettoyer les salles
        self.rooms.iter_mut().for_each(|mut room| {
            room.value_mut().retain(|&id| id != user_id);
        });
        
        // Nettoyer les sessions vocales
        self.voice_sessions.iter_mut().for_each(|mut session| {
            session.value_mut().retain(|&id| id != user_id);
        });
    }
    
    pub async fn send_to_user(&self, user_id: Uuid, message: WebSocketMessage) -> Result<()> {
        if let Some(connection) = self.connections.get(&user_id) {
            connection.send_message(message).await?;
        }
        Ok(())
    }
    
    pub async fn broadcast_to_room(&self, room_id: &str, message: WebSocketMessage, exclude_user: Option<Uuid>) -> Result<()> {
        if let Some(users) = self.rooms.get(room_id) {
            for &user_id in users.value() {
                if let Some(exclude) = exclude_user {
                    if user_id == exclude {
                        continue;
                    }
                }
                
                if let Err(e) = self.send_to_user(user_id, message.clone()).await {
                    warn!("Erreur lors de l'envoi du message à l'utilisateur {}: {}", user_id, e);
                }
            }
        }
        Ok(())
    }
    
    pub async fn broadcast_to_voice_session(&self, session_id: &str, message: WebSocketMessage, exclude_user: Option<Uuid>) -> Result<()> {
        if let Some(users) = self.voice_sessions.get(session_id) {
            for &user_id in users.value() {
                if let Some(exclude) = exclude_user {
                    if user_id == exclude {
                        continue;
                    }
                }
                
                if let Err(e) = self.send_to_user(user_id, message.clone()).await {
                    warn!("Erreur lors de l'envoi du message vocal à l'utilisateur {}: {}", user_id, e);
                }
            }
        }
        Ok(())
    }
    
    pub async fn join_room(&self, user_id: Uuid, room_id: String) {
        self.rooms
            .entry(room_id.clone())
            .or_insert_with(Vec::new)
            .push(user_id);
            
        info!("👤 Utilisateur {} a rejoint la salle {}", user_id, room_id);
    }
    
    pub async fn leave_room(&self, user_id: Uuid, room_id: &str) {
        if let Some(mut users) = self.rooms.get_mut(room_id) {
            users.retain(|&id| id != user_id);
            info!("👤 Utilisateur {} a quitté la salle {}", user_id, room_id);
        }
    }
    
    pub async fn join_voice_session(&self, user_id: Uuid, session_id: String) {
        self.voice_sessions
            .entry(session_id.clone())
            .or_insert_with(Vec::new)
            .push(user_id);
            
        info!("🎤 Utilisateur {} a rejoint la session vocale {}", user_id, session_id);
    }
    
    pub async fn leave_voice_session(&self, user_id: Uuid, session_id: &str) {
        if let Some(mut users) = self.voice_sessions.get_mut(session_id) {
            users.retain(|&id| id != user_id);
            info!("🎤 Utilisateur {} a quitté la session vocale {}", user_id, session_id);
        }
    }
    
    pub fn get_connected_users(&self) -> Vec<Uuid> {
        self.connections.iter().map(|entry| *entry.key()).collect()
    }
    
    pub fn get_room_users(&self, room_id: &str) -> Vec<Uuid> {
        self.rooms
            .get(room_id)
            .map(|users| users.clone())
            .unwrap_or_default()
    }
    
    pub fn get_voice_session_users(&self, session_id: &str) -> Vec<Uuid> {
        self.voice_sessions
            .get(session_id)
            .map(|users| users.clone())
            .unwrap_or_default()
    }
    
    pub fn get_stats(&self) -> WebSocketStats {
        WebSocketStats {
            connected_users: self.connections.len(),
            active_rooms: self.rooms.len(),
            active_voice_sessions: self.voice_sessions.len(),
        }
    }
}

#[derive(Debug, serde::Serialize)]
pub struct WebSocketStats {
    pub connected_users: usize,
    pub active_rooms: usize,
    pub active_voice_sessions: usize,
}

// Handler principal pour les connexions WebSocket
pub async fn websocket_handler(
    ws: WebSocketUpgrade,
    State(state): State<AppState>,
) -> Response {
    ws.on_upgrade(move |socket| handle_websocket(socket, state))
}

async fn handle_websocket(socket: WebSocket, state: AppState) {
    let (sender, mut receiver) = socket.split();
    let (tx, rx) = mpsc::unbounded_channel();
    
    // Créer la connexion WebSocket
    let connection = WebSocketConnection::new(tx);
    let mut message_handler = MessageHandler::new(state.clone());
    
    // Task pour envoyer les messages
    let send_task = tokio::spawn(async move {
        let mut rx = tokio_stream::wrappers::UnboundedReceiverStream::new(rx);
        let mut sender = sender;
        
        while let Some(msg) = rx.next().await {
            if let Ok(text) = serde_json::to_string(&msg) {
                if sender.send(Message::Text(text)).await.is_err() {
                    break;
                }
            }
        }
    });
    
    // Variable pour stocker l'ID utilisateur une fois authentifié
    let mut authenticated_user_id: Option<Uuid> = None;
    
    // Boucle principale pour recevoir les messages
    while let Some(msg) = receiver.next().await {
        match msg {
            Ok(Message::Text(text)) => {
                match serde_json::from_str::<WebSocketMessage>(&text) {
                    Ok(ws_message) => {
                        match message_handler.handle_message(ws_message, &connection, authenticated_user_id).await {
                            Ok(user_id) => {
                                // Si l'utilisateur vient de s'authentifier
                                if authenticated_user_id.is_none() && user_id.is_some() {
                                    authenticated_user_id = user_id;
                                    if let Some(user_id) = user_id {
                                        state.websocket.add_connection(user_id, connection.clone()).await;
                                    }
                                }
                            }
                            Err(e) => {
                                error!("Erreur lors du traitement du message: {}", e);
                                let error_msg = WebSocketMessage::Error {
                                    message: format!("Erreur: {}", e),
                                };
                                let _ = connection.send_message(error_msg).await;
                            }
                        }
                    }
                    Err(e) => {
                        warn!("Message WebSocket invalide: {}", e);
                        let error_msg = WebSocketMessage::Error {
                            message: "Format de message invalide".to_string(),
                        };
                        let _ = connection.send_message(error_msg).await;
                    }
                }
            }
            Ok(Message::Ping(data)) => {
                debug!("Ping reçu");
                let _ = connection.send_raw_message(Message::Pong(data)).await;
            }
            Ok(Message::Pong(_)) => {
                debug!("Pong reçu");
            }
            Ok(Message::Close(_)) => {
                info!("Connexion WebSocket fermée");
                break;
            }
            Err(e) => {
                error!("Erreur WebSocket: {}", e);
                break;
            }
            _ => {}
        }
    }
    
    // Nettoyage lors de la déconnexion
    if let Some(user_id) = authenticated_user_id {
        state.websocket.remove_connection(user_id).await;
    }
    
    send_task.abort();
}
