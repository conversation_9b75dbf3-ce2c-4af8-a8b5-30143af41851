# Dockerfile multi-stage pour optimiser la taille de l'image

# Stage 1: Build
FROM rust:1.75 as builder

WORKDIR /app

# Copier les fichiers de configuration Cargo
COPY Cargo.toml Cargo.lock ./

# Créer un projet dummy pour cache les dépendances
RUN mkdir src && echo "fn main() {}" > src/main.rs
RUN cargo build --release
RUN rm -rf src

# Copier le code source réel
COPY src ./src

# Rebuild avec le vrai code
RUN touch src/main.rs
RUN cargo build --release

# Stage 2: Runtime
FROM debian:bookworm-slim

# Installer les dépendances runtime
RUN apt-get update && apt-get install -y \
    ca-certificates \
    libssl3 \
    && rm -rf /var/lib/apt/lists/*

# Créer un utilisateur non-root
RUN useradd -r -s /bin/false meetvoice

WORKDIR /app

# Copier le binaire depuis le stage de build
COPY --from=builder /app/target/release/meetvoice-websocket /app/

# Changer le propriétaire
RUN chown meetvoice:meetvoice /app/meetvoice-websocket

# Utiliser l'utilisateur non-root
USER meetvoice

# Exposer les ports
EXPOSE 8080 9090

# Commande par défaut
CMD ["./meetvoice-websocket"]
