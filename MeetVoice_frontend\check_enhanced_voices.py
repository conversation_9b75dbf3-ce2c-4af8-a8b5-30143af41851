#!/usr/bin/env python3
"""
Vérification du répertoire api/enhanced_voice_tests/
"""

import requests
import j<PERSON>

def check_enhanced_voice_tests():
    """Vérifier le contenu du répertoire enhanced_voice_tests"""
    print("🔍 Vérification du répertoire api/enhanced_voice_tests/")
    print("=" * 60)

    # Essayer de lister le contenu via une requête API si possible
    try:
        # Test si il y a un endpoint pour lister les fichiers
        response = requests.get('http://127.0.0.1:8000/api/enhanced_voice_tests/')
        if response.status_code == 200:
            print("✅ Endpoint API trouvé")
            print(response.text[:500])
        else:
            print(f"❌ Endpoint API non trouvé (status: {response.status_code})")
    except Exception as e:
        print(f"❌ Pas d'endpoint API: {e}")

    print("\n🔍 Test d'accès direct aux fichiers...")

    # Essayer d'accéder à quelques fichiers de test typiques
    test_urls = [
        'http://127.0.0.1:8000/media/enhanced_voice_tests/',
        'http://127.0.0.1:8000/static/enhanced_voice_tests/',
        'http://127.0.0.1:8000/enhanced_voice_tests/',
        'http://127.0.0.1:8000/api/enhanced_voice_tests/list/',
        'http://127.0.0.1:8000/tts/enhanced_voice_tests/',
        'http://127.0.0.1:8000/tts/public/enhanced_voice_tests/'
    ]

    for url in test_urls:
        try:
            response = requests.get(url)
            print(f"{url}: {response.status_code}")
            if response.status_code == 200:
                content = response.text[:200]
                print(f"  Contenu: {content}...")
        except Exception as e:
            print(f"{url}: Erreur - {e}")

def check_current_voices():
    """Vérifier les voix actuelles pour comparaison"""
    print("\n🎤 Test des voix actuelles pour comparaison...")
    try:
        response = requests.get('http://127.0.0.1:8000/tts/public/voices/')
        if response.status_code == 200:
            voices = response.json()
            print(f"Voix actuelles: {len(voices)} voix disponibles")
            
            print("\n📋 Liste des voix:")
            for voice in voices:
                premium = "⭐ Premium" if voice['is_premium'] else "🆓 Gratuit"
                print(f"  - {voice['name']} (ID: {voice['id']}, Type: {voice['voice_type']}, {premium})")
                
        else:
            print(f"❌ Erreur récupération voix: {response.status_code}")
    except Exception as e:
        print(f"❌ Erreur voix actuelles: {e}")

def check_voice_tests_directory():
    """Vérifier le répertoire voice_tests standard"""
    print("\n🧪 Vérification du répertoire voice_tests standard...")
    
    test_urls = [
        'http://127.0.0.1:8000/media/voice_tests/',
        'http://127.0.0.1:8000/static/voice_tests/',
        'http://127.0.0.1:8000/api/voice_tests/'
    ]
    
    for url in test_urls:
        try:
            response = requests.get(url)
            print(f"{url}: {response.status_code}")
            if response.status_code == 200:
                content = response.text[:300]
                print(f"  Contenu: {content}...")
        except Exception as e:
            print(f"{url}: Erreur - {e}")

def test_enhanced_synthesis():
    """Tester si il y a des paramètres enhanced dans l'API de synthèse"""
    print("\n🔬 Test de synthèse avec paramètres enhanced...")
    
    # Test avec des paramètres enhanced possibles
    enhanced_params = [
        {'enhanced': True},
        {'quality': 'enhanced'},
        {'voice_quality': 'enhanced'},
        {'enhanced_mode': True}
    ]
    
    for params in enhanced_params:
        payload = {
            'text': 'Test enhanced voice',
            'voice_id': 1,
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0,
            **params
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            print(f"Paramètre {params}: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                if 'audio_url' in data:
                    print(f"  ✅ URL audio: {data['audio_url']}")
                    # Vérifier si l'URL contient 'enhanced'
                    if 'enhanced' in data['audio_url']:
                        print("  🎯 URL contient 'enhanced' !")
        except Exception as e:
            print(f"Paramètre {params}: Erreur - {e}")

if __name__ == "__main__":
    check_enhanced_voice_tests()
    check_current_voices()
    check_voice_tests_directory()
    test_enhanced_synthesis()
    
    print("\n" + "=" * 60)
    print("📊 RÉSUMÉ")
    print("=" * 60)
    print("Si des fichiers enhanced sont trouvés, ils pourraient contenir:")
    print("• Voix de meilleure qualité")
    print("• Nouveaux types de voix")
    print("• Paramètres audio améliorés")
    print("• Tests avec différents textes")
