#!/usr/bin/env python3
"""
Script de test pour l'API Coqui TTS
Teste tous les endpoints et affiche les modèles disponibles
"""

import requests
import json
import sys

BASE_URL = "http://localhost:5002"

def test_health():
    """Test de l'endpoint de santé"""
    print("🔍 Test de l'endpoint de santé...")
    try:
        response = requests.get(f"{BASE_URL}/api/tts")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Serveur en ligne: {data.get('message', 'OK')}")
            print(f"📱 Device: {data.get('device', 'unknown')}")
            print(f"🤖 Modèle chargé: {data.get('model_loaded', False)}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur de connexion: {e}")
        return False

def test_models():
    """Test de l'endpoint des modèles"""
    print("\n🔍 Test de l'endpoint des modèles...")
    try:
        response = requests.get(f"{BASE_URL}/api/tts/models")
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            total = data.get('total_models', len(models))
            recommended = data.get('recommended_count', 0)
            
            print(f"✅ {total} modèles trouvés ({recommended} recommandés)")
            print(f"🎯 Modèle actuel: {data.get('current_model', 'unknown')}")
            
            # Afficher les modèles recommandés
            print("\n🌟 Modèles recommandés:")
            for model in models:
                if model.get('recommended', False):
                    print(f"  • {model['name']}")
                    print(f"    📝 {model.get('description', 'N/A')}")
                    print(f"    🌍 Langue: {model.get('language', 'N/A')}")
                    print(f"    ⭐ Qualité: {model.get('quality', 'N/A')}")
                    print(f"    ⚡ Vitesse: {model.get('speed', 'N/A')}")
                    if 'features' in model:
                        print(f"    🎛️  Fonctionnalités: {', '.join(model['features'])}")
                    print()
            
            # Afficher quelques autres modèles
            other_models = [m for m in models if not m.get('recommended', False)]
            if other_models:
                print(f"📚 Autres modèles disponibles ({len(other_models)}):")
                for i, model in enumerate(other_models[:5]):  # Afficher les 5 premiers
                    print(f"  • {model['name']} ({model.get('language', 'N/A')})")
                if len(other_models) > 5:
                    print(f"  ... et {len(other_models) - 5} autres modèles")
            
            return models
        else:
            print(f"❌ Erreur: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_voices(model_name):
    """Test de l'endpoint des voix pour un modèle"""
    print(f"\n🔍 Test des voix pour {model_name}...")
    try:
        # Encoder le nom du modèle pour l'URL
        import urllib.parse
        encoded_model = urllib.parse.quote(model_name, safe='')
        
        response = requests.get(f"{BASE_URL}/api/tts/voices/{encoded_model}")
        if response.status_code == 200:
            data = response.json()
            voices = data.get('voices', [])
            
            print(f"✅ {len(voices)} voix trouvées")
            print(f"🎭 Support speakers: {data.get('supports_speakers', False)}")
            print(f"🌍 Support langues: {data.get('supports_languages', False)}")
            
            if voices:
                print("🎤 Voix disponibles:")
                for voice in voices:
                    print(f"  • {voice['name']} ({voice['type']})")
            
            return voices
        else:
            print(f"❌ Erreur: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_synthesis(model_name, text="Bonjour ! Ceci est un test de synthèse vocale avec Coqui TTS."):
    """Test de synthèse vocale"""
    print(f"\n🔍 Test de synthèse avec {model_name}...")
    try:
        payload = {
            "text": text,
            "model_name": model_name
        }
        
        response = requests.post(f"{BASE_URL}/api/tts", json=payload)
        if response.status_code == 200:
            print(f"✅ Synthèse réussie ({len(response.content)} bytes)")
            print(f"📄 Content-Type: {response.headers.get('content-type', 'unknown')}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            if response.headers.get('content-type') == 'application/json':
                try:
                    error_data = response.json()
                    print(f"💬 Message: {error_data.get('error', 'Erreur inconnue')}")
                except:
                    pass
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🐸 Test de l'API Coqui TTS pour MeetVoice")
    print("=" * 50)
    
    # Test de santé
    if not test_health():
        print("❌ Le serveur n'est pas accessible. Assurez-vous qu'il fonctionne.")
        sys.exit(1)
    
    # Test des modèles
    models = test_models()
    if not models:
        print("❌ Aucun modèle trouvé.")
        sys.exit(1)
    
    # Test des voix pour le premier modèle recommandé
    recommended_models = [m for m in models if m.get('recommended', False)]
    if recommended_models:
        test_model = recommended_models[0]['name']
        test_voices(test_model)
        
        # Test de synthèse
        test_synthesis(test_model)
    
    print("\n🎉 Tests terminés !")
    print(f"📊 Résumé: {len(models)} modèles disponibles")
    print("🌐 Interface web: http://localhost:8081")

if __name__ == "__main__":
    main()
