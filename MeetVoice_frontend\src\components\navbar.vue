<template>
  <div class="container-fluid">
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
      <a class="navbar-brand">
        <router-link to="/">
          <img src="@/assets/logo1.png" class="navbar-brand ms-1 ms-md-4" height="65" alt="logo" :class="{ active: $route.path === '/' }">
        </router-link>
      </a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarSupportedContent">
        <ul class="navbar-nav text-center ms-auto mb-2 mb-lg-0">
          <li class="nav-item">
            <router-link to="/" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/' }">Accueil</router-link>
          </li>
          <li class="nav-item">
            <router-link to="/actualite" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/actualite' }">Actualité</router-link>
          </li>
          <li v-if="isLoggedIn" class="nav-item">
            <router-link to="/formule" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/formule' }">Formule</router-link>
          </li>
          <li v-if="isLoggedIn" class="nav-item">
            <router-link to="/dl" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/dl' }">Téléchargement</router-link>
          </li>
          <li v-if="!isLoggedIn" class="nav-item">
            <router-link to="/register" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/register' }">Inscription</router-link>
          </li>
          <li v-if="!isLoggedIn" class="nav-item">
            <router-link to="/login" class="nav-link me-0 me-lg-5" :class="{ active: $route.path === '/login' }">Connexion</router-link>
          </li>
          <li v-if="isLoggedIn" class="nav-item">
            <router-link to="/login" class="nav-link me-0 me-lg-5" @click="logout">Déconnexion</router-link>
          </li>
        </ul>
      </div>
    </nav>
  </div>
</template>

<script>
import { mapActions } from 'vuex';

export default {
  computed: {
    isLoggedIn() {
      return this.$store.state.isLoggedIn;
    },
  },
  methods: {
    ...mapActions(['logout']),
    // ❌ SUPPRIMÉ : speakNavItem() - Plus de voix dans la navbar
  },
};
</script>


<style>
a.navbar-brand{
  color: white!important;
  font-size: 35px;
  font-family: 'Trebuchet MS', 'Lucida Sans Unicode', 'Lucida Grande', 'Lucida Sans', Arial, sans-serif;
  font-style: italic;
}

.navbar .nav-item .nav-link,
.dropdown-menu .dropdown-item {
  font-size: 20px;
  font-family: roboco;
  color: rgba(255, 255, 255, 0.644);
}

.navbar .nav-item .nav-link:hover,
.dropdown-menu .dropdown-item:hover,
.navbar .nav-item .nav-link.active,
.dropdown .dropdown-menu .dropdown-item.active {
  color: #ffffff; 
  background-color:#4e385f;
}

.navbar .nav-item .nav-link:hover,
.dropdown-menu .dropdown-item:hover,
.navbar .nav-item .nav-link.active,
.dropdown .dropdown-menu .dropdown-item.active::after{
 text-decoration: none;
 
}

.navbar button {
  background-color: rgba(255, 255, 255, 0.644);
}

.navbar {		
  background: #2A1D34;
  padding-left: 16px;
  padding-right: 16px;
  border-bottom: 2px solid rgb(0, 0, 0);    
}

#navbarDropdown{
  color: rgba(255, 255, 255, 0.644);  
  font-size: 22px;
}

#navbar{
  background: #2A1D34;
}


</style>