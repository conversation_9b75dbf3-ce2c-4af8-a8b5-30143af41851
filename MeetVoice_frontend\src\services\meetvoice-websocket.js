/**
 * Service WebSocket pour MeetVoice
 * Compatible avec le backend Rust WebSocket Server
 * URL: ws://localhost:8080/ws
 */

class MeetVoiceWebSocketService {
  constructor() {
    this.socket = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 1000
    this.isConnected = false
    this.isAuthenticated = false
    this.messageQueue = []
    this.eventListeners = new Map()
    this.store = null
    
    // Gestion des appels WebRTC
    this.localStream = null
    this.remoteStreams = new Map() // Map<userId, MediaStream>
    this.peerConnections = new Map() // Map<userId, RTCPeerConnection>
    this.isInCall = false
    this.currentSessionId = null
    this.currentParticipants = []
    
    // Configuration WebRTC
    this.rtcConfiguration = {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        { urls: 'stun:stun1.l.google.com:19302' },
        { urls: 'stun:stun2.l.google.com:19302' }
      ],
      iceCandidatePoolSize: 10
    }
    
    // Contraintes média par défaut
    this.defaultMediaConstraints = {
      video: {
        width: { ideal: 1280 },
        height: { ideal: 720 },
        frameRate: { ideal: 30 }
      },
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      }
    }
    
    // Ping/Pong pour maintenir la connexion
    this.pingInterval = null
    this.pongTimeout = null
    this.pingIntervalTime = 30000 // 30 secondes
    this.pongTimeoutTime = 5000 // 5 secondes
    this.lastPongReceived = Date.now()
    
    // URL du serveur WebSocket Rust
    this.wsUrl = process.env.VUE_APP_WS_URL || 'ws://localhost:8080/ws'
  }

  /**
   * Initialiser le service avec le store Vuex
   */
  init(store) {
    this.store = store
    console.log('🔌 MeetVoice WebSocket Service initialisé')
  }

  /**
   * Se connecter au serveur WebSocket Rust
   */
  async connect(token) {
    if (this.isConnected && this.socket?.readyState === WebSocket.OPEN) {
      console.log('⚠️ Déjà connecté au WebSocket')
      return
    }

    return new Promise((resolve, reject) => {
      try {
        console.log(`🔌 Connexion au serveur WebSocket: ${this.wsUrl}`)
        
        this.socket = new WebSocket(this.wsUrl)
        
        this.socket.onopen = () => {
          console.log('✅ Connexion WebSocket établie')
          this.isConnected = true
          this.reconnectAttempts = 0
          
          // Authentification immédiate
          this.authenticate(token)
          
          // Démarrer le ping/pong
          this.startPingPong()
          
          // Traiter la queue de messages
          this.processMessageQueue()
          
          resolve()
        }
        
        this.socket.onmessage = (event) => {
          this.handleMessage(event)
        }
        
        this.socket.onclose = (event) => {
          console.log('🔌 Connexion WebSocket fermée:', event.code, event.reason)
          this.isConnected = false
          this.isAuthenticated = false
          this.stopPingPong()
          
          if (!event.wasClean && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect(token)
          }
        }
        
        this.socket.onerror = (error) => {
          console.error('❌ Erreur WebSocket:', error)
          reject(error)
        }
        
      } catch (error) {
        console.error('❌ Erreur lors de la connexion WebSocket:', error)
        reject(error)
      }
    })
  }

  /**
   * Authentifier l'utilisateur
   */
  authenticate(token) {
    const authMessage = {
      type: 'Auth',
      data: { token }
    }
    
    this.sendMessage(authMessage)
    console.log('🔐 Message d\'authentification envoyé')
  }

  /**
   * Gérer les messages reçus du serveur Rust
   */
  handleMessage(event) {
    try {
      const message = JSON.parse(event.data)
      console.log('📨 Message reçu:', message)
      
      switch (message.type) {
        case 'AuthSuccess':
          this.handleAuthSuccess(message.data)
          break
          
        case 'AuthError':
          this.handleAuthError(message.data)
          break
          
        case 'MessageReceived':
          this.handleChatMessage(message.data)
          break
          
        case 'VoiceSessionJoined':
          this.handleVoiceSessionJoined(message.data)
          break
          
        case 'VoiceSessionLeft':
          this.handleVoiceSessionLeft(message.data)
          break
          
        case 'WebRTCOffer':
          this.handleWebRTCOffer(message.data)
          break
          
        case 'WebRTCAnswer':
          this.handleWebRTCAnswer(message.data)
          break
          
        case 'ICECandidate':
          this.handleICECandidate(message.data)
          break
          
        case 'Pong':
          this.handlePong()
          break
          
        case 'Error':
          this.handleError(message.data)
          break
          
        default:
          console.warn('⚠️ Type de message non géré:', message.type)
      }
      
      // Émettre l'événement pour les listeners
      this.emit(message.type, message.data)
      
    } catch (error) {
      console.error('❌ Erreur lors du traitement du message:', error)
    }
  }

  /**
   * Gérer le succès de l'authentification
   */
  handleAuthSuccess(data) {
    console.log('✅ Authentification réussie:', data)
    this.isAuthenticated = true
    
    if (this.store) {
      this.store.commit('websocket/setAuthenticated', true)
      this.store.commit('websocket/setUserId', data.user_id)
    }
  }

  /**
   * Gérer l'erreur d'authentification
   */
  handleAuthError(data) {
    console.error('❌ Erreur d\'authentification:', data.message)
    this.isAuthenticated = false
    
    if (this.store) {
      this.store.commit('websocket/setError', data.message)
    }
  }

  /**
   * Gérer les messages de chat
   */
  handleChatMessage(chatMessage) {
    console.log('💬 Message de chat reçu:', chatMessage)
    
    if (this.store) {
      this.store.dispatch('messages/addMessage', chatMessage)
    }
  }

  /**
   * Envoyer un message au serveur
   */
  sendMessage(message) {
    if (!this.isConnected || this.socket?.readyState !== WebSocket.OPEN) {
      console.log('📤 Ajout du message à la queue (non connecté)')
      this.messageQueue.push(message)
      return
    }

    try {
      const messageStr = JSON.stringify(message)
      this.socket.send(messageStr)
      console.log('📤 Message envoyé:', message)
    } catch (error) {
      console.error('❌ Erreur lors de l\'envoi du message:', error)
    }
  }

  /**
   * Envoyer un message de chat
   */
  sendChatMessage(roomId, content, messageType = 'Text') {
    const message = {
      type: 'SendMessage',
      data: {
        room_id: roomId,
        content: content,
        message_type: messageType
      }
    }
    
    this.sendMessage(message)
  }

  /**
   * Rejoindre une session vocale
   */
  joinVoiceSession(sessionId) {
    const message = {
      type: 'JoinVoiceSession',
      data: { session_id: sessionId }
    }
    
    this.sendMessage(message)
    this.currentSessionId = sessionId
  }

  /**
   * Quitter une session vocale
   */
  leaveVoiceSession(sessionId) {
    const message = {
      type: 'LeaveVoiceSession',
      data: { session_id: sessionId }
    }
    
    this.sendMessage(message)
    
    // Nettoyer les ressources locales
    this.cleanupCall()
  }

  /**
   * Gérer l'entrée dans une session vocale
   */
  handleVoiceSessionJoined(data) {
    console.log('🎤 Session vocale rejointe:', data)
    this.currentSessionId = data.session_id
    this.currentParticipants = data.participants
    this.isInCall = true
    
    if (this.store) {
      this.store.commit('websocket/setInCall', true)
      this.store.commit('websocket/setCurrentSession', data.session_id)
      this.store.commit('websocket/setParticipants', data.participants)
    }
  }

  /**
   * Gérer la sortie d'une session vocale
   */
  handleVoiceSessionLeft(data) {
    console.log('🎤 Session vocale quittée:', data)
    
    if (this.store) {
      this.store.commit('websocket/setInCall', false)
      this.store.commit('websocket/setCurrentSession', null)
      this.store.commit('websocket/setParticipants', [])
    }
    
    this.cleanupCall()
  }

  /**
   * Démarrer le ping/pong pour maintenir la connexion
   */
  startPingPong() {
    this.pingInterval = setInterval(() => {
      if (this.isConnected) {
        this.sendMessage({ type: 'Ping' })
        
        // Timeout pour le pong
        this.pongTimeout = setTimeout(() => {
          console.warn('⚠️ Pas de Pong reçu, reconnexion...')
          this.reconnect()
        }, this.pongTimeoutTime)
      }
    }, this.pingIntervalTime)
  }

  /**
   * Arrêter le ping/pong
   */
  stopPingPong() {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
    
    if (this.pongTimeout) {
      clearTimeout(this.pongTimeout)
      this.pongTimeout = null
    }
  }

  /**
   * Gérer la réception d'un Pong
   */
  handlePong() {
    this.lastPongReceived = Date.now()
    
    if (this.pongTimeout) {
      clearTimeout(this.pongTimeout)
      this.pongTimeout = null
    }
  }

  /**
   * Traiter la queue de messages
   */
  processMessageQueue() {
    while (this.messageQueue.length > 0) {
      const message = this.messageQueue.shift()
      this.sendMessage(message)
    }
  }

  /**
   * Reconnexion automatique
   */
  async reconnect(token) {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ Nombre maximum de tentatives de reconnexion atteint')
      return
    }

    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`🔄 Tentative de reconnexion ${this.reconnectAttempts}/${this.maxReconnectAttempts} dans ${delay}ms`)
    
    setTimeout(async () => {
      try {
        await this.connect(token)
      } catch (error) {
        console.error('❌ Échec de la reconnexion:', error)
      }
    }, delay)
  }

  /**
   * Se déconnecter proprement
   */
  disconnect() {
    console.log('🔌 Déconnexion du WebSocket')
    
    this.stopPingPong()
    this.cleanupCall()
    
    if (this.socket) {
      this.socket.close(1000, 'Déconnexion volontaire')
      this.socket = null
    }
    
    this.isConnected = false
    this.isAuthenticated = false
    this.currentSessionId = null
    this.currentParticipants = []
  }

  /**
   * Nettoyer les ressources d'appel
   */
  cleanupCall() {
    // Arrêter le stream local
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop())
      this.localStream = null
    }
    
    // Fermer les connexions peer
    this.peerConnections.forEach(pc => pc.close())
    this.peerConnections.clear()
    
    // Nettoyer les streams distants
    this.remoteStreams.clear()
    
    this.isInCall = false
    this.currentSessionId = null
    this.currentParticipants = []
  }

  /**
   * Ajouter un listener d'événement
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event).push(callback)
  }

  /**
   * Supprimer un listener d'événement
   */
  off(event, callback) {
    if (this.eventListeners.has(event)) {
      const listeners = this.eventListeners.get(event)
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  /**
   * Émettre un événement
   */
  emit(event, data) {
    if (this.eventListeners.has(event)) {
      this.eventListeners.get(event).forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('❌ Erreur dans le listener d\'événement:', error)
        }
      })
    }
  }

  /**
   * Gérer les erreurs
   */
  handleError(data) {
    console.error('❌ Erreur du serveur:', data.message)
    
    if (this.store) {
      this.store.commit('websocket/setError', data.message)
    }
  }

  /**
   * Vérifier l'état de la connexion
   */
  isConnectedToWebSocket() {
    return this.isConnected && this.socket?.readyState === WebSocket.OPEN
  }

  /**
   * Obtenir les statistiques de connexion
   */
  getConnectionStats() {
    return {
      isConnected: this.isConnected,
      isAuthenticated: this.isAuthenticated,
      reconnectAttempts: this.reconnectAttempts,
      lastPongReceived: this.lastPongReceived,
      messageQueueLength: this.messageQueue.length,
      isInCall: this.isInCall,
      currentSessionId: this.currentSessionId,
      participantsCount: this.currentParticipants.length
    }
  }
}

// Instance singleton
const meetVoiceWebSocketService = new MeetVoiceWebSocketService()

export default meetVoiceWebSocketService
