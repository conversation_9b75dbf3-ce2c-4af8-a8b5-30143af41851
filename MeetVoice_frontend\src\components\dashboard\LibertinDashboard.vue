<template>
  <div class="libertin-dashboard">
    <div class="dashboard-header">
      <h2>Tableau de bord Libertin</h2>
      <p>Explorez vos fantasmes en toute discrétion</p>
    </div>
    
    <div class="dashboard-content">
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">🎭</div>
          <div class="stat-info">
            <h3>{{ partiesCount }}</h3>
            <p>Soirées</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">👫</div>
          <div class="stat-info">
            <h3>{{ groupsCount }}</h3>
            <p>Groupes</p>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">🔒</div>
          <div class="stat-info">
            <h3>{{ privateCount }}</h3>
            <p>Rencontres privées</p>
          </div>
        </div>
      </div>
      
      <div class="upcoming-parties">
        <h3>Soirées à venir</h3>
        <div class="parties-list">
          <div v-for="party in upcomingParties" :key="party.id" class="party-card">
            <div class="party-info">
              <h4>{{ party.title }}</h4>
              <p>{{ party.date }} - {{ party.location }}</p>
              <span class="party-type">{{ party.type }}</span>
            </div>
            <button class="btn-join">Rejoindre</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LibertinDashboard',
  
  data() {
    return {
      partiesCount: 5,
      groupsCount: 8,
      privateCount: 12,
      upcomingParties: [
        {
          id: 1,
          title: 'Soirée masquée',
          date: 'Samedi 15 Juillet',
          location: 'Paris 16ème',
          type: 'Couples'
        },
        {
          id: 2,
          title: 'Pool party privée',
          date: 'Dimanche 23 Juillet',
          location: 'Villa privée',
          type: 'Mixte'
        }
      ]
    }
  }
}
</script>

<style scoped>
.libertin-dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 32px;
}

.dashboard-header h2 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 28px;
  font-weight: 700;
}

.dashboard-header p {
  margin: 0;
  color: #666;
  font-size: 16px;
}

.stats-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  font-size: 32px;
  width: 60px;
  height: 60px;
  background: #f3e5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 24px;
  font-weight: 700;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.upcoming-parties {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.upcoming-parties h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.parties-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.party-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.party-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.party-info p {
  margin: 0 0 4px 0;
  color: #666;
  font-size: 14px;
}

.party-type {
  background: #9c27b0;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.btn-join {
  background: #9c27b0;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-join:hover {
  background: #7b1fa2;
}
</style>
