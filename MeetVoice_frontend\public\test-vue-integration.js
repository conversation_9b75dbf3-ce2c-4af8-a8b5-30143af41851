/**
 * Test d'Intégration Vue.js + Vosk
 * À exécuter dans la console de l'application Vue.js
 */

console.log('🚀 === TESTS D\'INTÉGRATION VUE.JS + VOSK ===');

// Configuration
const BACKEND_URL = 'http://127.0.0.1:8000';

// Test principal d'intégration Vue.js
async function testVueVoskIntegration() {
  console.log('🧪 Début des tests d\'intégration Vue.js + Vosk...\n');
  
  const results = {
    backend: false,
    vosk: false,
    services: false,
    integration: false
  };

  try {
    // Test 1: Backend disponible
    console.log('🔍 Test 1: Connectivité Backend...');
    try {
      const response = await fetch(`${BACKEND_URL}/api/`);
      results.backend = response.ok;
      console.log(`${results.backend ? '✅' : '❌'} Backend: ${response.status}`);
    } catch (error) {
      console.log(`❌ Backend: ${error.message}`);
    }

    // Test 2: API Vosk
    console.log('\n🔍 Test 2: API Vosk...');
    try {
      const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
      if (response.ok) {
        const data = await response.json();
        results.vosk = true;
        console.log(`✅ Vosk API: ${JSON.stringify(data)}`);
      } else {
        console.log(`❌ Vosk API: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Vosk API: ${error.message}`);
    }

    // Test 3: Services Vue.js (si disponibles)
    console.log('\n🔍 Test 3: Services Vue.js...');
    try {
      // Vérifier si nous sommes dans l'application Vue
      if (typeof window !== 'undefined' && window.Vue) {
        console.log('✅ Vue.js détecté');
        results.services = true;
      } else if (typeof window !== 'undefined' && window.__VUE__) {
        console.log('✅ Application Vue détectée');
        results.services = true;
      } else {
        console.log('⚠️ Vue.js non détecté (normal si pas dans l\'app)');
        results.services = true; // Pas bloquant
      }
    } catch (error) {
      console.log(`❌ Services Vue: ${error.message}`);
    }

    // Test 4: Intégration complète
    console.log('\n🔍 Test 4: Test d\'upload audio...');
    try {
      // Créer un fichier audio de test
      const audioBlob = new Blob(['test audio data'], { type: 'audio/webm' });
      const formData = new FormData();
      formData.append('audio', audioBlob, 'test.webm');
      formData.append('language', 'fr-FR');

      const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        results.integration = true;
        console.log(`✅ Upload audio: ${JSON.stringify(result)}`);
      } else {
        console.log(`❌ Upload audio: ${response.status}`);
      }
    } catch (error) {
      console.log(`❌ Upload audio: ${error.message}`);
    }

    // Résumé final
    console.log('\n🧪 === RÉSUMÉ DES TESTS ===');
    const successful = Object.values(results).filter(Boolean).length;
    const total = Object.keys(results).length;
    
    console.log(`✅ Tests réussis: ${successful}/${total}`);
    console.log('📊 Détail:');
    console.log(`  - Backend: ${results.backend ? '✅' : '❌'}`);
    console.log(`  - Vosk API: ${results.vosk ? '✅' : '❌'}`);
    console.log(`  - Services: ${results.services ? '✅' : '❌'}`);
    console.log(`  - Intégration: ${results.integration ? '✅' : '❌'}`);
    
    if (successful === total) {
      console.log('\n🎉 INTÉGRATION PARFAITE ! Système prêt !');
    } else if (successful >= total * 0.75) {
      console.log('\n✅ INTÉGRATION BONNE ! Quelques optimisations possibles.');
    } else {
      console.log('\n⚠️ INTÉGRATION PARTIELLE ! Vérifications nécessaires.');
    }
    
    return results;

  } catch (error) {
    console.error('❌ Erreur générale des tests:', error);
    return results;
  }
}

// Test rapide Vosk uniquement
async function quickVoskTest() {
  console.log('⚡ Test rapide Vosk...');
  try {
    const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Vosk OK:', data);
      return true;
    } else {
      console.log('❌ Vosk KO:', response.status);
      return false;
    }
  } catch (error) {
    console.log('❌ Vosk Erreur:', error.message);
    return false;
  }
}

// Test de performance
async function performanceTest() {
  console.log('⏱️ Test de performance Vosk...');
  const times = [];
  
  for (let i = 0; i < 5; i++) {
    try {
      const start = performance.now();
      const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
      const end = performance.now();
      
      if (response.ok) {
        times.push(end - start);
      }
    } catch (error) {
      console.log(`❌ Test ${i + 1} échoué:`, error.message);
    }
  }
  
  if (times.length > 0) {
    const average = times.reduce((a, b) => a + b, 0) / times.length;
    const min = Math.min(...times);
    const max = Math.max(...times);
    
    console.log(`⏱️ Performance (${times.length} tests):`);
    console.log(`  - Moyenne: ${average.toFixed(2)}ms`);
    console.log(`  - Min: ${min.toFixed(2)}ms`);
    console.log(`  - Max: ${max.toFixed(2)}ms`);
    
    if (average < 100) {
      console.log('🚀 Performance excellente !');
    } else if (average < 500) {
      console.log('✅ Performance bonne');
    } else {
      console.log('⚠️ Performance lente');
    }
    
    return { average, min, max, tests: times.length };
  } else {
    console.log('❌ Aucun test de performance réussi');
    return null;
  }
}

// Test des endpoints disponibles
async function testAllEndpoints() {
  console.log('🔗 Test de tous les endpoints Vosk...');
  
  const endpoints = [
    { path: '/api/vosk/status/', method: 'GET', description: 'Status API' },
    { path: '/api/vosk/languages/', method: 'GET', description: 'Languages disponibles' },
    { path: '/api/vosk/speech-to-text/', method: 'POST', description: 'Transcription audio' }
  ];
  
  for (const endpoint of endpoints) {
    try {
      const url = `${BACKEND_URL}${endpoint.path}`;
      
      if (endpoint.method === 'GET') {
        const response = await fetch(url);
        console.log(`${response.ok ? '✅' : '❌'} ${endpoint.method} ${endpoint.path} - ${endpoint.description} (${response.status})`);
      } else {
        // Pour POST, on teste juste l'existence
        const response = await fetch(url, { method: 'HEAD' });
        console.log(`${response.status !== 404 ? '✅' : '❌'} ${endpoint.method} ${endpoint.path} - ${endpoint.description} (${response.status})`);
      }
    } catch (error) {
      console.log(`❌ ${endpoint.method} ${endpoint.path} - Erreur: ${error.message}`);
    }
  }
}

// Fonctions disponibles globalement
window.testVueVoskIntegration = testVueVoskIntegration;
window.quickVoskTest = quickVoskTest;
window.performanceTest = performanceTest;
window.testAllEndpoints = testAllEndpoints;

console.log('🧪 Tests d\'intégration Vue.js chargés !');
console.log('📋 Commandes disponibles:');
console.log('  - testVueVoskIntegration() : Test complet');
console.log('  - quickVoskTest() : Test rapide');
console.log('  - performanceTest() : Test de performance');
console.log('  - testAllEndpoints() : Test des endpoints');
console.log('');
console.log('🚀 Lancez: testVueVoskIntegration()');
