#!/usr/bin/env python3
"""
Test du système de voix combiné (API + Navigateur)
"""

def test_voice_combination():
    """Test de la combinaison des voix API et navigateur"""
    print("🎤 Test de la combinaison des voix...")
    print("=" * 60)
    
    # Simulation des voix API (Camille et Sophie)
    api_voices = [
        {
            "id": 1,
            "name": "<PERSON>",
            "gender": "female",
            "age": "young",
            "language": "fr",
            "description": "Voix féminine claire et dynamique",
            "type": "api"
        },
        {
            "id": 6,
            "name": "<PERSON>",
            "gender": "female",
            "age": "adult",
            "language": "fr",
            "description": "Voix féminine mature et professionnelle",
            "type": "api"
        }
    ]
    
    # Simulation des voix navigateur
    browser_voices = [
        {
            "id": "browser_0",
            "name": "Microsoft Marie - French (France)",
            "gender": "female",
            "age": "adult",
            "language": "fr-FR",
            "description": "Voix système : Microsoft Marie - French (France)",
            "type": "browser"
        },
        {
            "id": "browser_1",
            "name": "Google français",
            "gender": "female",
            "age": "adult",
            "language": "fr-FR",
            "description": "Voix système : Google français",
            "type": "browser"
        },
        {
            "id": "browser_2",
            "name": "Microsoft Paul - French (France)",
            "gender": "male",
            "age": "adult",
            "language": "fr-FR",
            "description": "Voix système : Microsoft Paul - French (France)",
            "type": "browser"
        }
    ]
    
    # Combiner les voix
    all_voices = api_voices + browser_voices
    
    print("🎤 Voix API disponibles:")
    for voice in api_voices:
        print(f"   - {voice['name']} (ID: {voice['id']}) - {voice['description']}")
    
    print(f"\n🌐 Voix navigateur disponibles:")
    for voice in browser_voices:
        print(f"   - {voice['name']} (ID: {voice['id']}) - Type: {voice['type']}")
    
    print(f"\n📊 Résumé:")
    print(f"   Voix API: {len(api_voices)}")
    print(f"   Voix navigateur: {len(browser_voices)}")
    print(f"   Total: {len(all_voices)}")
    
    # Vérifications
    expected_api_count = 2
    expected_browser_count = 3
    expected_total = 5
    
    api_count = len([v for v in all_voices if v['type'] == 'api'])
    browser_count = len([v for v in all_voices if v['type'] == 'browser'])
    total_count = len(all_voices)
    
    success = (api_count == expected_api_count and 
               browser_count == expected_browser_count and 
               total_count == expected_total)
    
    print(f"\n✅ Résultat: {'Succès' if success else 'Échec'}")
    
    return success

def test_voice_type_detection():
    """Test de la détection du type de voix"""
    print("\n🔍 Test de la détection du type de voix...")
    print("=" * 50)
    
    # Simulation de la fonction de détection
    def detect_voice_type(voice_id):
        if isinstance(voice_id, str) and voice_id.startswith('browser_'):
            return 'browser'
        elif isinstance(voice_id, (int, str)) and not str(voice_id).startswith('browser_'):
            return 'api'
        else:
            return 'unknown'
    
    test_cases = [
        (1, 'api', 'Camille (ID numérique)'),
        (6, 'api', 'Sophie (ID numérique)'),
        ('browser_0', 'browser', 'Voix navigateur 0'),
        ('browser_1', 'browser', 'Voix navigateur 1'),
        ('browser_2', 'browser', 'Voix navigateur 2'),
        ('female_young', 'api', 'Voix API legacy'),
    ]
    
    success_count = 0
    
    for voice_id, expected_type, description in test_cases:
        detected_type = detect_voice_type(voice_id)
        success = detected_type == expected_type
        status = "✅" if success else "❌"
        
        print(f"   {status} {description}: {voice_id} → {detected_type}")
        
        if success:
            success_count += 1
    
    total_tests = len(test_cases)
    print(f"\n📊 Résultat: {success_count}/{total_tests} tests réussis")
    
    return success_count == total_tests

def test_voice_selection_logic():
    """Test de la logique de sélection des voix"""
    print("\n🎯 Test de la logique de sélection...")
    print("=" * 50)
    
    # Simulation de la logique de sélection
    def select_voice_method(voice_type):
        if isinstance(voice_type, str) and voice_type.startswith('browser_'):
            return 'speakTextWithBrowser'
        else:
            return 'ttsService.speak'
    
    test_cases = [
        (1, 'ttsService.speak', 'Camille'),
        (6, 'ttsService.speak', 'Sophie'),
        ('browser_0', 'speakTextWithBrowser', 'Microsoft Marie'),
        ('browser_1', 'speakTextWithBrowser', 'Google français'),
        ('female_young', 'ttsService.speak', 'Legacy API'),
    ]
    
    success_count = 0
    
    for voice_type, expected_method, description in test_cases:
        selected_method = select_voice_method(voice_type)
        success = selected_method == expected_method
        status = "✅" if success else "❌"
        
        print(f"   {status} {description}: {voice_type} → {selected_method}")
        
        if success:
            success_count += 1
    
    total_tests = len(test_cases)
    print(f"\n📊 Résultat: {success_count}/{total_tests} tests réussis")
    
    return success_count == total_tests

def test_interface_display():
    """Test de l'affichage dans l'interface"""
    print("\n🖥️ Test de l'affichage interface...")
    print("=" * 50)
    
    # Simulation de l'affichage des voix dans l'interface
    voices = [
        {"id": 1, "name": "Camille", "type": "api", "description": "Voix féminine claire et dynamique"},
        {"id": 6, "name": "Sophie", "type": "api", "description": "Voix féminine mature et professionnelle"},
        {"id": "browser_0", "name": "Microsoft Marie", "type": "browser", "description": "Voix système : Microsoft Marie"},
        {"id": "browser_1", "name": "Google français", "type": "browser", "description": "Voix système : Google français"},
    ]
    
    print("Interface utilisateur - Sélecteur de voix:")
    
    # Grouper par type
    api_voices = [v for v in voices if v['type'] == 'api']
    browser_voices = [v for v in voices if v['type'] == 'browser']
    
    print("\n   📡 Voix API MeetVoice:")
    for voice in api_voices:
        print(f"      • {voice['name']} - {voice['description']}")
    
    print("\n   🌐 Voix du navigateur:")
    for voice in browser_voices:
        print(f"      • {voice['name']} - {voice['description']}")
    
    # Vérifications
    has_api_section = len(api_voices) > 0
    has_browser_section = len(browser_voices) > 0
    has_camille = any(v['name'] == 'Camille' for v in api_voices)
    has_sophie = any(v['name'] == 'Sophie' for v in api_voices)
    
    print(f"\n📊 Vérifications:")
    print(f"   Section API: {'✅' if has_api_section else '❌'}")
    print(f"   Section navigateur: {'✅' if has_browser_section else '❌'}")
    print(f"   Camille présente: {'✅' if has_camille else '❌'}")
    print(f"   Sophie présente: {'✅' if has_sophie else '❌'}")
    
    success = has_api_section and has_browser_section and has_camille and has_sophie
    print(f"   Statut: {'✅ Succès' if success else '❌ Échec'}")
    
    return success

def main():
    """Fonction principale"""
    print("🎤 Test du système de voix combiné (API + Navigateur)")
    print("=" * 70)
    
    # Tests
    combination_ok = test_voice_combination()
    detection_ok = test_voice_type_detection()
    selection_ok = test_voice_selection_logic()
    interface_ok = test_interface_display()
    
    print("\n" + "=" * 70)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 70)
    
    print(f"🎤 Combinaison voix: {'✅ OK' if combination_ok else '❌ ERREUR'}")
    print(f"🔍 Détection type: {'✅ OK' if detection_ok else '❌ ERREUR'}")
    print(f"🎯 Logique sélection: {'✅ OK' if selection_ok else '❌ ERREUR'}")
    print(f"🖥️ Affichage interface: {'✅ OK' if interface_ok else '❌ ERREUR'}")
    
    if combination_ok and detection_ok and selection_ok and interface_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Camille et Sophie (API) + voix navigateur disponibles")
        print("✅ Détection automatique du type de voix")
        print("✅ Sélection de la bonne méthode de synthèse")
        print("✅ Interface organisée par type de voix")
        print("\n💡 FONCTIONNALITÉS:")
        print("• Voix API haute qualité (Camille, Sophie)")
        print("• Voix système du navigateur (fallback)")
        print("• Sélection automatique de la méthode")
        print("• Interface claire et organisée")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("💡 Vérifiez l'implémentation du système combiné")

if __name__ == "__main__":
    main()
