/** @format */

import { geolocationService } from '@/_services/geolocation.service';

// Mock des APIs globales
global.navigator = {
  geolocation: {
    getCurrentPosition: jest.fn(),
  },
};

global.fetch = jest.fn();

describe('GeolocationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    fetch.mockClear();
  });

  describe('getCurrentPosition', () => {
    it('should resolve with position data when geolocation is successful', async () => {
      const mockPosition = {
        coords: {
          latitude: 48.8566,
          longitude: 2.3522,
          accuracy: 10,
        },
        timestamp: Date.now(),
      };

      navigator.geolocation.getCurrentPosition.mockImplementation((success) => {
        success(mockPosition);
      });

      const result = await geolocationService.getCurrentPosition();

      expect(result).toEqual({
        latitude: 48.8566,
        longitude: 2.3522,
        accuracy: 10,
        timestamp: mockPosition.timestamp,
      });
    });

    it('should reject when geolocation is not supported', async () => {
      const originalGeolocation = global.navigator.geolocation;
      delete global.navigator.geolocation;

      await expect(geolocationService.getCurrentPosition()).rejects.toThrow(
        'La géolocalisation n\'est pas supportée par ce navigateur'
      );

      global.navigator.geolocation = originalGeolocation;
    });

    it('should reject when permission is denied', async () => {
      const mockError = {
        code: 1, // PERMISSION_DENIED
        message: 'Permission denied',
      };

      navigator.geolocation.getCurrentPosition.mockImplementation((success, error) => {
        error(mockError);
      });

      await expect(geolocationService.getCurrentPosition()).rejects.toThrow(
        'L\'accès à la géolocalisation a été refusé'
      );
    });
  });

  describe('reverseGeocode', () => {
    it('should return address data for valid coordinates', async () => {
      const mockResponse = {
        display_name: 'Paris, France',
        address: {
          city: 'Paris',
          postcode: '75001',
          country: 'France',
        },
        lat: '48.8566',
        lon: '2.3522',
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await geolocationService.reverseGeocode(48.8566, 2.3522);

      expect(result).toEqual({
        display_name: 'Paris, France',
        address: {
          city: 'Paris',
          postcode: '75001',
          country: 'France',
          state: '',
          road: '',
          house_number: '',
        },
        coordinates: {
          latitude: 48.8566,
          longitude: 2.3522,
        },
        raw: mockResponse,
      });
    });

    it('should throw error when API request fails', async () => {
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      await expect(
        geolocationService.reverseGeocode(48.8566, 2.3522)
      ).rejects.toThrow('Erreur lors de la récupération de l\'adresse');
    });
  });

  describe('searchAddresses', () => {
    it('should return search results for valid query', async () => {
      const mockResponse = [
        {
          display_name: 'Paris, France',
          address: {
            city: 'Paris',
            country: 'France',
          },
          lat: '48.8566',
          lon: '2.3522',
          importance: 0.9,
          type: 'city',
        },
      ];

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await geolocationService.searchAddresses('Paris');

      expect(result).toHaveLength(1);
      expect(result[0]).toEqual({
        display_name: 'Paris, France',
        address: {
          city: 'Paris',
          postcode: '',
          country: 'France',
          state: '',
          road: '',
          house_number: '',
        },
        coordinates: {
          latitude: 48.8566,
          longitude: 2.3522,
        },
        importance: 0.9,
        type: 'city',
        raw: mockResponse[0],
      });
    });

    it('should throw error for queries shorter than 3 characters', async () => {
      await expect(geolocationService.searchAddresses('Pa')).rejects.toThrow(
        'La recherche doit contenir au moins 3 caractères'
      );
    });
  });

  describe('calculateDistance', () => {
    it('should calculate distance between two points correctly', () => {
      // Distance entre Paris et Lyon (environ 392 km)
      const distance = geolocationService.calculateDistance(
        48.8566, 2.3522, // Paris
        45.7640, 4.8357  // Lyon
      );

      expect(distance).toBeCloseTo(392, 0); // Tolérance de ±1 km
    });

    it('should return 0 for identical coordinates', () => {
      const distance = geolocationService.calculateDistance(
        48.8566, 2.3522,
        48.8566, 2.3522
      );

      expect(distance).toBe(0);
    });
  });

  describe('isWithinRadius', () => {
    it('should return true when position is within radius', () => {
      const userPosition = { latitude: 48.8566, longitude: 2.3522 }; // Paris
      const targetPosition = { latitude: 48.8606, longitude: 2.3376 }; // Proche de Paris
      
      const result = geolocationService.isWithinRadius(userPosition, targetPosition, 5);
      
      expect(result).toBe(true);
    });

    it('should return false when position is outside radius', () => {
      const userPosition = { latitude: 48.8566, longitude: 2.3522 }; // Paris
      const targetPosition = { latitude: 45.7640, longitude: 4.8357 }; // Lyon
      
      const result = geolocationService.isWithinRadius(userPosition, targetPosition, 100);
      
      expect(result).toBe(false);
    });
  });

  describe('getIPLocation', () => {
    it('should return IP location data', async () => {
      const mockResponse = {
        city: 'Paris',
        region: 'Île-de-France',
        country_name: 'France',
        latitude: 48.8566,
        longitude: 2.3522,
        timezone: 'Europe/Paris',
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await geolocationService.getIPLocation();

      expect(result).toEqual({
        city: 'Paris',
        region: 'Île-de-France',
        country: 'France',
        coordinates: {
          latitude: 48.8566,
          longitude: 2.3522,
        },
        timezone: 'Europe/Paris',
        raw: mockResponse,
      });
    });
  });

  describe('validateLocationConsistency', () => {
    it('should return consistent result when locations are close', async () => {
      const mockIPResponse = {
        city: 'Paris',
        latitude: 48.8566,
        longitude: 2.3522,
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockIPResponse,
      });

      const declaredPosition = { latitude: 48.8606, longitude: 2.3376 };
      const result = await geolocationService.validateLocationConsistency(declaredPosition, 10);

      expect(result.isConsistent).toBe(true);
      expect(result.distance).toBeLessThan(10);
    });

    it('should return inconsistent result when locations are far apart', async () => {
      const mockIPResponse = {
        city: 'Paris',
        latitude: 48.8566,
        longitude: 2.3522,
      };

      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => mockIPResponse,
      });

      const declaredPosition = { latitude: 45.7640, longitude: 4.8357 }; // Lyon
      const result = await geolocationService.validateLocationConsistency(declaredPosition, 100);

      expect(result.isConsistent).toBe(false);
      expect(result.distance).toBeGreaterThan(100);
      expect(result.warning).toContain('La position déclarée semble éloignée');
    });

    it('should handle API errors gracefully', async () => {
      fetch.mockRejectedValueOnce(new Error('Network error'));

      const declaredPosition = { latitude: 48.8566, longitude: 2.3522 };
      const result = await geolocationService.validateLocationConsistency(declaredPosition);

      expect(result.isConsistent).toBe(true);
      expect(result.error).toBe('Network error');
      expect(result.warning).toBe('Impossible de valider la localisation');
    });
  });
});
