<template>
  <div class="settings-view">
    <div class="container">
      <h1>Paramètres</h1>
      
      <div class="settings-content">
        <div class="settings-section">
          <h2>Profil</h2>
          <div class="setting-item">
            <label>Nom d'utilisateur</label>
            <input v-model="settings.username" type="text">
          </div>
          
          <div class="setting-item">
            <label>Email</label>
            <input v-model="settings.email" type="email">
          </div>
        </div>
        
        <div class="settings-section">
          <h2>Notifications</h2>
          <div class="setting-item">
            <label class="checkbox-label">
              <input v-model="settings.emailNotifications" type="checkbox">
              Notifications par email
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input v-model="settings.pushNotifications" type="checkbox">
              Notifications push
            </label>
          </div>
        </div>
        
        <div class="settings-section">
          <h2>Confidentialité</h2>
          <div class="setting-item">
            <label class="checkbox-label">
              <input v-model="settings.profileVisible" type="checkbox">
              Profil visible publiquement
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input v-model="settings.showOnlineStatus" type="checkbox">
              Afficher le statut en ligne
            </label>
          </div>
        </div>
        
        <div class="settings-actions">
          <button @click="saveSettings" class="btn-save">Sauvegarder</button>
          <router-link to="/dashboard" class="btn-cancel">Annuler</router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'SettingsView',
  
  data() {
    return {
      settings: {
        username: 'MonNom',
        email: '<EMAIL>',
        emailNotifications: true,
        pushNotifications: false,
        profileVisible: true,
        showOnlineStatus: true
      }
    }
  },
  
  methods: {
    saveSettings() {
      console.log('Sauvegarde des paramètres:', this.settings)
      alert('Paramètres sauvegardés !')
    }
  }
}
</script>

<style scoped>
.settings-view {
  padding: 40px 20px;
  max-width: 800px;
  margin: 0 auto;
}

.settings-content {
  background: white;
  border-radius: 12px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.settings-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #eee;
}

.settings-section:last-of-type {
  border-bottom: none;
}

.settings-section h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.setting-item {
  margin-bottom: 16px;
}

.setting-item label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.setting-item input[type="text"],
.setting-item input[type="email"] {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
}

.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto !important;
  margin: 0;
}

.settings-actions {
  display: flex;
  gap: 16px;
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #eee;
}

.btn-save {
  background: #007bff;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-save:hover {
  background: #0056b3;
}

.btn-cancel {
  background: #f8f9fa;
  color: #333;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 6px;
  border: 1px solid #ddd;
  transition: background 0.3s ease;
}

.btn-cancel:hover {
  background: #e9ecef;
}

@media (max-width: 768px) {
  .settings-actions {
    flex-direction: column;
  }
}
</style>
