use anyhow::Result;
use libp2p::{
    gossipsub::{self, IdentTopic as Topic, MessageAuthenticity, ValidationMode},
    identify,
    kad::{self, store::MemoryStore},
    mdns,
    ping,
    request_response::{self, ProtocolSupport},
    swarm::NetworkBehaviour,
    PeerId,
};
use std::time::Duration;

use crate::config::P2PConfig;
use super::protocols::{VoiceProtocol, VideoProtocol};

#[derive(NetworkBehaviour)]
#[behaviour(to_swarm = "MeetVoiceBehaviourEvent")]
pub struct MeetVoiceBehaviour {
    pub gossipsub: gossipsub::Behaviour,
    pub mdns: mdns::tokio::Behaviour,
    pub identify: identify::Behaviour,
    pub kademlia: kad::Behaviour<MemoryStore>,
    pub ping: ping::Behaviour,
    pub voice: request_response::Behaviour<VoiceProtocol>,
    pub video: request_response::Behaviour<VideoProtocol>,
}

#[derive(Debug)]
pub enum MeetVoiceBehaviourEvent {
    Gossipsub(gossipsub::Event),
    Mdns(mdns::Event),
    Identify(identify::Event),
    Kade<PERSON><PERSON>(kad::Event),
    Ping(ping::Event),
    Voice(request_response::Event<Vec<u8>, Vec<u8>>),
    Video(request_response::Event<Vec<u8>, Vec<u8>>),
}

impl From<gossipsub::Event> for MeetVoiceBehaviourEvent {
    fn from(event: gossipsub::Event) -> Self {
        MeetVoiceBehaviourEvent::Gossipsub(event)
    }
}

impl From<mdns::Event> for MeetVoiceBehaviourEvent {
    fn from(event: mdns::Event) -> Self {
        MeetVoiceBehaviourEvent::Mdns(event)
    }
}

impl From<identify::Event> for MeetVoiceBehaviourEvent {
    fn from(event: identify::Event) -> Self {
        MeetVoiceBehaviourEvent::Identify(event)
    }
}

impl From<kad::Event> for MeetVoiceBehaviourEvent {
    fn from(event: kad::Event) -> Self {
        MeetVoiceBehaviourEvent::Kademlia(event)
    }
}

impl From<ping::Event> for MeetVoiceBehaviourEvent {
    fn from(event: ping::Event) -> Self {
        MeetVoiceBehaviourEvent::Ping(event)
    }
}

impl From<request_response::Event<Vec<u8>, Vec<u8>>> for MeetVoiceBehaviourEvent {
    fn from(event: request_response::Event<Vec<u8>, Vec<u8>>) -> Self {
        // Note: En pratique, nous aurions besoin de distinguer entre voice et video
        // Pour cette démo, nous utilisons Voice par défaut
        MeetVoiceBehaviourEvent::Voice(event)
    }
}

impl MeetVoiceBehaviour {
    pub async fn new(
        local_key: &libp2p::identity::Keypair,
        config: &P2PConfig,
    ) -> Result<Self> {
        let local_peer_id = PeerId::from(local_key.public());
        
        // Configuration Gossipsub pour la messagerie pub/sub
        let gossipsub_config = gossipsub::ConfigBuilder::default()
            .heartbeat_interval(Duration::from_secs(10))
            .validation_mode(ValidationMode::Strict)
            .message_id_fn(|message| {
                use std::collections::hash_map::DefaultHasher;
                use std::hash::{Hash, Hasher};
                let mut hasher = DefaultHasher::new();
                message.data.hash(&mut hasher);
                gossipsub::MessageId::from(hasher.finish().to_string())
            })
            .build()
            .map_err(|msg| anyhow::anyhow!("Erreur de configuration Gossipsub: {}", msg))?;
            
        let gossipsub = gossipsub::Behaviour::new(
            MessageAuthenticity::Signed(local_key.clone()),
            gossipsub_config,
        )?;
        
        // Configuration mDNS pour la découverte locale
        let mdns = if config.enable_mdns {
            libp2p::mdns::tokio::Behaviour::new(libp2p::mdns::Config::default(), local_peer_id)?
        } else {
            // Créer un comportement mDNS désactivé
            libp2p::mdns::tokio::Behaviour::new(libp2p::mdns::Config::default(), local_peer_id)?
        };
        
        // Configuration Identify pour l'échange d'informations
        let identify = identify::Behaviour::new(
            identify::Config::new("/meetvoice/1.0.0".to_string(), local_key.public())
                .with_agent_version("MeetVoice-P2P/0.1.0".to_string()),
        );
        
        // Configuration Kademlia pour le routage DHT
        let store = MemoryStore::new(local_peer_id);
        let kademlia = kad::Behaviour::new(local_peer_id, store);
        
        // Configuration Ping pour la surveillance des connexions
        let ping = ping::Behaviour::new(ping::Config::new().with_interval(Duration::from_secs(30)));
        
        // Configuration des protocoles de streaming
        let voice_protocol = VoiceProtocol::new();
        let voice = request_response::Behaviour::new(
            voice_protocol,
            std::iter::once((voice_protocol.protocol_name(), ProtocolSupport::Full)),
            request_response::Config::default(),
        );
        
        let video_protocol = VideoProtocol::new();
        let video = request_response::Behaviour::new(
            video_protocol,
            std::iter::once((video_protocol.protocol_name(), ProtocolSupport::Full)),
            request_response::Config::default(),
        );
        
        Ok(Self {
            gossipsub,
            mdns,
            identify,
            kademlia,
            ping,
            voice,
            video,
        })
    }
    
    // Méthodes utilitaires pour interagir avec les sous-comportements
    pub fn subscribe_to_topic(&mut self, topic: &str) -> Result<bool> {
        let topic = Topic::new(topic);
        self.gossipsub.subscribe(&topic)
            .map_err(|e| anyhow::anyhow!("Erreur d'abonnement: {}", e))
    }
    
    pub fn unsubscribe_from_topic(&mut self, topic: &str) -> Result<bool> {
        let topic = Topic::new(topic);
        self.gossipsub.unsubscribe(&topic)
            .map_err(|e| anyhow::anyhow!("Erreur de désabonnement: {}", e))
    }
    
    pub fn publish_message(&mut self, topic: &str, data: Vec<u8>) -> Result<gossipsub::MessageId> {
        let topic = Topic::new(topic);
        self.gossipsub.publish(topic, data)
            .map_err(|e| anyhow::anyhow!("Erreur de publication: {}", e))
    }
    
    pub fn add_address(&mut self, peer_id: &PeerId, address: libp2p::Multiaddr) {
        self.kademlia.add_address(peer_id, address);
    }
    
    pub fn bootstrap(&mut self) -> Result<kad::QueryId> {
        self.kademlia.bootstrap()
            .map_err(|e| anyhow::anyhow!("Erreur de bootstrap: {}", e))
    }
    
    pub fn get_closest_peers(&mut self, peer_id: &PeerId) -> kad::QueryId {
        self.kademlia.get_closest_peers(*peer_id)
    }
}
