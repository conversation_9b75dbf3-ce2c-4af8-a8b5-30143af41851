import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000/api'

export default {
  namespaced: true,
  
  state: {
    notifications: [],
    unreadCount: 0,
    loading: false,
    error: null,
    settings: {
      push: true,
      email: true,
      sms: false,
      matches: true,
      messages: true,
      events: true,
      parties: true,
      likes: true,
      comments: true
    }
  },
  
  mutations: {
    setNotifications(state, notifications) {
      state.notifications = notifications
    },
    
    addNotification(state, notification) {
      // Ajouter un ID unique si pas présent
      if (!notification.id) {
        notification.id = Date.now() + Math.random()
      }
      
      // Ajouter un timestamp si pas présent
      if (!notification.timestamp) {
        notification.timestamp = new Date().toISOString()
      }
      
      state.notifications.unshift(notification)
      
      // Limiter à 100 notifications
      if (state.notifications.length > 100) {
        state.notifications = state.notifications.slice(0, 100)
      }
      
      // Mettre à jour le compteur non lu
      if (!notification.read) {
        state.unreadCount++
      }
    },
    
    markAsRead(state, notificationId) {
      const notification = state.notifications.find(n => n.id === notificationId)
      if (notification && !notification.read) {
        notification.read = true
        state.unreadCount = Math.max(0, state.unreadCount - 1)
      }
    },
    
    markAllAsRead(state) {
      state.notifications.forEach(notification => {
        notification.read = true
      })
      state.unreadCount = 0
    },
    
    removeNotification(state, notificationId) {
      const index = state.notifications.findIndex(n => n.id === notificationId)
      if (index !== -1) {
        const notification = state.notifications[index]
        if (!notification.read) {
          state.unreadCount = Math.max(0, state.unreadCount - 1)
        }
        state.notifications.splice(index, 1)
      }
    },
    
    clearNotifications(state) {
      state.notifications = []
      state.unreadCount = 0
    },
    
    setUnreadCount(state, count) {
      state.unreadCount = count
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    },
    
    setSettings(state, settings) {
      state.settings = { ...state.settings, ...settings }
    }
  },
  
  actions: {
    async loadNotifications({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/notifications/`)
        const notifications = response.data.results || response.data
        
        commit('setNotifications', notifications)
        
        // Calculer le nombre de notifications non lues
        const unreadCount = notifications.filter(n => !n.read).length
        commit('setUnreadCount', unreadCount)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des notifications')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    async markNotificationAsRead({ commit }, notificationId) {
      try {
        await axios.patch(`${API_BASE_URL}/notifications/${notificationId}/`, {
          read: true
        })
        
        commit('markAsRead', notificationId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors du marquage comme lu')
        throw error
      }
    },
    
    async markAllNotificationsAsRead({ commit }) {
      try {
        await axios.post(`${API_BASE_URL}/notifications/mark-all-read/`)
        
        commit('markAllAsRead')
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors du marquage de toutes les notifications')
        throw error
      }
    },
    
    async deleteNotification({ commit }, notificationId) {
      try {
        await axios.delete(`${API_BASE_URL}/notifications/${notificationId}/`)
        
        commit('removeNotification', notificationId)
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de la suppression de la notification')
        throw error
      }
    },
    
    async clearAllNotifications({ commit }) {
      try {
        await axios.delete(`${API_BASE_URL}/notifications/clear-all/`)
        
        commit('clearNotifications')
        
        return true
      } catch (error) {
        commit('setError', 'Erreur lors de la suppression de toutes les notifications')
        throw error
      }
    },
    
    async loadNotificationSettings({ commit }) {
      try {
        const response = await axios.get(`${API_BASE_URL}/notifications/settings/`)
        
        commit('setSettings', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des paramètres')
        throw error
      }
    },
    
    async updateNotificationSettings({ commit }, settings) {
      try {
        const response = await axios.patch(`${API_BASE_URL}/notifications/settings/`, settings)
        
        commit('setSettings', response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la mise à jour des paramètres')
        throw error
      }
    },
    
    // Action pour ajouter une notification locale (pour les notifications temps réel)
    addLocalNotification({ commit }, notification) {
      commit('addNotification', notification)
    },
    
    // Action pour créer différents types de notifications
    createMatchNotification({ commit }, match) {
      const notification = {
        type: 'match',
        title: 'Nouveau match !',
        message: `Vous avez un nouveau match avec ${match.user.prenom}`,
        data: { matchId: match.id, userId: match.user.id },
        read: false
      }
      commit('addNotification', notification)
    },
    
    createMessageNotification({ commit }, message) {
      const notification = {
        type: 'message',
        title: 'Nouveau message',
        message: `${message.sender.prenom} vous a envoyé un message`,
        data: { conversationId: message.conversationId, senderId: message.sender.id },
        read: false
      }
      commit('addNotification', notification)
    },
    
    createEventNotification({ commit }, event) {
      const notification = {
        type: 'event',
        title: 'Nouvel événement',
        message: `${event.title} - ${event.date}`,
        data: { eventId: event.id },
        read: false
      }
      commit('addNotification', notification)
    },
    
    createLikeNotification({ commit }, like) {
      const notification = {
        type: 'like',
        title: 'Nouveau like',
        message: `${like.user.prenom} a aimé votre profil`,
        data: { userId: like.user.id },
        read: false
      }
      commit('addNotification', notification)
    },
    
    clearError({ commit }) {
      commit('clearError')
    }
  },
  
  getters: {
    notifications: state => state.notifications,
    unreadCount: state => state.unreadCount,
    loading: state => state.loading,
    error: state => state.error,
    settings: state => state.settings,
    
    unreadNotifications: state => {
      return state.notifications.filter(n => !n.read)
    },
    
    recentNotifications: state => {
      return state.notifications.slice(0, 10)
    },
    
    notificationsByType: state => {
      const grouped = {}
      state.notifications.forEach(notification => {
        if (!grouped[notification.type]) {
          grouped[notification.type] = []
        }
        grouped[notification.type].push(notification)
      })
      return grouped
    },
    
    hasUnreadNotifications: state => {
      return state.unreadCount > 0
    }
  }
}
