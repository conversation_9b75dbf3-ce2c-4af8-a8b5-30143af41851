#!/usr/bin/env python3
"""
Test de la nouvelle API TTS avec URL audio
"""

import requests
import json

def test_new_api():
    """Test de la nouvelle API TTS"""
    print("🧪 Test de la nouvelle API TTS...")
    
    payload = {
        'text': 'Test de la nouvelle API.',
        'voice_id': 1,  # Sophie
        'language': 'fr',
        'speed': 1.0,
        'pitch': 1.0
    }
    
    try:
        response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
        print(f"Status: {response.status_code}")
        print(f"Content-Type: {response.headers.get('content-type', 'unknown')}")
        
        if response.status_code == 200:
            if 'application/json' in response.headers.get('content-type', ''):
                data = response.json()
                print("📄 Réponse JSON:")
                print(json.dumps(data, indent=2, ensure_ascii=False))
                
                if 'audio_url' in data:
                    print(f"✅ URL audio trouvée: {data['audio_url']}")
                else:
                    print("❌ Pas d'URL audio dans la réponse")
                    
            else:
                print(f"📄 Réponse binaire: {len(response.content)} bytes")
                print("⚠️  L'API retourne encore du binaire au lieu de JSON")
        else:
            print(f"❌ Erreur: {response.text[:200]}")
            
    except Exception as e:
        print(f"❌ Erreur: {e}")

def test_different_voices():
    """Test avec différentes voix"""
    print("\n🎤 Test avec différentes voix:")
    print("-" * 40)
    
    voices_to_test = [
        (1, "Sophie"),
        (6, "Élise"),
        (3, "Lucas"),
        (4, "Antoine")
    ]
    
    for voice_id, voice_name in voices_to_test:
        payload = {
            'text': f'Je suis {voice_name}.',
            'voice_id': voice_id,
            'language': 'fr',
            'speed': 1.0,
            'pitch': 1.0
        }
        
        try:
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            if response.status_code == 200:
                if 'application/json' in response.headers.get('content-type', ''):
                    data = response.json()
                    if 'audio_url' in data:
                        print(f"✅ {voice_name} (ID {voice_id}): {data['audio_url']}")
                    else:
                        print(f"❌ {voice_name} (ID {voice_id}): Pas d'URL audio")
                else:
                    print(f"⚠️  {voice_name} (ID {voice_id}): Réponse binaire ({len(response.content)} bytes)")
            else:
                print(f"❌ {voice_name} (ID {voice_id}): Erreur {response.status_code}")
                
        except Exception as e:
            print(f"❌ {voice_name} (ID {voice_id}): Erreur {e}")

if __name__ == "__main__":
    test_new_api()
    test_different_voices()
