#!/usr/bin/env node

/**
 * Test d'intégration backend pour l'interview vocal MeetVoice
 * Usage: node test-backend-integration.js
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// Configuration du test
const CONFIG = {
    backendUrl: 'http://127.0.0.1:8000',
    registrationEndpoint: '/api/register/',
    timeout: 10000
};

// Données de test complètes (TOUS les champs backend)
const TEST_DATA = {
    // === CHAMPS OBLIGATOIRES ===
    email: `test.${Date.now()}@example.com`,
    password: 'TestPassword123!',

    // === INFORMATIONS PERSONNELLES DE BASE ===
    username: `test_user_${Date.now()}`,
    nom: 'TestDupont',
    prenom: 'TestMarie',
    date_de_naissance: '1995-03-15',
    numberPhone: '0612345678',

    // === CARACTÉRISTIQUES PHYSIQUES ===
    sexe: 'Femme',
    taille: 165,
    poids: '60',
    ethnique: 'Caucasien',
    yeux: 'Marron',
    shillouette: 'Normal',

    // === INFORMATIONS SOCIALES ===
    religion: 'Catholique',
    metier: 'Ingénieur informatique',
    recherche: 'Amour',

    // === PRÉFÉRENCES ET GOÛTS (ManyToMany - texte pour test) ===
    preference_de_sortie: 'Cinéma, restaurants, concerts',
    style_de_film: 'Action, comédie, thriller',
    style_de_musique: 'Pop, rock, jazz',
    caratere: 'Sociable, optimiste, spontané',
    hobie: 'Sport, lecture, voyages, cuisine',
    tendance: 'Écologique, urbain, moderne',

    // === STATUT RELATIONNEL ===
    en_couple: false,

    // === DESCRIPTION LIBRE ===
    description_libre: 'Je suis quelqu\'un de joyeux et spontané qui aime découvrir de nouvelles choses.',
    
    // Métadonnées interview vocal
    registration_mode: 'voice_interview',
    voice_interview_completed: 'true',
    voice_answers: JSON.stringify([
        'Je m\'appelle TestMarie',
        'TestDupont',
        `test_user_${Date.now()}`,
        `test.${Date.now()}@example.com`,
        '15 mars 1995',
        'Non',
        'Je recherche des hommes',
        'Je suis ingénieur informatique',
        'Master en informatique',
        '1 mètre 65',
        'Non je ne fume pas',
        'Socialement',
        'Aucun enfant',
        'J\'ai un chat',
        'Français et anglais',
        'J\'aime le sport, la lecture et voyager',
        'Je suis quelqu\'un de joyeux et spontané'
    ])
};

// Champs requis (backend réels - SEULEMENT 2 obligatoires)
const REQUIRED_FIELDS = [
    'email', 'password'
];

// Champs optionnels principaux à tester
const OPTIONAL_FIELDS = [
    'username', 'nom', 'prenom', 'date_de_naissance', 'numberPhone',
    'sexe', 'taille', 'poids', 'ethnique', 'yeux', 'shillouette',
    'religion', 'metier', 'recherche',
    'preference_de_sortie', 'style_de_film', 'style_de_musique',
    'caratere', 'hobie', 'tendance', 'en_couple', 'description_libre'
];

// Couleurs pour la console
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function createFormData(data) {
    const boundary = '----formdata-test-' + Math.random().toString(16);
    let body = '';
    
    for (const [key, value] of Object.entries(data)) {
        body += `--${boundary}\r\n`;
        body += `Content-Disposition: form-data; name="${key}"\r\n\r\n`;
        body += `${value}\r\n`;
    }
    body += `--${boundary}--\r\n`;
    
    return {
        body: body,
        contentType: `multipart/form-data; boundary=${boundary}`
    };
}

function makeRequest(url, options, data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        const isHttps = urlObj.protocol === 'https:';
        const client = isHttps ? https : http;
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || (isHttps ? 443 : 80),
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: options.headers || {},
            timeout: CONFIG.timeout
        };
        
        const req = client.request(requestOptions, (res) => {
            let responseData = '';
            
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: responseData
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

async function testBackendConnection() {
    log('\n🔍 Test 1: Vérification de la connexion backend', 'cyan');
    
    try {
        const response = await makeRequest(`${CONFIG.backendUrl}/admin/`, {
            method: 'GET'
        });
        
        if (response.statusCode === 200 || response.statusCode === 302) {
            log('✅ Backend accessible', 'green');
            return true;
        } else {
            log(`⚠️  Backend répond avec le status ${response.statusCode}`, 'yellow');
            return true; // Peut être normal selon la configuration
        }
    } catch (error) {
        log(`❌ Erreur de connexion: ${error.message}`, 'red');
        return false;
    }
}

async function validateTestData() {
    log('\n📋 Test 2: Validation des données de test', 'cyan');
    
    let isValid = true;
    let missingFields = [];
    
    // Vérifier les champs obligatoires
    for (const field of REQUIRED_FIELDS) {
        if (!TEST_DATA[field] || TEST_DATA[field] === '') {
            missingFields.push(field);
            isValid = false;
        }
    }
    
    if (isValid) {
        log(`✅ Tous les champs obligatoires présents (${REQUIRED_FIELDS.length}/2)`, 'green');
        
        // Compter les champs optionnels présents
        const presentOptional = OPTIONAL_FIELDS.filter(field =>
            TEST_DATA[field] !== null &&
            TEST_DATA[field] !== undefined &&
            TEST_DATA[field] !== ''
        ).length;
        log(`📋 Champs optionnels remplis: ${presentOptional}/${OPTIONAL_FIELDS.length}`, 'blue');
        
        return true;
    } else {
        log(`❌ Champs obligatoires manquants: ${missingFields.join(', ')}`, 'red');
        return false;
    }
}

async function testRegistrationEndpoint() {
    log('\n📤 Test 3: Envoi des données au backend', 'cyan');
    
    try {
        const formData = createFormData(TEST_DATA);
        
        log(`📡 Envoi vers: ${CONFIG.backendUrl}${CONFIG.registrationEndpoint}`, 'blue');
        log(`📦 Taille des données: ${formData.body.length} bytes`, 'blue');
        
        const response = await makeRequest(
            `${CONFIG.backendUrl}${CONFIG.registrationEndpoint}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': formData.contentType,
                    'Content-Length': Buffer.byteLength(formData.body)
                }
            },
            formData.body
        );
        
        log(`📊 Status: ${response.statusCode}`, 'blue');
        
        if (response.statusCode >= 200 && response.statusCode < 300) {
            log('✅ Inscription réussie!', 'green');
            log(`📄 Réponse: ${response.data.substring(0, 200)}...`, 'blue');
            return { success: true, status: response.statusCode, data: response.data };
        } else if (response.statusCode === 400) {
            log('⚠️  Erreur de validation (400)', 'yellow');
            log(`📄 Détails: ${response.data}`, 'yellow');
            return { success: false, status: response.statusCode, data: response.data };
        } else if (response.statusCode === 500) {
            log('❌ Erreur serveur (500)', 'red');
            log(`📄 Détails: ${response.data}`, 'red');
            return { success: false, status: response.statusCode, data: response.data };
        } else {
            log(`⚠️  Status inattendu: ${response.statusCode}`, 'yellow');
            log(`📄 Réponse: ${response.data}`, 'yellow');
            return { success: false, status: response.statusCode, data: response.data };
        }
        
    } catch (error) {
        log(`❌ Erreur lors de l'envoi: ${error.message}`, 'red');
        return { success: false, error: error.message };
    }
}

async function testFieldMapping() {
    log('\n🔍 Test 4: Vérification du mapping des champs', 'cyan');
    
    const fieldTests = [
        { field: 'prenom', value: TEST_DATA.prenom, type: 'string' },
        { field: 'nom', value: TEST_DATA.nom, type: 'string' },
        { field: 'date_de_naissance', value: TEST_DATA.date_de_naissance, type: 'date' },
        { field: 'sexe', value: TEST_DATA.sexe, type: 'choice' },
        { field: 'recherche', value: TEST_DATA.recherche, type: 'choice' }
    ];
    
    let allValid = true;
    
    for (const test of fieldTests) {
        const isValid = validateFieldType(test.field, test.value, test.type);
        if (isValid) {
            log(`✅ ${test.field}: ${test.type} - OK`, 'green');
        } else {
            log(`❌ ${test.field}: ${test.type} - ERREUR`, 'red');
            allValid = false;
        }
    }
    
    return allValid;
}

function validateFieldType(field, value, type) {
    switch (type) {
        case 'string':
            return typeof value === 'string' && value.length > 0;
        case 'date':
            return /^\d{4}-\d{2}-\d{2}$/.test(value);
        case 'choice':
            // Valeurs acceptées pour sexe et recherche
            const validSexe = ['Homme', 'Femme', 'Non-binaire', 'Autre'];
            const validRecherche = ['Amour', 'Amitié', 'Aventure', 'Hommes', 'Femmes', 'Tous'];
            return validSexe.includes(value) || validRecherche.includes(value);
        case 'json_array':
            try {
                const parsed = JSON.parse(value);
                return Array.isArray(parsed);
            } catch {
                return false;
            }
        default:
            return true;
    }
}

async function runAllTests() {
    log('🧪 DÉBUT DES TESTS FONCTIONNELS BACKEND', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    const results = {
        connection: false,
        validation: false,
        registration: false,
        mapping: false
    };
    
    // Test 1: Connexion backend
    results.connection = await testBackendConnection();
    
    // Test 2: Validation des données
    results.validation = await validateTestData();
    
    // Test 3: Mapping des champs
    results.mapping = await testFieldMapping();
    
    // Test 4: Envoi au backend (seulement si les autres tests passent)
    if (results.connection && results.validation && results.mapping) {
        const registrationResult = await testRegistrationEndpoint();
        results.registration = registrationResult.success;
        
        if (registrationResult.success) {
            log('\n🎉 UTILISATEUR CRÉÉ AVEC SUCCÈS!', 'green');
            log(`👤 Username: ${TEST_DATA.username}`, 'green');
            log(`📧 Email: ${TEST_DATA.email}`, 'green');
        }
    } else {
        log('\n⏭️  Test d\'inscription ignoré (prérequis non remplis)', 'yellow');
    }
    
    // Résumé final
    log('\n📊 RÉSUMÉ DES TESTS', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    const testNames = {
        connection: 'Connexion Backend',
        validation: 'Validation Données',
        mapping: 'Mapping Champs',
        registration: 'Inscription Backend'
    };
    
    for (const [key, name] of Object.entries(testNames)) {
        const status = results[key] ? '✅ RÉUSSI' : '❌ ÉCHOUÉ';
        const color = results[key] ? 'green' : 'red';
        log(`${name}: ${status}`, color);
    }
    
    const successCount = Object.values(results).filter(Boolean).length;
    const totalTests = Object.keys(results).length;
    
    log(`\n📈 Score: ${successCount}/${totalTests} tests réussis`, 'cyan');
    
    if (successCount === totalTests) {
        log('\n🎉 TOUS LES TESTS SONT RÉUSSIS!', 'green');
        log('L\'interview vocal s\'enregistre correctement en backend.', 'green');
    } else {
        log('\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ', 'yellow');
        log('Vérifiez la configuration du backend et les logs ci-dessus.', 'yellow');
    }
    
    return results;
}

// Exécution des tests
if (require.main === module) {
    runAllTests().catch(error => {
        log(`\n💥 ERREUR FATALE: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = { runAllTests, TEST_DATA, CONFIG };
