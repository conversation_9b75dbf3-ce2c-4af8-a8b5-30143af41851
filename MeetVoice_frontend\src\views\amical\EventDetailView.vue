<template>
  <div class="event-detail-view">
    <div class="container">
      <div v-if="loading" class="loading">
        <div class="spinner"></div>
        <p>Chargement de l'événement...</p>
      </div>
      
      <div v-else-if="event" class="event-detail">
        <!-- En-tête de l'événement -->
        <div class="event-header">
          <div class="event-image">
            <img :src="event.image || '/images/default-event.jpg'" :alt="event.title">
            <div class="event-category">{{ event.category }}</div>
          </div>
          
          <div class="event-info">
            <h1>{{ event.title }}</h1>
            <div class="event-meta">
              <div class="meta-item">
                <span class="icon">📅</span>
                <span>{{ formatDate(event.date) }}</span>
              </div>
              <div class="meta-item">
                <span class="icon">📍</span>
                <span>{{ event.location }}</span>
              </div>
              <div class="meta-item">
                <span class="icon">👥</span>
                <span>{{ event.participants.length }}/{{ event.maxParticipants }} participants</span>
              </div>
              <div class="meta-item">
                <span class="icon">💰</span>
                <span>{{ event.price === 0 ? 'Gratuit' : `${event.price}€` }}</span>
              </div>
            </div>
            
            <div class="event-actions">
              <button 
                v-if="!isParticipant" 
                @click="joinEvent" 
                class="btn-join"
                :disabled="isFull"
              >
                {{ isFull ? 'Complet' : 'Participer' }}
              </button>
              
              <button 
                v-else 
                @click="leaveEvent" 
                class="btn-leave"
              >
                Se désinscrire
              </button>
              
              <button @click="shareEvent" class="btn-share">
                <span class="icon">📤</span>
                Partager
              </button>
            </div>
          </div>
        </div>
        
        <!-- Description de l'événement -->
        <div class="event-content">
          <div class="event-description">
            <h2>Description</h2>
            <p>{{ event.description }}</p>
          </div>
          
          <!-- Organisateur -->
          <div class="event-organizer">
            <h3>Organisateur</h3>
            <div class="organizer-card">
              <img :src="event.organizer.avatar" :alt="event.organizer.name" class="organizer-avatar">
              <div class="organizer-info">
                <h4>{{ event.organizer.name }}</h4>
                <p>{{ event.organizer.bio }}</p>
                <button @click="contactOrganizer" class="btn-contact">Contacter</button>
              </div>
            </div>
          </div>
          
          <!-- Participants -->
          <div class="event-participants">
            <h3>Participants ({{ event.participants.length }})</h3>
            <div class="participants-grid">
              <div 
                v-for="participant in event.participants" 
                :key="participant.id" 
                class="participant-card"
              >
                <img :src="participant.avatar" :alt="participant.name" class="participant-avatar">
                <span class="participant-name">{{ participant.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div v-else class="error">
        <h2>Événement non trouvé</h2>
        <p>L'événement que vous recherchez n'existe pas ou a été supprimé.</p>
        <router-link to="/amical/events" class="btn-back">Retour aux événements</router-link>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EventDetailView',
  
  data() {
    return {
      loading: true,
      event: null
    }
  },
  
  computed: {
    isParticipant() {
      if (!this.event || !this.$store.state.auth.user) return false
      return this.event.participants.some(p => p.id === this.$store.state.auth.user.id)
    },
    
    isFull() {
      if (!this.event) return false
      return this.event.participants.length >= this.event.maxParticipants
    }
  },
  
  methods: {
    async loadEvent() {
      try {
        this.loading = true
        const eventId = this.$route.params.id
        
        // Simuler un appel API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // Données d'exemple
        this.event = {
          id: eventId,
          title: 'Soirée jeux de société',
          description: 'Venez passer une soirée conviviale autour de jeux de société ! Nous avons une grande sélection de jeux pour tous les goûts : stratégie, ambiance, coopératifs... Que vous soyez débutant ou expert, vous trouverez votre bonheur !',
          date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
          location: 'Café des Amis, 123 Rue de la Paix, Paris',
          category: 'Loisirs',
          price: 0,
          maxParticipants: 20,
          image: '/images/events/jeux-societe.jpg',
          organizer: {
            id: 'org1',
            name: 'Marie Dupont',
            avatar: '/images/profiles/marie.jpg',
            bio: 'Passionnée de jeux de société depuis 10 ans'
          },
          participants: [
            { id: 'p1', name: 'Pierre', avatar: '/images/profiles/pierre.jpg' },
            { id: 'p2', name: 'Sophie', avatar: '/images/profiles/sophie.jpg' },
            { id: 'p3', name: 'Thomas', avatar: '/images/profiles/thomas.jpg' }
          ]
        }
      } catch (error) {
        console.error('Erreur lors du chargement de l\'événement:', error)
        this.event = null
      } finally {
        this.loading = false
      }
    },
    
    async joinEvent() {
      try {
        // Simuler l'inscription à l'événement
        const user = this.$store.state.auth.user
        if (user) {
          this.event.participants.push({
            id: user.id,
            name: user.name,
            avatar: user.avatar
          })
        }
      } catch (error) {
        console.error('Erreur lors de l\'inscription:', error)
      }
    },
    
    async leaveEvent() {
      try {
        // Simuler la désinscription
        const userId = this.$store.state.auth.user?.id
        if (userId) {
          this.event.participants = this.event.participants.filter(p => p.id !== userId)
        }
      } catch (error) {
        console.error('Erreur lors de la désinscription:', error)
      }
    },
    
    shareEvent() {
      if (navigator.share) {
        navigator.share({
          title: this.event.title,
          text: this.event.description,
          url: window.location.href
        })
      } else {
        // Fallback pour les navigateurs qui ne supportent pas l'API Web Share
        navigator.clipboard.writeText(window.location.href)
        alert('Lien copié dans le presse-papiers !')
      }
    },
    
    contactOrganizer() {
      // Rediriger vers la messagerie avec l'organisateur
      this.$router.push({
        name: 'Chat',
        params: { userId: this.event.organizer.id }
      })
    },
    
    formatDate(date) {
      return new Date(date).toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      })
    }
  },
  
  mounted() {
    this.loadEvent()
  }
}
</script>

<style scoped>
.event-detail-view {
  min-height: 100vh;
  background: #f8f9fa;
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.loading {
  text-align: center;
  padding: 60px 20px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.event-detail {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.event-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
}

.event-image {
  position: relative;
  height: 400px;
  overflow: hidden;
}

.event-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.event-category {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 123, 255, 0.9);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.event-info {
  padding: 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.event-info h1 {
  margin: 0 0 24px 0;
  color: #333;
  font-size: 32px;
  font-weight: 700;
}

.event-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 32px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: #666;
}

.meta-item .icon {
  font-size: 18px;
  width: 24px;
}

.event-actions {
  display: flex;
  gap: 16px;
}

.btn-join,
.btn-leave,
.btn-share {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-join {
  background: #28a745;
  color: white;
}

.btn-join:hover:not(:disabled) {
  background: #218838;
}

.btn-join:disabled {
  background: #6c757d;
  cursor: not-allowed;
}

.btn-leave {
  background: #dc3545;
  color: white;
}

.btn-leave:hover {
  background: #c82333;
}

.btn-share {
  background: #f8f9fa;
  color: #333;
  border: 1px solid #dee2e6;
}

.btn-share:hover {
  background: #e9ecef;
}

.event-content {
  padding: 40px;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 40px;
}

.event-description h2 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 24px;
  font-weight: 600;
}

.event-description p {
  color: #666;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.event-organizer,
.event-participants {
  margin-bottom: 32px;
}

.event-organizer h3,
.event-participants h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
  font-weight: 600;
}

.organizer-card {
  display: flex;
  gap: 16px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.organizer-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
}

.organizer-info h4 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.organizer-info p {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
}

.btn-contact {
  background: #007bff;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-contact:hover {
  background: #0056b3;
}

.participants-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.participant-card {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.participant-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  margin-bottom: 8px;
}

.participant-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.error {
  text-align: center;
  padding: 60px 20px;
}

.error h2 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 24px;
}

.error p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.btn-back {
  display: inline-block;
  background: #007bff;
  color: white;
  text-decoration: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 500;
  transition: background 0.3s ease;
}

.btn-back:hover {
  background: #0056b3;
}

@media (max-width: 768px) {
  .event-header {
    grid-template-columns: 1fr;
  }
  
  .event-image {
    height: 250px;
  }
  
  .event-info {
    padding: 24px;
  }
  
  .event-info h1 {
    font-size: 24px;
  }
  
  .event-content {
    grid-template-columns: 1fr;
    padding: 24px;
    gap: 24px;
  }
  
  .event-actions {
    flex-direction: column;
  }
  
  .organizer-card {
    flex-direction: column;
    text-align: center;
  }
}
</style>
