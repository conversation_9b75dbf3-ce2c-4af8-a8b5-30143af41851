<template>
  <div class="voice-interview-container">
    <div class="left-panel">
      <div class="interview-content">
        
        <!-- Progress bar -->
        <div class="progress-container">
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
          </div>
          <span class="progress-text">{{ currentPageIndex + 1 }}/{{ totalPages }} - {{ progressPercentage }}%</span>
        </div>

        <!-- Question actuelle -->
        <article v-if="currentQuestion" class="question-section">
          <header class="question-text">
            <h3>{{ currentQuestion.text }}</h3>
            <p v-if="currentQuestion.hint" class="question-hint">
              💡 {{ currentQuestion.hint }}
            </p>
          </header>

          <!-- Contrôles vocaux SIMPLES -->
          <section class="voice-controls">
            
            <!-- Microphone principal -->
            <div class="main-controls">
              <button 
                @click="toggleListening" 
                :disabled="!isSupported || isProcessing"
                :class="['btn-microphone', { 'listening': isListening, 'processing': isProcessing }]"
                aria-label="Microphone principal"
              >
                <span class="mic-icon">🎤</span>
              </button>
              
              <button 
                @click="testMicrophone" 
                :disabled="isTestingMic"
                class="btn-test"
                aria-label="Tester le microphone"
              >
                🔧
              </button>
            </div>

            <!-- Sélecteur de microphone SIMPLE -->
            <div class="microphone-selector">
              <select 
                v-model="selectedMicrophoneId" 
                @change="saveMicrophoneChoice"
                class="mic-select"
              >
                <option value="">Microphone par défaut</option>
                <option 
                  v-for="mic in availableMicrophones" 
                  :key="mic.deviceId" 
                  :value="mic.deviceId"
                >
                  {{ mic.label }}
                </option>
              </select>
            </div>

            <!-- Statut -->
            <div v-if="isListening" class="listening-status">
              <span class="pulse">🔴</span> Écoute en cours... ({{ timeLeft }}s)
            </div>

          </section>

          <!-- Réponse -->
          <section v-if="currentAnswer" class="answer-section">
            <div class="answer-display">
              <h4>Votre réponse :</h4>
              <p class="answer-text">{{ currentAnswer }}</p>
              <div class="answer-actions">
                <button @click="playAnswer" class="btn-play">🔊 Réécouter</button>
                <button @click="clearAnswer" class="btn-clear">🗑️ Effacer</button>
              </div>
            </div>
          </section>

          <!-- Test microphone résultat -->
          <section v-if="micTestResult" class="test-result">
            <div :class="['test-status', micTestResult.success ? 'success' : 'error']">
              <p>{{ micTestResult.message }}</p>
              <p v-if="micTestResult.details" class="test-details">{{ micTestResult.details }}</p>
            </div>
          </section>

          <!-- Navigation -->
          <nav class="navigation-controls">
            <button 
              @click="nextQuestion" 
              :disabled="!currentAnswer && currentQuestion.required"
              class="btn-next"
            >
              {{ isLastQuestionInPage ? (isLastPage ? 'Terminer' : 'Page suivante') : 'Question suivante' }}
            </button>
          </nav>

        </article>

        <!-- Erreurs -->
        <div v-if="error" class="error-message">
          {{ error }}
        </div>

      </div>
    </div>
  </div>
</template>

<script>
import { voiceService } from '@/_services';

export default {
  name: 'VoiceInterviewSimple',
  
  data() {
    return {
      // État de base
      isSupported: false,
      isListening: false,
      isProcessing: false,
      isTestingMic: false,
      
      // Microphone SIMPLE
      availableMicrophones: [],
      selectedMicrophoneId: '',
      
      // Réponses et erreurs
      currentAnswer: '',
      error: '',
      micTestResult: null,
      
      // Timer
      timeLeft: 60,
      timerInterval: null,
      
      // Questions (exemple simple)
      currentPageIndex: 0,
      currentQuestionInPage: 0,
      
      questions: [
        [
          { text: "Quel est votre prénom ?", required: true, hint: "Dites simplement votre prénom" },
          { text: "Quel est votre nom de famille ?", required: true },
          { text: "Quel est votre email ?", required: true, hint: "Épeler votre adresse email" }
        ],
        [
          { text: "Quelle est votre date de naissance ?", required: true },
          { text: "Quel est votre genre ?", required: true }
        ]
      ]
    };
  },

  computed: {
    currentQuestion() {
      const page = this.questions[this.currentPageIndex];
      return page ? page[this.currentQuestionInPage] : null;
    },
    
    totalPages() {
      return this.questions.length;
    },
    
    progressPercentage() {
      const totalQuestions = this.questions.flat().length;
      const answeredQuestions = this.currentPageIndex * this.questions[0].length + this.currentQuestionInPage;
      return Math.round((answeredQuestions / totalQuestions) * 100);
    },
    
    isLastQuestionInPage() {
      const page = this.questions[this.currentPageIndex];
      return this.currentQuestionInPage >= page.length - 1;
    },
    
    isLastPage() {
      return this.currentPageIndex >= this.questions.length - 1;
    }
  },

  mounted() {
    this.checkVoiceSupport();
    this.loadMicrophones();
    this.loadSavedMicrophone();
  },

  methods: {
    checkVoiceSupport() {
      this.isSupported = voiceService.checkSupport();
      console.log('🔍 Support vocal:', this.isSupported);
    },

    async loadMicrophones() {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        const devices = await navigator.mediaDevices.enumerateDevices();
        
        this.availableMicrophones = devices
          .filter(device => device.kind === 'audioinput')
          .map(device => ({
            deviceId: device.deviceId,
            label: device.label || `Microphone ${device.deviceId.substring(0, 8)}`
          }));

        console.log('🎤 Microphones trouvés:', this.availableMicrophones);
      } catch (error) {
        console.error('❌ Erreur microphones:', error);
      }
    },

    loadSavedMicrophone() {
      const saved = localStorage.getItem('meetvoice_microphone');
      if (saved && this.availableMicrophones.find(m => m.deviceId === saved)) {
        this.selectedMicrophoneId = saved;
        console.log('📂 Microphone sauvegardé chargé:', saved);
      }
    },

    saveMicrophoneChoice() {
      localStorage.setItem('meetvoice_microphone', this.selectedMicrophoneId);
      console.log('💾 Microphone sauvegardé:', this.selectedMicrophoneId);
    },

    async toggleListening() {
      if (this.isListening) {
        this.stopListening();
      } else {
        await this.startListening();
      }
    },

    async startListening() {
      try {
        this.isProcessing = true;
        this.error = '';
        this.timeLeft = 60;
        
        console.log('🎤 Démarrage écoute...');
        
        const result = await voiceService.startListening({
          timeout: 60000,
          silenceTimeout: 4000,
          language: 'fr-FR',
          microphoneId: this.selectedMicrophoneId,
          onStart: () => {
            this.isListening = true;
            this.startTimer();
          },
          onInterim: (text) => {
            this.currentAnswer = text;
          }
        });

        this.currentAnswer = result;
        console.log('✅ Réponse:', result);

      } catch (error) {
        console.error('❌ Erreur écoute:', error);
        this.error = error.message;
      } finally {
        this.isProcessing = false;
        this.isListening = false;
        this.stopTimer();
      }
    },

    stopListening() {
      voiceService.stopListening();
      this.isListening = false;
      this.stopTimer();
    },

    startTimer() {
      this.timerInterval = setInterval(() => {
        this.timeLeft--;
        if (this.timeLeft <= 0) {
          this.stopListening();
        }
      }, 1000);
    },

    stopTimer() {
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
        this.timerInterval = null;
      }
    },

    async testMicrophone() {
      try {
        this.isTestingMic = true;
        this.micTestResult = null;
        
        const result = await voiceService.testMicrophone(this.selectedMicrophoneId);
        
        if (result.success) {
          this.micTestResult = {
            success: true,
            message: `✅ Microphone OK ! Niveau: ${(result.testResults.maxAudioLevel * 100).toFixed(1)}%`
          };
        } else {
          this.micTestResult = {
            success: false,
            message: `❌ Problème microphone: ${result.message}`
          };
        }
      } catch (error) {
        this.micTestResult = {
          success: false,
          message: `❌ Erreur test: ${error.message}`
        };
      } finally {
        this.isTestingMic = false;
      }
    },

    async playAnswer() {
      if (!this.currentAnswer) return;
      
      try {
        await voiceService.speakText(this.currentAnswer);
      } catch (error) {
        console.error('❌ Erreur lecture:', error);
      }
    },

    clearAnswer() {
      this.currentAnswer = '';
    },

    nextQuestion() {
      if (!this.isLastQuestionInPage) {
        this.currentQuestionInPage++;
        this.currentAnswer = '';
      } else if (!this.isLastPage) {
        this.currentPageIndex++;
        this.currentQuestionInPage = 0;
        this.currentAnswer = '';
      } else {
        this.completeInterview();
      }
    },

    completeInterview() {
      console.log('🎉 Interview terminée !');
      this.$emit('interview-complete');
    }
  }
};
</script>

<style scoped>
.voice-interview-container {
  display: flex;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.left-panel {
  flex: 1;
  padding: 40px;
  color: white;
}

.progress-container {
  margin-bottom: 30px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #4ade80;
  transition: width 0.3s ease;
}

.progress-text {
  display: block;
  margin-top: 8px;
  font-size: 14px;
  opacity: 0.8;
}

.question-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 30px;
  margin-bottom: 20px;
}

.question-text h3 {
  font-size: 24px;
  margin-bottom: 10px;
}

.question-hint {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 20px;
}

.voice-controls {
  margin: 30px 0;
}

.main-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
}

.btn-microphone {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
  background: #ef4444;
  color: white;
  font-size: 32px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-microphone:hover {
  transform: scale(1.1);
}

.btn-microphone.listening {
  background: #22c55e;
  animation: pulse 1s infinite;
}

.btn-test {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background: #8b5cf6;
  color: white;
  font-size: 20px;
  cursor: pointer;
}

.microphone-selector {
  text-align: center;
  margin-bottom: 20px;
}

.mic-select {
  padding: 10px 15px;
  border-radius: 8px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  min-width: 250px;
}

.listening-status {
  text-align: center;
  font-size: 18px;
}

.pulse {
  animation: pulse 1s infinite;
}

.answer-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
}

.answer-text {
  background: rgba(255, 255, 255, 0.1);
  padding: 15px;
  border-radius: 8px;
  margin: 10px 0;
}

.answer-actions {
  display: flex;
  gap: 10px;
}

.btn-play, .btn-clear {
  padding: 8px 15px;
  border: none;
  border-radius: 6px;
  background: #6366f1;
  color: white;
  cursor: pointer;
}

.test-result {
  margin: 20px 0;
}

.test-status {
  padding: 15px;
  border-radius: 8px;
}

.test-status.success {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid #22c55e;
}

.test-status.error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid #ef4444;
}

.navigation-controls {
  text-align: center;
  margin-top: 30px;
}

.btn-next {
  padding: 15px 30px;
  background: #22c55e;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

.btn-next:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

.error-message {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid #ef4444;
  padding: 15px;
  border-radius: 8px;
  margin: 20px 0;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}
</style>
