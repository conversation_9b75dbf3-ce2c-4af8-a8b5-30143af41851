/** @format */

/**
 * Service de reconnaissance vocale utilisant l'API Web Speech
 * Fournit des fonctionnalités de reconnaissance vocale et de synthèse vocale
 * Utilise l'API TTS MeetVoice pour une meilleure qualité vocale
 */

// Import des services TTS et STT HYBRIDE
import ttsService from './tts.service.js';
import Axios from './caller.service.js';
import hybridSttService from './hybrid-stt.service.js';
// Services individuels disponibles si besoin
import simpleSttService from './simple-stt.service.js';
import voskSttService from './vosk-stt.service.js';
import aiExtractionService from './ai-extraction.service.js';
// Ancien service désactivé
// import sttService from './stt.service.js'; // Temporairement désactivé

// Service STT Backend complet - UTILISE SEULEMENT LES APIs VOSK
const sttService = {
  baseUrl: '/api/vosk/speech-to-text/', // Utilise SEULEMENT l'API Vosk
  isRecording: false,
  mediaRecorder: null,
  audioChunks: [],
  stream: null,

  async checkAvailability() {
    try {
      // Utiliser l'API Vosk status au lieu de stt-languages
      const response = await Axios.get('/api/vosk/status/');
      return response.status === 200;
    } catch (error) {
      console.warn('⚠️ API Vosk non disponible:', error.message);
      return false;
    }
  },

  isListening() {
    return this.isRecording;
  },

  stopListening() {
    if (this.isRecording && this.mediaRecorder) {
      console.log('🛑 Arrêt STT backend');
      this.isRecording = false;
      this.mediaRecorder.stop();
    }
  },

  async startListening(options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        console.log('🚀 Démarrage STT backend...');

        // Configuration microphone
        const constraints = {
          audio: {
            deviceId: options.microphoneId ? { exact: options.microphoneId } : undefined,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true,
            sampleRate: 16000,
            channelCount: 1
          }
        };

        // Obtenir accès microphone
        this.stream = await navigator.mediaDevices.getUserMedia(constraints);
        console.log('🎤 Accès microphone STT backend obtenu');

        // Initialiser MediaRecorder
        const mimeType = this.getSupportedMimeType();
        this.mediaRecorder = new MediaRecorder(this.stream, {
          mimeType: mimeType,
          audioBitsPerSecond: 128000
        });

        this.audioChunks = [];
        this.isRecording = true;

        // Événements MediaRecorder
        this.mediaRecorder.ondataavailable = (event) => {
          if (event.data.size > 0) {
            this.audioChunks.push(event.data);
          }
        };

        this.mediaRecorder.onstop = async () => {
          try {
            console.log('🎤 Enregistrement terminé, envoi au backend STT...');

            const audioBlob = new Blob(this.audioChunks, { type: mimeType });
            const transcription = await this.sendAudioToBackend(audioBlob, options);

            this.cleanup();
            resolve(transcription);

          } catch (error) {
            this.cleanup();
            reject(error);
          }
        };

        this.mediaRecorder.onerror = (error) => {
          console.error('❌ Erreur MediaRecorder STT:', error);
          this.cleanup();
          reject(new Error(`Erreur enregistrement STT: ${error.message}`));
        };

        // Démarrer enregistrement
        this.mediaRecorder.start(1000);
        console.log('🎤 Enregistrement STT backend démarré');

        if (options.onStart) options.onStart();

        // Gestion timeout
        if (options.timeout === 0) {
          console.log('🔄 Mode manuel STT - enregistrement continu');
        } else {
          const timeout = options.timeout || 30000;
          setTimeout(() => {
            if (this.isRecording) {
              console.log('⏰ Timeout STT atteint');
              this.stopListening();
            }
          }, timeout);
        }

      } catch (error) {
        console.error('❌ Erreur démarrage STT backend:', error);
        this.cleanup();
        reject(error);
      }
    });
  },

  async sendAudioToBackend(audioBlob, options = {}) {
    try {
      console.log(`📤 Envoi audio API Vosk (${audioBlob.size} bytes)`);

      // Convertir en WAV pour l'API Vosk
      const wavBlob = await this.convertToWav(audioBlob);

      const formData = new FormData();
      formData.append('audio', wavBlob, 'recording.wav');
      formData.append('language', options.language || 'fr-FR');

      const response = await Axios.post(this.baseUrl, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      console.log('✅ Réponse STT backend:', response.data);

      const transcription = response.data.text || response.data.transcription || '';

      if (!transcription.trim()) {
        console.warn('⚠️ Aucun texte reconnu par STT backend');
        return '';
      }

      console.log('📝 Texte STT reconnu:', transcription);
      return transcription.trim();

    } catch (error) {
      console.error('❌ Erreur envoi audio STT backend:', error);
      const errorMessage = error.response?.data?.message || error.message;
      throw new Error(`Échec reconnaissance STT: ${errorMessage}`);
    }
  },

  getSupportedMimeType() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/wav'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        console.log('🎵 Type MIME STT:', type);
        return type;
      }
    }

    console.warn('⚠️ Type MIME STT par défaut');
    return 'audio/webm';
  },

  cleanup() {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }

    this.mediaRecorder = null;
    this.audioChunks = [];
    this.isRecording = false;

    console.log('🧹 Ressources STT nettoyées');
  },

  // Fonction de conversion WAV simple
  async convertToWav(audioBlob) {
    try {
      console.log('🔄 Conversion vers WAV pour API Vosk...');

      // Créer un WAV minimal valide
      const sampleRate = 16000;
      const duration = 1;
      const numSamples = sampleRate * duration;
      const numChannels = 1;
      const bitsPerSample = 16;

      const buffer = new ArrayBuffer(44 + numSamples * 2);
      const view = new DataView(buffer);

      // Header WAV
      const writeString = (offset, string) => {
        for (let i = 0; i < string.length; i++) {
          view.setUint8(offset + i, string.charCodeAt(i));
        }
      };

      writeString(0, 'RIFF');
      view.setUint32(4, 36 + numSamples * 2, true);
      writeString(8, 'WAVE');
      writeString(12, 'fmt ');
      view.setUint32(16, 16, true);
      view.setUint16(20, 1, true);
      view.setUint16(22, numChannels, true);
      view.setUint32(24, sampleRate, true);
      view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
      view.setUint16(32, numChannels * bitsPerSample / 8, true);
      view.setUint16(34, bitsPerSample, true);
      writeString(36, 'data');
      view.setUint32(40, numSamples * 2, true);

      // Données audio (silence pour l'instant)
      for (let i = 0; i < numSamples; i++) {
        view.setInt16(44 + i * 2, 0, true);
      }

      console.log('✅ WAV créé pour API Vosk');
      return new Blob([buffer], { type: 'audio/wav' });

    } catch (error) {
      console.warn('⚠️ Erreur conversion WAV, utilisation original:', error.message);
      return audioBlob;
    }
  }
};

class VoiceService {
  constructor() {
    this.recognition = null;
    this.isListening = false;
    this.isSupported = this.checkSupport();
    this.currentLanguage = 'fr-FR';
    this.selectedMicrophoneId = null;
    this.useBackendSTT = true; // Préférer le backend STT
    this.sttBackendAvailable = null; // Cache de disponibilité

    // Test initial de disponibilité du backend STT
    this.checkSTTBackendOnInit();
  }
  
  /**
   * Vérifier si l'API de reconnaissance vocale est supportée
   * @returns {boolean} True si supporté
   */
  checkSupport() {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    return !!SpeechRecognition;
  }

  /**
   * Vérification initiale du backend STT
   */
  async checkSTTBackendOnInit() {
    try {
      const available = await sttService.checkAvailability();
      this.sttBackendAvailable = available;
      console.log(`🔍 Backend STT ${available ? 'disponible' : 'indisponible'} au démarrage`);
    } catch (error) {
      this.sttBackendAvailable = false;
      console.warn('⚠️ Erreur vérification backend STT:', error.message);
    }
  }


  /**
   * Lire un texte à voix haute avec l'API TTS MeetVoice UNIQUEMENT
   * @param {string} text - Texte à lire
   * @param {Object} options - Options de lecture
   * @returns {Promise<void>}
   */
  async speakText(text, options = {}) {
    try {
      console.log('🎤 Synthèse vocale API TTS:', text.substring(0, 50) + '...');

      // Arrêter toute lecture en cours
      this.stopSpeaking();

      // Détecter l'ID de voix
      const voiceId = options.voiceId || options.voiceType || 4; // Jenny par défaut

      console.log('🎤 Utilisation API TTS backend avec voix ID:', voiceId, 'Speed:', options.speed, 'Pitch:', options.pitch);

      // Utiliser UNIQUEMENT l'API TTS du backend MeetVoice
      await ttsService.speak(text, {
        voiceId: voiceId,
        speed: options.speed || options.rate || 1.0,
        pitch: options.pitch || 1.0,
        language: 'fr',
        onStart: options.onStart,
        onEnd: options.onEnd,
        onError: options.onError
      });

      console.log('✅ Lecture terminée avec API TTS:', text.substring(0, 50) + '...');

    } catch (error) {
      console.error('❌ Erreur API TTS backend:', error);

      // Appeler le callback d'erreur si fourni
      if (options.onError) {
        options.onError(error);
      }

      // Relancer l'erreur pour que l'appelant puisse la gérer
      throw new Error(`API TTS backend indisponible: ${error.message}`);
    }
  }




  /**
   * Arrêter la synthèse vocale en cours (API TTS UNIQUEMENT)
   */
  stopSpeaking() {
    try {
      // Arrêter UNIQUEMENT l'API TTS backend
      ttsService.stopAll();
      console.log('🔇 API TTS backend arrêtée');
    } catch (error) {
      console.error('❌ Erreur lors de l\'arrêt de l\'API TTS:', error);
    }
  }

  
  /**
   * Initialiser la reconnaissance vocale
   * @param {Object} options - Options de configuration
   * @returns {Promise<SpeechRecognition>} Instance de reconnaissance vocale
   */
  initRecognition(options = {}) {
    return new Promise((resolve, reject) => {
      if (!this.isSupported) {
        reject(new Error('La reconnaissance vocale n\'est pas supportée par ce navigateur'));
        return;
      }
      
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      this.recognition = new SpeechRecognition();
      
      // Configuration optimisée pour la détection de parole
      this.recognition.continuous = options.continuous || false;
      this.recognition.interimResults = options.interimResults !== false; // true par défaut
      this.recognition.lang = options.language || this.currentLanguage;
      this.recognition.maxAlternatives = options.maxAlternatives || 3; // Plus d'alternatives

      // Paramètres pour améliorer la détection
      if (this.recognition.serviceURI) {
        console.log('🎤 Service de reconnaissance:', this.recognition.serviceURI);
      }

      // Configuration spécifique Chrome/WebKit
      if (window.webkitSpeechRecognition) {
        this.recognition.continuous = true; // Forcer continuous pour WebKit
        console.log('🌐 WebKit Speech Recognition détecté - mode continu forcé');
      }
      
      resolve(this.recognition);
    });
  }
  
  /**
   * Démarrer l'écoute vocale avec backend STT en priorité
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  startListening(options = {}) {
    console.log('🚀 Utilisation STT Hybride (Vosk + IA + Fallback)');
    return this.startListeningWithHybridSTT(options);
  }

  /**
   * Démarrer l'écoute avec STT Hybride (Vosk + IA + Fallback)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithHybridSTT(options = {}) {
    console.log('🚀 Démarrage STT hybride intelligent...');

    try {
      // Utiliser le service STT hybride
      return await hybridSttService.startListening({
        language: options.language || 'fr-FR',

        onStart: () => {
          this.isListening = true;
          console.log('✅ STT hybride démarré');
          if (options.onStart) options.onStart();
        },

        onResult: (finalText, interimText) => {
          console.log('📝 STT hybride résultat:', finalText);

          // Afficher les informations extraites
          const extractedInfo = hybridSttService.getExtractedInfo();
          if (extractedInfo.names.length > 0) {
            console.log('👤 Noms extraits:', extractedInfo.names);
          }

          if (options.onResult) {
            options.onResult(finalText, interimText);
          }
        },

        onInterim: (interimText) => {
          console.log('📝 STT hybride partiel:', interimText);
          if (options.onInterim) {
            options.onInterim(interimText);
          }
        },

        onEnd: () => {
          this.isListening = false;
          console.log('🛑 STT hybride terminé');

          // Afficher le résumé des informations extraites
          const summary = hybridSttService.getInfoSummary();
          if (summary && summary !== 'Aucune information personnelle extraite pour le moment.') {
            console.log('📋 Résumé des informations:', summary);
          }

          if (options.onEnd) options.onEnd();
        },

        onError: (error) => {
          this.isListening = false;
          console.error('❌ Erreur STT hybride:', error);
          if (options.onError) options.onError(error);
        }
      });

    } catch (error) {
      console.error('❌ Erreur démarrage STT hybride:', error);
      this.isListening = false;
      throw error;
    }
  }

  /**
   * Démarrer l'écoute avec STT Simple et Indépendant (BACKUP)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithSimpleSTT(options = {}) {
    console.log('🎤 Démarrage STT simple et indépendant...');

    try {
      // Utiliser le service STT simple
      return await simpleSttService.startListening({
        language: options.language || 'fr-FR',

        onStart: () => {
          this.isListening = true;
          console.log('✅ STT simple démarré');
          if (options.onStart) options.onStart();
        },

        onResult: (finalText, interimText) => {
          console.log('📝 STT résultat:', finalText);
          if (options.onResult) {
            options.onResult(finalText, interimText);
          }
        },

        onInterim: (interimText) => {
          console.log('📝 STT partiel:', interimText);
          if (options.onInterim) {
            options.onInterim(interimText);
          }
        },

        onEnd: () => {
          this.isListening = false;
          console.log('🛑 STT simple terminé');
          if (options.onEnd) options.onEnd();
        },

        onError: (error) => {
          this.isListening = false;
          console.error('❌ Erreur STT simple:', error);
          if (options.onError) options.onError(error);
        }
      });

    } catch (error) {
      console.error('❌ Erreur démarrage STT simple:', error);
      this.isListening = false;
      throw error;
    }
  }

  /**
   * Démarrer l'écoute avec API browser améliorée (DÉSACTIVÉ)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithBrowserImproved(options = {}) {
    console.log('🌐 Démarrage API browser améliorée...');

    return new Promise((resolve, reject) => {
      try {
        // Vérifier le support
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
          throw new Error('API Speech Recognition non supportée');
        }

        // Créer l'instance de reconnaissance
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        this.recognition = new SpeechRecognition();

        // Configuration améliorée
        this.recognition.continuous = true;
        this.recognition.interimResults = true;
        this.recognition.lang = options.language || 'fr-FR';
        this.recognition.maxAlternatives = 1;

        // Variables pour gérer la stabilité
        let finalTranscript = '';
        let restartTimeout = null;
        let isManualStop = false;

        // Événement de démarrage
        this.recognition.onstart = () => {
          this.isListening = true;
          console.log('✅ API browser démarrée');
          if (options.onStart) options.onStart();
        };

        // Événement de résultat
        this.recognition.onresult = (event) => {
          let interimTranscript = '';

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;

            if (event.results[i].isFinal) {
              finalTranscript += transcript + ' ';
              console.log('📝 Résultat final:', transcript);
              if (options.onResult) {
                options.onResult(transcript.trim(), '');
              }
            } else {
              interimTranscript += transcript;
              console.log('📝 Résultat partiel:', transcript);
              if (options.onInterim) {
                options.onInterim(transcript);
              }
            }
          }
        };

        // Événement d'erreur amélioré
        this.recognition.onerror = (event) => {
          console.warn('⚠️ Erreur reconnaissance:', event.error);

          // Ne pas redémarrer automatiquement sur certaines erreurs
          if (event.error === 'no-speech' || event.error === 'audio-capture') {
            console.log('🔄 Redémarrage automatique...');
            if (!isManualStop && this.isListening) {
              restartTimeout = setTimeout(() => {
                if (this.isListening) {
                  this.recognition.start();
                }
              }, 1000);
            }
          } else {
            this.isListening = false;
            if (options.onError) options.onError(event.error);
          }
        };

        // Événement de fin amélioré
        this.recognition.onend = () => {
          console.log('🛑 Reconnaissance terminée');

          // Redémarrage automatique si pas d'arrêt manuel
          if (!isManualStop && this.isListening) {
            console.log('🔄 Redémarrage automatique...');
            restartTimeout = setTimeout(() => {
              if (this.isListening) {
                this.recognition.start();
              }
            }, 500);
          } else {
            this.isListening = false;
            if (options.onEnd) options.onEnd();
          }
        };

        // Méthode d'arrêt manuel
        this.stopListening = () => {
          console.log('🛑 Arrêt manuel demandé');
          isManualStop = true;
          this.isListening = false;

          if (restartTimeout) {
            clearTimeout(restartTimeout);
            restartTimeout = null;
          }

          if (this.recognition) {
            this.recognition.stop();
          }
        };

        // Démarrer la reconnaissance
        this.recognition.start();
        resolve('Écoute démarrée avec API browser améliorée');

      } catch (error) {
        console.error('❌ Erreur API browser:', error);
        this.isListening = false;
        reject(error);
      }
    });
  }

  /**
   * Démarrer l'écoute avec Vosk STT (DÉSACTIVÉ TEMPORAIREMENT)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithVosk(options = {}) {
    console.log('🎤 Démarrage écoute avec Vosk STT');

    try {
      // Vérifier le support de Vosk
      if (!voskSTTService.isSupported()) {
        throw new Error('Vosk STT non supporté par ce navigateur');
      }

      // Démarrer l'écoute avec Vosk
      return await voskSTTService.startListening({
        modelSize: options.modelSize || 'small',
        language: options.language || 'fr-FR',
        continuous: options.continuous || true,

        onStart: () => {
          this.isListening = true;
          console.log('✅ Écoute Vosk démarrée');
          if (options.onStart) options.onStart();
        },

        onResult: (finalText, interimText) => {
          console.log('📝 Vosk résultat:', finalText);
          if (options.onResult) {
            options.onResult(finalText, interimText);
          }
        },

        onInterim: (interimText) => {
          console.log('📝 Vosk partiel:', interimText);
          if (options.onInterim) {
            options.onInterim(interimText);
          }
        },

        onEnd: () => {
          this.isListening = false;
          console.log('🛑 Écoute Vosk terminée');
          if (options.onEnd) options.onEnd();
        },

        onError: (error) => {
          this.isListening = false;
          console.error('❌ Erreur Vosk:', error);
          if (options.onError) options.onError(error);
        }
      });

    } catch (error) {
      console.error('❌ Erreur démarrage Vosk:', error);
      this.isListening = false;
      throw error;
    }
  }

  /**
   * Démarrer l'écoute avec le backend STT (DÉSACTIVÉ)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithBackend(options = {}) {
    console.log('🎤 Démarrage écoute avec backend STT');

    // Vérifier la disponibilité du backend
    const isAvailable = await sttService.checkAvailability();
    if (!isAvailable) {
      throw new Error('Backend STT non disponible');
    }

    this.sttBackendAvailable = true;

    // Démarrer l'écoute avec le service STT
    return await sttService.startListening({
      microphoneId: options.microphoneId,
      language: options.language || this.currentLanguage,
      timeout: options.timeout,
      onStart: () => {
        this.isListening = true;
        if (options.onStart) options.onStart();
      },
      onEnd: () => {
        this.isListening = false;
        if (options.onEnd) options.onEnd();
      }
    });
  }

  /**
   * Démarrer l'écoute avec l'API browser (méthode existante renommée)
   * @param {Object} options - Options d'écoute
   * @returns {Promise<string>} Texte reconnu
   */
  async startListeningWithBrowser(options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        // Si un microphone spécifique est demandé, s'assurer qu'il est accessible
        if (options.microphoneId) {
          try {
            const constraints = {
              audio: {
                deviceId: { exact: options.microphoneId },
                echoCancellation: true,
                noiseSuppression: true,
                autoGainControl: true
              }
            };
            const stream = await navigator.mediaDevices.getUserMedia(constraints);
            // Fermer immédiatement le stream, on voulait juste vérifier l'accès
            stream.getTracks().forEach(track => track.stop());
            console.log('🎤 Microphone configuré accessible:', options.microphoneId);

            // Mettre à jour le microphone sélectionné dans le service
            this.selectedMicrophoneId = options.microphoneId;
          } catch (error) {
            console.warn('⚠️ Microphone configuré inaccessible, utilisation du microphone par défaut');
            console.warn('Erreur microphone:', error.message);
            // Réinitialiser l'ID du microphone en cas d'erreur
            this.selectedMicrophoneId = null;
          }
        }

        // Vérifier le support de l'API Speech Recognition
        if (!('webkitSpeechRecognition' in window) && !('SpeechRecognition' in window)) {
          throw new Error('API Speech Recognition non supportée par ce navigateur');
        }

        if (!this.recognition) {
          await this.initRecognition(options);
        }

        if (this.isListening) {
          console.warn('⚠️ Écoute déjà en cours, arrêt de l\'écoute précédente');
          this.stopListening();
          // Attendre un peu avant de redémarrer
          await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        let finalTranscript = '';
        let timeoutId = null;
        let extendedTimeoutId = null;
        let lastSpeechTime = Date.now();
        let speechDetected = false;

        // Timeout configurable - 0 = pas de timeout (mode manuel)
        const timeout = options.timeout !== undefined ? options.timeout : 90000; // 90 secondes par défaut, 0 = manuel
        const silenceTimeout = options.silenceTimeout !== undefined ? options.silenceTimeout : 8000; // 8 secondes par défaut, 0 = pas de silence timeout
        const maxExtensions = options.maxExtensions !== undefined ? options.maxExtensions : 3; // 3 par défaut, 0 = pas d'extensions

        console.log('🎤 Configuration reconnaissance:', {
          timeout,
          silenceTimeout,
          maxExtensions,
          continuous: options.continuous
        });
        let extensionCount = 0;
        
        this.recognition.onstart = () => {
          this.isListening = true;
          lastSpeechTime = Date.now();
          console.log('🎤 Reconnaissance démarrée - surveillance des erreurs réseau activée');
          if (options.onStart) options.onStart();

          // Démarrer le timeout principal SEULEMENT si timeout > 0
          if (timeout > 0) {
            timeoutId = setTimeout(() => {
              if (!speechDetected) {
                this.stopListening();
                reject(new Error('Timeout: aucune parole détectée dans les délais'));
              } else {
                // Si de la parole a été détectée, donner plus de temps
                this.extendListeningTime(options, resolve, reject);
              }
            }, timeout);
          } else {
            console.log('🔄 Mode manuel activé - PAS DE TIMEOUT');
          }

          // Notifier le début avec informations de timeout
          if (options.onTimeoutInfo) {
            options.onTimeoutInfo({
              initialTimeout: timeout,
              remainingTime: timeout,
              canExtend: true
            });
          }
        };

        // Fonction pour étendre le temps d'écoute
        this.extendListeningTime = (opts, res, rej) => {
          if (extensionCount < maxExtensions) {
            extensionCount++;
            const extensionTime = 30000; // 30 secondes supplémentaires

            if (extendedTimeoutId) clearTimeout(extendedTimeoutId);

            extendedTimeoutId = setTimeout(() => {
              this.stopListening();
              res(finalTranscript || 'Temps d\'écoute dépassé');
            }, extensionTime);

            if (opts.onTimeoutExtended) {
              opts.onTimeoutExtended({
                extension: extensionCount,
                maxExtensions: maxExtensions,
                additionalTime: extensionTime
              });
            }
          } else {
            this.stopListening();
            res(finalTranscript || 'Temps d\'écoute maximum atteint');
          }
        };
        
        this.recognition.onresult = (event) => {
          let interimTranscript = '';
          speechDetected = true;
          lastSpeechTime = Date.now();

          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript;

            if (event.results[i].isFinal) {
              finalTranscript += transcript;

              // Démarrer un timer de silence SEULEMENT si silenceTimeout > 0
              if (silenceTimeout > 0) {
                if (extendedTimeoutId) clearTimeout(extendedTimeoutId);
                extendedTimeoutId = setTimeout(() => {
                  // Si pas de nouvelle parole après le silence, terminer
                  if (Date.now() - lastSpeechTime > silenceTimeout) {
                    this.stopListening();
                    resolve(finalTranscript);
                  }
                }, silenceTimeout);
              }

            } else {
              interimTranscript += transcript;
            }
          }

          // Notifier les callbacks
          if (options.onInterim) {
            options.onInterim(interimTranscript);
          }

          if (options.onResult) {
            options.onResult(finalTranscript, interimTranscript);
          }

          // Notifier l'activité vocale détectée
          if (options.onSpeechDetected && speechDetected) {
            options.onSpeechDetected({
              finalTranscript,
              interimTranscript,
              lastSpeechTime,
              timeRemaining: timeout - (Date.now() - lastSpeechTime)
            });
          }
        };
        
        this.recognition.onend = () => {
          this.isListening = false;
          if (timeoutId) clearTimeout(timeoutId);
          if (extendedTimeoutId) clearTimeout(extendedTimeoutId);

          if (options.onEnd) {
            options.onEnd({
              finalTranscript,
              speechDetected,
              extensionsUsed: extensionCount,
              totalListeningTime: Date.now() - lastSpeechTime
            });
          }

          if (finalTranscript.trim()) {
            resolve(finalTranscript.trim());
          } else if (speechDetected) {
            resolve(''); // Parole détectée mais pas de transcription claire
          } else if (timeout === 0) {
            // Mode manuel - pas d'erreur si pas de parole, juste résoudre vide
            resolve('');
          } else {
            reject(new Error('Aucune parole détectée dans les délais'));
          }
        };
        
        this.recognition.onerror = (event) => {
          this.isListening = false;
          if (timeoutId) clearTimeout(timeoutId);

          let errorMessage = 'Erreur de reconnaissance vocale';
          let shouldRetry = false;

          switch (event.error) {
            case 'no-speech':
              console.log('🔇 Aucune parole détectée par l\'API');

              // En mode manuel (timeout=0), ne pas considérer comme une erreur
              if (timeout === 0) {
                console.log('🔄 Mode manuel - pas d\'erreur pour no-speech');
                this.stopListening();
                resolve(finalTranscript || '');
                return;
              }

              // Retourner le transcript actuel s'il y en a un
              if (finalTranscript.trim()) {
                console.log('✅ Récupération du texte existant malgré no-speech');
                this.stopListening();
                resolve(finalTranscript);
                return;
              }

              errorMessage = 'Aucune parole détectée. Parlez plus fort ou rapprochez-vous du microphone.';
              shouldRetry = true; // Permettre retry pour no-speech
              break;
            case 'audio-capture':
              errorMessage = 'Impossible d\'accéder au microphone';
              break;
            case 'not-allowed':
              errorMessage = 'Permission microphone refusée';
              break;
            case 'network':
              // Erreur réseau - très fréquente avec l'API Speech Recognition
              console.log('🌐 Erreur réseau détectée - mode de récupération activé');

              // Si on a du contenu, le retourner TOUJOURS
              if (finalTranscript.trim()) {
                console.log('✅ Récupération du texte existant:', finalTranscript);
                this.stopListening();
                resolve(finalTranscript);
                return;
              }

              // Même sans contenu, en mode manuel (timeout=0), ne pas échouer
              if (timeout === 0) {
                console.log('🔄 Mode manuel - résolution avec chaîne vide au lieu d\'erreur');
                this.stopListening();
                resolve('');
                return;
              }

              errorMessage = 'Erreur réseau - L\'API Speech Recognition a des limitations. Utilisez le bouton de test microphone.';
              shouldRetry = true;
              break;
            case 'aborted':
              errorMessage = 'Reconnaissance interrompue';
              // Si on a du contenu, le retourner
              if (finalTranscript.trim()) {
                this.stopListening();
                resolve(finalTranscript);
                return;
              }
              break;
          }

          // Retry automatique pour les erreurs réseau
          if (shouldRetry && (!options.retryCount || options.retryCount < 3)) {
            const retryCount = (options.retryCount || 0) + 1;
            // Délai plus court pour les erreurs réseau
            const delay = event.error === 'network' ? retryCount * 800 : retryCount * 1500; // 0.8s, 1.6s, 2.4s pour network

            console.log(`🔄 Tentative de reconnexion ${retryCount}/3 dans ${delay}ms... (erreur: ${event.error})`);

            setTimeout(() => {
              // Réinitialiser complètement le service
              this.reset();

              this.startListening({
                ...options,
                retryCount: retryCount
              }).then(resolve).catch(reject);
            }, delay);
            return;
          }

          if (options.onError) options.onError(event);
          reject(new Error(errorMessage));
        };
        
        // Démarrer la reconnaissance avec gestion d'erreur
        try {
          console.log('🎤 Démarrage de la reconnaissance vocale...');
          this.recognition.start();
          this.isListening = true;

          // Callback de démarrage
          if (options.onStart) {
            setTimeout(() => options.onStart(), 100); // Petit délai pour s'assurer que la reconnaissance a démarré
          }

        } catch (startError) {
          console.error('❌ Erreur au démarrage de la reconnaissance:', startError);
          this.isListening = false;
          reject(new Error(`Impossible de démarrer la reconnaissance vocale: ${startError.message}`));
        }

      } catch (error) {
        reject(error);
      }
    });
  }
  
  /**
   * Arrêter l'écoute vocale (backend STT ou browser API)
   */
  stopListening() {
    console.log('🛑 Arrêt de l\'écoute vocale...');

    try {
      // Utiliser le service STT hybride pour arrêter
      hybridSttService.stopListening();
      console.log('🛑 STT hybride arrêté');
    } catch (error) {
      console.warn('⚠️ Erreur arrêt STT hybride:', error.message);
    }

    this.isListening = false;
  }

  /**
   * Obtenir les informations extraites par l'IA
   * @returns {Object} Informations extraites
   */
  getExtractedInfo() {
    return hybridSttService.getExtractedInfo();
  }

  /**
   * Obtenir un résumé des informations extraites
   * @returns {string} Résumé textuel
   */
  getInfoSummary() {
    return hybridSttService.getInfoSummary();
  }

  /**
   * Obtenir le statut du système hybride
   * @returns {Object} Statut complet
   */
  getHybridStatus() {
    return hybridSttService.getStatus();
  }

  /**
   * Configurer l'extraction automatique
   * @param {boolean} enabled - Activer/désactiver
   */
  setAutoExtraction(enabled) {
    hybridSttService.setAutoExtraction(enabled);
  }

  /**
   * Configurer l'utilisation de Vosk
   * @param {boolean} enabled - Activer/désactiver Vosk
   */
  setVoskEnabled(enabled) {
    hybridSttService.setVoskEnabled(enabled);
  }

  /**
   * Réinitialiser le service de reconnaissance vocale
   */
  reset() {
    console.log('🔄 Reset du service de reconnaissance vocale');

    // Arrêter l'écoute en cours
    if (this.isListening) {
      this.stopListening();
    }

    // Réinitialiser l'instance
    this.recognition = null;
    this.isListening = false;

    // Recréer une nouvelle instance si supportée
    if (this.isSupported) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognition) {
        this.recognition = new SpeechRecognition();
        console.log('✅ Nouvelle instance de reconnaissance créée');
      }
    }
  }

  /**
   * Étendre manuellement le temps d'écoute
   * @param {number} additionalTime - Temps supplémentaire en millisecondes
   * @returns {boolean} True si l'extension a réussi
   */
  extendListeningTimeManually(additionalTime = 30000) {
    if (!this.isListening) {
      return false;
    }

    // Cette méthode sera appelée depuis le composant
    // pour étendre le temps d'écoute à la demande de l'utilisateur
    console.log(`Extension manuelle du temps d'écoute: +${additionalTime}ms`);
    return true;
  }

  /**
   * Obtenir le statut actuel de l'écoute
   * @returns {Object} Statut de l'écoute
   */
  getListeningStatus() {
    return {
      isListening: this.isListening,
      isSupported: this.isSupported,
      isSpeaking: this.isSpeaking()
    };
  }

  /**
   * Vérifier si une synthèse vocale est en cours (toujours false car on utilise l'API TTS)
   * @returns {boolean} False car l'API TTS gère sa propre lecture
   */
  isSpeaking() {
    return false; // L'API TTS gère sa propre lecture
  }
  
  /**
   * Demander la permission d'accès au microphone avec diagnostic détaillé
   * @returns {Promise<Object>} Résultat avec statut et informations détaillées
   */
  async requestMicrophonePermission() {
    try {
      // Vérifier d'abord si l'API est disponible
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        return {
          success: false,
          error: 'API_NOT_SUPPORTED',
          message: 'Votre navigateur ne supporte pas l\'accès au microphone. Utilisez Chrome, Firefox, Safari ou Edge.',
          details: 'navigator.mediaDevices.getUserMedia non disponible'
        };
      }

      // Tenter d'obtenir la permission avec le microphone sélectionné
      const constraints = {
        audio: {
          deviceId: this.selectedMicrophoneId ? { exact: this.selectedMicrophoneId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      // Vérifier que le stream contient des pistes audio
      const audioTracks = stream.getAudioTracks();
      if (audioTracks.length === 0) {
        stream.getTracks().forEach(track => track.stop());
        return {
          success: false,
          error: 'NO_AUDIO_TRACKS',
          message: 'Aucun microphone détecté. Vérifiez qu\'un microphone est connecté.',
          details: 'Stream obtenu mais aucune piste audio'
        };
      }

      // Vérifier les propriétés du microphone
      const audioTrack = audioTracks[0];
      const settings = audioTrack.getSettings();

      // Fermer le stream
      stream.getTracks().forEach(track => track.stop());

      return {
        success: true,
        message: 'Microphone détecté et accessible',
        deviceInfo: {
          label: audioTrack.label || 'Microphone par défaut',
          deviceId: settings.deviceId,
          sampleRate: settings.sampleRate,
          channelCount: settings.channelCount,
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseSuppression,
          autoGainControl: settings.autoGainControl
        }
      };

    } catch (error) {
      console.error('Erreur permission microphone:', error);

      let errorType = 'UNKNOWN_ERROR';
      let message = 'Erreur inconnue lors de l\'accès au microphone';
      let details = error.message;

      switch (error.name) {
        case 'NotAllowedError':
          errorType = 'PERMISSION_DENIED';
          message = 'Permission microphone refusée. Cliquez sur l\'icône de microphone dans la barre d\'adresse pour autoriser l\'accès.';
          break;
        case 'NotFoundError':
          errorType = 'NO_DEVICE_FOUND';
          message = 'Aucun microphone trouvé. Vérifiez qu\'un microphone est connecté et reconnu par votre système.';
          break;
        case 'NotReadableError':
          errorType = 'DEVICE_IN_USE';
          message = 'Microphone déjà utilisé par une autre application. Fermez les autres applications utilisant le microphone.';
          break;
        case 'OverconstrainedError':
          errorType = 'CONSTRAINTS_ERROR';
          message = 'Configuration microphone non supportée. Essayez avec un autre microphone.';
          break;
        case 'SecurityError':
          errorType = 'SECURITY_ERROR';
          message = 'Accès microphone bloqué pour des raisons de sécurité. Vérifiez les paramètres de votre navigateur.';
          break;
        case 'TypeError':
          errorType = 'TYPE_ERROR';
          message = 'Erreur de configuration. Rechargez la page et réessayez.';
          break;
      }

      return {
        success: false,
        error: errorType,
        message,
        details,
        troubleshooting: this.getMicrophoneTroubleshooting(errorType)
      };
    }
  }

  /**
   * Obtenir des conseils de dépannage pour les problèmes de microphone
   * @param {string} errorType - Type d'erreur
   * @returns {Array} Liste de conseils
   */
  getMicrophoneTroubleshooting(errorType) {
    const commonTips = [
      'Vérifiez que votre microphone est correctement connecté',
      'Redémarrez votre navigateur',
      'Vérifiez les paramètres de confidentialité de votre navigateur'
    ];

    const specificTips = {
      'PERMISSION_DENIED': [
        'Cliquez sur l\'icône de microphone dans la barre d\'adresse',
        'Sélectionnez "Toujours autoriser" pour ce site',
        'Vérifiez les paramètres de confidentialité de votre navigateur',
        'Essayez en navigation privée pour tester'
      ],
      'NO_DEVICE_FOUND': [
        'Vérifiez que votre microphone est connecté',
        'Testez votre microphone dans les paramètres système',
        'Essayez de débrancher et rebrancher votre microphone',
        'Vérifiez les pilotes de votre microphone'
      ],
      'DEVICE_IN_USE': [
        'Fermez les autres applications utilisant le microphone',
        'Vérifiez qu\'aucun autre onglet n\'utilise le microphone',
        'Redémarrez votre navigateur',
        'Redémarrez votre ordinateur si nécessaire'
      ],
      'API_NOT_SUPPORTED': [
        'Utilisez un navigateur moderne (Chrome, Firefox, Safari, Edge)',
        'Mettez à jour votre navigateur',
        'Vérifiez que vous êtes sur une connexion HTTPS'
      ]
    };

    return [...(specificTips[errorType] || []), ...commonTips];
  }
  
  /**
   * Énumérer tous les périphériques audio disponibles
   * @returns {Promise<Object>} Liste des périphériques audio
   */
  async enumerateAudioDevices() {
    try {
      // Demander les permissions d'abord pour obtenir les labels
      const permissionResult = await this.requestMicrophonePermission();
      if (!permissionResult.success) {
        return {
          success: false,
          error: permissionResult.error,
          message: permissionResult.message,
          devices: { audioInput: [], audioOutput: [] }
        };
      }

      // Obtenir la liste des périphériques
      const devices = await navigator.mediaDevices.enumerateDevices();

      const audioInputDevices = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId
        }));

      const audioOutputDevices = devices
        .filter(device => device.kind === 'audiooutput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Haut-parleur ${device.deviceId.slice(0, 8)}`,
          groupId: device.groupId
        }));

      return {
        success: true,
        devices: {
          audioInput: audioInputDevices,
          audioOutput: audioOutputDevices
        },
        defaultDevices: {
          audioInput: audioInputDevices.find(d => d.deviceId === 'default') || audioInputDevices[0],
          audioOutput: audioOutputDevices.find(d => d.deviceId === 'default') || audioOutputDevices[0]
        }
      };

    } catch (error) {
      console.error('Erreur énumération périphériques:', error);
      return {
        success: false,
        error: 'ENUMERATION_FAILED',
        message: 'Impossible d\'énumérer les périphériques audio',
        details: error.message,
        devices: { audioInput: [], audioOutput: [] }
      };
    }
  }

  /**
   * Tester un microphone spécifique
   * @param {string} deviceId - ID du périphérique à tester
   * @returns {Promise<Object>} Résultat du test
   */
  async testMicrophone(deviceId = null) {
    try {
      // Utiliser le microphone sélectionné par défaut si aucun deviceId spécifié
      const micId = deviceId || this.selectedMicrophoneId;

      const constraints = {
        audio: {
          deviceId: micId ? { exact: micId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      const audioTracks = stream.getAudioTracks();

      if (audioTracks.length === 0) {
        stream.getTracks().forEach(track => track.stop());
        return {
          success: false,
          error: 'NO_AUDIO_TRACKS',
          message: 'Aucune piste audio détectée'
        };
      }

      const audioTrack = audioTracks[0];
      const settings = audioTrack.getSettings();

      // Tester le niveau audio pendant 2 secondes
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);

      microphone.connect(analyser);
      analyser.fftSize = 256;

      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      let maxLevel = 0;
      let samples = 0;

      const testDuration = 2000; // 2 secondes
      const sampleInterval = 100; // Échantillonner toutes les 100ms

      const testPromise = new Promise((resolve) => {
        const interval = setInterval(() => {
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / bufferLength;
          const level = average / 255;

          if (level > maxLevel) maxLevel = level;
          samples++;

          if (samples * sampleInterval >= testDuration) {
            clearInterval(interval);
            resolve();
          }
        }, sampleInterval);
      });

      await testPromise;

      // Nettoyer
      stream.getTracks().forEach(track => track.stop());
      audioContext.close();

      return {
        success: true,
        message: 'Test microphone réussi',
        deviceInfo: {
          label: audioTrack.label,
          deviceId: settings.deviceId,
          sampleRate: settings.sampleRate,
          channelCount: settings.channelCount,
          echoCancellation: settings.echoCancellation,
          noiseSuppression: settings.noiseSuppression,
          autoGainControl: settings.autoGainControl
        },
        testResults: {
          maxAudioLevel: maxLevel,
          isWorking: maxLevel > 0.005, // Seuil PLUS BAS pour détecter les micros faibles
          quality: maxLevel > 0.08 ? 'excellent' : maxLevel > 0.04 ? 'good' : maxLevel > 0.01 ? 'fair' : 'poor'
        }
      };

    } catch (error) {
      console.error('Erreur test microphone:', error);
      return {
        success: false,
        error: error.name || 'TEST_FAILED',
        message: `Échec du test microphone: ${error.message}`,
        details: error.message
      };
    }
  }

  /**
   * Obtenir un analyseur audio pour le niveau du microphone
   * @param {string} deviceId - ID du périphérique (optionnel)
   * @returns {Promise<Object>} Analyseur audio avec méthodes
   */
  async getAudioAnalyser(deviceId = null) {
    try {
      const constraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const analyser = audioContext.createAnalyser();
      const microphone = audioContext.createMediaStreamSource(stream);

      microphone.connect(analyser);
      analyser.fftSize = 256;

      const bufferLength = analyser.frequencyBinCount;
      const dataArray = new Uint8Array(bufferLength);

      return {
        stream,
        audioContext,
        analyser,
        dataArray,
        getLevel: () => {
          analyser.getByteFrequencyData(dataArray);
          const average = dataArray.reduce((a, b) => a + b) / bufferLength;
          return average / 255; // Normaliser entre 0 et 1
        },
        stop: () => {
          stream.getTracks().forEach(track => track.stop());
          audioContext.close();
        }
      };
    } catch (error) {
      throw new Error(`Impossible d'accéder au microphone: ${error.message}`);
    }
  }

  /**
   * Définir le microphone à utiliser
   * @param {string} deviceId - ID du périphérique microphone
   * @returns {Promise<boolean>} True si le changement a réussi
   */
  async setMicrophone(deviceId) {
    try {
      console.log('🎤 Configuration du microphone:', deviceId);

      // Tester l'accès au microphone spécifié
      const constraints = {
        audio: {
          deviceId: deviceId ? { exact: deviceId } : undefined,
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);

      // Fermer le stream de test
      stream.getTracks().forEach(track => track.stop());

      // Sauvegarder l'ID du microphone sélectionné
      this.selectedMicrophoneId = deviceId;

      console.log('✅ Microphone configuré avec succès:', deviceId);
      return true;

    } catch (error) {
      console.error('❌ Erreur lors de la configuration du microphone:', error);
      throw new Error(`Impossible de configurer le microphone: ${error.message}`);
    }
  }

  /**
   * Obtenir l'ID du microphone actuellement sélectionné
   * @returns {string|null} ID du microphone ou null
   */
  getSelectedMicrophone() {
    return this.selectedMicrophoneId;
  }

  /**
   * Obtenir la liste des microphones disponibles
   * @returns {Promise<Array>} Liste des microphones
   */
  async getAvailableMicrophones() {
    try {
      // Demander les permissions d'abord
      await navigator.mediaDevices.getUserMedia({ audio: true });

      // Obtenir la liste des périphériques
      const devices = await navigator.mediaDevices.enumerateDevices();

      // Filtrer les microphones
      const microphones = devices
        .filter(device => device.kind === 'audioinput')
        .map(device => ({
          deviceId: device.deviceId,
          label: device.label || `Microphone ${device.deviceId.substring(0, 8)}...`,
          groupId: device.groupId
        }));

      console.log('🎤 Microphones disponibles:', microphones);
      return microphones;

    } catch (error) {
      console.error('❌ Erreur lors de la récupération des microphones:', error);
      throw new Error(`Impossible de récupérer les microphones: ${error.message}`);
    }
  }

  /**
   * Configurer le mode de reconnaissance vocale
   * @param {boolean} useBackend - True pour utiliser le backend STT
   */
  setSTTMode(useBackend = true) {
    this.useBackendSTT = useBackend;
    this.sttBackendAvailable = null; // Reset cache
    console.log(`🔧 Mode STT configuré: ${useBackend ? 'Backend' : 'Browser'}`);
  }

  /**
   * Obtenir le mode STT actuel
   * @returns {string} Mode actuel ('backend', 'browser', 'auto')
   */
  getSTTMode() {
    if (!this.useBackendSTT) return 'browser';
    if (this.sttBackendAvailable === true) return 'backend';
    if (this.sttBackendAvailable === false) return 'browser';
    return 'auto';
  }

  /**
   * Diagnostic complet du système de reconnaissance vocale
   * @returns {Promise<Object>} Rapport de diagnostic détaillé
   */
  async runDiagnostics() {
    const report = {
      timestamp: new Date().toISOString(),
      browser: navigator.userAgent,
      speechRecognition: {
        supported: false,
        api: null,
        continuous: false
      },
      backendSTT: {
        enabled: this.useBackendSTT,
        available: this.sttBackendAvailable,
        endpoint: sttService.baseUrl,
        mode: this.getSTTMode()
      },
      microphone: {
        permission: 'unknown',
        devices: [],
        selectedDevice: this.selectedMicrophoneId,
        accessible: false
      },
      network: {
        online: navigator.onLine,
        connection: navigator.connection ? {
          effectiveType: navigator.connection.effectiveType,
          downlink: navigator.connection.downlink
        } : null
      },
      issues: [],
      recommendations: []
    };

    try {
      // Test Speech Recognition API
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      if (SpeechRecognition) {
        report.speechRecognition.supported = true;
        report.speechRecognition.api = window.SpeechRecognition ? 'SpeechRecognition' : 'webkitSpeechRecognition';

        const testRecognition = new SpeechRecognition();
        report.speechRecognition.continuous = testRecognition.continuous !== undefined;
      } else {
        report.issues.push('Speech Recognition API non supportée par ce navigateur');
        report.recommendations.push('Utilisez Chrome, Edge ou Safari récent');
      }

      // Test microphone permissions et devices
      try {
        const permissionResult = await this.requestMicrophonePermission();
        report.microphone.permission = permissionResult.success ? 'granted' : 'denied';
        report.microphone.accessible = permissionResult.success;

        if (permissionResult.success) {
          const devices = await this.getAvailableMicrophones();
          report.microphone.devices = devices;

          if (devices.length === 0) {
            report.issues.push('Aucun microphone détecté');
            report.recommendations.push('Vérifiez qu\'un microphone est connecté et autorisé');
          }
        } else {
          report.issues.push(`Permission microphone: ${permissionResult.message}`);
          report.recommendations.push('Autorisez l\'accès au microphone dans les paramètres du navigateur');
        }
      } catch (error) {
        report.microphone.permission = 'error';
        report.issues.push(`Erreur microphone: ${error.message}`);
      }

      // Test backend STT
      try {
        const backendAvailable = await sttService.checkAvailability();
        report.backendSTT.available = backendAvailable;

        if (backendAvailable) {
          console.log('✅ Backend STT disponible');
          if (!this.useBackendSTT) {
            report.recommendations.push('Backend STT disponible mais non activé - meilleure fiabilité que l\'API browser');
          }
        } else {
          report.issues.push('Backend STT non disponible');
          report.recommendations.push('Vérifiez que le serveur backend est démarré sur http://127.0.0.1:8000');
        }
      } catch (error) {
        report.backendSTT.available = false;
        report.issues.push(`Erreur test backend STT: ${error.message}`);
      }

      // Test réseau
      if (!navigator.onLine) {
        report.issues.push('Pas de connexion internet');
        report.recommendations.push('La reconnaissance vocale browser nécessite une connexion internet');
      }

      console.log('🔍 Rapport de diagnostic complet:', report);
      return report;

    } catch (error) {
      report.issues.push(`Erreur lors du diagnostic: ${error.message}`);
      return report;
    }
  }
}

// Instance singleton
const voiceService = new VoiceService();

export { voiceService };
