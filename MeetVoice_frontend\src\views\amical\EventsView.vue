<template>
  <main class="events-container">
    <!-- Header avec filtres et recherche -->
    <header class="events-header">
      <section class="header-content">
        <h1>Événements près de chez vous</h1>
        <p class="subtitle">Découvrez et participez aux sorties organisées par la communauté</p>
      </section>
      
      <nav class="events-actions" aria-label="Actions événements">
        <button 
          @click="$router.push('/events/create')"
          class="btn-create-event"
          aria-label="Créer un nouvel événement"
        >
          <span aria-hidden="true">➕</span>
          Créer un événement
        </button>
        
        <button 
          @click="$router.push('/calendar')"
          class="btn-calendar"
          aria-label="Voir le calendrier"
        >
          <span aria-hidden="true">📅</span>
          Mon calendrier
        </button>
      </nav>
    </header>

    <!-- Filtres -->
    <section class="filters-section" aria-label="Filtres de recherche">
      <div class="filters-container">
        <div class="filter-group">
          <label for="category-filter">Catégorie :</label>
          <select 
            id="category-filter"
            v-model="selectedCategory"
            @change="filterEvents"
            class="filter-select"
          >
            <option value="">Toutes les catégories</option>
            <option 
              v-for="category in eventCategories" 
              :key="category"
              :value="category"
            >
              {{ getCategoryLabel(category) }}
            </option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="date-filter">Date :</label>
          <select 
            id="date-filter"
            v-model="selectedDateRange"
            @change="filterEvents"
            class="filter-select"
          >
            <option value="">Toutes les dates</option>
            <option value="today">Aujourd'hui</option>
            <option value="tomorrow">Demain</option>
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="location-filter">Lieu :</label>
          <input 
            id="location-filter"
            v-model="locationFilter"
            @input="filterEvents"
            type="text"
            placeholder="Ville, code postal..."
            class="filter-input"
          >
        </div>
        
        <button 
          @click="resetFilters"
          class="btn-reset-filters"
          aria-label="Réinitialiser les filtres"
        >
          Réinitialiser
        </button>
      </div>
    </section>

    <!-- Liste des événements -->
    <section class="events-list" aria-label="Liste des événements">
      <div v-if="loading" class="loading-state">
        <p>Chargement des événements...</p>
      </div>
      
      <div v-else-if="filteredEvents.length === 0" class="empty-state">
        <h2>Aucun événement trouvé</h2>
        <p>Essayez de modifier vos critères de recherche ou créez le premier événement !</p>
        <button 
          @click="$router.push('/events/create')"
          class="btn-create-first"
        >
          Créer un événement
        </button>
      </div>
      
      <div v-else class="events-grid">
        <article 
          v-for="event in filteredEvents" 
          :key="event.id"
          class="event-card"
          @click="viewEvent(event.id)"
          role="button"
          tabindex="0"
          @keydown.enter="viewEvent(event.id)"
          @keydown.space.prevent="viewEvent(event.id)"
        >
          <header class="event-header">
            <div class="event-category">
              <span class="category-icon">{{ getCategoryIcon(event.category) }}</span>
              <span class="category-label">{{ getCategoryLabel(event.category) }}</span>
            </div>
            <time class="event-date" :datetime="event.date">
              {{ formatEventDate(event.date) }}
            </time>
          </header>
          
          <div class="event-content">
            <h3 class="event-title">{{ event.title }}</h3>
            <p class="event-description">{{ event.description }}</p>
            
            <div class="event-details">
              <div class="event-location">
                <span aria-hidden="true">📍</span>
                {{ event.location }}
              </div>
              <div class="event-time">
                <span aria-hidden="true">🕐</span>
                {{ formatEventTime(event.time) }}
              </div>
            </div>
          </div>
          
          <footer class="event-footer">
            <div class="participants-info">
              <span class="participants-count">
                {{ event.participants?.length || 0 }}/{{ event.maxParticipants }} participants
              </span>
              <div class="participants-avatars">
                <img 
                  v-for="participant in event.participants?.slice(0, 3)" 
                  :key="participant.id"
                  :src="participant.avatar || '/default-avatar.jpg'"
                  :alt="`Avatar de ${participant.name}`"
                  class="participant-avatar"
                >
                <span 
                  v-if="event.participants?.length > 3"
                  class="more-participants"
                >
                  +{{ event.participants.length - 3 }}
                </span>
              </div>
            </div>
            
            <div class="event-actions">
              <button 
                v-if="!isParticipating(event.id)"
                @click.stop="joinEvent(event.id)"
                class="btn-join"
                :disabled="isEventFull(event)"
              >
                {{ isEventFull(event) ? 'Complet' : 'Participer' }}
              </button>
              <button 
                v-else
                @click.stop="leaveEvent(event.id)"
                class="btn-leave"
              >
                Se désinscrire
              </button>
            </div>
          </footer>
        </article>
      </div>
    </section>

    <!-- Pagination -->
    <nav class="pagination" v-if="totalPages > 1" aria-label="Navigation pagination">
      <button 
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        class="btn-page"
      >
        Précédent
      </button>
      
      <span class="page-info">
        Page {{ currentPage }} sur {{ totalPages }}
      </span>
      
      <button 
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="btn-page"
      >
        Suivant
      </button>
    </nav>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'EventsView',
  
  data() {
    return {
      loading: false,
      selectedCategory: '',
      selectedDateRange: '',
      locationFilter: '',
      currentPage: 1,
      eventsPerPage: 12
    }
  },
  
  computed: {
    ...mapState(['events', 'eventCategories', 'user']),
    
    filteredEvents() {
      let filtered = [...this.events];
      
      // Filtre par catégorie
      if (this.selectedCategory) {
        filtered = filtered.filter(event => event.category === this.selectedCategory);
      }
      
      // Filtre par date
      if (this.selectedDateRange) {
        filtered = this.filterByDateRange(filtered);
      }
      
      // Filtre par lieu
      if (this.locationFilter) {
        filtered = filtered.filter(event => 
          event.location.toLowerCase().includes(this.locationFilter.toLowerCase())
        );
      }
      
      // Pagination
      const start = (this.currentPage - 1) * this.eventsPerPage;
      const end = start + this.eventsPerPage;
      return filtered.slice(start, end);
    },
    
    totalPages() {
      return Math.ceil(this.events.length / this.eventsPerPage);
    }
  },
  
  methods: {
    ...mapActions(['loadEvents', 'joinEvent', 'leaveEvent']),
    
    getCategoryLabel(category) {
      const labels = {
        sport: 'Sport',
        jeux: 'Jeux',
        danse: 'Danse',
        restaurant: 'Restaurant',
        cinéma: 'Cinéma'
      };
      return labels[category] || category;
    },
    
    getCategoryIcon(category) {
      const icons = {
        sport: '⚽',
        jeux: '🎮',
        danse: '💃',
        restaurant: '🍽️',
        cinéma: '🎬'
      };
      return icons[category] || '📅';
    },
    
    formatEventDate(date) {
      return new Date(date).toLocaleDateString('fr-FR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      });
    },
    
    formatEventTime(time) {
      return new Date(`2000-01-01T${time}`).toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },
    
    filterByDateRange(events) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      
      return events.filter(event => {
        const eventDate = new Date(event.date);
        
        switch (this.selectedDateRange) {
          case 'today':
            return eventDate.toDateString() === today.toDateString();
          case 'tomorrow':
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            return eventDate.toDateString() === tomorrow.toDateString();
          case 'week':
            const weekEnd = new Date(today);
            weekEnd.setDate(weekEnd.getDate() + 7);
            return eventDate >= today && eventDate <= weekEnd;
          case 'month':
            const monthEnd = new Date(today);
            monthEnd.setMonth(monthEnd.getMonth() + 1);
            return eventDate >= today && eventDate <= monthEnd;
          default:
            return true;
        }
      });
    },
    
    filterEvents() {
      this.currentPage = 1; // Reset pagination
    },
    
    resetFilters() {
      this.selectedCategory = '';
      this.selectedDateRange = '';
      this.locationFilter = '';
      this.currentPage = 1;
    },
    
    viewEvent(eventId) {
      this.$router.push(`/events/${eventId}`);
    },
    
    isParticipating(eventId) {
      const event = this.events.find(e => e.id === eventId);
      return event?.participants?.some(p => p.id === this.user?.id);
    },
    
    isEventFull(event) {
      return event.participants?.length >= event.maxParticipants;
    },
    
    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
      }
    }
  },
  
  async mounted() {
    this.loading = true;
    try {
      await this.loadEvents();
    } catch (error) {
      console.error('Erreur lors du chargement des événements:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}

.events-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.events-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--accent-blue);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.events-actions {
  display: flex;
  gap: 12px;
}

.btn-create-event,
.btn-calendar {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-event:hover,
.btn-calendar:hover {
  background: var(--accent-blue);
  transform: translateY(-2px);
}

.filters-section {
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--slogan-gray);
  font-weight: 500;
}

.filter-select,
.filter-input {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 0.9rem;
}

.filter-select:focus,
.filter-input:focus {
  outline: none;
  border-color: var(--accent-blue);
  background: rgba(255, 255, 255, 0.15);
}

.btn-reset-filters {
  padding: 8px 16px;
  background: transparent;
  color: var(--accent-purple);
  border: 1px solid var(--accent-purple);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-reset-filters:hover {
  background: var(--accent-purple);
  color: var(--text-white);
}

.events-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.event-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.event-card:hover {
  transform: translateY(-4px);
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--accent-blue);
}

.event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.event-category {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 12px;
  background: var(--accent-purple);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.event-date {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.event-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--accent-blue);
}

.event-description {
  color: var(--slogan-gray);
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.event-location,
.event-time {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: var(--slogan-gray);
}

.event-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.participants-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.participants-count {
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.participants-avatars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.participant-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--accent-blue);
}

.more-participants {
  font-size: 0.7rem;
  color: var(--slogan-gray);
  margin-left: 4px;
}

.btn-join,
.btn-leave {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-join {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-join:hover:not(:disabled) {
  background: var(--accent-purple);
}

.btn-join:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.btn-leave {
  background: transparent;
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.btn-leave:hover {
  background: #ff6b6b;
  color: var(--text-white);
}

.loading-state,
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-state h2 {
  color: var(--accent-blue);
  margin-bottom: 16px;
}

.btn-create-first {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-first:hover {
  background: var(--accent-blue);
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}

.btn-page {
  padding: 8px 16px;
  background: var(--accent-purple);
  color: var(--text-white);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-page:hover:not(:disabled) {
  background: var(--accent-blue);
}

.btn-page:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.page-info {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 768px) {
  .events-container {
    padding: 16px;
  }
  
  .events-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }
  
  .events-grid {
    grid-template-columns: 1fr;
  }
  
  .event-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}
</style>
