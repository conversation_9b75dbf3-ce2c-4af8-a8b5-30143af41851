<!-- @format -->

<template>
  <picture class="webp-image">
    <!-- Source WebP pour les navigateurs compatibles -->
    <source 
      v-if="webpSrc"
      :srcset="webpSrc" 
      type="image/webp"
    />
    
    <!-- Fallback pour les navigateurs non compatibles -->
    <img
      :src="fallbackSrc"
      :alt="alt"
      :width="width"
      :height="height"
      :loading="loading"
      :class="imageClass"
      :style="imageStyle"
      @load="$emit('load', $event)"
      @error="$emit('error', $event)"
    />
  </picture>
</template>

<script>
export default {
  name: 'WebPImage',
  emits: ['load', 'error'],
  props: {
    // Source de l'image (sans extension ou avec)
    src: {
      type: String,
      required: true
    },
    
    // Texte alternatif
    alt: {
      type: String,
      required: true
    },
    
    // Largeur de l'image
    width: {
      type: [String, Number],
      default: null
    },
    
    // Hauteur de l'image
    height: {
      type: [String, Number],
      default: null
    },
    
    // Type de chargement (lazy, eager)
    loading: {
      type: String,
      default: 'lazy',
      validator: value => ['lazy', 'eager'].includes(value)
    },
    
    // Classes CSS personnalisées
    imageClass: {
      type: [String, Array, Object],
      default: ''
    },
    
    // Styles CSS personnalisés
    imageStyle: {
      type: [String, Object],
      default: null
    },
    
    // Forcer l'utilisation du fallback
    forceFallback: {
      type: Boolean,
      default: false
    }
  },
  
  computed: {
    // Source WebP optimisée
    webpSrc() {
      if (this.forceFallback) return null;
      
      // Si l'image a déjà l'extension .webp
      if (this.src.endsWith('.webp')) {
        return this.src;
      }
      
      // Remplacer l'extension par .webp
      return this.src.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
    },
    
    // Source de fallback (image originale)
    fallbackSrc() {
      return this.src;
    }
  }
};
</script>

<style scoped>
.webp-image {
  display: inline-block;
  max-width: 100%;
}

.webp-image img {
  max-width: 100%;
  height: auto;
  display: block;
}
</style>
