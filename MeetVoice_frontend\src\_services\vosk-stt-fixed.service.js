/**
 * Service Vosk STT Corrigé - Format WAV
 * Version corrigée qui envoie du WAV au lieu de WebM
 */

import Axios from './caller.service.js';

class VoskSTTFixedService {
  constructor() {
    this.baseUrl = '/api';
    this.isListening = false;
    this.isInitialized = true;
    this.mediaRecorder = null;
    this.mediaStream = null;
    this.audioChunks = [];
    this.recordingInterval = null;

    console.log('🎯 Service Vosk STT Corrigé initialisé (format WAV)');
  }

  /**
   * Vérifier la disponibilité de l'API Vosk
   */
  async initialize() {
    try {
      const response = await Axios.get(`${this.baseUrl}/vosk/status/`);
      this.isInitialized = response.status === 200;
      console.log('✅ API Vosk backend disponible');
      return true;
    } catch (error) {
      console.warn('⚠️ API Vosk backend indisponible:', error.message);
      this.isInitialized = false;
      return false;
    }
  }

  /**
   * Démarrer l'écoute avec l'API Vosk backend
   */
  async startListening(options = {}) {
    try {
      if (!this.isInitialized) {
        console.log('🔄 Vérification API Vosk...');
        const available = await this.initialize();
        if (!available) {
          throw new Error('API Vosk backend non disponible');
        }
      }

      console.log('🎤 Démarrage écoute Vosk API (format WAV)...');

      // Configuration microphone
      const constraints = {
        audio: {
          deviceId: options.microphoneId ? { exact: options.microphoneId } : undefined,
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      };

      // Obtenir l'accès au microphone
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints);

      // Configurer MediaRecorder avec le meilleur format disponible
      const supportedMimeType = this.getSupportedMimeType();
      this.mediaRecorder = new MediaRecorder(this.mediaStream, {
        mimeType: supportedMimeType
      });

      this.audioChunks = [];
      this.isListening = true;

      // Événements MediaRecorder
      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          console.log(`🎵 Chunk audio reçu: ${event.data.size} bytes`);
          this.audioChunks.push(event.data);
        } else {
          console.warn('⚠️ Chunk audio vide reçu');
        }
      };

      this.mediaRecorder.onstop = async () => {
        console.log('🎤 Enregistrement terminé, traitement...');
        await this.processAudioChunks(options);
      };

      this.mediaRecorder.onerror = (error) => {
        console.error('❌ Erreur MediaRecorder Vosk:', error);
        this.cleanup();
      };

      // Démarrer l'enregistrement
      this.mediaRecorder.start();

      // Traitement périodique des chunks (toutes les 3 secondes pour avoir plus d'audio)
      this.recordingInterval = setInterval(() => {
        if (this.isListening && this.mediaRecorder && this.mediaRecorder.state === 'recording') {
          console.log(`🎤 Demande de données audio (chunks: ${this.audioChunks.length})`);
          this.mediaRecorder.requestData();
        }
      }, 3000);

      console.log('✅ Écoute Vosk API démarrée');

      if (options.onStart) {
        options.onStart();
      }

    } catch (error) {
      console.error('❌ Erreur démarrage Vosk:', error);
      this.cleanup();
      throw error;
    }
  }

  /**
   * Traiter les chunks audio avec l'API Vosk
   */
  async processAudioChunks(options) {
    try {
      if (this.audioChunks.length === 0) return;

      // Créer un blob audio avec le bon type MIME
      const mimeType = this.getSupportedMimeType();
      const audioBlob = new Blob(this.audioChunks, { type: mimeType });

      // Convertir en WAV
      const wavBlob = await this.convertToWav(audioBlob);

      // Préparer FormData pour l'API
      const formData = new FormData();
      formData.append('audio', wavBlob, 'audio.wav');
      formData.append('language', options.language || 'fr-FR');

      console.log(`📤 Envoi audio WAV à Vosk (${wavBlob.size} bytes)`);

      // Envoyer à l'API Vosk
      const response = await Axios.post(`${this.baseUrl}/vosk/speech-to-text/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 10000
      });

      if (response.data && response.data.text) {
        const text = response.data.text.trim();
        console.log('📝 Vosk API résultat:', text);

        if (text && options.onResult) {
          options.onResult(text, '');
        }
      }

      // Réinitialiser les chunks pour le prochain cycle
      this.audioChunks = [];

    } catch (error) {
      console.warn('⚠️ Erreur traitement audio Vosk:', error.message);
      // Ne pas arrêter l'écoute, juste ignorer ce chunk
      this.audioChunks = [];
    }
  }

  /**
   * Arrêter l'écoute
   */
  stopListening() {
    if (!this.isListening) return;

    console.log('🛑 Arrêt écoute Vosk API...');
    this.isListening = false;

    // Arrêter l'intervalle
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
      this.recordingInterval = null;
    }

    // Arrêter MediaRecorder
    if (this.mediaRecorder && this.mediaRecorder.state !== 'inactive') {
      this.mediaRecorder.stop();
    }

    this.cleanup();
    console.log('✅ Écoute Vosk API arrêtée');
  }

  /**
   * Nettoyer les ressources
   */
  cleanup() {
    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
    this.mediaRecorder = null;
    this.audioChunks = [];
  }

  /**
   * Obtenir le type MIME supporté pour l'enregistrement
   */
  getSupportedMimeType() {
    const types = [
      'audio/webm;codecs=opus',
      'audio/webm',
      'audio/mp4',
      'audio/ogg'
    ];

    for (const type of types) {
      if (MediaRecorder.isTypeSupported(type)) {
        console.log(`🎵 Type MIME sélectionné: ${type}`);
        return type;
      }
    }

    console.warn('⚠️ Aucun type MIME supporté trouvé, utilisation par défaut');
    return 'audio/webm';
  }

  /**
   * Convertir l'audio en format WAV
   */
  async convertToWav(audioBlob) {
    try {
      console.log(`🔄 Conversion audio vers WAV (${audioBlob.size} bytes)...`);

      // Si l'audio est trop petit, c'est probablement du silence
      if (audioBlob.size < 1000) {
        console.warn('⚠️ Audio trop petit, probablement du silence');
        return this.createValidWav();
      }

      // Essayer de convertir l'audio réel
      const audioContext = new (window.AudioContext || window.webkitAudioContext)();
      const arrayBuffer = await audioBlob.arrayBuffer();

      try {
        const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
        const wavBlob = this.audioBufferToWav(audioBuffer);
        console.log(`✅ Conversion WAV réussie (${wavBlob.size} bytes)`);
        return wavBlob;
      } catch (decodeError) {
        console.warn('⚠️ Erreur décodage audio, utilisation format original:', decodeError.message);
        // Si on ne peut pas décoder, renvoyer l'audio original avec extension .wav
        return new Blob([arrayBuffer], { type: 'audio/wav' });
      }

    } catch (error) {
      console.warn('⚠️ Erreur conversion WAV:', error.message);
      return this.createValidWav();
    }
  }

  /**
   * Créer un fichier WAV minimal valide
   */
  createValidWav() {
    const sampleRate = 16000;
    const duration = 1; // 1 seconde de silence
    const numSamples = sampleRate * duration;
    const numChannels = 1;
    const bitsPerSample = 16;
    
    const buffer = new ArrayBuffer(44 + numSamples * 2);
    const view = new DataView(buffer);
    
    // Header WAV
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };
    
    writeString(0, 'RIFF');
    view.setUint32(4, 36 + numSamples * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
    view.setUint16(32, numChannels * bitsPerSample / 8, true);
    view.setUint16(34, bitsPerSample, true);
    writeString(36, 'data');
    view.setUint32(40, numSamples * 2, true);
    
    // Données audio (silence)
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(44 + i * 2, 0, true);
    }
    
    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * Convertir AudioBuffer en WAV Blob
   */
  audioBufferToWav(audioBuffer) {
    const numChannels = audioBuffer.numberOfChannels;
    const sampleRate = audioBuffer.sampleRate;
    const format = 1; // PCM
    const bitDepth = 16;

    const length = audioBuffer.length * numChannels * 2;
    const buffer = new ArrayBuffer(44 + length);
    const view = new DataView(buffer);

    // Header WAV
    const writeString = (offset, string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, format, true);
    view.setUint16(22, numChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numChannels * bitDepth / 8, true);
    view.setUint16(32, numChannels * bitDepth / 8, true);
    view.setUint16(34, bitDepth, true);
    writeString(36, 'data');
    view.setUint32(40, length, true);

    // Données audio
    let offset = 44;
    for (let i = 0; i < audioBuffer.length; i++) {
      for (let channel = 0; channel < numChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample * 0x7FFF, true);
        offset += 2;
      }
    }

    console.log(`🎵 WAV créé: ${numChannels} canaux, ${sampleRate}Hz, ${audioBuffer.length} échantillons`);
    return new Blob([buffer], { type: 'audio/wav' });
  }

  /**
   * Vérifier si l'écoute est active
   */
  isListeningActive() {
    return this.isListening;
  }

  /**
   * Vérifier le support
   */
  isSupported() {
    return !!(navigator.mediaDevices &&
              navigator.mediaDevices.getUserMedia &&
              window.MediaRecorder);
  }

  /**
   * Obtenir le statut
   */
  getStatus() {
    return {
      isListening: this.isListening,
      isInitialized: this.isInitialized,
      isSupported: this.isSupported(),
      apiUrl: `${this.baseUrl}/vosk/`,
      hasMediaRecorder: !!this.mediaRecorder
    };
  }

  /**
   * Réinitialiser le service
   */
  reset() {
    console.log('🔄 Reset service Vosk corrigé...');
    this.stopListening();
    this.isInitialized = true;
  }
}

// Export de l'instance unique
export const voskSTTFixedService = new VoskSTTFixedService();
export default voskSTTFixedService;
