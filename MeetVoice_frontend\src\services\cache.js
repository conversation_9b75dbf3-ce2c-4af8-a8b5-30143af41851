class CacheService {
  constructor() {
    this.cache = new Map()
    this.timestamps = new Map()
    this.defaultTTL = 5 * 60 * 1000 // 5 minutes par défaut
    this.maxSize = 100 // Nombre maximum d'entrées en cache
    this.cleanupInterval = null
    
    // Démarrer le nettoyage automatique
    this.startCleanup()
  }

  /**
   * Obtenir une valeur du cache
   * @param {string} key - Clé du cache
   * @returns {any|null} - Valeur ou null si expirée/inexistante
   */
  get(key) {
    if (!this.cache.has(key)) {
      return null
    }

    const timestamp = this.timestamps.get(key)
    const now = Date.now()

    // Vérifier si l'entrée a expiré
    if (timestamp && now - timestamp.created > timestamp.ttl) {
      this.delete(key)
      return null
    }

    // Mettre à jour l'heure d'accès
    if (timestamp) {
      timestamp.accessed = now
    }

    return this.cache.get(key)
  }

  /**
   * Définir une valeur dans le cache
   * @param {string} key - Clé du cache
   * @param {any} value - Valeur à mettre en cache
   * @param {number} ttl - Durée de vie en millisecondes
   */
  set(key, value, ttl = this.defaultTTL) {
    // Vérifier la taille du cache
    if (this.cache.size >= this.maxSize) {
      this.evictLRU()
    }

    this.cache.set(key, value)
    this.timestamps.set(key, {
      created: Date.now(),
      accessed: Date.now(),
      ttl: ttl
    })
  }

  /**
   * Supprimer une entrée du cache
   * @param {string} key - Clé à supprimer
   */
  delete(key) {
    this.cache.delete(key)
    this.timestamps.delete(key)
  }

  /**
   * Vérifier si une clé existe dans le cache
   * @param {string} key - Clé à vérifier
   * @returns {boolean}
   */
  has(key) {
    return this.get(key) !== null
  }

  /**
   * Vider tout le cache
   */
  clear() {
    this.cache.clear()
    this.timestamps.clear()
  }

  /**
   * Obtenir la taille actuelle du cache
   * @returns {number}
   */
  size() {
    return this.cache.size
  }

  /**
   * Éviction LRU (Least Recently Used)
   */
  evictLRU() {
    let oldestKey = null
    let oldestTime = Date.now()

    for (const [key, timestamp] of this.timestamps.entries()) {
      if (timestamp.accessed < oldestTime) {
        oldestTime = timestamp.accessed
        oldestKey = key
      }
    }

    if (oldestKey) {
      this.delete(oldestKey)
    }
  }

  /**
   * Nettoyage automatique des entrées expirées
   */
  cleanup() {
    const now = Date.now()
    const expiredKeys = []

    for (const [key, timestamp] of this.timestamps.entries()) {
      if (now - timestamp.created > timestamp.ttl) {
        expiredKeys.push(key)
      }
    }

    expiredKeys.forEach(key => this.delete(key))
    
    if (expiredKeys.length > 0) {
      console.log(`Cache: ${expiredKeys.length} entrées expirées supprimées`)
    }
  }

  /**
   * Démarrer le nettoyage automatique
   */
  startCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
    }
    
    // Nettoyer toutes les minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanup()
    }, 60 * 1000)
  }

  /**
   * Arrêter le nettoyage automatique
   */
  stopCleanup() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval)
      this.cleanupInterval = null
    }
  }

  /**
   * Obtenir des statistiques du cache
   * @returns {object}
   */
  getStats() {
    const now = Date.now()
    let totalAge = 0
    let expiredCount = 0

    for (const timestamp of this.timestamps.values()) {
      const age = now - timestamp.created
      totalAge += age
      
      if (age > timestamp.ttl) {
        expiredCount++
      }
    }

    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      averageAge: this.cache.size > 0 ? totalAge / this.cache.size : 0,
      expiredCount: expiredCount,
      hitRate: this.hitCount / (this.hitCount + this.missCount) || 0
    }
  }

  /**
   * Wrapper pour les requêtes avec cache automatique
   * @param {string} key - Clé du cache
   * @param {Function} fetcher - Fonction qui retourne une Promise
   * @param {number} ttl - Durée de vie du cache
   * @returns {Promise}
   */
  async fetch(key, fetcher, ttl = this.defaultTTL) {
    // Vérifier le cache d'abord
    const cached = this.get(key)
    if (cached !== null) {
      return cached
    }

    try {
      // Exécuter la fonction de récupération
      const result = await fetcher()
      
      // Mettre en cache le résultat
      this.set(key, result, ttl)
      
      return result
    } catch (error) {
      // Ne pas mettre en cache les erreurs
      throw error
    }
  }

  /**
   * Invalidation de cache par pattern
   * @param {string|RegExp} pattern - Pattern pour matcher les clés
   */
  invalidatePattern(pattern) {
    const keysToDelete = []
    
    for (const key of this.cache.keys()) {
      if (typeof pattern === 'string') {
        if (key.includes(pattern)) {
          keysToDelete.push(key)
        }
      } else if (pattern instanceof RegExp) {
        if (pattern.test(key)) {
          keysToDelete.push(key)
        }
      }
    }
    
    keysToDelete.forEach(key => this.delete(key))
    
    return keysToDelete.length
  }

  /**
   * Préchargement de données
   * @param {Array} requests - Tableau d'objets {key, fetcher, ttl}
   */
  async preload(requests) {
    const promises = requests.map(({ key, fetcher, ttl }) => {
      if (!this.has(key)) {
        return this.fetch(key, fetcher, ttl).catch(error => {
          console.warn(`Erreur de préchargement pour ${key}:`, error)
          return null
        })
      }
      return Promise.resolve(this.get(key))
    })

    return Promise.all(promises)
  }

  /**
   * Sérialisation du cache pour la persistance
   * @returns {string}
   */
  serialize() {
    const data = {
      cache: Array.from(this.cache.entries()),
      timestamps: Array.from(this.timestamps.entries())
    }
    return JSON.stringify(data)
  }

  /**
   * Désérialisation du cache depuis la persistance
   * @param {string} data - Données sérialisées
   */
  deserialize(data) {
    try {
      const parsed = JSON.parse(data)
      
      this.cache = new Map(parsed.cache)
      this.timestamps = new Map(parsed.timestamps)
      
      // Nettoyer les entrées expirées après la désérialisation
      this.cleanup()
    } catch (error) {
      console.error('Erreur lors de la désérialisation du cache:', error)
      this.clear()
    }
  }

  /**
   * Sauvegarder le cache dans localStorage
   */
  saveToStorage() {
    try {
      localStorage.setItem('meetvoice_cache', this.serialize())
    } catch (error) {
      console.warn('Impossible de sauvegarder le cache:', error)
    }
  }

  /**
   * Charger le cache depuis localStorage
   */
  loadFromStorage() {
    try {
      const data = localStorage.getItem('meetvoice_cache')
      if (data) {
        this.deserialize(data)
      }
    } catch (error) {
      console.warn('Impossible de charger le cache:', error)
    }
  }
}

// Instance singleton
const cacheService = new CacheService()

// Sauvegarder le cache avant la fermeture de la page
window.addEventListener('beforeunload', () => {
  cacheService.saveToStorage()
})

// Charger le cache au démarrage
cacheService.loadFromStorage()

export default cacheService

// Utilitaires pour les clés de cache
export const CacheKeys = {
  // Profils
  PROFILE: (id) => `profile:${id}`,
  PROFILES_LIST: (filters) => `profiles:${JSON.stringify(filters)}`,
  
  // Événements
  EVENT: (id) => `event:${id}`,
  EVENTS_LIST: (filters) => `events:${JSON.stringify(filters)}`,
  USER_EVENTS: (userId) => `user_events:${userId}`,
  
  // Messages
  CONVERSATION: (id) => `conversation:${id}`,
  CONVERSATIONS_LIST: (userId) => `conversations:${userId}`,
  MESSAGES: (conversationId, page) => `messages:${conversationId}:${page}`,
  
  // Communauté
  COMMUNITY_POSTS: (page) => `community_posts:${page}`,
  COMMUNITY_MEMBERS: () => 'community_members',
  GROUPS: () => 'groups',
  
  // Notifications
  NOTIFICATIONS: (userId) => `notifications:${userId}`,
  
  // Autres
  USER_STATS: (userId) => `user_stats:${userId}`,
  SEARCH_RESULTS: (query, type) => `search:${type}:${query}`
}
