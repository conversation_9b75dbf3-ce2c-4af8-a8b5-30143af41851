import { createStore } from 'vuex'
import storeConfig from '@/store'

// Mock accountService
jest.mock('@/_services', () => ({
  accountService: {
    login: jest.fn(),
    logout: jest.fn(),
    saveToken: jest.fn(),
    getToken: jest.fn(),
    getUser: jest.fn(),
    initializeAuth: jest.fn(),
    refreshUserData: jest.fn()
  }
}))

import { accountService } from '@/_services'

describe('Vuex Store', () => {
  let store

  beforeEach(() => {
    store = createStore(storeConfig)
    jest.clearAllMocks()
  })

  describe('mutations', () => {
    it('should set token', () => {
      const token = 'test-token'
      
      store.commit('setToken', token)
      
      expect(store.state.token).toBe(token)
    })

    it('should set logged in status', () => {
      store.commit('setLoggedIn', true)
      
      expect(store.state.isLoggedIn).toBe(true)
    })

    it('should set user', () => {
      const user = { id: 1, username: 'test' }
      
      store.commit('setUser', user)
      
      expect(store.state.user).toEqual(user)
    })

    it('should set error', () => {
      const error = 'Test error'
      
      store.commit('setError', error)
      
      expect(store.state.error).toBe(error)
    })

    it('should set loading', () => {
      store.commit('setLoading', true)
      
      expect(store.state.loading).toBe(true)
    })

    it('should clear error', () => {
      store.state.error = 'Test error'
      
      store.commit('clearError')
      
      expect(store.state.error).toBeNull()
    })
  })

  describe('actions', () => {
    describe('saveToken', () => {
      it('should commit token and save to service', () => {
        const token = 'test-token'
        
        store.dispatch('saveToken', token)
        
        expect(store.state.token).toBe(token)
        expect(accountService.saveToken).toHaveBeenCalledWith(token)
      })
    })

    describe('login', () => {
      it('should login successfully', async () => {
        const credentials = { email: '<EMAIL>', password: 'password' }
        const mockResponse = {
          data: {
            access: 'token123',
            user: { id: 1, username: 'test' }
          }
        }
        
        accountService.login.mockResolvedValue(mockResponse)
        
        const result = await store.dispatch('login', credentials)
        
        expect(accountService.login).toHaveBeenCalledWith(credentials)
        expect(store.state.token).toBe('token123')
        expect(store.state.user).toEqual({ id: 1, username: 'test' })
        expect(store.state.isLoggedIn).toBe(true)
        expect(store.state.loading).toBe(false)
        expect(result).toEqual(mockResponse.data)
      })

      it('should handle login error', async () => {
        const credentials = { email: '<EMAIL>', password: 'wrong' }
        const error = new Error('Invalid credentials')
        error.response = { data: { message: 'Invalid credentials' } }
        
        accountService.login.mockRejectedValue(error)
        
        await expect(store.dispatch('login', credentials)).rejects.toThrow('Invalid credentials')
        
        expect(store.state.error).toBe('Invalid credentials')
        expect(store.state.loading).toBe(false)
        expect(store.state.isLoggedIn).toBe(false)
      })

      it('should handle login without access token', async () => {
        const credentials = { email: '<EMAIL>', password: 'password' }
        const mockResponse = { data: {} }
        
        accountService.login.mockResolvedValue(mockResponse)
        
        await expect(store.dispatch('login', credentials)).rejects.toThrow('Token d\'accès non reçu')
        
        expect(store.state.error).toBe('Token d\'accès non reçu')
      })
    })

    describe('logout', () => {
      it('should logout and clear state', () => {
        // Set initial state
        store.state.isLoggedIn = true
        store.state.user = { id: 1, username: 'test' }
        store.state.token = 'token123'
        store.state.error = 'Some error'
        
        store.dispatch('logout')
        
        expect(store.state.isLoggedIn).toBe(false)
        expect(store.state.user).toBeNull()
        expect(store.state.token).toBeNull()
        expect(store.state.error).toBeNull()
        expect(accountService.logout).toHaveBeenCalled()
      })
    })

    describe('initializeAuth', () => {
      it('should initialize auth successfully', async () => {
        const user = { id: 1, username: 'test' }
        const token = 'token123'
        
        accountService.initializeAuth.mockReturnValue(true)
        accountService.getUser.mockReturnValue(user)
        accountService.getToken.mockReturnValue(token)
        accountService.refreshUserData.mockResolvedValue(user)
        
        const result = await store.dispatch('initializeAuth')
        
        expect(accountService.initializeAuth).toHaveBeenCalled()
        expect(store.state.isLoggedIn).toBe(true)
        expect(store.state.user).toEqual(user)
        expect(store.state.token).toBe(token)
        expect(result).toBe(true)
      })

      it('should handle failed initialization', async () => {
        accountService.initializeAuth.mockReturnValue(false)
        
        const result = await store.dispatch('initializeAuth')
        
        expect(result).toBe(false)
        expect(store.state.isLoggedIn).toBe(false)
      })

      it('should handle refresh error gracefully', async () => {
        const user = { id: 1, username: 'test' }
        const token = 'token123'
        
        accountService.initializeAuth.mockReturnValue(true)
        accountService.getUser.mockReturnValue(user)
        accountService.getToken.mockReturnValue(token)
        accountService.refreshUserData.mockRejectedValue(new Error('Refresh failed'))
        
        const result = await store.dispatch('initializeAuth')
        
        expect(result).toBe(true) // Should still return true even if refresh fails
        expect(store.state.user).toEqual(user) // Should keep cached user data
      })
    })

    describe('refreshUserData', () => {
      it('should refresh user data successfully', async () => {
        const userData = { id: 1, username: 'updated' }
        
        accountService.refreshUserData.mockResolvedValue(userData)
        
        const result = await store.dispatch('refreshUserData')
        
        expect(accountService.refreshUserData).toHaveBeenCalled()
        expect(store.state.user).toEqual(userData)
        expect(result).toEqual(userData)
      })

      it('should handle refresh error', async () => {
        const error = new Error('Refresh failed')
        
        accountService.refreshUserData.mockRejectedValue(error)
        
        await expect(store.dispatch('refreshUserData')).rejects.toThrow('Refresh failed')
        
        expect(store.state.error).toBe('Impossible de mettre à jour les données utilisateur')
      })
    })

    describe('clearError', () => {
      it('should clear error', () => {
        store.state.error = 'Test error'
        
        store.dispatch('clearError')
        
        expect(store.state.error).toBeNull()
      })
    })
  })

  describe('getters', () => {
    it('should get token', () => {
      store.state.token = 'test-token'
      
      expect(store.getters.getToken).toBe('test-token')
    })

    it('should get user', () => {
      const user = { id: 1, username: 'test' }
      store.state.user = user
      
      expect(store.getters.getUser).toEqual(user)
    })

    it('should get authenticated status', () => {
      store.state.isLoggedIn = true
      
      expect(store.getters.isAuthenticated).toBe(true)
    })

    it('should get error', () => {
      store.state.error = 'Test error'
      
      expect(store.getters.getError).toBe('Test error')
    })

    it('should get loading status', () => {
      store.state.loading = true
      
      expect(store.getters.isLoading).toBe(true)
    })

    describe('getUserFullName', () => {
      it('should return full name when available', () => {
        store.state.user = {
          id: 1,
          username: 'test',
          first_name: 'John',
          last_name: 'Doe'
        }
        
        expect(store.getters.getUserFullName).toBe('John Doe')
      })

      it('should return username when no full name', () => {
        store.state.user = {
          id: 1,
          username: 'test'
        }
        
        expect(store.getters.getUserFullName).toBe('test')
      })

      it('should return email when no username', () => {
        store.state.user = {
          id: 1,
          email: '<EMAIL>'
        }
        
        expect(store.getters.getUserFullName).toBe('<EMAIL>')
      })

      it('should return null when no user', () => {
        store.state.user = null
        
        expect(store.getters.getUserFullName).toBeNull()
      })
    })
  })
})
