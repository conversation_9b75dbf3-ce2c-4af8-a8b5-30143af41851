#!/usr/bin/env python3
"""
Test final de l'intégration TTS avec l'API backend Django
"""

import requests
import json
import sys

BASE_URL = "http://127.0.0.1:8000/tts/public"

def test_voices():
    """Test de récupération des voix"""
    print("🎤 Test de récupération des voix...")
    try:
        response = requests.get(f"{BASE_URL}/voices/")
        if response.status_code == 200:
            voices = response.json()
            
            print(f"✅ {len(voices)} voix récupérées")
            
            # Organiser par langue
            french_voices = [v for v in voices if v['language'] == 'fr']
            international_voices = [v for v in voices if v['language'] != 'fr']
            premium_voices = [v for v in voices if v['is_premium']]
            
            print(f"\n🇫🇷 Voix françaises ({len(french_voices)}):")
            for voice in french_voices:
                premium_indicator = " ⭐" if voice['is_premium'] else ""
                print(f"  • {voice['name']}{premium_indicator}")
                print(f"    📝 {voice['description']}")
                print(f"    🎭 Type: {voice['voice_type']}")
                print()
            
            print(f"🌍 Voix internationales ({len(international_voices)}):")
            for voice in international_voices:
                premium_indicator = " ⭐" if voice['is_premium'] else ""
                print(f"  • {voice['name']} ({voice['language'].upper()}){premium_indicator}")
                print(f"    📝 {voice['description']}")
                print()
            
            print(f"⭐ Voix premium: {len(premium_voices)}")
            
            return voices
        else:
            print(f"❌ Erreur: {response.status_code}")
            return []
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return []

def test_synthesis(voice_type="female_young", text="Bonjour ! Test de synthèse vocale avec MeetVoice."):
    """Test de synthèse vocale"""
    print(f"\n🔊 Test de synthèse avec {voice_type}...")
    try:
        payload = {
            "text": text,
            "voice_type": voice_type,
            "language": "fr",
            "speed": 1.0,
            "pitch": 1.0
        }
        
        response = requests.post(f"{BASE_URL}/synthesize/", json=payload)
        if response.status_code == 200:
            print(f"✅ Synthèse réussie ({len(response.content)} bytes)")
            print(f"📄 Content-Type: {response.headers.get('content-type', 'unknown')}")
            return True
        else:
            print(f"❌ Erreur: {response.status_code}")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_data = response.json()
                    print(f"💬 Message: {error_data.get('error', 'Erreur inconnue')}")
                except:
                    pass
            return False
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def main():
    """Fonction principale"""
    print("🎯 Test Final - Intégration TTS Backend pour MeetVoice")
    print("=" * 60)
    
    # Test des voix
    voices = test_voices()
    if not voices:
        print("❌ Impossible de récupérer les voix.")
        sys.exit(1)
    
    # Test de synthèse avec différentes voix
    test_voices_list = [
        ("female_young", "Sophie - Voix Féminine Jeune"),
        ("male_young", "Lucas - Voix Masculine Jeune"),
        ("female_mature", "Camille - Voix Féminine Mature"),
        ("neutral", "Alex - Voix Neutre")
    ]
    
    print("\n🧪 Tests de synthèse avec différentes voix:")
    for voice_type, voice_name in test_voices_list:
        # Vérifier si la voix existe
        voice_exists = any(v['voice_type'] == voice_type for v in voices)
        if voice_exists:
            success = test_synthesis(voice_type, f"Bonjour, je suis {voice_name}.")
            if success:
                print(f"  ✅ {voice_name}")
            else:
                print(f"  ❌ {voice_name}")
        else:
            print(f"  ⚠️  {voice_name} - Non disponible")
    
    print("\n🎉 Tests terminés !")
    print("🌐 Interface web: http://localhost:8081")
    print("🔧 API Backend: http://127.0.0.1:8000/tts/public")
    print("\n📋 Résumé:")
    print(f"  • {len(voices)} voix disponibles")
    print(f"  • {len([v for v in voices if v['language'] == 'fr'])} voix françaises")
    print(f"  • {len([v for v in voices if v['is_premium']])} voix premium")

if __name__ == "__main__":
    main()
