<!-- @format -->

<template>
  <div class="profile-edit-container">
    <div class="profile-edit-card">
      <h2>Modifier mon profil</h2>
      
      <form @submit.prevent="saveProfile" class="profile-form">
        <!-- Section Photo de profil -->
        <div class="photo-section">
          <h3>Photo de profil</h3>
          <div class="current-photo" v-if="profile.profile_photo || photoPreview">
            <img 
              :src="photoPreview || profile.profile_photo" 
              alt="Photo de profil" 
              class="profile-image"
            />
            <button type="button" @click="removePhoto" class="remove-photo-btn">
              ✕ Supprimer
            </button>
          </div>
          
          <div class="photo-upload" v-if="!profile.profile_photo && !photoPreview">
            <input
              type="file"
              id="profile_photo"
              @change="handlePhotoUpload"
              accept="image/*"
              style="display: none"
            />
            <label for="profile_photo" class="upload-btn">
              <span>📷</span>
              <span>Ajouter une photo</span>
            </label>
          </div>
          
          <button 
            v-if="profile.profile_photo && !photoPreview" 
            type="button" 
            @click="triggerPhotoUpload"
            class="change-photo-btn"
          >
            Changer la photo
          </button>
        </div>

        <!-- Informations personnelles -->
        <div class="form-section">
          <h3>Informations personnelles</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label for="first_name">Prénom</label>
              <input
                type="text"
                id="first_name"
                v-model="profile.first_name"
                required
              />
            </div>
            
            <div class="form-group">
              <label for="last_name">Nom</label>
              <input
                type="text"
                id="last_name"
                v-model="profile.last_name"
                required
              />
            </div>
          </div>
          
          <div class="form-group">
            <label for="username">Nom d'utilisateur</label>
            <input
              type="text"
              id="username"
              v-model="profile.username"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="email">Email</label>
            <input
              type="email"
              id="email"
              v-model="profile.email"
              disabled
              title="L'email ne peut pas être modifié"
            />
          </div>
          
          <div class="form-group">
            <label for="birth_date">Date de naissance</label>
            <input
              type="date"
              id="birth_date"
              v-model="profile.birth_date"
              required
            />
          </div>
        </div>

        <!-- Préférences de rencontre -->
        <div class="form-section">
          <h3>Préférences de rencontre</h3>
          
          <div class="form-row">
            <div class="form-group">
              <label for="gender">Genre</label>
              <select id="gender" v-model="profile.gender" required>
                <option value="M">Homme</option>
                <option value="F">Femme</option>
                <option value="O">Autre</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="looking_for">Vous recherchez</label>
              <select id="looking_for" v-model="profile.looking_for" required>
                <option value="M">Des hommes</option>
                <option value="F">Des femmes</option>
                <option value="B">Les deux</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label for="city">Ville</label>
            <input
              type="text"
              id="city"
              v-model="profile.city"
              required
            />
          </div>
          
          <div class="form-group">
            <label for="bio">Description</label>
            <textarea
              id="bio"
              v-model="profile.bio"
              rows="4"
              placeholder="Parlez-nous de vous..."
            ></textarea>
          </div>
        </div>

        <!-- Messages -->
        <div v-if="errors" class="error-message">{{ errors }}</div>
        <div v-if="successMessage" class="success-message">{{ successMessage }}</div>

        <!-- Boutons d'action -->
        <div class="form-actions">
          <button type="button" @click="cancelEdit" class="btn-cancel">
            Annuler
          </button>
          <button type="submit" :disabled="isLoading" class="btn-save">
            {{ isLoading ? 'Sauvegarde...' : 'Sauvegarder' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { accountService } from "@/_services";

export default {
  name: "ProfileEditView",
  data() {
    return {
      profile: {
        first_name: "",
        last_name: "",
        username: "",
        email: "",
        birth_date: "",
        gender: "",
        looking_for: "",
        city: "",
        bio: "",
        profile_photo: null,
      },
      originalProfile: {},
      photoPreview: null,
      newPhotoFile: null,
      errors: "",
      successMessage: "",
      isLoading: false,
    };
  },
  
  async mounted() {
    await this.loadUserProfile();
  },
  
  methods: {
    async loadUserProfile() {
      try {
        this.isLoading = true;
        
        // Récupérer les données utilisateur depuis le store ou l'API
        const user = this.$store.getters.getUser;
        if (user) {
          this.profile = { ...user };
          this.originalProfile = { ...user };
        } else {
          // Si pas de données dans le store, récupérer depuis l'API
          await accountService.refreshUserData();
          const updatedUser = this.$store.getters.getUser;
          if (updatedUser) {
            this.profile = { ...updatedUser };
            this.originalProfile = { ...updatedUser };
          }
        }
      } catch (error) {
        console.error("Erreur lors du chargement du profil:", error);
        this.errors = "Erreur lors du chargement du profil";
      } finally {
        this.isLoading = false;
      }
    },
    
    handlePhotoUpload(event) {
      const file = event.target.files[0];
      if (file) {
        // Validation de la taille (5MB max)
        if (file.size > 5 * 1024 * 1024) {
          this.errors = "La photo ne doit pas dépasser 5MB";
          return;
        }
        
        // Validation du type
        if (!file.type.startsWith('image/')) {
          this.errors = "Veuillez sélectionner une image valide";
          return;
        }
        
        this.newPhotoFile = file;
        
        // Créer l'aperçu
        const reader = new FileReader();
        reader.onload = (e) => {
          this.photoPreview = e.target.result;
        };
        reader.readAsDataURL(file);
        
        this.errors = "";
      }
    },
    
    triggerPhotoUpload() {
      document.getElementById('profile_photo').click();
    },
    
    removePhoto() {
      this.profile.profile_photo = null;
      this.photoPreview = null;
      this.newPhotoFile = null;
      
      // Réinitialiser l'input file
      const fileInput = document.getElementById('profile_photo');
      if (fileInput) {
        fileInput.value = '';
      }
    },
    
    async saveProfile() {
      try {
        this.isLoading = true;
        this.errors = "";
        this.successMessage = "";
        
        const formData = new FormData();
        
        // Ajouter tous les champs du profil
        Object.keys(this.profile).forEach(key => {
          if (key !== 'profile_photo' && this.profile[key] !== null) {
            formData.append(key, this.profile[key]);
          }
        });
        
        // Ajouter la nouvelle photo si elle existe
        if (this.newPhotoFile) {
          formData.append('profile_photo', this.newPhotoFile);
        }
        
        // Appel API pour sauvegarder le profil
        const response = await fetch("http://127.0.0.1:8000/api/user/profile/", {
          method: "PUT",
          headers: {
            'Authorization': `Bearer ${accountService.getToken()}`
          },
          body: formData,
        });
        
        if (response.ok) {
          const updatedUser = await response.json();
          
          // Mettre à jour le store
          this.$store.commit('setUser', updatedUser);
          accountService.saveUser(updatedUser);
          
          this.successMessage = "Profil mis à jour avec succès !";
          this.originalProfile = { ...this.profile };
          
          // Rediriger après 2 secondes
          setTimeout(() => {
            this.$router.push('/');
          }, 2000);
        } else {
          const errorData = await response.json();
          this.errors = errorData.message || "Erreur lors de la sauvegarde";
        }
      } catch (error) {
        console.error("Erreur lors de la sauvegarde:", error);
        this.errors = "Erreur de connexion au serveur";
      } finally {
        this.isLoading = false;
      }
    },
    
    cancelEdit() {
      // Restaurer les données originales
      this.profile = { ...this.originalProfile };
      this.photoPreview = null;
      this.newPhotoFile = null;
      this.errors = "";
      this.successMessage = "";
      
      // Retourner à la page précédente
      this.$router.go(-1);
    },
  },
};
</script>

<style scoped>
.profile-edit-container {
  min-height: 100vh;
  background-image: url("@/assets/Accueil.webp");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: flex-start;
}

.profile-edit-card {
  background: #3c0940;
  border-radius: 10px;
  padding: 30px;
  max-width: 800px;
  width: 100%;
  margin-top: 50px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.profile-edit-card h2 {
  color: #ffffff;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
  font-style: italic;
}

.profile-form {
  color: white;
}

/* Section Photo */
.photo-section {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #666;
}

.photo-section h3 {
  color: #1f8ea5;
  margin-bottom: 20px;
}

.current-photo {
  position: relative;
  display: inline-block;
  margin-bottom: 15px;
}

.profile-image {
  width: 150px;
  height: 150px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid #1f8ea5;
}

.remove-photo-btn {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #ff6b6b;
  color: white;
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  cursor: pointer;
  font-size: 12px;
}

.remove-photo-btn:hover {
  background-color: #ff5252;
}

.photo-upload {
  margin: 20px 0;
}

.upload-btn {
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  padding: 20px;
  background-color: #1f8ea5;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.upload-btn:hover {
  background-color: #1a7a94;
}

.upload-btn span:first-child {
  font-size: 24px;
}

.change-photo-btn {
  background-color: #1f8ea5;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 10px;
}

.change-photo-btn:hover {
  background-color: #1a7a94;
}

/* Sections du formulaire */
.form-section {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #666;
}

.form-section:last-of-type {
  border-bottom: none;
}

.form-section h3 {
  color: #1f8ea5;
  margin-bottom: 20px;
  font-size: 18px;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
}

.form-group {
  flex: 1;
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  color: #ffffff;
  font-weight: 500;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
  background-color: white;
  color: #333;
}

.form-group input:disabled {
  background-color: #f5f5f5;
  color: #666;
  cursor: not-allowed;
}

.form-group textarea {
  resize: vertical;
  font-family: inherit;
}

/* Messages */
.error-message {
  color: #ff6b6b;
  background-color: rgba(255, 107, 107, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

.success-message {
  color: #51cf66;
  background-color: rgba(81, 207, 102, 0.1);
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
  text-align: center;
}

/* Boutons d'action */
.form-actions {
  display: flex;
  justify-content: space-between;
  gap: 15px;
  margin-top: 30px;
}

.btn-cancel,
.btn-save {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.btn-cancel {
  background-color: #666;
  color: white;
}

.btn-cancel:hover {
  background-color: #555;
}

.btn-save {
  background-color: #1f8ea5;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background-color: #1a7a94;
}

.btn-save:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 768px) {
  .profile-edit-container {
    padding: 10px;
  }

  .profile-edit-card {
    margin-top: 20px;
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .form-actions {
    flex-direction: column;
  }
}
</style>
