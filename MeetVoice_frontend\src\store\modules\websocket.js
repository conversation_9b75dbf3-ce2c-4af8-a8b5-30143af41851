import meetVoiceWebSocketService from '@/services/meetvoice-websocket'
import webRTCService from '@/services/webrtc-service'

export default {
  namespaced: true,
  
  state: {
    // État de connexion WebSocket
    isConnected: false,
    isAuthenticated: false,
    connectionError: null,
    reconnectAttempts: 0,
    
    // Informations utilisateur
    userId: null,
    
    // Sessions vocales
    isInCall: false,
    currentSessionId: null,
    participants: [],
    
    // Appels WebRTC
    incomingCall: null,
    localStream: null,
    remoteStreams: new Map(),
    callStats: {},
    
    // Contrôles média
    isMicrophoneEnabled: true,
    isCameraEnabled: true,
    isScreenSharing: false,
    
    // Messages en temps réel
    activeRooms: [],
    unreadMessages: 0,
    
    // Statistiques de connexion
    connectionStats: {
      lastPingTime: null,
      lastPongTime: null,
      messagesSent: 0,
      messagesReceived: 0
    }
  },
  
  mutations: {
    // Connexion WebSocket
    setConnected(state, connected) {
      state.isConnected = connected
    },
    
    setAuthenticated(state, authenticated) {
      state.isAuthenticated = authenticated
    },
    
    setConnectionError(state, error) {
      state.connectionError = error
    },
    
    setReconnectAttempts(state, attempts) {
      state.reconnectAttempts = attempts
    },
    
    // Utilisateur
    setUserId(state, userId) {
      state.userId = userId
    },
    
    // Sessions vocales
    setInCall(state, inCall) {
      state.isInCall = inCall
    },
    
    setCurrentSession(state, sessionId) {
      state.currentSessionId = sessionId
    },
    
    setParticipants(state, participants) {
      state.participants = participants
    },
    
    addParticipant(state, participant) {
      if (!state.participants.includes(participant)) {
        state.participants.push(participant)
      }
    },
    
    removeParticipant(state, participant) {
      const index = state.participants.indexOf(participant)
      if (index > -1) {
        state.participants.splice(index, 1)
      }
    },
    
    // Appels WebRTC
    setIncomingCall(state, callData) {
      state.incomingCall = callData
    },
    
    clearIncomingCall(state) {
      state.incomingCall = null
    },
    
    setLocalStream(state, stream) {
      state.localStream = stream
    },
    
    addRemoteStream(state, { userId, stream }) {
      state.remoteStreams.set(userId, stream)
    },
    
    removeRemoteStream(state, userId) {
      state.remoteStreams.delete(userId)
    },
    
    clearRemoteStreams(state) {
      state.remoteStreams.clear()
    },
    
    setCallStats(state, stats) {
      state.callStats = stats
    },
    
    // Contrôles média
    setMicrophoneEnabled(state, enabled) {
      state.isMicrophoneEnabled = enabled
    },
    
    setCameraEnabled(state, enabled) {
      state.isCameraEnabled = enabled
    },
    
    setScreenSharing(state, sharing) {
      state.isScreenSharing = sharing
    },
    
    // Messages
    addActiveRoom(state, roomId) {
      if (!state.activeRooms.includes(roomId)) {
        state.activeRooms.push(roomId)
      }
    },
    
    removeActiveRoom(state, roomId) {
      const index = state.activeRooms.indexOf(roomId)
      if (index > -1) {
        state.activeRooms.splice(index, 1)
      }
    },
    
    incrementUnreadMessages(state) {
      state.unreadMessages++
    },
    
    resetUnreadMessages(state) {
      state.unreadMessages = 0
    },
    
    // Statistiques
    updateConnectionStats(state, stats) {
      state.connectionStats = { ...state.connectionStats, ...stats }
    },
    
    incrementMessagesSent(state) {
      state.connectionStats.messagesSent++
    },
    
    incrementMessagesReceived(state) {
      state.connectionStats.messagesReceived++
    }
  },
  
  actions: {
    // Initialiser la connexion WebSocket
    async initializeWebSocket({ commit, rootGetters }) {
      try {
        const token = rootGetters['auth/token']
        if (!token) {
          throw new Error('Token d\'authentification manquant')
        }
        
        // Initialiser le service
        meetVoiceWebSocketService.init(this)
        
        // Configurer les listeners d'événements
        this.dispatch('websocket/setupEventListeners')
        
        // Se connecter
        await meetVoiceWebSocketService.connect(token)
        
        commit('setConnected', true)
        commit('setConnectionError', null)
        
        return true
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Configurer les listeners d'événements
    setupEventListeners({ commit, dispatch }) {
      // Événements de connexion
      meetVoiceWebSocketService.on('AuthSuccess', (data) => {
        commit('setAuthenticated', true)
        commit('setUserId', data.user_id)
      })
      
      meetVoiceWebSocketService.on('AuthError', (data) => {
        commit('setConnectionError', data.message)
      })
      
      // Événements de session vocale
      meetVoiceWebSocketService.on('VoiceSessionJoined', (data) => {
        commit('setInCall', true)
        commit('setCurrentSession', data.session_id)
        commit('setParticipants', data.participants)
      })
      
      meetVoiceWebSocketService.on('VoiceSessionLeft', () => {
        commit('setInCall', false)
        commit('setCurrentSession', null)
        commit('setParticipants', [])
      })
      
      // Événements WebRTC
      webRTCService.on('incomingCall', (callData) => {
        commit('setIncomingCall', callData)
      })
      
      webRTCService.on('remoteStreamAdded', ({ userId, stream }) => {
        commit('addRemoteStream', { userId, stream })
      })
      
      webRTCService.on('remoteStreamRemoved', ({ userId }) => {
        commit('removeRemoteStream', userId)
      })
      
      webRTCService.on('callEnded', () => {
        dispatch('endCall')
      })
      
      // Événements de messages
      meetVoiceWebSocketService.on('MessageReceived', (message) => {
        commit('incrementMessagesReceived')
        commit('incrementUnreadMessages')
        dispatch('messages/addMessage', message, { root: true })
      })
    },
    
    // Envoyer un message de chat
    sendChatMessage({ commit }, { roomId, content, messageType = 'Text' }) {
      meetVoiceWebSocketService.sendChatMessage(roomId, content, messageType)
      commit('incrementMessagesSent')
      commit('addActiveRoom', roomId)
    },
    
    // Rejoindre une session vocale
    async joinVoiceSession({ commit }, sessionId) {
      try {
        meetVoiceWebSocketService.joinVoiceSession(sessionId)
        commit('setCurrentSession', sessionId)
        return true
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Quitter une session vocale
    async leaveVoiceSession({ commit, state }) {
      try {
        if (state.currentSessionId) {
          meetVoiceWebSocketService.leaveVoiceSession(state.currentSessionId)
        }
        
        // Nettoyer l'état local
        commit('setInCall', false)
        commit('setCurrentSession', null)
        commit('setParticipants', [])
        
        return true
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Démarrer un appel WebRTC
    async startCall({ commit }, { targetUserId, callType = 'voice' }) {
      try {
        const localStream = await webRTCService.startCall(targetUserId, callType)
        commit('setLocalStream', localStream)
        commit('setInCall', true)
        
        return localStream
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Répondre à un appel
    async answerCall({ commit, state }, { accept = true, callType = 'voice' }) {
      try {
        if (!state.incomingCall) {
          throw new Error('Aucun appel entrant')
        }
        
        if (accept) {
          const localStream = await webRTCService.answerCall(
            state.incomingCall.fromUserId,
            state.incomingCall.offer,
            callType
          )
          
          commit('setLocalStream', localStream)
          commit('setInCall', true)
        }
        
        commit('clearIncomingCall')
        return true
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Terminer un appel
    endCall({ commit }) {
      webRTCService.endCall()
      
      // Nettoyer l'état
      commit('setInCall', false)
      commit('setLocalStream', null)
      commit('clearRemoteStreams')
      commit('clearIncomingCall')
      commit('setMicrophoneEnabled', true)
      commit('setCameraEnabled', true)
      commit('setScreenSharing', false)
    },
    
    // Basculer le microphone
    toggleMicrophone({ commit }) {
      const enabled = webRTCService.toggleMicrophone()
      commit('setMicrophoneEnabled', enabled)
      return enabled
    },
    
    // Basculer la caméra
    toggleCamera({ commit }) {
      const enabled = webRTCService.toggleCamera()
      commit('setCameraEnabled', enabled)
      return enabled
    },
    
    // Changer de caméra
    async switchCamera() {
      try {
        await webRTCService.switchCamera()
        return true
      } catch (error) {
        console.error('Erreur lors du changement de caméra:', error)
        throw error
      }
    },
    
    // Partager l'écran
    async shareScreen({ commit }) {
      try {
        // TODO: Implémenter le partage d'écran
        commit('setScreenSharing', true)
        return true
      } catch (error) {
        commit('setConnectionError', error.message)
        throw error
      }
    },
    
    // Obtenir les statistiques d'appel
    async getCallStats({ commit }) {
      try {
        const stats = await webRTCService.getCallStats()
        commit('setCallStats', stats)
        return stats
      } catch (error) {
        console.error('Erreur lors de la récupération des statistiques:', error)
        return {}
      }
    },
    
    // Obtenir les périphériques disponibles
    async getAvailableDevices() {
      try {
        return await webRTCService.getAvailableDevices()
      } catch (error) {
        console.error('Erreur lors de l\'énumération des périphériques:', error)
        return { audioInputs: [], videoInputs: [], audioOutputs: [] }
      }
    },
    
    // Mettre à jour les statistiques de connexion
    updateConnectionStats({ commit }) {
      const stats = meetVoiceWebSocketService.getConnectionStats()
      commit('updateConnectionStats', {
        lastPingTime: Date.now(),
        ...stats
      })
    },
    
    // Se déconnecter
    disconnect({ commit }) {
      meetVoiceWebSocketService.disconnect()
      webRTCService.endCall()
      
      // Réinitialiser l'état
      commit('setConnected', false)
      commit('setAuthenticated', false)
      commit('setUserId', null)
      commit('setInCall', false)
      commit('setCurrentSession', null)
      commit('setParticipants', [])
      commit('clearIncomingCall')
      commit('setLocalStream', null)
      commit('clearRemoteStreams')
    }
  },
  
  getters: {
    // État de connexion
    isConnected: state => state.isConnected,
    isAuthenticated: state => state.isAuthenticated,
    connectionError: state => state.connectionError,
    
    // Informations utilisateur
    userId: state => state.userId,
    
    // Sessions vocales
    isInCall: state => state.isInCall,
    currentSessionId: state => state.currentSessionId,
    participants: state => state.participants,
    participantsCount: state => state.participants.length,
    
    // Appels WebRTC
    hasIncomingCall: state => !!state.incomingCall,
    incomingCall: state => state.incomingCall,
    localStream: state => state.localStream,
    remoteStreams: state => state.remoteStreams,
    remoteStreamsArray: state => Array.from(state.remoteStreams.entries()),
    
    // Contrôles média
    isMicrophoneEnabled: state => state.isMicrophoneEnabled,
    isCameraEnabled: state => state.isCameraEnabled,
    isScreenSharing: state => state.isScreenSharing,
    
    // Messages
    activeRooms: state => state.activeRooms,
    unreadMessages: state => state.unreadMessages,
    hasUnreadMessages: state => state.unreadMessages > 0,
    
    // Statistiques
    connectionStats: state => state.connectionStats,
    
    // État global
    isWebRTCSupported: () => webRTCService.constructor.isSupported(),
    
    canMakeCall: state => state.isConnected && state.isAuthenticated && !state.isInCall,
    
    callQuality: state => {
      // Calculer la qualité de l'appel basée sur les statistiques
      const stats = Object.values(state.callStats)
      if (stats.length === 0) return 'unknown'
      
      const avgPacketLoss = stats.reduce((sum, stat) => {
        return sum + (stat.audio?.packetsLost || 0) + (stat.video?.packetsLost || 0)
      }, 0) / stats.length
      
      if (avgPacketLoss < 1) return 'excellent'
      if (avgPacketLoss < 3) return 'good'
      if (avgPacketLoss < 5) return 'fair'
      return 'poor'
    }
  }
}
