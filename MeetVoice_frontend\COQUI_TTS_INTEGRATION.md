# 🐸 Intégration Coqui TTS dans MeetVoice

## 🎯 Avantages de Coqui TTS

- ✅ **100% Open Source** - <PERSON>cun coût, aucune limite
- ✅ **Qualité professionnelle** - Voix neurales très naturelles
- ✅ **Clonage vocal** - Créer des voix personnalisées
- ✅ **Multilingue** - Support français natif
- ✅ **XTTS v2** - Modèle de pointe avec latence <200ms
- ✅ **Local** - Fonctionne entièrement hors ligne

## 🔧 Installation Backend

### 1. Installation de Coqui TTS

```bash
# Installation via pip
pip install TTS

# Ou installation depuis les sources
git clone https://github.com/coqui-ai/TTS
cd TTS
pip install -e .
```

### 2. Serveur TTS pour MeetVoice

Créez un serveur Flask/FastAPI pour exposer Coqui TTS :

```python
# backend/tts_server.py
from flask import Flask, request, send_file
from TTS.api import TTS
import torch
import io
import base64

app = Flask(__name__)

# Initialiser TTS avec le modèle français
device = "cuda" if torch.cuda.is_available() else "cpu"
tts = TTS("tts_models/fr/css10/vits").to(device)

@app.route('/api/tts/synthesize', methods=['POST'])
def synthesize():
    data = request.json
    text = data.get('text', '')
    
    # Générer l'audio
    wav = tts.tts(text=text)
    
    # Convertir en base64 pour le frontend
    audio_buffer = io.BytesIO()
    # Sauvegarder l'audio dans le buffer
    # ... code de conversion ...
    
    return {
        'audio': base64.b64encode(audio_buffer.getvalue()).decode(),
        'success': True
    }

@app.route('/api/tts/models', methods=['GET'])
def list_models():
    return {
        'models': TTS().list_models(),
        'success': True
    }

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

## 🎤 Modèles recommandés pour le français

### 1. Modèles français natifs
```python
# Modèle VITS français (recommandé)
tts = TTS("tts_models/fr/css10/vits")

# Modèle Tacotron2 français
tts = TTS("tts_models/fr/mai/tacotron2-DDC")
```

### 2. Modèles multilingues avec français
```python
# XTTS v2 - Le meilleur pour le clonage vocal
tts = TTS("tts_models/multilingual/multi-dataset/xtts_v2")

# YourTTS - Bon pour le multilingue
tts = TTS("tts_models/multilingual/multi-dataset/your_tts")
```

### 3. Modèles Fairseq (1100+ langues)
```python
# Modèle français Fairseq
tts = TTS("tts_models/fr/fairseq/vits")
```

## 🌐 Intégration Frontend

### Service TTS pour Vue.js

```javascript
// src/_services/coqui-tts.service.js
class CoquiTTSService {
  constructor() {
    this.baseURL = 'http://localhost:5000/api/tts';
  }

  async synthesize(text, options = {}) {
    try {
      const response = await fetch(`${this.baseURL}/synthesize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text,
          model: options.model || 'tts_models/fr/css10/vits',
          speaker: options.speaker,
          language: options.language || 'fr'
        })
      });

      const data = await response.json();
      
      if (data.success) {
        // Convertir base64 en audio
        const audioBlob = this.base64ToBlob(data.audio, 'audio/wav');
        const audioUrl = URL.createObjectURL(audioBlob);
        return audioUrl;
      }
      
      throw new Error('Erreur de synthèse TTS');
    } catch (error) {
      console.error('Erreur Coqui TTS:', error);
      throw error;
    }
  }

  async getAvailableModels() {
    try {
      const response = await fetch(`${this.baseURL}/models`);
      const data = await response.json();
      return data.models;
    } catch (error) {
      console.error('Erreur récupération modèles:', error);
      return [];
    }
  }

  base64ToBlob(base64, mimeType) {
    const byteCharacters = atob(base64);
    const byteNumbers = new Array(byteCharacters.length);
    
    for (let i = 0; i < byteCharacters.length; i++) {
      byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    
    const byteArray = new Uint8Array(byteNumbers);
    return new Blob([byteArray], { type: mimeType });
  }
}

export const coquiTTSService = new CoquiTTSService();
```

### Composant Vue.js

```vue
<!-- src/components/CoquiTTSPlayer.vue -->
<template>
  <div class="coqui-tts-player">
    <div class="tts-controls">
      <textarea 
        v-model="text" 
        placeholder="Entrez le texte à synthétiser..."
        rows="3"
      ></textarea>
      
      <select v-model="selectedModel">
        <option value="tts_models/fr/css10/vits">Français VITS</option>
        <option value="tts_models/multilingual/multi-dataset/xtts_v2">XTTS v2</option>
        <option value="tts_models/fr/fairseq/vits">Français Fairseq</option>
      </select>
      
      <button 
        @click="synthesizeText" 
        :disabled="isLoading || !text.trim()"
        class="synthesize-btn"
      >
        {{ isLoading ? '🔄 Génération...' : '🎤 Générer la voix' }}
      </button>
    </div>

    <div v-if="audioUrl" class="audio-player">
      <audio :src="audioUrl" controls></audio>
      <button @click="downloadAudio" class="download-btn">
        📥 Télécharger
      </button>
    </div>

    <div v-if="error" class="error-message">
      ❌ {{ error }}
    </div>
  </div>
</template>

<script>
import { coquiTTSService } from '@/_services/coqui-tts.service';

export default {
  name: 'CoquiTTSPlayer',
  data() {
    return {
      text: 'Bonjour ! Bienvenue sur MeetVoice. Cette voix est générée par Coqui TTS, une solution open source de haute qualité.',
      selectedModel: 'tts_models/fr/css10/vits',
      audioUrl: null,
      isLoading: false,
      error: ''
    };
  },
  
  methods: {
    async synthesizeText() {
      if (!this.text.trim()) return;
      
      try {
        this.isLoading = true;
        this.error = '';
        
        // Libérer l'URL précédente
        if (this.audioUrl) {
          URL.revokeObjectURL(this.audioUrl);
        }
        
        // Générer la nouvelle synthèse
        this.audioUrl = await coquiTTSService.synthesize(this.text, {
          model: this.selectedModel
        });
        
      } catch (error) {
        this.error = error.message;
      } finally {
        this.isLoading = false;
      }
    },
    
    downloadAudio() {
      if (!this.audioUrl) return;
      
      const link = document.createElement('a');
      link.href = this.audioUrl;
      link.download = 'coqui-tts-audio.wav';
      link.click();
    }
  },
  
  beforeUnmount() {
    if (this.audioUrl) {
      URL.revokeObjectURL(this.audioUrl);
    }
  }
};
</script>
```

## 🚀 Déploiement

### 1. Serveur TTS

```bash
# Démarrer le serveur TTS
cd backend
python tts_server.py
```

### 2. Configuration Frontend

```javascript
// src/_services/index.js
export { coquiTTSService } from './coqui-tts.service';
```

## 🎯 Utilisation dans MeetVoice

### 1. Interview vocal avec Coqui TTS

Remplacer la synthèse vocale native par Coqui TTS dans l'interview vocal :

```javascript
// Dans VoiceInterview.vue
import { coquiTTSService } from '@/_services';

methods: {
  async speakQuestion(questionText) {
    try {
      const audioUrl = await coquiTTSService.synthesize(questionText);
      const audio = new Audio(audioUrl);
      audio.play();
    } catch (error) {
      console.error('Erreur TTS:', error);
      // Fallback vers synthèse native
      this.fallbackToNativeTTS(questionText);
    }
  }
}
```

### 2. Configuration des médias avec Coqui TTS

Ajouter Coqui TTS comme option dans la configuration :

```javascript
// Dans MediaDeviceConfig.vue
async testCoquiTTS() {
  try {
    const audioUrl = await coquiTTSService.synthesize(
      'Test de Coqui TTS. Cette voix est générée par intelligence artificielle.'
    );
    
    const audio = new Audio(audioUrl);
    audio.play();
    
    this.successMessage = 'Test Coqui TTS réussi !';
  } catch (error) {
    this.error = 'Erreur Coqui TTS: ' + error.message;
  }
}
```

## 💡 Avantages pour MeetVoice

1. **Qualité professionnelle** - Voix beaucoup plus naturelles
2. **Personnalisation** - Possibilité de créer des voix uniques
3. **Multilingue** - Support natif de nombreuses langues
4. **Évolutif** - Peut être amélioré avec de nouveaux modèles
5. **Open Source** - Contrôle total et aucun coût

## 🔄 Migration progressive

1. **Phase 1** : Ajouter Coqui TTS comme option supplémentaire
2. **Phase 2** : Utiliser Coqui TTS par défaut avec fallback natif
3. **Phase 3** : Remplacer complètement la synthèse native

**Coqui TTS va révolutionner la qualité vocale de MeetVoice ! 🚀**
