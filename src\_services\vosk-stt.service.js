/**
 * Service Vosk Speech-to-Text - VERSION SIMPLIFIÉE
 * Reconnaissance vocale offline avec Vosk + WebAssembly
 * Remplace l'API browser pour plus de stabilité
 */

// Import dynamique pour éviter les erreurs de compilation
// import { createModel, createRecognizer } from 'vosk-browser';

class VoskSTTService {
  constructor() {
    this.model = null;
    this.recognizer = null;
    this.isInitialized = false;
    this.isListening = false;
    this.audioContext = null;
    this.mediaStream = null;
    this.processor = null;
    this.sampleRate = 16000;
    
    // URLs des modèles Vosk français
    this.modelUrls = {
      small: 'https://alphacephei.com/vosk/models/vosk-model-small-fr-0.22.zip',
      medium: 'https://alphacephei.com/vosk/models/vosk-model-fr-0.22.zip'
    };
    
    console.log('🎤 Service Vosk STT initialisé');
  }

  /**
   * Initialiser Vosk avec le modèle français
   * @param {string} modelSize - 'small' ou 'medium'
   */
  async initialize(modelSize = 'small') {
    if (this.isInitialized) {
      console.log('✅ Vosk déjà initialisé');
      return true;
    }

    try {
      console.log(`🔄 Chargement du modèle Vosk ${modelSize}...`);

      // Import dynamique de vosk-browser
      const { createModel, createRecognizer } = await import('vosk-browser');

      // Charger le modèle Vosk français
      this.model = await createModel({
        url: this.modelUrls[modelSize],
        name: `vosk-model-fr-${modelSize}`
      });

      console.log('✅ Modèle Vosk chargé avec succès');

      // Créer le recognizer
      this.recognizer = await createRecognizer(this.model, this.sampleRate);

      this.isInitialized = true;
      console.log('🎤 Vosk prêt pour la reconnaissance vocale');

      return true;

    } catch (error) {
      console.error('❌ Erreur initialisation Vosk:', error);
      console.warn('🔄 Fallback vers API browser...');

      // Fallback vers l'API browser si Vosk échoue
      throw new Error(`Vosk non disponible, utilisation API browser: ${error.message}`);
    }
  }

  /**
   * Démarrer l'écoute vocale avec Vosk
   * @param {Object} options - Options d'écoute
   */
  async startListening(options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        // Vérifier l'initialisation
        if (!this.isInitialized) {
          console.log('🔄 Initialisation Vosk en cours...');
          await this.initialize(options.modelSize || 'small');
        }

        if (this.isListening) {
          console.warn('⚠️ Écoute déjà en cours');
          return;
        }

        console.log('🎤 Démarrage écoute Vosk...');

        // Obtenir l'accès au microphone
        this.mediaStream = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: this.sampleRate,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true
          }
        });

        // Créer le contexte audio
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
          sampleRate: this.sampleRate
        });

        const source = this.audioContext.createMediaStreamSource(this.mediaStream);
        
        // Créer le processeur audio pour Vosk
        this.processor = this.audioContext.createScriptProcessor(4096, 1, 1);
        
        this.processor.onaudioprocess = (event) => {
          if (!this.isListening || !this.recognizer) return;

          const inputBuffer = event.inputBuffer;
          const inputData = inputBuffer.getChannelData(0);
          
          // Convertir en Int16Array pour Vosk
          const int16Data = new Int16Array(inputData.length);
          for (let i = 0; i < inputData.length; i++) {
            int16Data[i] = Math.max(-32768, Math.min(32767, inputData[i] * 32768));
          }

          // Envoyer à Vosk
          this.recognizer.acceptWaveform(int16Data);
        };

        // Connecter le pipeline audio
        source.connect(this.processor);
        this.processor.connect(this.audioContext.destination);

        // Configurer les callbacks Vosk
        this.recognizer.onResult = (result) => {
          const text = result.text;
          if (text && text.trim()) {
            console.log('📝 Vosk résultat final:', text);
            if (options.onResult) {
              options.onResult(text, '');
            }
          }
        };

        this.recognizer.onPartialResult = (result) => {
          const text = result.partial;
          if (text && text.trim()) {
            console.log('📝 Vosk résultat partiel:', text);
            if (options.onInterim) {
              options.onInterim(text);
            }
          }
        };

        this.isListening = true;
        
        if (options.onStart) {
          options.onStart();
        }

        console.log('✅ Écoute Vosk démarrée');
        resolve('Écoute démarrée avec Vosk');

      } catch (error) {
        console.error('❌ Erreur démarrage Vosk:', error);
        this.cleanup();
        reject(error);
      }
    });
  }

  /**
   * Arrêter l'écoute vocale
   */
  stopListening() {
    console.log('🛑 Arrêt écoute Vosk...');
    
    this.isListening = false;
    
    this.cleanup();
    
    console.log('✅ Écoute Vosk arrêtée');
  }

  /**
   * Nettoyer les ressources audio
   */
  cleanup() {
    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }

    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }
  }

  /**
   * Vérifier si Vosk est supporté
   */
  isSupported() {
    return !!(navigator.mediaDevices && 
              navigator.mediaDevices.getUserMedia && 
              (window.AudioContext || window.webkitAudioContext) &&
              typeof WebAssembly !== 'undefined');
  }

  /**
   * Obtenir le statut du service
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isListening: this.isListening,
      isSupported: this.isSupported(),
      modelLoaded: !!this.model,
      recognizerReady: !!this.recognizer
    };
  }

  /**
   * Réinitialiser le service
   */
  reset() {
    console.log('🔄 Reset service Vosk...');
    this.stopListening();
    this.cleanup();
    
    // Garder le modèle chargé pour éviter de le recharger
    // this.model = null;
    // this.recognizer = null;
    // this.isInitialized = false;
  }
}

// Export de l'instance unique
export const voskSTTService = new VoskSTTService();
export default voskSTTService;
