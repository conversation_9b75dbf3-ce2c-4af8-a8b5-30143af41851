use anyhow::Result;
use libp2p::{
    core::upgrade,
    futures::StreamExt,
    gossipsub::{self, IdentTopic as Topic, MessageAuthenticity, ValidationMode},
    identify,
    kad::{self, store::MemoryStore},
    mdns,
    noise,
    ping,
    request_response::{self, ProtocolSupport},
    swarm::{NetworkBehaviour, SwarmEvent},
    tcp,
    yamux, Multiaddr, PeerId, Swarm, Transport,
};
use std::{
    collections::HashMap,
    sync::Arc,
    time::Duration,
};
use tokio::sync::{mpsc, RwLock};
use tracing::{info, warn, error, debug};

use crate::config::P2PConfig;

pub mod behaviour;
pub mod protocols;

use behaviour::MeetVoiceBehaviour;
use protocols::{VoiceProtocol, VideoProtocol};

#[derive(Clone)]
pub struct P2PManager {
    peer_id: PeerId,
    connected_peers: Arc<RwLock<HashMap<PeerId, PeerInfo>>>,
    command_sender: mpsc::UnboundedSender<P2PCommand>,
}

#[derive(Debug, Clone)]
pub struct PeerInfo {
    pub peer_id: PeerId,
    pub addresses: Vec<Multiaddr>,
    pub protocols: Vec<String>,
    pub connected_at: chrono::DateTime<chrono::Utc>,
    pub last_seen: chrono::DateTime<chrono::Utc>,
}

#[derive(Debug)]
pub enum P2PCommand {
    ConnectToPeer(String),
    DisconnectFromPeer(String),
    SendVoiceData { peer_id: PeerId, data: Vec<u8> },
    SendVideoData { peer_id: PeerId, data: Vec<u8> },
    PublishMessage { topic: String, data: Vec<u8> },
    SubscribeToTopic(String),
    UnsubscribeFromTopic(String),
}

impl P2PManager {
    pub async fn new(config: &P2PConfig) -> Result<Self> {
        info!("🔗 Initialisation du gestionnaire P2P...");
        
        // Génération de la clé et du PeerId
        let local_key = libp2p::identity::Keypair::generate_ed25519();
        let local_peer_id = PeerId::from(local_key.public());
        
        info!("🆔 Peer ID: {}", local_peer_id);
        
        // Configuration du transport
        let transport = libp2p::tcp::tokio::Transport::new(libp2p::tcp::Config::default().nodelay(true))
            .upgrade(upgrade::Version::V1)
            .authenticate(noise::Config::new(&local_key)?)
            .multiplex(yamux::Config::default())
            .boxed();
        
        // Configuration du comportement réseau
        let behaviour = MeetVoiceBehaviour::new(&local_key, config).await?;
        
        // Création du Swarm
        let mut swarm = Swarm::with_tokio_executor(transport, behaviour, local_peer_id);
        
        // Configuration de l'écoute
        let listen_addr: Multiaddr = format!("/ip4/0.0.0.0/tcp/{}", config.listen_port)
            .parse()?;
        swarm.listen_on(listen_addr.clone())?;
        
        info!("👂 Écoute P2P sur: {}", listen_addr);
        
        // Canal de commandes
        let (command_sender, command_receiver) = mpsc::unbounded_channel();
        
        let connected_peers = Arc::new(RwLock::new(HashMap::new()));
        let connected_peers_clone = connected_peers.clone();
        
        // Task pour gérer le swarm
        tokio::spawn(async move {
            Self::run_swarm(swarm, command_receiver, connected_peers_clone).await;
        });
        
        Ok(Self {
            peer_id: local_peer_id,
            connected_peers,
            command_sender,
        })
    }
    
    async fn run_swarm(
        mut swarm: Swarm<MeetVoiceBehaviour>,
        mut command_receiver: mpsc::UnboundedReceiver<P2PCommand>,
        connected_peers: Arc<RwLock<HashMap<PeerId, PeerInfo>>>,
    ) {
        loop {
            tokio::select! {
                // Traitement des commandes
                command = command_receiver.recv() => {
                    if let Some(command) = command {
                        Self::handle_command(&mut swarm, command).await;
                    } else {
                        break;
                    }
                }
                
                // Traitement des événements du swarm
                event = swarm.select_next_some() => {
                    Self::handle_swarm_event(event, &connected_peers).await;
                }
            }
        }
    }
    
    async fn handle_command(swarm: &mut Swarm<MeetVoiceBehaviour>, command: P2PCommand) {
        match command {
            P2PCommand::ConnectToPeer(addr_str) => {
                match addr_str.parse::<Multiaddr>() {
                    Ok(addr) => {
                        if let Err(e) = swarm.dial(addr.clone()) {
                            error!("❌ Erreur lors de la connexion à {}: {}", addr, e);
                        } else {
                            info!("📞 Tentative de connexion à: {}", addr);
                        }
                    }
                    Err(e) => {
                        error!("❌ Adresse invalide {}: {}", addr_str, e);
                    }
                }
            }
            
            P2PCommand::DisconnectFromPeer(peer_id_str) => {
                match peer_id_str.parse::<PeerId>() {
                    Ok(peer_id) => {
                        // Note: libp2p ne fournit pas de méthode directe pour déconnecter
                        // En pratique, on peut fermer les connexions spécifiques
                        info!("🔌 Déconnexion demandée pour: {}", peer_id);
                    }
                    Err(e) => {
                        error!("❌ Peer ID invalide {}: {}", peer_id_str, e);
                    }
                }
            }
            
            P2PCommand::SendVoiceData { peer_id, data } => {
                debug!("🎤 Envoi de données vocales à {}: {} bytes", peer_id, data.len());
                // Implémentation de l'envoi de données vocales
                // Utiliserait le protocole VoiceProtocol
            }
            
            P2PCommand::SendVideoData { peer_id, data } => {
                debug!("📹 Envoi de données vidéo à {}: {} bytes", peer_id, data.len());
                // Implémentation de l'envoi de données vidéo
                // Utiliserait le protocole VideoProtocol
            }
            
            P2PCommand::PublishMessage { topic, data } => {
                if let Err(e) = swarm.behaviour_mut().gossipsub.publish(
                    Topic::new(&topic),
                    data,
                ) {
                    error!("❌ Erreur lors de la publication sur {}: {}", topic, e);
                } else {
                    debug!("📢 Message publié sur le topic: {}", topic);
                }
            }
            
            P2PCommand::SubscribeToTopic(topic) => {
                if let Err(e) = swarm.behaviour_mut().gossipsub.subscribe(&Topic::new(&topic)) {
                    error!("❌ Erreur lors de l'abonnement à {}: {}", topic, e);
                } else {
                    info!("📡 Abonné au topic: {}", topic);
                }
            }
            
            P2PCommand::UnsubscribeFromTopic(topic) => {
                if let Err(e) = swarm.behaviour_mut().gossipsub.unsubscribe(&Topic::new(&topic)) {
                    error!("❌ Erreur lors du désabonnement de {}: {}", topic, e);
                } else {
                    info!("📡 Désabonné du topic: {}", topic);
                }
            }
        }
    }
    
    async fn handle_swarm_event(
        event: SwarmEvent<behaviour::MeetVoiceBehaviourEvent>,
        connected_peers: &Arc<RwLock<HashMap<PeerId, PeerInfo>>>,
    ) {
        match event {
            SwarmEvent::NewListenAddr { address, .. } => {
                info!("👂 Nouvelle adresse d'écoute: {}", address);
            }
            
            SwarmEvent::ConnectionEstablished { peer_id, endpoint, .. } => {
                info!("🤝 Connexion établie avec: {} ({})", peer_id, endpoint.get_remote_address());
                
                let peer_info = PeerInfo {
                    peer_id,
                    addresses: vec![endpoint.get_remote_address().clone()],
                    protocols: Vec::new(),
                    connected_at: chrono::Utc::now(),
                    last_seen: chrono::Utc::now(),
                };
                
                connected_peers.write().await.insert(peer_id, peer_info);
            }
            
            SwarmEvent::ConnectionClosed { peer_id, cause, .. } => {
                info!("👋 Connexion fermée avec: {} (cause: {:?})", peer_id, cause);
                connected_peers.write().await.remove(&peer_id);
            }
            
            SwarmEvent::Behaviour(event) => {
                Self::handle_behaviour_event(event, connected_peers).await;
            }
            
            SwarmEvent::IncomingConnection { .. } => {
                debug!("📞 Connexion entrante");
            }
            
            SwarmEvent::IncomingConnectionError { error, .. } => {
                warn!("❌ Erreur de connexion entrante: {}", error);
            }
            
            SwarmEvent::OutgoingConnectionError { peer_id, error, .. } => {
                warn!("❌ Erreur de connexion sortante vers {:?}: {}", peer_id, error);
            }
            
            _ => {}
        }
    }
    
    async fn handle_behaviour_event(
        event: behaviour::MeetVoiceBehaviourEvent,
        connected_peers: &Arc<RwLock<HashMap<PeerId, PeerInfo>>>,
    ) {
        match event {
            behaviour::MeetVoiceBehaviourEvent::Mdns(mdns::Event::Discovered(list)) => {
                for (peer_id, multiaddr) in list {
                    info!("🔍 Peer découvert via mDNS: {} à {}", peer_id, multiaddr);
                }
            }
            
            behaviour::MeetVoiceBehaviourEvent::Mdns(mdns::Event::Expired(list)) => {
                for (peer_id, multiaddr) in list {
                    debug!("⏰ Peer expiré via mDNS: {} à {}", peer_id, multiaddr);
                }
            }
            
            behaviour::MeetVoiceBehaviourEvent::Identify(identify::Event::Received { peer_id, info }) => {
                info!("🆔 Informations reçues de {}: agent={}, protocols={:?}", 
                      peer_id, info.agent_version, info.protocols);
                
                if let Some(mut peer_info) = connected_peers.write().await.get_mut(&peer_id) {
                    peer_info.protocols = info.protocols.iter().map(|p| p.to_string()).collect();
                    peer_info.last_seen = chrono::Utc::now();
                }
            }
            
            behaviour::MeetVoiceBehaviourEvent::Gossipsub(gossipsub::Event::Message {
                propagation_source,
                message_id,
                message,
            }) => {
                info!("📨 Message Gossipsub reçu de {} (ID: {}): {} bytes", 
                      propagation_source, message_id, message.data.len());
            }
            
            behaviour::MeetVoiceBehaviourEvent::Ping(ping::Event { peer, result }) => {
                match result {
                    Ok(duration) => {
                        debug!("🏓 Ping vers {} réussi: {:?}", peer, duration);
                        if let Some(mut peer_info) = connected_peers.write().await.get_mut(&peer) {
                            peer_info.last_seen = chrono::Utc::now();
                        }
                    }
                    Err(failure) => {
                        warn!("❌ Ping vers {} échoué: {}", peer, failure);
                    }
                }
            }
            
            _ => {}
        }
    }
    
    // API publique
    pub async fn connect_to_peer(&self, addr: &str) -> Result<()> {
        self.command_sender.send(P2PCommand::ConnectToPeer(addr.to_string()))?;
        Ok(())
    }
    
    pub async fn disconnect_from_peer(&self, peer_id: &str) -> Result<()> {
        self.command_sender.send(P2PCommand::DisconnectFromPeer(peer_id.to_string()))?;
        Ok(())
    }
    
    pub async fn send_voice_data(&self, peer_id: PeerId, data: Vec<u8>) -> Result<()> {
        self.command_sender.send(P2PCommand::SendVoiceData { peer_id, data })?;
        Ok(())
    }
    
    pub async fn send_video_data(&self, peer_id: PeerId, data: Vec<u8>) -> Result<()> {
        self.command_sender.send(P2PCommand::SendVideoData { peer_id, data })?;
        Ok(())
    }
    
    pub async fn publish_message(&self, topic: &str, data: Vec<u8>) -> Result<()> {
        self.command_sender.send(P2PCommand::PublishMessage {
            topic: topic.to_string(),
            data,
        })?;
        Ok(())
    }
    
    pub async fn subscribe_to_topic(&self, topic: &str) -> Result<()> {
        self.command_sender.send(P2PCommand::SubscribeToTopic(topic.to_string()))?;
        Ok(())
    }
    
    pub async fn get_connected_peers(&self) -> Result<Vec<PeerInfo>> {
        let peers = self.connected_peers.read().await;
        Ok(peers.values().cloned().collect())
    }
    
    pub fn get_peer_id(&self) -> PeerId {
        self.peer_id
    }
}
