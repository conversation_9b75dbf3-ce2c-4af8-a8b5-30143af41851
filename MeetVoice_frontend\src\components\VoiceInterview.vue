<!-- @format -->

<template>
  <main class="voice-interview" role="main">
    <section class="interview-container">
      <header>
        <h1>Interview Vocal</h1>
        <p class="interview-description">
          Répondez aux questions suivantes pour compléter votre profil.
          Cliquez sur le microphone et parlez clairement.
        </p>
      </header>
      
      <!-- Indicateur de progression par pages -->
      <section class="progress-container" aria-label="Progression de l'interview">
        <progress
          class="progress-bar"
          :value="currentPageIndex"
          :max="totalPages - 1"
          :aria-valuenow="currentPageIndex + 1"
          :aria-valuemax="totalPages"
          aria-label="Progression par pages"
        >
          {{ currentPageIndex + 1 }} sur {{ totalPages }}
        </progress>
        <p class="progress-text">
          Page {{ currentPageIndex + 1 }} sur {{ totalPages }} - Question {{ currentQuestionInPage + 1 }} sur {{ currentPageQuestions.length }}
        </p>
        <nav class="page-indicator" aria-label="Navigation des pages">
          <span
            v-for="(page, index) in totalPages"
            :key="index"
            :class="['page-dot', {
              'completed': index < currentPageIndex,
              'current': index === currentPageIndex,
              'pending': index > currentPageIndex
            }]"
            :aria-label="`Page ${index + 1} ${index < currentPageIndex ? 'complétée' : index === currentPageIndex ? 'actuelle' : 'en attente'}`"
          ></span>
        </nav>
      </section>
      
      <!-- Page actuelle -->
      <section v-if="!isCompleted" class="page-section">
        <header class="page-header">
          <h2 class="page-title">{{ currentPageTitle }}</h2>
          <p class="page-description">{{ currentPageDescription }}</p>
        </header>

        <!-- Question actuelle de la page -->
        <article v-if="currentQuestion" class="question-section">
          <header class="question-text">
            <h3>{{ currentQuestion.text }}</h3>
            <p v-if="currentQuestion.hint" class="question-hint">
              💡 {{ currentQuestion.hint }}
            </p>

          </header>
        
        <!-- Configuration déjà faite - Sophie utilise les paramètres du store -->

        <!-- Contrôles vocaux -->
        <section class="voice-controls" aria-label="Contrôles d'enregistrement vocal">
          <fieldset class="mic-controls">
            <legend class="sr-only">Contrôles du microphone</legend>
            <button
              @click="toggleListening"
              :disabled="!isSupported || isProcessing"
              :class="['mic-button', {
                'listening': isListening,
                'processing': isProcessing,
                'disabled': !isSupported
              }]"
              :aria-label="isListening ? 'Arrêter l\'enregistrement (manuel)' : 'Commencer l\'enregistrement (manuel)'"
            >
              <span v-if="isListening" aria-hidden="true">🎤</span>
              <span v-else-if="isProcessing" aria-hidden="true">⏳</span>
              <span v-else aria-hidden="true">🎙️</span>
            </button>

            <button
              @click="testMicrophone"
              :disabled="isTestingMic || isListening || isProcessing"
              :class="['test-mic-button', { 'testing': isTestingMic }]"
              aria-label="Tester le microphone"
            >
              <span v-if="isTestingMic" aria-hidden="true">🔄</span>
              <span v-else aria-hidden="true">🔧</span>
            </button>

            <button
              @click="relaunchSophie"
              :disabled="isListening || isProcessing"
              class="sophie-button"
              aria-label="Relancer Sophie pour lire la question"
            >
              🗣️ Sophie
            </button>
          </fieldset>

          <output class="voice-status" aria-live="polite">
            <p v-if="isListening" class="status-listening">
              🔴 En écoute... Parlez maintenant ({{ timeoutCountdown }}s)
            </p>
            <p v-else-if="isProcessing" class="status-processing">
              ⏳ Traitement en cours...
            </p>
            <p v-else-if="isTestingMic" class="status-testing">
              🔧 Test du microphone...
            </p>
            <p v-else-if="!isSupported" class="status-error">
              ❌ Reconnaissance vocale non supportée
            </p>
            <p v-else class="status-ready">
              ✅ Prêt à écouter (manuel - cliquez pour arrêter)
              <br><small class="api-info">💡 En cas d'erreur réseau, utilisez le bouton de test (bleu)</small>
            </p>
          </output>

          <!-- Indicateur de niveau audio -->
          <div v-if="isListening" class="audio-level-indicator">
            <label>Niveau audio :</label>
            <div class="audio-level-bar">
              <div class="audio-level-fill" :style="{ width: `${audioLevel * 100}%` }"></div>
            </div>
            <span class="audio-level-text">{{ (audioLevel * 100).toFixed(0) }}%</span>
            <small class="audio-guidance" :class="{ 'good': audioLevel > 0.1, 'low': audioLevel <= 0.1 }">
              {{ audioLevel > 0.1 ? '✅ Bon niveau' : '⚠️ Parlez plus fort' }}
            </small>
          </div>

          <!-- Résultat du test microphone -->
          <aside v-if="micTestResult" class="mic-test-result" :class="{ 'success': micTestResult.success, 'error': !micTestResult.success }">
            <h5>{{ micTestResult.success ? '✅ Test réussi' : '❌ Test échoué' }}</h5>
            <p class="test-message">{{ micTestResult.message }}</p>
            <details v-if="micTestResult.details" class="test-details">
              <summary>Détails techniques</summary>
              <p>{{ micTestResult.details }}</p>
              <div v-if="micTestResult.deviceInfo" class="device-info">
                <h6>Informations du périphérique :</h6>
                <ul>
                  <li><strong>Nom :</strong> {{ micTestResult.deviceInfo.label }}</li>
                  <li><strong>Fréquence :</strong> {{ micTestResult.deviceInfo.sampleRate }} Hz</li>
                  <li><strong>Canaux :</strong> {{ micTestResult.deviceInfo.channelCount }}</li>
                  <li><strong>Réduction de bruit :</strong> {{ micTestResult.deviceInfo.noiseSuppression ? 'Activée' : 'Désactivée' }}</li>
                </ul>
              </div>
            </details>
            <details v-if="micTestResult.troubleshooting" class="troubleshooting">
              <summary>Conseils de dépannage</summary>
              <ul>
                <li v-for="tip in micTestResult.troubleshooting" :key="tip">{{ tip }}</li>
              </ul>
            </details>
          </aside>
        </section>
        
        <!-- Transcription en temps réel -->
        <section v-if="currentTranscript || interimTranscript" class="transcript-section" aria-label="Transcription en temps réel">
          <h4>📝 Ce que Sophie entend :</h4>
          <output class="transcript-text" aria-live="polite">
            <span class="final-text">{{ currentTranscript }}</span>
            <span class="interim-text">{{ interimTranscript }}</span>
          </output>
        </section>

        <!-- Saisie texte alternative -->
        <section v-if="!currentAnswer" class="text-input-section" aria-label="Saisie texte alternative">
          <h4>✏️ Alternative : Saisie texte</h4>
          <div class="text-input-container">
            <textarea
              v-model="textInput"
              @keydown.enter.prevent="submitTextInput"
              placeholder="Tapez votre réponse ici et appuyez sur Entrée..."
              class="text-input"
              rows="3"
              :disabled="isProcessing"
            ></textarea>
            <button
              @click="submitTextInput"
              :disabled="!textInput.trim() || isProcessing"
              class="btn-submit-text"
            >
              ✅ Valider la réponse
            </button>
          </div>
        </section>

        <!-- Réponse actuelle -->
        <section v-if="currentAnswer" class="answer-section" aria-label="Votre réponse">
          <h4>✅ Votre réponse validée :</h4>
          <output class="answer-text">{{ currentAnswer }}</output>
          <nav class="answer-actions">
            <button @click="playAnswer" :disabled="isPlayingAnswer" class="btn-play">
              {{ isPlayingAnswer ? '🔊 Sophie lit...' : '🔊 Sophie relit' }}
            </button>
            <button @click="retryQuestion" class="btn-retry">
              🔄 Recommencer
            </button>
            <button @click="nextQuestion" class="btn-next">
              {{ isLastQuestionInPage ? (isLastPage ? '🏁 Terminer' : '📄 Page suivante') : '➡️ Question suivante' }}
            </button>
            <button
              v-if="!currentQuestion?.required && !currentAnswer"
              @click="skipQuestion"
              class="btn-skip"
            >
              ⏭️ Passer cette question
            </button>
          </nav>
        </section>

        <!-- Navigation entre pages -->
        <nav class="page-navigation" aria-label="Navigation entre les pages">
          <button
            v-if="currentPageIndex > 0"
            @click="previousPage"
            class="btn-previous-page"
          >
            ← Page précédente
          </button>
          <button
            v-if="canSkipPage"
            @click="skipPage"
            class="btn-skip-page"
          >
            Passer cette page
          </button>
        </nav>
        </article>
      </section>

      <!-- Résumé final -->
      <section v-if="isCompleted" class="completion-section" aria-label="Interview terminée">
        <header>
          <h2>🎉 Interview terminée !</h2>
          <p>Merci d'avoir complété votre profil vocal.</p>
        </header>

        <details class="answers-summary">
          <summary>Résumé de vos réponses</summary>
          <article
            v-for="(answer, index) in answers"
            :key="index"
            class="answer-item"
          >
            <h4>{{ questions[index].text }}</h4>
            <p>{{ answer }}</p>
          </article>
        </details>

        <nav class="completion-actions">
          <button @click="saveAnswers" :disabled="isSaving" class="btn-save">
            {{ isSaving ? 'Sauvegarde...' : '💾 Sauvegarder' }}
          </button>
          <button @click="restartInterview" class="btn-restart">
            🔄 Recommencer
          </button>
        </nav>
      </section>

      <!-- Messages d'erreur -->
      <aside v-if="error" class="error-message" role="alert" aria-live="assertive">
        ❌ {{ error }}
        <div v-if="error.includes('réseau') || error.includes('network')" class="error-actions">
          <button @click="retryConnection" class="btn-retry-connection">
            🔄 Réessayer la connexion
          </button>
        </div>
      </aside>
    </section>
  </main>
</template>

<script>
import { voiceService } from '@/_services/voice.service';
import { ttsService } from '@/_services/tts.service';

export default {
  name: 'VoiceInterview',

  props: {
    userEmail: {
      type: String,
      required: true
    }
  },
  props: {
    questionPages: {
      type: Array,
      default: () => [
        // Page 1: Informations de base
        {
          title: "Informations de base",
          description: "Commençons par les informations essentielles",
          questions: [
            // EMAIL ET MOT DE PASSE RETIRÉS - DÉJÀ SAISIS AVANT L'INTERVIEW
            {
              text: "Quel nom d'utilisateur voulez-vous ? Les autres utilisateurs vous identifieront uniquement par ce pseudo sur votre fiche profil. Choisissez quelque chose d'original et mémorable.",
              hint: "Votre pseudo unique sur Meet Voice",
              field: "username",
              required: true
            },
            {
              text: "Quel est votre nom de famille ? Cette information restera privée et ne sera visible que par vous dans votre profil personnel.",
              hint: "Votre nom de famille",
              field: "nom",
              required: true
            },
            {
              text: "C'est quoi ton prénom ? C'est le nom qui apparaîtra sur votre profil public et que les autres utilisateurs verront lors des conversations.",
              hint: "Votre prénom",
              field: "prenom",
              required: true
            }
          ]
        },
        // Page 2: Informations personnelles
        {
          title: "Informations personnelles",
          description: "Parlez-nous de vous",
          questions: [
            {
              text: "Quelle est votre date de naissance ?",
              hint: "Format : jour mois année, exemple : 15 mars 1990",
              field: "date_de_naissance",
              required: true
            },
            {
              text: "Quel est votre sexe ? Cette information aide les autres utilisateurs à mieux vous identifier et nous permet de vous proposer des profils compatibles. Vous pouvez dire homme, femme, homosexuelle, lesbienne, bisexuelle, transgenre, ou non-binaire selon votre identité.",
              hint: "Homme, Femme, Homosexuelle, Lesbienne, Bisexuelle, Transgenre, Non-binaire",
              field: "sexe",
              required: true,
              choices: ["Homme", "Femme", "Homosexuelle", "Lesbienne", "Bisexuelle", "Transgenre", "Non-binaire"]
            },
            {
              text: "Êtes-vous non binaire ?",
              hint: "Oui ou Non",
              field: "non_binaire",
              required: false,
              choices: ["Oui", "Non"]
            },
            {
              text: "Que recherchez-vous sur Meet Voice ? Cette information nous aide à vous proposer des profils compatibles avec vos attentes. Vous pouvez chercher des relations amicales pour des rencontres platoniques et de l'amitié, l'amour pour une relation sérieuse et durable, ou des rencontres libertines pour des relations plus libres et sans engagement.",
              hint: "Amical, Amour, ou Libertin",
              field: "recherche",
              required: true,
              choices: ["Amical", "Amour", "Libertin"]
            }
          ]
        },
        // Page 3: Apparence physique
        {
          title: "Apparence physique",
          description: "Décrivez votre apparence",
          questions: [
            {
              text: "Quelle est votre taille en centimètres ? Cette information aide les autres utilisateurs à mieux vous visualiser. Dites simplement le nombre, par exemple cent soixante-quinze pour 175 centimètres.",
              hint: "Exemple : 175",
              field: "taille",
              required: false
            },
            {
              text: "Quel est votre poids en kilogrammes ? Cette information reste optionnelle et vous aide à trouver des personnes avec des affinités physiques similaires. Dites simplement le nombre en kilogrammes.",
              hint: "En kilogrammes, exemple : 70",
              field: "poids",
              required: false
            },
            {
              text: "Quelle est votre origine ethnique ?",
              hint: "Caucasien, Métisse, Arabe, Africaine, Indienne, Latine, Asiatique",
              field: "ethnique",
              required: false,
              choices: ["Caucasien", "Métisse", "Arabe", "Africaine", "Indienne", "Latine", "Asiatique"]
            },
            {
              text: "De quelle couleur sont vos yeux ? La couleur de vos yeux est un détail important de votre apparence qui aide les autres à mieux vous imaginer. Vous pouvez dire noire, marron, bleu, vert, ou noisette.",
              hint: "Noire, Marron, Bleu, Vert, Noisette",
              field: "yeux",
              required: false,
              choices: ["Noire", "Marron", "Bleu", "Vert", "Noisette"]
            },
            {
              text: "Comment décririez-vous votre silhouette ? Cette information aide à donner une idée générale de votre physique. Vous pouvez dire normal pour une corpulence moyenne, mince, athlétique si vous êtes sportif, ronde, forte, ou préciser si vous avez un handicap ou utilisez une chaise roulante.",
              hint: "Normal, Mince, Athlétique, Ronde, Forte, Handicapé, Chaise Roulante",
              field: "shilhouette",
              required: false,
              choices: ["Normal", "Mince", "Athlétique", "Ronde", "Forte", "Handicapé", "Chaise Roulante"]
            }
          ]
        },
        // Page 4: Vie professionnelle et sociale
        {
          title: "Vie professionnelle et sociale",
          description: "Votre situation et vos préférences",
          questions: [
        {
          text: "Quel est votre métier ?",
          hint: "Décrivez votre profession",
          field: "metier",
          required: false
        },
        {
          text: "Quelle est votre religion ?",
          hint: "Chrétienne, Musulmane, Juive, Bouddhiste, Hindoue, Athée, Agnostique, Autre, Préfère ne pas dire",
          field: "religion",
          required: false,
          choices: ["Chrétienne", "Musulmane", "Juive", "Bouddhiste", "Hindoue", "Athée", "Agnostique", "Autre", "Préfère ne pas dire"]
        },
        {
          text: "Quelles langues parlez-vous ?",
          hint: "Exemple : Français, Anglais, Espagnol",
          field: "langues",
          required: false
        },
        {
          text: "Quels types de films aimez-vous ?",
          hint: "Action, Comédie, Drame, Horreur, Romance, Science-fiction, etc.",
          field: "films",
          required: false
        },
        {
          text: "Quel style de musique écoutez-vous ?",
          hint: "Pop, Rock, Rap, Classique, Jazz, Électronique, etc.",
          field: "musique",
          required: false
        },
        {
          text: "Comment décririez-vous votre caractère ?",
          hint: "Joyeux, Calme, Spontané, Timide, Extraverti, etc.",
          field: "caractere",
          required: false
        },
        {
          text: "Quels sont vos hobbies ?",
          hint: "Sport, Lecture, Voyage, Cuisine, Photographie, etc.",
          field: "hobbies",
          required: false
        },
        {
          text: "Quelles sont vos tendances vestimentaires ?",
          hint: "Décontracté, Élégant, Sportif, Bohème, Classique, etc.",
          field: "tendances",
          required: false
        },
        {
          text: "Quel type de rencontre recherchez-vous ?",
          hint: "Amical, Amour, Libertin",
          field: "rencontre",
          required: false,
          choices: ["Amical", "Amour", "Libertin"]
        },
        {
          text: "Quelle est votre situation professionnelle ?",
          hint: "Étudiant, Employé, Cadre, Indépendant, Retraité, Sans emploi",
          field: "situation",
          required: false,
          choices: ["Étudiant", "Employé", "Cadre", "Indépendant", "Retraité", "Sans emploi"]
        },
        {
          text: "Décrivez-vous en quelques mots",
          hint: "Votre personnalité, ce qui vous caractérise",
          field: "description_personnelle",
          required: false
        }
          ]
        }
      ]
    }
  },
  emits: ['interview-completed', 'answers-saved'],
  data() {
    return {
      // Navigation par pages
      currentPageIndex: 0,
      currentQuestionInPage: 0,

      // Données existantes
      currentQuestionIndex: 0,
      answers: [],
      currentAnswer: '',
      currentTranscript: '',
      interimTranscript: '',
      textInput: '', // Saisie texte alternative
      isListening: false,
      isProcessing: false,
      isCompleted: false,
      isSaving: false,
      isSupported: false,
      error: '',
      isTestingMic: false,
      micTestResult: null,
      timeoutCountdown: 60,
      countdownInterval: null,
      isRunningDiagnostics: false,
      diagnosticsResult: null,
      sttMode: 'auto', // 'auto', 'backend', 'browser'
      audioLevel: 0,
      audioAnalyser: null,
      isPlayingAnswer: false,
      // Microphone simple
      availableMicrophones: [],
      selectedMicrophoneId: '',
      isRefreshingMics: false,

      // Éviter la répétition de Sophie
      hasSpokenFirstQuestion: false
    };
  },

  computed: {
    // Configurations depuis le store
    voiceConfig() {
      return this.$store.getters.getVoiceConfig;
    },
    microphoneConfig() {
      return this.$store.getters.getMicrophoneConfig;
    },
    // Microphones en local, pas de store compliqué
    availableVoices() {
      return this.$store.getters.getAvailableVoices;
    },
    // Navigation par pages
    totalPages() {
      return this.questionPages.length;
    },

    currentPageQuestions() {
      return this.questionPages[this.currentPageIndex]?.questions || [];
    },

    currentPageTitle() {
      return this.questionPages[this.currentPageIndex]?.title || '';
    },

    currentPageDescription() {
      return this.questionPages[this.currentPageIndex]?.description || '';
    },

    currentQuestion() {
      return this.currentPageQuestions[this.currentQuestionInPage] || null;
    },

    isLastQuestionInPage() {
      return this.currentQuestionInPage >= this.currentPageQuestions.length - 1;
    },

    isLastPage() {
      return this.currentPageIndex >= this.totalPages - 1;
    },

    canSkipPage() {
      // Permettre de passer les pages optionnelles (pas la première)
      return this.currentPageIndex > 0 && this.currentPageQuestions.every(q => !q.required);
    },

    // Computed pour obtenir toutes les questions de toutes les pages
    questions() {
      const allQuestions = [];
      this.questionPages.forEach(page => {
        if (page.questions) {
          allQuestions.push(...page.questions);
        }
      });
      return allQuestions;
    }
  },
  
  async mounted() {
    this.checkVoiceSupport();
    // Charger les configs du store
    await this.$store.dispatch('loadSavedConfigs');
    // Charger les microphones et attendre la fin
    await this.loadAvailableMicrophones();

    console.log('🎯 Configuration finale microphone:', this.selectedMicrophoneId);

    // Sophie lit la première question UNE SEULE FOIS
    setTimeout(() => {
      this.speakCurrentQuestion();
    }, 2000);
  },

  // Watcher supprimé pour éviter la répétition automatique des questions
  // Sophie ne lit plus automatiquement les nouvelles questions
  
  methods: {
    checkVoiceSupport() {
      // Utiliser la vérification robuste du voiceService
      this.isSupported = voiceService.checkSupport();

      console.log('🔍 Vérification support vocal:', this.isSupported);

      if (!this.isSupported) {
        this.error = 'Votre navigateur ne supporte pas la reconnaissance vocale. Veuillez utiliser Chrome, Edge ou Safari récent.';
      } else {
        console.log('✅ Reconnaissance vocale supportée');
      }
    },

    async toggleListening() {
      console.log('🔘 BOUTON GAUCHE CLIQUÉ - toggleListening appelé');
      console.log('📊 État actuel:', {
        isListening: this.isListening,
        isProcessing: this.isProcessing,
        isSupported: this.isSupported,
        selectedMicrophoneId: this.selectedMicrophoneId
      });

      if (this.isListening) {
        console.log('🛑 Arrêt de l\'écoute...');
        this.stopListening();
      } else {
        console.log('▶️ Démarrage de l\'écoute...');
        await this.startListening();
      }
    },
    
    async startListening() {
      if (!this.isSupported) {
        this.error = 'Reconnaissance vocale non supportée';
        return;
      }

      try {
        this.error = '';
        this.currentTranscript = '';
        this.interimTranscript = '';
        this.isProcessing = true;

        console.log('🎤 Démarrage de l\'écoute MANUELLE (comme bouton bleu)...');
        console.log('🎯 Microphone sélectionné:', this.selectedMicrophoneId);
        console.log('🎯 Configuration complète:', {
          selectedMicrophoneId: this.selectedMicrophoneId,
          availableMicrophones: this.availableMicrophones.length,
          storeConfig: this.$store.getters.getSelectedMicrophone,
          localStorageConfig: localStorage.getItem('meetvoice_microphone')
        });

        // Vérifier l'accès au microphone avant de commencer
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            audio: {
              deviceId: this.selectedMicrophoneId ? { exact: this.selectedMicrophoneId } : undefined
            }
          });
          console.log('✅ Accès microphone confirmé');
          // Fermer le stream de test
          stream.getTracks().forEach(track => track.stop());
        } catch (micError) {
          console.error('❌ Erreur accès microphone:', micError);
          this.error = 'Impossible d\'accéder au microphone. Vérifiez les permissions et que le microphone est connecté.';
          this.isProcessing = false;
          return;
        }

        // ÉCOUTE MANUELLE - PAS DE TIMEOUT
        const result = await voiceService.startListening({
          timeout: 0, // PAS DE TIMEOUT - MANUEL
          silenceTimeout: 0, // PAS DE SILENCE TIMEOUT
          maxExtensions: 0, // PAS D'EXTENSIONS
          language: 'fr-FR',
          continuous: true, // CONTINU JUSQU'À ARRÊT MANUEL
          interimResults: true,
          microphoneId: this.selectedMicrophoneId, // FIX: Use consistent microphone ID

          onStart: () => {
            this.isListening = true;
            this.isProcessing = false;
            this.startAudioLevelMonitoring();
            console.log('✅ Écoute démarrée - Cliquez à nouveau pour arrêter');
          },

          onResult: (finalText, interimText) => {
            this.currentTranscript = finalText;
            this.interimTranscript = interimText;
            console.log('📝 Transcription:', { final: finalText, interim: interimText });

            // Si on a un texte final, traiter immédiatement
            if (finalText && finalText.trim()) {
              console.log('🎯 Texte final reçu dans onResult, traitement immédiat:', finalText);
              this.currentAnswer = finalText.trim();
              this.error = ''; // Effacer toute erreur
              this.stopListening(); // Arrêter l'écoute
              this.processAnswer(finalText.trim());
            }
          },

          onInterim: (interimText) => {
            this.interimTranscript = interimText;
          },

          onSpeechDetected: (data) => {
            console.log('🗣️ Parole détectée:', data);
            // Mettre à jour le countdown avec le temps restant
            if (data.timeRemaining > 0) {
              this.timeoutCountdown = Math.ceil(data.timeRemaining / 1000);
            }
          },

          onTimeoutExtended: (data) => {
            console.log('⏰ Temps d\'écoute étendu:', data);
            this.timeoutCountdown = Math.ceil(data.additionalTime / 1000);
          },

          onEnd: (data) => {
            this.isListening = false;
            this.isProcessing = false;
            this.clearCountdown();
            this.stopAudioLevelMonitoring();
            console.log('🔚 Reconnaissance vocale terminée:', data);
          },

          onError: (error) => {
            this.isListening = false;
            this.isProcessing = false;
            this.clearCountdown();
            console.error('❌ Erreur reconnaissance:', error);
          }
        });

        // Traitement du résultat - GESTION OBJECT ET STRING
        console.log('🔍 Type de résultat reçu:', typeof result, result);
        console.log('🔍 Contenu complet du résultat:', JSON.stringify(result, null, 2));

        let finalText = '';
        if (typeof result === 'string') {
          finalText = result.trim();
          console.log('📝 Résultat string:', finalText);
        } else if (result && typeof result === 'object') {
          // Si c'est un object, extraire le texte
          console.log('📝 Résultat object, extraction du texte...');
          finalText = result.text || result.finalText || result.transcript || result.final || '';
          console.log('📝 Texte extrait:', finalText);
          if (typeof finalText !== 'string') {
            finalText = JSON.stringify(result);
            console.log('📝 Fallback JSON:', finalText);
          }
          finalText = finalText.trim();
        }

        console.log('📝 Texte final à traiter:', finalText);
        console.log('📝 Transcription actuelle:', this.currentTranscript);

        // Si on a un finalText, le traiter immédiatement
        if (finalText && finalText.trim()) {
          const textToProcess = finalText.trim();
          this.currentAnswer = textToProcess;
          this.currentTranscript = textToProcess;
          this.interimTranscript = '';
          this.error = ''; // Effacer toute erreur
          console.log('✅ Reconnaissance terminée:', textToProcess);

          // 🔊 DÉCLENCHER SOPHIE IMMÉDIATEMENT
          console.log('🔊 DÉCLENCHEMENT SOPHIE avec:', textToProcess);
          console.log('🔊 Appel de processAnswer...');
          this.processAnswer(textToProcess);
        }
        // Si pas de finalText mais qu'on a currentTranscript, on laisse stopListening() s'en occuper
        // Ne plus afficher d'erreur ici car la transcription peut être en cours

      } catch (error) {
        console.error('❌ Erreur lors du démarrage:', error);

        // Messages d'erreur plus spécifiques basés sur le service
        if (error.message.includes('Permission') || error.message.includes('not-allowed')) {
          this.error = 'Permission microphone refusée. Cliquez sur l\'icône microphone dans la barre d\'adresse pour autoriser.';
        } else if (error.message.includes('no-speech') || error.message.includes('Aucune parole')) {
          this.error = 'Aucune parole détectée. Vérifiez que votre microphone fonctionne, parlez plus fort, ou utilisez le bouton de test microphone (bleu) pour diagnostiquer.';
        } else if (error.message.includes('audio-capture') || error.message.includes('microphone')) {
          this.error = 'Impossible d\'accéder au microphone. Vérifiez qu\'il est connecté, autorisé dans le navigateur, et utilisez le bouton "Actualiser" pour recharger la liste.';
        } else if (error.message.includes('network') || error.message.includes('réseau')) {
          this.error = 'L\'API Speech Recognition a des limitations réseau. Utilisez le bouton de test microphone (bleu) qui fonctionne mieux, ou réessayez le bouton principal.';
        } else if (error.message.includes('Tentative de reconnexion')) {
          this.error = 'Reconnexion en cours... Utilisez le bouton de test microphone en attendant.';
        } else {
          this.error = `Erreur microphone: ${error.message}`;
        }

        this.isListening = false;
        this.isProcessing = false;
        this.clearCountdown();
      }
    },

    stopListening() {
      console.log('🛑 Arrêt de l\'écoute demandé');
      console.log('📝 Transcription actuelle avant arrêt:', this.currentTranscript);
      console.log('📝 Réponse actuelle:', this.currentAnswer);

      // Si on a déjà du texte transcrit ET qu'aucune réponse n'est encore validée
      if (this.currentTranscript && this.currentTranscript.trim() && !this.currentAnswer) {
        console.log('✅ Texte disponible sans réponse validée, traitement immédiat:', this.currentTranscript);
        const textToProcess = this.currentTranscript.trim();
        this.currentAnswer = textToProcess;
        this.error = ''; // Effacer toute erreur
        this.processAnswer(textToProcess);
      }

      voiceService.stopListening();
      this.isListening = false;
      this.isProcessing = false;
    },

    // 🔊 NOUVELLE MÉTHODE : Traiter la réponse et déclencher Sophie
    async processAnswer(finalText) {
      try {
        console.log('🔊 PROCESSANSWER APPELÉE - Traitement de la réponse:', finalText);
        console.log('🔊 Type de finalText:', typeof finalText);
        console.log('🔊 Longueur:', finalText ? finalText.length : 'undefined');

        // Déclencher Sophie immédiatement pour confirmer
        console.log('🔊 Appel de speakConfirmation...');
        await this.speakConfirmation(finalText);
        console.log('🔊 speakConfirmation terminée');

      } catch (error) {
        console.error('❌ Erreur lors du traitement de la réponse:', error);
        console.error('❌ Stack trace:', error.stack);
      }
    },

    // 🔊 Sophie confirme la réponse
    async speakConfirmation(answer) {
      try {
        console.log('🗣️ SPEAKCONFIRMATION APPELÉE avec:', answer);

        // Nettoyer la réponse pour éviter les problèmes TTS
        const cleanAnswer = answer.replace(/[^\w\s]/g, '').trim();
        const confirmationText = `J'ai bien entendu ${cleanAnswer}`;

        console.log('🗣️ Sophie confirme:', confirmationText);
        console.log('🔧 Config voix disponible:', this.voiceConfig);

        // Utiliser une config par défaut si voiceConfig n'est pas défini
        const voiceConfig = this.voiceConfig || {
          selectedVoiceId: 'sophie',
          pitch: 1.0,
          volume: 1.0
        };

        console.log('🔧 Config voix utilisée:', voiceConfig);
        console.log('🔧 TTS Service disponible:', !!ttsService);
        console.log('🔧 Méthode speak disponible:', typeof ttsService.speak);

        console.log('🔊 Appel de ttsService.speak...');

        // Timeout pour éviter que Sophie bloque l'interview
        const speakPromise = ttsService.speak(confirmationText, {
          voiceId: voiceConfig.selectedVoiceId,
          speed: 1.2,
          pitch: voiceConfig.pitch,
          volume: voiceConfig.volume
        });

        // Timeout de 5 secondes maximum
        await Promise.race([
          speakPromise,
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('TTS timeout')), 5000)
          )
        ]);

        console.log('✅ Confirmation lue par Sophie - TTS terminé');

      } catch (error) {
        console.error('❌ Erreur TTS confirmation:', error);
      }
    },
    
    retryQuestion() {
      this.currentAnswer = '';
      this.currentTranscript = '';
      this.interimTranscript = '';
      this.error = '';
    },

    async retryConnection() {
      console.log('🔄 Retry connexion manuel demandé');
      this.error = '';

      try {
        // Réinitialiser complètement le service vocal
        voiceService.reset();

        // Réessayer l'écoute
        if (!this.isListening) {
          await this.startListening();
        }

      } catch (error) {
        console.error('❌ Erreur retry connexion:', error);
        this.error = 'Impossible de rétablir la connexion. Vérifiez votre microphone et votre connexion internet.';
      }
    },
    
    nextQuestion() {
      // Vérifier si la question est obligatoire et si une réponse est requise
      const isRequired = this.currentQuestion?.required;
      const hasAnswer = this.currentAnswer && this.currentAnswer.trim();

      if (isRequired && !hasAnswer) {
        this.error = "Cette question est obligatoire. Veuillez donner une réponse.";
        return;
      }

      // Sauvegarder la réponse avec l'index global (même si vide pour les questions optionnelles)
      const globalIndex = this.getGlobalQuestionIndex();
      this.answers[globalIndex] = this.currentAnswer || '';

      // Passer à la question suivante dans la page
      if (!this.isLastQuestionInPage) {
        this.currentQuestionInPage++;
        this.resetCurrentAnswer();
        // Sophie ne lit plus automatiquement les nouvelles questions
      } else if (!this.isLastPage) {
        // Passer à la page suivante
        this.nextPage();
      } else {
        // Interview terminée
        this.completeInterview();
      }
    },

    nextPage() {
      this.currentPageIndex++;
      this.currentQuestionInPage = 0;
      this.resetCurrentAnswer();
      console.log(`📄 Page suivante: ${this.currentPageIndex + 1}/${this.totalPages}`);
      // Sophie ne lit plus automatiquement les nouvelles questions
    },

    previousPage() {
      if (this.currentPageIndex > 0) {
        this.currentPageIndex--;
        this.currentQuestionInPage = this.currentPageQuestions.length - 1;
        this.resetCurrentAnswer();
        console.log(`📄 Page précédente: ${this.currentPageIndex + 1}/${this.totalPages}`);
      }
    },

    skipPage() {
      if (this.canSkipPage) {
        console.log(`⏭️ Page ${this.currentPageIndex + 1} ignorée`);
        this.nextPage();
      }
    },

    skipQuestion() {
      console.log(`⏭️ Question ${this.currentQuestionInPage + 1} ignorée`);
      this.currentAnswer = ''; // Réponse vide pour les questions optionnelles
      this.nextQuestion();
    },

    resetCurrentAnswer() {
      this.currentAnswer = '';
      this.currentTranscript = '';
      this.interimTranscript = '';
      this.textInput = '';
      this.error = '';
      this.micTestResult = null;
    },

    submitTextInput() {
      if (!this.textInput.trim()) return;

      const text = this.textInput.trim();
      console.log('✏️ Soumission texte:', text);

      this.currentAnswer = text;
      this.currentTranscript = text;
      this.textInput = '';
      this.error = '';

      // Traiter la réponse comme une réponse vocale
      this.processAnswer(text);
    },

    getGlobalQuestionIndex() {
      let globalIndex = 0;
      for (let i = 0; i < this.currentPageIndex; i++) {
        globalIndex += this.questionPages[i].questions.length;
      }
      return globalIndex + this.currentQuestionInPage;
    },

    completeInterview() {
      this.isCompleted = true;
      console.log('🎉 Interview terminée!', this.answers);
      this.$emit('interview-completed', this.answers);
    },
    
    async saveAnswers() {
      try {
        this.isSaving = true;
        this.error = '';

        // Créer un objet avec les réponses structurées selon le modèle backend
        const backendData = {};

        // Mapper les réponses aux champs du backend
        this.questions.forEach((question, index) => {
          const answer = this.answers[index];
          if (answer && question.field) {
            // Traitement spécial pour certains champs
            switch (question.field) {
              case 'date_de_naissance':
                // Convertir la date parlée en format YYYY-MM-DD
                backendData[question.field] = this.parseSpokenDate(answer);
                break;
              case 'taille':
                // Extraire le nombre de la réponse
                const taille = this.extractNumber(answer);
                if (taille) backendData[question.field] = parseInt(taille);
                break;
              case 'poids':
                // Extraire le nombre de la réponse
                const poids = this.extractNumber(answer);
                if (poids) backendData[question.field] = poids.toString();
                break;
              case 'email':
                // Nettoyer l'email
                backendData[question.field] = answer.toLowerCase().replace(/\s/g, '');
                break;
              default:
                backendData[question.field] = answer;
            }
          }
        });

        // Données complètes pour l'émission
        const interviewData = {
          backend_fields: backendData,
          raw_answers: this.answers,
          questions: this.questions.map(q => ({ text: q.text, field: q.field })),
          completed_at: new Date().toISOString()
        };

        console.log('Données à sauvegarder:', interviewData);

        // Émettre l'événement avec les données
        this.$emit('answers-saved', interviewData);

        // Simuler une sauvegarde (à remplacer par un appel API réel)
        await new Promise(resolve => setTimeout(resolve, 1000));

        // Optionnel: rediriger ou fermer le composant

      } catch (error) {
        console.error('Erreur lors de la sauvegarde:', error);
        this.error = 'Erreur lors de la sauvegarde des réponses';
      } finally {
        this.isSaving = false;
      }
    },

    // Méthodes utilitaires pour traiter les réponses vocales
    parseSpokenDate(spokenDate) {
      // Convertir une date parlée en format YYYY-MM-DD
      // Exemple: "15 mars 1990" -> "1990-03-15"
      try {
        const months = {
          'janvier': '01', 'février': '02', 'mars': '03', 'avril': '04',
          'mai': '05', 'juin': '06', 'juillet': '07', 'août': '08',
          'septembre': '09', 'octobre': '10', 'novembre': '11', 'décembre': '12'
        };

        const parts = spokenDate.toLowerCase().split(' ');
        if (parts.length >= 3) {
          const day = parts[0].padStart(2, '0');
          const month = months[parts[1]] || '01';
          const year = parts[2];
          return `${year}-${month}-${day}`;
        }
        return null;
      } catch (error) {
        console.error('Erreur parsing date:', error);
        return null;
      }
    },

    extractNumber(text) {
      // Extraire un nombre d'un texte
      const match = text.match(/\d+/);
      return match ? match[0] : null;
    },
    
    restartInterview() {
      this.currentQuestionIndex = 0;
      this.answers = [];
      this.currentAnswer = '';
      this.currentTranscript = '';
      this.interimTranscript = '';
      this.textInput = '';
      this.isCompleted = false;
      this.error = '';
      this.micTestResult = null;
      this.clearCountdown();
    },

    async testMicrophone() {
      try {
        this.isTestingMic = true;
        this.micTestResult = null;

        console.log('🔧 Test complet du microphone sélectionné:', this.selectedMicrophoneId);

        // Étape 1: Vérifier les permissions
        const permissionResult = await voiceService.requestMicrophonePermission();

        if (!permissionResult.success) {
          this.micTestResult = {
            success: false,
            message: `❌ ${permissionResult.message}`,
            details: permissionResult.details,
            troubleshooting: permissionResult.troubleshooting
          };
          console.error('❌ Permissions microphone échouées:', permissionResult);
          return;
        }

        // Étape 2: Test fonctionnel du microphone SÉLECTIONNÉ
        const testResult = await voiceService.testMicrophone(this.selectedMicrophoneId);

        if (testResult.success) {
          const quality = testResult.testResults.quality;
          const isWorking = testResult.testResults.isWorking;
          const maxLevel = (testResult.testResults.maxAudioLevel * 100).toFixed(1);

          if (isWorking) {
            this.micTestResult = {
              success: true,
              message: `✅ Microphone fonctionnel ! Qualité: ${quality} (niveau: ${maxLevel}%)`,
              details: `Périphérique: ${testResult.deviceInfo.label}`,
              deviceInfo: testResult.deviceInfo
            };
            console.log('✅ Test microphone réussi:', testResult);
          } else {
            this.micTestResult = {
              success: false,
              message: `⚠️ Microphone détecté mais niveau audio très faible (${maxLevel}%)`,
              details: 'Vérifiez le volume du microphone ou parlez plus fort',
              deviceInfo: testResult.deviceInfo
            };
          }
        } else {
          this.micTestResult = {
            success: false,
            message: `❌ Test microphone échoué: ${testResult.message}`,
            details: testResult.details
          };
          console.error('❌ Test microphone échoué:', testResult);
        }

      } catch (error) {
        console.error('❌ Erreur test microphone:', error);
        this.micTestResult = {
          success: false,
          message: '❌ Erreur lors du test microphone',
          details: error.message
        };
      } finally {
        this.isTestingMic = false;
      }
    },

    startCountdown() {
      this.timeoutCountdown = 60;
      this.countdownInterval = setInterval(() => {
        this.timeoutCountdown--;
        if (this.timeoutCountdown <= 0) {
          this.clearCountdown();
        }
      }, 1000);
    },

    clearCountdown() {
      if (this.countdownInterval) {
        clearInterval(this.countdownInterval);
        this.countdownInterval = null;
      }
      this.timeoutCountdown = 60;
    },

    async startAudioLevelMonitoring() {
      try {
        this.audioAnalyser = await voiceService.getAudioAnalyser();

        const updateLevel = () => {
          if (this.audioAnalyser && this.isListening) {
            this.audioLevel = this.audioAnalyser.getLevel();
            requestAnimationFrame(updateLevel);
          }
        };

        updateLevel();
        console.log('🎵 Monitoring audio démarré');

      } catch (error) {
        console.warn('⚠️ Impossible de démarrer le monitoring audio:', error);
        this.audioLevel = 0;
      }
    },

    stopAudioLevelMonitoring() {
      if (this.audioAnalyser) {
        this.audioAnalyser.stop();
        this.audioAnalyser = null;
      }
      this.audioLevel = 0;
      console.log('🔇 Monitoring audio arrêté');
    },

    async playAnswer() {
      if (!this.currentAnswer || this.isPlayingAnswer) return;

      try {
        this.isPlayingAnswer = true;
        console.log('🔊 Lecture de la réponse avec TTS backend:', this.currentAnswer);

        // Utiliser UNIQUEMENT l'API TTS backend MeetVoice avec vitesse fixe 1.2
        await ttsService.speak(this.currentAnswer, {
          voiceId: this.voiceConfig.selectedVoiceId,
          speed: 1.2, // VITESSE FIXE À 1.2
          pitch: this.voiceConfig.pitch,
          volume: this.voiceConfig.volume
        });

        console.log('✅ Lecture terminée (TTS backend)');

      } catch (error) {
        console.error('❌ Erreur TTS backend lors de la lecture:', error);

        // AUCUN FALLBACK - Afficher l'erreur
        this.error = `Erreur TTS backend: ${error.message}. Vérifiez que le serveur TTS est démarré.`;

      } finally {
        this.isPlayingAnswer = false;
      }
    },

    async speakCurrentQuestion() {
      if (!this.currentQuestion) return;

      try {
        console.log('🗣️ Sophie lit la question:', this.currentQuestion.text);

        // Nettoyer le texte pour éviter les problèmes TTS
        const cleanText = this.currentQuestion.text
          .replace(/\s+/g, ' ')  // Normaliser les espaces
          .trim();

        console.log('🗣️ Texte nettoyé:', cleanText);

        // Réinitialiser le service TTS avant chaque question
        ttsService.reset();

        // Utiliser la config du store pour Sophie avec vitesse fixe 1.2
        const voiceConfig = this.$store.getters.getVoiceConfig;

        // Utiliser une promesse avec timeout pour éviter le blocage
        const speakPromise = ttsService.speak(cleanText, {
          voiceId: voiceConfig.selectedVoiceId,
          speed: 1.2, // VITESSE FIXE À 1.2
          pitch: voiceConfig.pitch,
          volume: voiceConfig.volume,
          onError: (error) => {
            console.error('❌ Erreur TTS pendant la lecture:', error);
          }
        });

        // Timeout de 10 secondes maximum pour la lecture
        await Promise.race([
          speakPromise,
          new Promise((resolve) =>
            setTimeout(() => {
              console.warn('⏰ Timeout TTS - Sophie prend trop de temps');
              ttsService.reset();
              resolve();
            }, 10000)
          )
        ]);

        console.log('✅ Question lue par Sophie avec config du store');

      } catch (error) {
        console.error('❌ Erreur TTS:', error);
        console.log('⚠️ Sophie ne peut pas lire cette question, mais l\'interview continue');

        // Réinitialiser le service en cas d'erreur
        ttsService.reset();

        // Pas d'erreur affichée à l'utilisateur pendant l'interview
        // L'interview peut continuer même si Sophie ne parle pas
      }
    },

    async relaunchSophie() {
      console.log('🗣️ Relancement manuel de Sophie demandé');
      try {
        // Réinitialiser complètement le service TTS
        ttsService.reset();

        // Attendre un peu pour la réinitialisation
        await new Promise(resolve => setTimeout(resolve, 500));

        // Relancer la lecture de la question actuelle
        await this.speakCurrentQuestion();

        console.log('✅ Sophie relancée avec succès');
      } catch (error) {
        console.error('❌ Erreur lors du relancement de Sophie:', error);
      }
    },

    async loadAvailableMicrophones() {
      try {
        console.log('🎤 Chargement des microphones disponibles...');

        // Demander les permissions d'abord
        await navigator.mediaDevices.getUserMedia({ audio: true });

        // Obtenir la liste des périphériques
        const devices = await navigator.mediaDevices.enumerateDevices();

        // Filtrer les microphones
        this.availableMicrophones = devices
          .filter(device => device.kind === 'audioinput')
          .map(device => ({
            deviceId: device.deviceId,
            label: device.label || `Microphone ${device.deviceId.substring(0, 8)}...`,
            groupId: device.groupId
          }));

        console.log('✅ Microphones trouvés:', this.availableMicrophones);

        // Charger le microphone sauvegardé depuis le store (priorité) puis localStorage
        let savedMic = this.$store.getters.getSelectedMicrophone;
        if (!savedMic) {
          savedMic = localStorage.getItem('meetvoice_microphone');
        }

        if (savedMic && this.availableMicrophones.find(m => m.deviceId === savedMic)) {
          this.selectedMicrophoneId = savedMic;
          // Synchroniser avec le store
          this.$store.dispatch('updateMicrophoneConfig', {
            selectedMicrophoneId: savedMic
          });
          console.log('🎯 Microphone sauvegardé chargé:', savedMic);
        } else if (this.availableMicrophones.length > 0) {
          this.selectedMicrophoneId = this.availableMicrophones[0].deviceId;
          // Synchroniser avec le store
          this.$store.dispatch('updateMicrophoneConfig', {
            selectedMicrophoneId: this.selectedMicrophoneId
          });
          console.log('🎯 Premier microphone sélectionné');
        }

      } catch (error) {
        console.error('❌ Erreur lors du chargement des microphones:', error);
        this.availableMicrophones = [{
          deviceId: '',
          label: 'Microphone par défaut',
          groupId: ''
        }];
      }
    },

    async refreshMicrophones() {
      this.isRefreshingMics = true;
      try {
        await this.loadAvailableMicrophones();
        console.log('🔄 Liste des microphones actualisée');
      } catch (error) {
        console.error('❌ Erreur lors de l\'actualisation:', error);
      } finally {
        this.isRefreshingMics = false;
      }
    },

    saveMicrophoneChoice() {
      // Sauvegarder dans localStorage ET dans le store pour cohérence
      localStorage.setItem('meetvoice_microphone', this.selectedMicrophoneId);
      this.$store.dispatch('updateMicrophoneConfig', {
        selectedMicrophoneId: this.selectedMicrophoneId
      });
      console.log('💾 Microphone sauvegardé (localStorage + store):', this.selectedMicrophoneId);
    },

    async runDiagnostics() {
      try {
        this.isRunningDiagnostics = true;
        this.diagnosticsResult = null;

        console.log('🔍 Lancement du diagnostic complet...');
        const report = await voiceService.runDiagnostics();

        this.diagnosticsResult = {
          success: report.issues.length === 0,
          report: report,
          summary: this.generateDiagnosticSummary(report)
        };

        console.log('📋 Rapport de diagnostic:', report);

      } catch (error) {
        this.diagnosticsResult = {
          success: false,
          error: error.message,
          summary: `Erreur lors du diagnostic: ${error.message}`
        };
        console.error('❌ Erreur diagnostic:', error);
      } finally {
        this.isRunningDiagnostics = false;
      }
    },

    generateDiagnosticSummary(report) {
      const issues = report.issues;
      const recommendations = report.recommendations;

      if (issues.length === 0) {
        return '✅ Système fonctionnel - Aucun problème détecté';
      }

      let summary = `❌ ${issues.length} problème(s) détecté(s):\n`;
      issues.forEach((issue, index) => {
        summary += `${index + 1}. ${issue}\n`;
      });

      if (recommendations.length > 0) {
        summary += '\n💡 Recommandations:\n';
        recommendations.forEach((rec, index) => {
          summary += `${index + 1}. ${rec}\n`;
        });
      }

      return summary;
    },

    toggleSTTMode() {
      const modes = ['auto', 'backend', 'browser'];
      const currentIndex = modes.indexOf(this.sttMode);
      const nextIndex = (currentIndex + 1) % modes.length;
      this.sttMode = modes[nextIndex];

      // Configurer le service
      switch (this.sttMode) {
        case 'backend':
          voiceService.setSTTMode(true);
          break;
        case 'browser':
          voiceService.setSTTMode(false);
          break;
        case 'auto':
        default:
          voiceService.setSTTMode(true); // Auto = backend en priorité
          break;
      }

      console.log(`🔧 Mode STT changé: ${this.sttMode}`);
    },

    async checkTTSService() {
      try {
        console.log('🔍 Vérification du service TTS backend...');

        // Tester la connexion au service TTS
        const voices = await ttsService.getAvailableVoices();

        if (voices && voices.length > 0) {
          console.log('✅ Service TTS backend connecté:', voices.length, 'voix disponibles');

          // Trouver la voix Julie
          const julieVoice = voices.find(v => v.name === 'Julie' || v.id === 4);
          if (julieVoice) {
            console.log('✅ Voix Julie trouvée:', julieVoice);
          } else {
            console.warn('⚠️ Voix Julie non trouvée, utilisation de la voix par défaut');
          }
        } else {
          throw new Error('Aucune voix disponible');
        }

      } catch (error) {
        console.error('❌ Service TTS backend non disponible:', error);
        this.error = `⚠️ Service TTS non disponible. Démarrez le serveur TTS sur http://localhost:8000 pour entendre Sophie.`;
      }
    },

    // Configuration voix supprimée - utilise les paramètres du store configurés avant l'interview
  },
  
  beforeUnmount() {
    // Arrêter l'écoute si le composant est détruit
    if (this.isListening) {
      this.stopListening();
    }
  }
};
</script>

<style scoped>
.voice-interview {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.interview-container {
  background: #3c0940;
  border-radius: 10px;
  padding: 30px;
  color: white;
}

.interview-container h3 {
  color: #1f8ea5;
  text-align: center;
  margin-bottom: 10px;
}

.interview-description {
  text-align: center;
  color: #ccc;
  margin-bottom: 30px;
  font-size: 14px;
}

/* Barre de progression */
.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #666;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 10px;
}

.progress-fill {
  height: 100%;
  background-color: #1f8ea5;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 12px;
  color: #ccc;
  margin-bottom: 30px;
}

/* Section question */
.question-section {
  margin-bottom: 30px;
}

.question-text h4 {
  color: #1f8ea5;
  margin-bottom: 10px;
  font-size: 18px;
}

.question-hint {
  color: #ffc107;
  font-size: 14px;
  font-style: italic;
  margin-bottom: 20px;
}

/* Contrôles vocaux */
.voice-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 30px 0;
}

.mic-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

.mic-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: none;
  font-size: 32px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-bottom: 15px;
  background-color: #666;
}

.mic-button:not(.disabled):hover {
  transform: scale(1.1);
}

.mic-button.listening {
  background-color: #ff6b6b;
  animation: pulse 1s infinite;
}

.mic-button.processing {
  background-color: #ffc107;
}

.mic-button.disabled {
  background-color: #444;
  cursor: not-allowed;
  opacity: 0.5;
}

.test-mic-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background-color: #4a90e2;
  color: white;
  font-size: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.test-mic-button:hover:not(:disabled) {
  background-color: #357abd;
  transform: scale(1.1);
}

.test-mic-button:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

.test-mic-button.testing {
  background-color: #f39c12;
  animation: pulse 1s infinite;
}

.sophie-button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: none;
  background-color: #e91e63;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.sophie-button:hover:not(:disabled) {
  background-color: #c2185b;
  transform: scale(1.1);
}

.sophie-button:disabled {
  background-color: #666;
  cursor: not-allowed;
  opacity: 0.5;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.voice-status {
  text-align: center;
  font-size: 14px;
}

.status-listening {
  color: #ff6b6b;
  animation: blink 1s infinite;
}

.status-processing {
  color: #ffc107;
}

.status-error {
  color: #ff6b6b;
}

.status-ready {
  color: #51cf66;
}

.api-info {
  color: #6366f1;
  font-weight: 400;
  opacity: 0.8;
}

.status-testing {
  color: #f39c12;
  font-weight: 500;
}

.mic-test-result {
  margin-top: 15px;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.mic-test-result.success {
  background-color: rgba(76, 175, 80, 0.1);
  color: #4CAF50;
  border: 1px solid #4CAF50;
}

.mic-test-result.error {
  background-color: rgba(244, 67, 54, 0.1);
  color: #f44336;
  border: 1px solid #f44336;
}

.mic-test-result h5 {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 600;
}

.mic-test-result .test-message {
  margin: 0 0 15px 0;
  font-size: 14px;
}

.mic-test-result .test-details,
.mic-test-result .troubleshooting {
  margin-top: 15px;
}

.mic-test-result .test-details summary,
.mic-test-result .troubleshooting summary {
  cursor: pointer;
  font-weight: 500;
  color: #2563eb;
  margin-bottom: 10px;
}

.mic-test-result .device-info {
  margin-top: 10px;
  padding: 10px;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

.mic-test-result .device-info h6 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.mic-test-result .device-info ul {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.mic-test-result .device-info li {
  margin-bottom: 4px;
}

.mic-test-result .troubleshooting ul {
  margin: 0;
  padding-left: 20px;
  font-size: 13px;
}

.mic-test-result .troubleshooting li {
  margin-bottom: 6px;
  line-height: 1.4;
}

/* Indicateur de niveau audio */
.audio-level-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 15px;
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.audio-level-indicator label {
  font-size: 14px;
  color: #ccc;
  min-width: 80px;
}

.audio-level-bar {
  flex: 1;
  height: 8px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.audio-level-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #8BC34A, #CDDC39, #FFC107, #FF9800, #F44336);
  border-radius: 4px;
  transition: width 0.1s ease;
  min-width: 2px;
}

.audio-level-text {
  font-size: 12px;
  color: #ccc;
  min-width: 35px;
  text-align: right;
}

.audio-guidance {
  font-size: 11px;
  margin-left: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  font-weight: 500;
}

.audio-guidance.good {
  color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.audio-guidance.low {
  color: #f59e0b;
  background: rgba(245, 158, 11, 0.1);
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* Transcription */
.transcript-section {
  margin: 20px 0;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.transcript-section h5 {
  color: #1f8ea5;
  margin-bottom: 10px;
  font-size: 14px;
}

.transcript-text {
  font-family: monospace;
  font-size: 14px;
  line-height: 1.4;
}

.final-text {
  color: white;
}

.interim-text {
  color: #ccc;
  font-style: italic;
}

/* Réponse */
.answer-section {
  margin: 20px 0;
  padding: 15px;
  background-color: rgba(31, 142, 165, 0.2);
  border-radius: 8px;
  border: 1px solid #1f8ea5;
}

.answer-section h5 {
  color: #1f8ea5;
  margin-bottom: 10px;
  font-size: 14px;
}

.answer-text {
  color: white;
  margin-bottom: 15px;
  line-height: 1.4;
}

.answer-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.btn-retry,
.btn-next {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
}

.btn-retry {
  background-color: #666;
  color: white;
}

.btn-retry:hover {
  background-color: #555;
}

.btn-next {
  background-color: #1f8ea5;
  color: white;
}

.btn-next:hover {
  background-color: #1a7a94;
}

/* Section de completion */
.completion-section {
  text-align: center;
}

.completion-section h4 {
  color: #51cf66;
  margin-bottom: 15px;
}

.answers-summary {
  margin: 30px 0;
  text-align: left;
}

.answers-summary h5 {
  color: #1f8ea5;
  margin-bottom: 15px;
}

.answer-item {
  margin-bottom: 15px;
  padding: 10px;
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

.answer-item strong {
  color: #1f8ea5;
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.answer-item p {
  color: #ccc;
  margin: 0;
  font-size: 13px;
  line-height: 1.3;
}

.completion-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
}

.btn-save,
.btn-restart {
  padding: 12px 24px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.3s;
}

.btn-save {
  background-color: #51cf66;
  color: white;
}

.btn-save:hover:not(:disabled) {
  background-color: #47b85c;
}

.btn-save:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-restart {
  background-color: #666;
  color: white;
}

.btn-restart:hover {
  background-color: #555;
}

/* Messages d'erreur */
.error-message {
  margin-top: 20px;
  padding: 10px;
  background-color: rgba(255, 107, 107, 0.2);
  border: 1px solid #ff6b6b;
  border-radius: 4px;
  color: #ff6b6b;
  text-align: center;
  font-size: 14px;
}

.error-actions {
  margin-top: 12px;
  display: flex;
  gap: 10px;
  justify-content: center;
}

.btn-retry-connection {
  background: #6366f1;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.btn-retry-connection:hover {
  background: #4f46e5;
}

/* Responsive */
@media (max-width: 768px) {
  .voice-interview {
    padding: 10px;
  }

  .interview-container {
    padding: 20px;
  }

  .mic-button {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }

  .answer-actions,
  .completion-actions {
    flex-direction: column;
  }

  .page-navigation {
    flex-direction: column;
    gap: 15px;
  }

  .btn-previous-page,
  .btn-skip-page {
    width: 100%;
  }
}

/* Configuration audio */
.audio-configuration {
  margin-bottom: 25px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-selector {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.voice-controls-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  align-items: end;
}

.voice-select-group,
.voice-param-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-select-group label,
.voice-param-group label {
  font-size: 14px;
  color: #ccc;
  font-weight: 500;
}

.voice-select {
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  color: white;
  font-size: 14px;
}

.voice-select option {
  background: #1f2937;
  color: white;
}

.voice-slider {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  outline: none;
  cursor: pointer;
}

.voice-slider::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
}

.voice-slider::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: #2563eb;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.btn-test-voice {
  grid-column: span 2;
  padding: 10px 20px;
  background: #8b5cf6;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-test-voice:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

.microphone-selector {
  padding-top: 20px;
}

.microphone-selector h4 {
  margin: 0 0 15px 0;
  color: #2563eb;
  font-size: 16px;
  font-weight: 600;
}

.mic-selector-controls {
  display: flex;
  gap: 10px;
  align-items: center;
}

.mic-select {
  flex: 1;
  padding: 10px 15px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.mic-select:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.15);
  border-color: #2563eb;
}

.mic-select:focus {
  outline: none;
  border-color: #2563eb;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.2);
}

.mic-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.mic-select option {
  background: #1f2937;
  color: white;
  padding: 8px;
}

.btn-refresh-mics {
  padding: 10px 15px;
  background: #4a90e2;
  border: none;
  border-radius: 8px;
  color: white;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-refresh-mics:hover:not(:disabled) {
  background: #357abd;
  transform: scale(1.05);
}

.btn-refresh-mics:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-repeat-question {
  margin-top: 15px;
  padding: 8px 15px;
  background: #8b5cf6;
  border: none;
  border-radius: 6px;
  color: white;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-repeat-question:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

/* Accessibilité */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Styles pour les balises sémantiques */
fieldset.mic-controls {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-bottom: 20px;
}

progress.progress-bar {
  width: 100%;
  height: 8px;
  border: none;
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.1);
  margin-bottom: 15px;
}

progress.progress-bar::-webkit-progress-bar {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

progress.progress-bar::-webkit-progress-value {
  background: linear-gradient(90deg, #2563eb, #1d4ed8);
  border-radius: 4px;
}

progress.progress-bar::-moz-progress-bar {
  background: linear-gradient(90deg, #2563eb, #1d4ed8);
  border-radius: 4px;
}

output {
  display: block;
}

details.answers-summary {
  margin: 20px 0;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

details.answers-summary summary {
  cursor: pointer;
  font-weight: 600;
  color: #2563eb;
  margin-bottom: 15px;
}

details.answers-summary[open] summary {
  margin-bottom: 20px;
}

/* Styles pour les pages */
.progress-container {
  margin-bottom: 30px;
}

.page-indicator {
  display: flex;
  justify-content: center;
  gap: 8px;
  margin-top: 15px;
}

.page-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.page-dot.completed {
  background-color: #4CAF50;
}

.page-dot.current {
  background-color: #2563eb;
  transform: scale(1.2);
}

.page-dot.pending {
  background-color: #ccc;
}

.page-section {
  margin-bottom: 30px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.page-title {
  color: #2563eb;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 10px;
}

.page-description {
  color: #ccc;
  font-size: 16px;
  margin: 0;
}

.page-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-previous-page,
.btn-skip-page {
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-skip {
  background-color: #f59e0b;
  color: white;
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 10px;
}

.btn-skip:hover {
  background-color: #d97706;
  transform: translateY(-1px);
}

.btn-previous-page {
  background-color: #6b7280;
  color: white;
}

.btn-previous-page:hover {
  background-color: #4b5563;
  transform: translateY(-2px);
}

.btn-skip-page {
  background-color: transparent;
  color: #9ca3af;
  border: 1px solid #4b5563;
}

.btn-skip-page:hover {
  background-color: #374151;
  color: white;
}

/* Styles pour la saisie texte alternative */
.text-input-section {
  margin: 20px 0;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.text-input-section h4 {
  color: #f59e0b;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.text-input {
  width: 100%;
  padding: 12px 16px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: white;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 80px;
  transition: all 0.3s ease;
}

.text-input:focus {
  outline: none;
  border-color: #f59e0b;
  background: rgba(255, 255, 255, 0.15);
  box-shadow: 0 0 0 2px rgba(245, 158, 11, 0.2);
}

.text-input::placeholder {
  color: #9ca3af;
}

.text-input:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-submit-text {
  align-self: flex-start;
  padding: 10px 20px;
  background-color: #f59e0b;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-submit-text:hover:not(:disabled) {
  background-color: #d97706;
  transform: translateY(-1px);
}

.btn-submit-text:disabled {
  background-color: #6b7280;
  cursor: not-allowed;
  transform: none;
}
</style>
