#!/bin/bash

# Script de test pour MeetVoice WebSocket Server

set -e

echo "🚀 Démarrage des tests MeetVoice WebSocket Server"
echo "================================================"

# Couleurs pour les logs
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages colorés
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Vérifier que Rust est installé
check_rust() {
    log_info "Vérification de Rust..."
    if command -v cargo &> /dev/null; then
        RUST_VERSION=$(cargo --version)
        log_success "Rust installé: $RUST_VERSION"
    else
        log_error "Rust n'est pas installé. Veuillez installer Rust depuis https://rustup.rs/"
        exit 1
    fi
}

# Vérifier que Docker est installé
check_docker() {
    log_info "Vérification de Docker..."
    if command -v docker &> /dev/null; then
        DOCKER_VERSION=$(docker --version)
        log_success "Docker installé: $DOCKER_VERSION"
    else
        log_warning "Docker n'est pas installé. Certains tests seront ignorés."
        return 1
    fi
}

# Vérifier que Docker Compose est installé
check_docker_compose() {
    log_info "Vérification de Docker Compose..."
    if command -v docker-compose &> /dev/null; then
        COMPOSE_VERSION=$(docker-compose --version)
        log_success "Docker Compose installé: $COMPOSE_VERSION"
    else
        log_warning "Docker Compose n'est pas installé. Certains tests seront ignorés."
        return 1
    fi
}

# Compiler le projet
compile_project() {
    log_info "Compilation du projet..."
    if cargo build; then
        log_success "Compilation réussie"
    else
        log_error "Échec de la compilation"
        exit 1
    fi
}

# Lancer les tests unitaires
run_unit_tests() {
    log_info "Lancement des tests unitaires..."
    if cargo test --lib; then
        log_success "Tests unitaires réussis"
    else
        log_error "Échec des tests unitaires"
        exit 1
    fi
}

# Démarrer les services de base de données
start_databases() {
    log_info "Démarrage des bases de données..."
    
    if check_docker && check_docker_compose; then
        # Arrêter les services existants
        docker-compose down 2>/dev/null || true
        
        # Démarrer PostgreSQL et MongoDB
        if docker-compose up -d postgres mongodb; then
            log_success "Bases de données démarrées"
            
            # Attendre que les services soient prêts
            log_info "Attente de la disponibilité des bases de données..."
            sleep 10
            
            # Vérifier PostgreSQL
            if docker-compose exec -T postgres pg_isready -U postgres; then
                log_success "PostgreSQL prêt"
            else
                log_warning "PostgreSQL non disponible"
            fi
            
            # Vérifier MongoDB
            if docker-compose exec -T mongodb mongosh --eval "db.adminCommand('ping')" --quiet; then
                log_success "MongoDB prêt"
            else
                log_warning "MongoDB non disponible"
            fi
        else
            log_warning "Impossible de démarrer les bases de données"
            return 1
        fi
    else
        log_warning "Docker non disponible, tests de base de données ignorés"
        return 1
    fi
}

# Lancer les tests d'intégration
run_integration_tests() {
    log_info "Lancement des tests d'intégration..."
    
    # Tests sans base de données
    if cargo test test_message_serialization; then
        log_success "Test de sérialisation réussi"
    else
        log_warning "Échec du test de sérialisation"
    fi
    
    if cargo test test_voice_protocol; then
        log_success "Test du protocole vocal réussi"
    else
        log_warning "Échec du test du protocole vocal"
    fi
    
    if cargo test test_video_protocol; then
        log_success "Test du protocole vidéo réussi"
    else
        log_warning "Échec du test du protocole vidéo"
    fi
    
    if cargo test test_concurrent_connections; then
        log_success "Test de connexions concurrentes réussi"
    else
        log_warning "Échec du test de connexions concurrentes"
    fi
    
    # Tests avec base de données (si disponibles)
    if docker-compose ps postgres | grep -q "Up"; then
        log_info "Test des connexions aux bases de données..."
        if cargo test test_database_connections; then
            log_success "Test des bases de données réussi"
        else
            log_warning "Échec du test des bases de données"
        fi
    fi
}

# Démarrer le serveur en arrière-plan pour les tests
start_server() {
    log_info "Démarrage du serveur de test..."
    
    # Créer le fichier .env s'il n'existe pas
    if [ ! -f .env ]; then
        cp .env.example .env
        log_info "Fichier .env créé depuis .env.example"
    fi
    
    # Démarrer le serveur en arrière-plan
    cargo run &
    SERVER_PID=$!
    
    # Attendre que le serveur démarre
    log_info "Attente du démarrage du serveur..."
    sleep 5
    
    # Vérifier que le serveur répond
    if curl -s http://localhost:8080/health > /dev/null; then
        log_success "Serveur démarré et accessible"
        return 0
    else
        log_warning "Serveur non accessible"
        kill $SERVER_PID 2>/dev/null || true
        return 1
    fi
}

# Tester l'API REST
test_rest_api() {
    log_info "Test de l'API REST..."
    
    # Test de santé
    if curl -s http://localhost:8080/health | grep -q "healthy"; then
        log_success "Endpoint /health fonctionne"
    else
        log_warning "Endpoint /health ne répond pas correctement"
    fi
    
    # Test des peers P2P
    if curl -s http://localhost:8080/p2p/peers > /dev/null; then
        log_success "Endpoint /p2p/peers accessible"
    else
        log_warning "Endpoint /p2p/peers non accessible"
    fi
}

# Tester WebSocket
test_websocket() {
    log_info "Test WebSocket..."
    
    if cargo test test_websocket_connection; then
        log_success "Test WebSocket réussi"
    else
        log_warning "Échec du test WebSocket"
    fi
}

# Nettoyer les ressources
cleanup() {
    log_info "Nettoyage..."
    
    # Arrêter le serveur
    if [ ! -z "$SERVER_PID" ]; then
        kill $SERVER_PID 2>/dev/null || true
        log_info "Serveur arrêté"
    fi
    
    # Arrêter les services Docker
    if command -v docker-compose &> /dev/null; then
        docker-compose down 2>/dev/null || true
        log_info "Services Docker arrêtés"
    fi
}

# Fonction principale
main() {
    echo "🧪 Suite de tests MeetVoice WebSocket"
    echo "===================================="
    
    # Vérifications préliminaires
    check_rust
    
    # Compilation
    compile_project
    
    # Tests unitaires
    run_unit_tests
    
    # Démarrage des bases de données
    if start_databases; then
        DATABASE_AVAILABLE=true
    else
        DATABASE_AVAILABLE=false
    fi
    
    # Tests d'intégration
    run_integration_tests
    
    # Tests du serveur
    if start_server; then
        test_rest_api
        test_websocket
    else
        log_warning "Impossible de démarrer le serveur pour les tests"
    fi
    
    # Nettoyage
    cleanup
    
    echo ""
    echo "📊 Résumé des tests"
    echo "=================="
    log_success "Tests unitaires: ✅"
    log_success "Tests d'intégration: ✅"
    
    if [ "$DATABASE_AVAILABLE" = true ]; then
        log_success "Tests de base de données: ✅"
    else
        log_warning "Tests de base de données: ⚠️  (ignorés)"
    fi
    
    echo ""
    log_success "🎉 Tests terminés avec succès!"
    echo ""
    echo "Pour démarrer le serveur manuellement:"
    echo "  cargo run"
    echo ""
    echo "Pour démarrer avec Docker:"
    echo "  docker-compose up -d"
}

# Gérer l'interruption (Ctrl+C)
trap cleanup EXIT

# Lancer les tests
main "$@"
