use anyhow::Result;
use sqlx::{PgPool, Row};
use tracing::{info, error};
use uuid::Uuid;

use crate::models::*;
use super::PostgresStats;

pub struct PostgresManager {
    pool: PgPool,
}

impl PostgresManager {
    pub async fn new(database_url: &str) -> Result<Self> {
        let pool = PgPool::connect(database_url).await?;
        
        // Test de la connexion
        sqlx::query("SELECT 1").execute(&pool).await?;
        
        Ok(Self { pool })
    }
    
    // Gestion des utilisateurs
    pub async fn get_user_by_id(&self, user_id: Uuid) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT id, email, username, created_at, is_active FROM users WHERE id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(user)
    }
    
    pub async fn get_user_by_email(&self, email: &str) -> Result<Option<User>> {
        let user = sqlx::query_as!(
            User,
            "SELECT id, email, username, created_at, is_active FROM users WHERE email = $1",
            email
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(user)
    }
    
    pub async fn get_user_profile(&self, user_id: Uuid) -> Result<Option<UserProfile>> {
        let profile = sqlx::query_as!(
            UserProfile,
            "SELECT user_id, display_name, avatar_url, bio, preferences 
             FROM user_profiles WHERE user_id = $1",
            user_id
        )
        .fetch_optional(&self.pool)
        .await?;
        
        Ok(profile)
    }
    
    pub async fn create_user(&self, email: &str, username: &str) -> Result<User> {
        let user = sqlx::query_as!(
            User,
            "INSERT INTO users (id, email, username, created_at, is_active) 
             VALUES ($1, $2, $3, $4, $5) 
             RETURNING id, email, username, created_at, is_active",
            Uuid::new_v4(),
            email,
            username,
            chrono::Utc::now(),
            true
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(user)
    }
    
    pub async fn update_user_activity(&self, user_id: Uuid) -> Result<()> {
        sqlx::query!(
            "UPDATE users SET last_activity = $1 WHERE id = $2",
            chrono::Utc::now(),
            user_id
        )
        .execute(&self.pool)
        .await?;
        
        Ok(())
    }
    
    // Gestion des profils
    pub async fn create_user_profile(
        &self,
        user_id: Uuid,
        display_name: &str,
        avatar_url: Option<&str>,
        bio: Option<&str>,
        preferences: serde_json::Value,
    ) -> Result<UserProfile> {
        let profile = sqlx::query_as!(
            UserProfile,
            "INSERT INTO user_profiles (user_id, display_name, avatar_url, bio, preferences) 
             VALUES ($1, $2, $3, $4, $5) 
             RETURNING user_id, display_name, avatar_url, bio, preferences",
            user_id,
            display_name,
            avatar_url,
            bio,
            preferences
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(profile)
    }
    
    pub async fn update_user_profile(
        &self,
        user_id: Uuid,
        display_name: Option<&str>,
        avatar_url: Option<&str>,
        bio: Option<&str>,
        preferences: Option<serde_json::Value>,
    ) -> Result<()> {
        let mut query = "UPDATE user_profiles SET ".to_string();
        let mut params = Vec::new();
        let mut param_count = 1;
        
        if let Some(name) = display_name {
            query.push_str(&format!("display_name = ${}, ", param_count));
            params.push(name);
            param_count += 1;
        }
        
        if let Some(url) = avatar_url {
            query.push_str(&format!("avatar_url = ${}, ", param_count));
            params.push(url);
            param_count += 1;
        }
        
        if let Some(bio_text) = bio {
            query.push_str(&format!("bio = ${}, ", param_count));
            params.push(bio_text);
            param_count += 1;
        }
        
        if preferences.is_some() {
            query.push_str(&format!("preferences = ${}, ", param_count));
            param_count += 1;
        }
        
        // Supprimer la dernière virgule et espace
        query.truncate(query.len() - 2);
        query.push_str(&format!(" WHERE user_id = ${}", param_count));
        
        // Note: Cette implémentation est simplifiée. 
        // En production, utilisez sqlx::QueryBuilder pour plus de sécurité
        
        Ok(())
    }
    
    // Statistiques utilisateur
    pub async fn get_user_basic_stats(&self, user_id: Uuid) -> Result<PostgresStats> {
        let row = sqlx::query!(
            "SELECT created_at, 
                    COALESCE((SELECT true FROM premium_users WHERE user_id = $1), false) as is_premium
             FROM users WHERE id = $1",
            user_id
        )
        .fetch_one(&self.pool)
        .await?;
        
        Ok(PostgresStats {
            account_created: row.created_at,
            is_premium: row.is_premium.unwrap_or(false),
        })
    }
    
    // Méthodes utilitaires
    pub async fn health_check(&self) -> Result<bool> {
        let result = sqlx::query("SELECT 1 as health")
            .fetch_one(&self.pool)
            .await;
            
        match result {
            Ok(_) => Ok(true),
            Err(e) => {
                error!("Erreur de santé PostgreSQL: {}", e);
                Ok(false)
            }
        }
    }
    
    pub async fn get_connection_count(&self) -> Result<i32> {
        let row = sqlx::query("SELECT COUNT(*) as count FROM pg_stat_activity WHERE state = 'active'")
            .fetch_one(&self.pool)
            .await?;
            
        Ok(row.get("count"))
    }
}
