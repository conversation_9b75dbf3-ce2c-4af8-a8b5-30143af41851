<!DOCTYPE html>
<html>
<head>
    <title>Test Ultra Simple</title>
</head>
<body>
    <h1>Test Ultra Simple TTS</h1>
    
    <select id="voiceSelect">
        <option value="female_young">Sophie - Voix Féminine Jeune</option>
        <option value="female_mature">Camille - Voix Féminine Mature</option>
        <option value="male_young">Lucas - Voix Masculine Jeune</option>
        <option value="male_mature">Antoine - Voix Masculine Mature</option>
    </select>
    
    <button onclick="testVoice()">Tester la voix sélectionnée</button>
    <div id="result"></div>
    
    <script>
        async function testVoice() {
            const voiceSelect = document.getElementById('voiceSelect');
            const selectedVoice = voiceSelect.value;
            const resultDiv = document.getElementById('result');

            resultDiv.innerHTML = `🔄 Test de ${selectedVoice}...`;

            // Mapping des voice_type vers les IDs numériques
            const voiceMapping = {
                'female_young': 1,    // <PERSON>
                'female_mature': 2,   // <PERSON>
                'male_young': 3,      // <PERSON>
                'male_mature': 4      // <PERSON>
            };

            try {
                const payload = {
                    text: `Bonjour, je suis la voix ${selectedVoice}. Ceci est un test de synthèse vocale.`,
                    voice_id: voiceMapping[selectedVoice] || 1,
                    language: 'fr',
                    speed: 1.0,
                    pitch: 1.0
                };

                console.log('🔊 Test payload:', payload);

                const response = await fetch('http://127.0.0.1:8000/tts/synthesize/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(payload)
                });

                console.log('📡 Response:', response.status, response.statusText);

                if (response.ok) {
                    const data = await response.json();
                    console.log('🎵 Response data:', data);

                    if (data.audio_url) {
                        const audio = new Audio(data.audio_url);
                        await audio.play();
                        resultDiv.innerHTML = `✅ ${selectedVoice} - Audio joué !`;
                    } else {
                        resultDiv.innerHTML = '❌ Pas d\'URL audio dans la réponse';
                    }
                } else {
                    const errorText = await response.text();
                    console.error('❌ Error:', errorText);
                    resultDiv.innerHTML = `❌ Erreur ${response.status}`;
                }

            } catch (error) {
                console.error('❌ Exception:', error);
                resultDiv.innerHTML = `❌ Exception: ${error.message}`;
            }
        }
    </script>
</body>
</html>
