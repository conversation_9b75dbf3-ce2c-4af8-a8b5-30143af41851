<template>
  <div class="payment-methods">
    <div class="header">
      <h2>Méthodes de paiement</h2>
      <button @click="showAddCard = true" class="btn-add">
        <span class="icon">+</span>
        Ajouter une carte
      </button>
    </div>

    <!-- Liste des méthodes de paiement -->
    <div v-if="paymentMethods.length > 0" class="methods-list">
      <div
        v-for="method in paymentMethods"
        :key="method.id"
        class="payment-method-card"
        :class="{ 'default': method.is_default }"
      >
        <div class="card-info">
          <div class="card-brand">
            <img :src="getBrandIcon(method.card.brand)" :alt="method.card.brand" />
          </div>
          
          <div class="card-details">
            <div class="card-number">
              **** **** **** {{ method.card.last4 }}
            </div>
            <div class="card-expiry">
              Expire {{ method.card.exp_month.toString().padStart(2, '0') }}/{{ method.card.exp_year }}
            </div>
            <div class="card-holder" v-if="method.billing_details?.name">
              {{ method.billing_details.name }}
            </div>
          </div>
        </div>

        <div class="card-actions">
          <button
            v-if="!method.is_default"
            @click="setAsDefault(method.id)"
            class="btn-default"
            :disabled="loading"
          >
            Définir par défaut
          </button>
          
          <button
            @click="deleteMethod(method.id)"
            class="btn-delete"
            :disabled="loading"
          >
            <span class="icon">🗑️</span>
            Supprimer
          </button>
        </div>

        <div v-if="method.is_default" class="default-badge">
          Par défaut
        </div>
      </div>
    </div>

    <!-- État vide -->
    <div v-else-if="!loading" class="empty-state">
      <div class="empty-icon">💳</div>
      <h3>Aucune méthode de paiement</h3>
      <p>Ajoutez une carte de crédit pour faciliter vos paiements futurs.</p>
      <button @click="showAddCard = true" class="btn-add-first">
        Ajouter ma première carte
      </button>
    </div>

    <!-- État de chargement -->
    <div v-if="loading" class="loading-state">
      <div class="spinner"></div>
      <p>Chargement des méthodes de paiement...</p>
    </div>

    <!-- Modal d'ajout de carte -->
    <div v-if="showAddCard" class="modal-overlay" @click="closeModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>Ajouter une nouvelle carte</h3>
          <button @click="closeModal" class="btn-close">×</button>
        </div>
        
        <div class="modal-body">
          <CreditCardForm
            :processing="processing"
            submit-text="Sauvegarder la carte"
            @submit="handleAddCard"
            @cancel="closeModal"
          />
        </div>
      </div>
    </div>

    <!-- Messages d'erreur/succès -->
    <div v-if="error" class="alert alert-error">
      {{ error }}
      <button @click="clearError" class="alert-close">×</button>
    </div>

    <div v-if="successMessage" class="alert alert-success">
      {{ successMessage }}
      <button @click="successMessage = ''" class="alert-close">×</button>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'
import CreditCardForm from './CreditCardForm.vue'

export default {
  name: 'PaymentMethods',
  
  components: {
    CreditCardForm
  },
  
  data() {
    return {
      showAddCard: false,
      processing: false,
      successMessage: ''
    }
  },
  
  computed: {
    ...mapState('payment', ['loading', 'error']),
    ...mapGetters('payment', ['paymentMethods'])
  },
  
  async mounted() {
    await this.loadPaymentMethods()
  },
  
  methods: {
    ...mapActions('payment', [
      'loadPaymentMethods',
      'savePaymentMethod',
      'deletePaymentMethod',
      'clearError'
    ]),
    
    async handleAddCard(paymentData) {
      try {
        this.processing = true
        
        await this.savePaymentMethod({
          billingDetails: paymentData.billingDetails
        })
        
        this.successMessage = 'Carte ajoutée avec succès !'
        this.showAddCard = false
        
        // Recharger la liste
        await this.loadPaymentMethods()
        
      } catch (error) {
        console.error('Erreur ajout carte:', error)
      } finally {
        this.processing = false
      }
    },
    
    async deleteMethod(methodId) {
      if (!confirm('Êtes-vous sûr de vouloir supprimer cette méthode de paiement ?')) {
        return
      }
      
      try {
        await this.deletePaymentMethod(methodId)
        this.successMessage = 'Méthode de paiement supprimée'
      } catch (error) {
        console.error('Erreur suppression:', error)
      }
    },
    
    async setAsDefault(methodId) {
      try {
        // Appeler l'API pour définir comme méthode par défaut
        const response = await fetch(`/api/abonnement/payment-methods/${methodId}/set-default/`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        })
        
        if (response.ok) {
          this.successMessage = 'Méthode de paiement définie par défaut'
          await this.loadPaymentMethods()
        }
      } catch (error) {
        console.error('Erreur définition par défaut:', error)
      }
    },
    
    closeModal() {
      this.showAddCard = false
    },
    
    getBrandIcon(brand) {
      const icons = {
        visa: '/images/cards/visa.svg',
        mastercard: '/images/cards/mastercard.svg',
        amex: '/images/cards/amex.svg',
        discover: '/images/cards/discover.svg',
        diners: '/images/cards/diners.svg',
        jcb: '/images/cards/jcb.svg',
        unionpay: '/images/cards/unionpay.svg'
      }
      return icons[brand] || '/images/cards/generic.svg'
    }
  }
}
</script>

<style scoped>
.payment-methods {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.header h2 {
  color: #333;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.btn-add {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.3s ease;
}

.btn-add:hover {
  background: #0056b3;
}

.btn-add .icon {
  font-size: 18px;
  font-weight: bold;
}

/* Liste des méthodes */
.methods-list {
  display: grid;
  gap: 20px;
}

.payment-method-card {
  background: white;
  border: 2px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  position: relative;
  transition: all 0.3s ease;
}

.payment-method-card:hover {
  border-color: #007bff;
  box-shadow: 0 5px 15px rgba(0, 123, 255, 0.1);
}

.payment-method-card.default {
  border-color: #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #e8f5e8 100%);
}

.card-info {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.card-brand img {
  height: 30px;
  width: auto;
}

.card-details {
  flex: 1;
}

.card-number {
  font-size: 18px;
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.card-expiry {
  color: #666;
  font-size: 14px;
  margin-bottom: 3px;
}

.card-holder {
  color: #666;
  font-size: 14px;
  text-transform: uppercase;
}

.card-actions {
  display: flex;
  gap: 10px;
}

.btn-default,
.btn-delete {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s ease;
}

.btn-default {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.btn-default:hover {
  background: #e9ecef;
}

.btn-delete {
  background: #dc3545;
  color: white;
  display: flex;
  align-items: center;
  gap: 5px;
}

.btn-delete:hover {
  background: #c82333;
}

.default-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  background: #28a745;
  color: white;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

/* État vide */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  margin-bottom: 30px;
  font-size: 16px;
}

.btn-add-first {
  padding: 12px 30px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
}

.btn-add-first:hover {
  background: #0056b3;
}

/* État de chargement */
.loading-state {
  text-align: center;
  padding: 40px;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.modal-content {
  background: white;
  border-radius: 12px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  border-bottom: 1px solid #e1e5e9;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-close:hover {
  color: #333;
}

.modal-body {
  padding: 0;
}

/* Alertes */
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.alert-error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert-success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
}

.alert-close:hover {
  opacity: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .payment-methods {
    padding: 10px;
  }
  
  .header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .card-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .card-actions {
    flex-direction: column;
  }
  
  .modal-overlay {
    padding: 10px;
  }
  
  .modal-header {
    padding: 15px 20px;
  }
}
</style>
