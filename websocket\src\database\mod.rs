use anyhow::Result;
use mongodb::{Client as MongoClient, Database as MongoDatabase};
use sqlx::{PgPool, Row};
use std::sync::Arc;
use tracing::{info, error};
use uuid::Uuid;

use crate::config::AppConfig;
use crate::models::*;

pub mod postgres;
pub mod mongodb;

pub use postgres::PostgresManager;
pub use mongodb::MongoManager;

#[derive(Clone)]
pub struct DatabaseManager {
    pub postgres: Arc<PostgresManager>,
    pub mongo: Arc<MongoManager>,
}

impl DatabaseManager {
    pub async fn new(config: &AppConfig) -> Result<Self> {
        info!("🔌 Connexion aux bases de données...");
        
        // Connexion PostgreSQL
        let postgres = Arc::new(PostgresManager::new(&config.database.postgres_url).await?);
        info!("✅ PostgreSQL connecté");
        
        // Connexion MongoDB
        let mongo = Arc::new(MongoManager::new(
            &config.database.mongodb_url,
            &config.database.mongodb_database,
        ).await?);
        info!("✅ MongoDB connecté");
        
        Ok(Self { postgres, mongo })
    }
    
    // Méthodes combinées pour les opérations courantes
    pub async fn get_user_with_profile(&self, user_id: Uuid) -> Result<Option<(User, Option<UserProfile>)>> {
        let user = self.postgres.get_user_by_id(user_id).await?;
        
        if let Some(user) = user {
            let profile = self.postgres.get_user_profile(user_id).await?;
            Ok(Some((user, profile)))
        } else {
            Ok(None)
        }
    }
    
    pub async fn save_chat_message(&self, message: &ChatMessage) -> Result<()> {
        self.mongo.save_chat_message(message).await
    }
    
    pub async fn get_chat_history(&self, room_id: &str, limit: i64) -> Result<Vec<ChatMessage>> {
        self.mongo.get_chat_history(room_id, limit).await
    }
    
    pub async fn create_voice_session(&self, session: &VoiceSession) -> Result<()> {
        self.mongo.create_voice_session(session).await
    }
    
    pub async fn update_voice_session_status(
        &self,
        session_id: &str,
        status: SessionStatus,
    ) -> Result<()> {
        self.mongo.update_voice_session_status(session_id, status).await
    }
    
    pub async fn save_webrtc_offer(&self, offer: &WebRTCOffer) -> Result<()> {
        self.mongo.save_webrtc_offer(offer).await
    }
    
    pub async fn get_pending_offers(&self, user_id: Uuid) -> Result<Vec<WebRTCOffer>> {
        self.mongo.get_pending_offers(user_id).await
    }
    
    // Méthodes de nettoyage et maintenance
    pub async fn cleanup_expired_offers(&self) -> Result<u64> {
        self.mongo.cleanup_expired_offers().await
    }
    
    pub async fn cleanup_old_sessions(&self, days: i64) -> Result<u64> {
        self.mongo.cleanup_old_sessions(days).await
    }
    
    // Méthodes de statistiques
    pub async fn get_active_sessions_count(&self) -> Result<i64> {
        self.mongo.get_active_sessions_count().await
    }
    
    pub async fn get_user_stats(&self, user_id: Uuid) -> Result<UserStats> {
        let postgres_stats = self.postgres.get_user_basic_stats(user_id).await?;
        let mongo_stats = self.mongo.get_user_activity_stats(user_id).await?;
        
        Ok(UserStats {
            user_id,
            total_messages: mongo_stats.total_messages,
            total_voice_sessions: mongo_stats.total_voice_sessions,
            total_connection_time_minutes: mongo_stats.total_connection_time_minutes,
            last_activity: mongo_stats.last_activity,
            account_created: postgres_stats.account_created,
            is_premium: postgres_stats.is_premium,
        })
    }
}

#[derive(Debug, serde::Serialize)]
pub struct UserStats {
    pub user_id: Uuid,
    pub total_messages: i64,
    pub total_voice_sessions: i64,
    pub total_connection_time_minutes: i64,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
    pub account_created: chrono::DateTime<chrono::Utc>,
    pub is_premium: bool,
}

#[derive(Debug)]
pub struct PostgresStats {
    pub account_created: chrono::DateTime<chrono::Utc>,
    pub is_premium: bool,
}

#[derive(Debug)]
pub struct MongoStats {
    pub total_messages: i64,
    pub total_voice_sessions: i64,
    pub total_connection_time_minutes: i64,
    pub last_activity: Option<chrono::DateTime<chrono::Utc>>,
}
