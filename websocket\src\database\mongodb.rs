use anyhow::Result;
use chrono::{DateTime, Utc};
use mongodb::{
    bson::{doc, oid::ObjectId, Document},
    options::{FindOptions, UpdateOptions},
    Client as MongoClient, Collection, Database as MongoDatabase,
};
use tracing::{info, error};
use uuid::Uuid;

use crate::models::*;
use super::MongoStats;

pub struct MongoManager {
    database: MongoDatabase,
    chat_messages: Collection<ChatMessage>,
    voice_sessions: Collection<VoiceSession>,
    webrtc_offers: Collection<WebRTCOffer>,
}

impl MongoManager {
    pub async fn new(connection_string: &str, database_name: &str) -> Result<Self> {
        let client = MongoClient::with_uri_str(connection_string).await?;
        let database = client.database(database_name);
        
        // Test de la connexion
        database.run_command(doc! {"ping": 1}, None).await?;
        
        let chat_messages = database.collection::<ChatMessage>("chat_messages");
        let voice_sessions = database.collection::<VoiceSession>("voice_sessions");
        let webrtc_offers = database.collection::<WebRTCOffer>("webrtc_offers");
        
        // Création des index pour optimiser les performances
        Self::create_indexes(&chat_messages, &voice_sessions, &webrtc_offers).await?;
        
        Ok(Self {
            database,
            chat_messages,
            voice_sessions,
            webrtc_offers,
        })
    }
    
    async fn create_indexes(
        chat_messages: &Collection<ChatMessage>,
        voice_sessions: &Collection<VoiceSession>,
        webrtc_offers: &Collection<WebRTCOffer>,
    ) -> Result<()> {
        // Index pour les messages de chat
        chat_messages
            .create_index(
                doc! {
                    "room_id": 1,
                    "timestamp": -1
                },
                None,
            )
            .await?;
            
        chat_messages
            .create_index(doc! {"user_id": 1}, None)
            .await?;
        
        // Index pour les sessions vocales
        voice_sessions
            .create_index(doc! {"session_id": 1}, None)
            .await?;
            
        voice_sessions
            .create_index(doc! {"participants": 1}, None)
            .await?;
            
        voice_sessions
            .create_index(doc! {"status": 1}, None)
            .await?;
        
        // Index pour les offres WebRTC
        webrtc_offers
            .create_index(doc! {"to_user": 1}, None)
            .await?;
            
        webrtc_offers
            .create_index(doc! {"expires_at": 1}, None)
            .await?;
        
        info!("✅ Index MongoDB créés");
        Ok(())
    }
    
    // Gestion des messages de chat
    pub async fn save_chat_message(&self, message: &ChatMessage) -> Result<()> {
        self.chat_messages.insert_one(message, None).await?;
        Ok(())
    }
    
    pub async fn get_chat_history(&self, room_id: &str, limit: i64) -> Result<Vec<ChatMessage>> {
        let filter = doc! {"room_id": room_id};
        let options = FindOptions::builder()
            .sort(doc! {"timestamp": -1})
            .limit(limit)
            .build();
            
        let mut cursor = self.chat_messages.find(filter, options).await?;
        let mut messages = Vec::new();
        
        while cursor.advance().await? {
            messages.push(cursor.deserialize_current()?);
        }
        
        // Inverser pour avoir l'ordre chronologique
        messages.reverse();
        Ok(messages)
    }
    
    pub async fn get_user_messages(&self, user_id: Uuid, limit: i64) -> Result<Vec<ChatMessage>> {
        let filter = doc! {"user_id": user_id.to_string()};
        let options = FindOptions::builder()
            .sort(doc! {"timestamp": -1})
            .limit(limit)
            .build();
            
        let mut cursor = self.chat_messages.find(filter, options).await?;
        let mut messages = Vec::new();
        
        while cursor.advance().await? {
            messages.push(cursor.deserialize_current()?);
        }
        
        Ok(messages)
    }
    
    // Gestion des sessions vocales
    pub async fn create_voice_session(&self, session: &VoiceSession) -> Result<()> {
        self.voice_sessions.insert_one(session, None).await?;
        Ok(())
    }
    
    pub async fn update_voice_session_status(
        &self,
        session_id: &str,
        status: SessionStatus,
    ) -> Result<()> {
        let filter = doc! {"session_id": session_id};
        let update = doc! {
            "$set": {
                "status": mongodb::bson::to_bson(&status)?,
                "updated_at": Utc::now()
            }
        };
        
        if matches!(status, SessionStatus::Ended) {
            let update = doc! {
                "$set": {
                    "status": mongodb::bson::to_bson(&status)?,
                    "ended_at": Utc::now()
                }
            };
            self.voice_sessions.update_one(filter, update, None).await?;
        } else {
            self.voice_sessions.update_one(filter, update, None).await?;
        }
        
        Ok(())
    }
    
    pub async fn get_active_sessions(&self) -> Result<Vec<VoiceSession>> {
        let filter = doc! {"status": "active"};
        let mut cursor = self.voice_sessions.find(filter, None).await?;
        let mut sessions = Vec::new();
        
        while cursor.advance().await? {
            sessions.push(cursor.deserialize_current()?);
        }
        
        Ok(sessions)
    }
    
    pub async fn get_active_sessions_count(&self) -> Result<i64> {
        let filter = doc! {"status": "active"};
        let count = self.voice_sessions.count_documents(filter, None).await?;
        Ok(count as i64)
    }
    
    // Gestion des offres WebRTC
    pub async fn save_webrtc_offer(&self, offer: &WebRTCOffer) -> Result<()> {
        self.webrtc_offers.insert_one(offer, None).await?;
        Ok(())
    }
    
    pub async fn get_pending_offers(&self, user_id: Uuid) -> Result<Vec<WebRTCOffer>> {
        let filter = doc! {
            "to_user": user_id.to_string(),
            "expires_at": {"$gt": Utc::now()}
        };
        
        let mut cursor = self.webrtc_offers.find(filter, None).await?;
        let mut offers = Vec::new();
        
        while cursor.advance().await? {
            offers.push(cursor.deserialize_current()?);
        }
        
        Ok(offers)
    }
    
    pub async fn cleanup_expired_offers(&self) -> Result<u64> {
        let filter = doc! {"expires_at": {"$lt": Utc::now()}};
        let result = self.webrtc_offers.delete_many(filter, None).await?;
        Ok(result.deleted_count)
    }
    
    pub async fn cleanup_old_sessions(&self, days: i64) -> Result<u64> {
        let cutoff_date = Utc::now() - chrono::Duration::days(days);
        let filter = doc! {
            "status": "ended",
            "ended_at": {"$lt": cutoff_date}
        };
        
        let result = self.voice_sessions.delete_many(filter, None).await?;
        Ok(result.deleted_count)
    }
    
    // Statistiques utilisateur
    pub async fn get_user_activity_stats(&self, user_id: Uuid) -> Result<MongoStats> {
        let user_id_str = user_id.to_string();
        
        // Compter les messages
        let message_count = self.chat_messages
            .count_documents(doc! {"user_id": &user_id_str}, None)
            .await? as i64;
        
        // Compter les sessions vocales
        let session_count = self.voice_sessions
            .count_documents(doc! {"participants": &user_id_str}, None)
            .await? as i64;
        
        // Calculer le temps de connexion total (approximatif)
        let pipeline = vec![
            doc! {
                "$match": {
                    "participants": &user_id_str,
                    "status": "ended",
                    "started_at": {"$exists": true},
                    "ended_at": {"$exists": true}
                }
            },
            doc! {
                "$project": {
                    "duration": {
                        "$subtract": ["$ended_at", "$started_at"]
                    }
                }
            },
            doc! {
                "$group": {
                    "_id": null,
                    "total_duration": {"$sum": "$duration"}
                }
            }
        ];
        
        let mut cursor = self.voice_sessions.aggregate(pipeline, None).await?;
        let total_connection_time = if cursor.advance().await? {
            let doc = cursor.current();
            doc.get_i64("total_duration").unwrap_or(0) / (1000 * 60) // Convertir en minutes
        } else {
            0
        };
        
        // Dernière activité
        let last_message = self.chat_messages
            .find_one(
                doc! {"user_id": &user_id_str},
                FindOptions::builder().sort(doc! {"timestamp": -1}).build(),
            )
            .await?;
            
        let last_activity = last_message.map(|msg| msg.timestamp);
        
        Ok(MongoStats {
            total_messages: message_count,
            total_voice_sessions: session_count,
            total_connection_time_minutes: total_connection_time,
            last_activity,
        })
    }
    
    // Méthodes utilitaires
    pub async fn health_check(&self) -> Result<bool> {
        match self.database.run_command(doc! {"ping": 1}, None).await {
            Ok(_) => Ok(true),
            Err(e) => {
                error!("Erreur de santé MongoDB: {}", e);
                Ok(false)
            }
        }
    }
}
