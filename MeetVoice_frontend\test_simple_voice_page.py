#!/usr/bin/env python3
"""
Test de la page Simple Voice Test avec toutes les voix
"""

import requests
import json
import sys

def test_api_voices():
    """Test que l'API backend fonctionne"""
    print("🎤 Test de l'API TTS Backend...")
    try:
        response = requests.get("http://127.0.0.1:8000/tts/public/voices/")
        if response.status_code == 200:
            voices = response.json()
            print(f"✅ {len(voices)} voix disponibles dans l'API")
            
            # Organiser par langue
            french_voices = [v for v in voices if v['language'] == 'fr']
            international_voices = [v for v in voices if v['language'] != 'fr']
            premium_voices = [v for v in voices if v['is_premium']]
            
            print(f"🇫🇷 Voix françaises: {len(french_voices)}")
            print(f"🌍 Voix internationales: {len(international_voices)}")
            print(f"⭐ Voix premium: {len(premium_voices)}")
            
            return True
        else:
            print(f"❌ Erreur API: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur connexion API: {e}")
        return False

def test_frontend():
    """Test que le frontend fonctionne"""
    print("\n🌐 Test du frontend Vue.js...")
    try:
        response = requests.get("http://localhost:8081/simple-voice-test")
        if response.status_code == 200:
            print("✅ Page Simple Voice Test accessible")
            
            # Vérifier que la page contient les éléments attendus
            content = response.text
            
            checks = [
                ("🎤 Test vocal simplifié", "Titre principal"),
                ("API MeetVoice", "Option API"),
                ("Voix du navigateur", "Option navigateur"),
                ("Choisir une voix", "Sélecteur de voix"),
                ("Voix disponibles", "Section des voix"),
                ("Test de reconnaissance", "Section reconnaissance")
            ]
            
            for text, description in checks:
                if text in content:
                    print(f"  ✅ {description}")
                else:
                    print(f"  ⚠️  {description} - Non trouvé")
            
            return True
        else:
            print(f"❌ Erreur frontend: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Erreur connexion frontend: {e}")
        return False

def test_synthesis():
    """Test de synthèse avec différentes voix"""
    print("\n🔊 Test de synthèse avec différentes voix...")
    
    test_voices = [
        ("female_young", "Sophie - Voix Féminine Jeune"),
        ("male_young", "Lucas - Voix Masculine Jeune"),
        ("female_mature", "Camille - Voix Féminine Mature"),
        ("neutral", "Alex - Voix Neutre")
    ]
    
    success_count = 0
    
    for voice_type, voice_name in test_voices:
        try:
            payload = {
                "text": f"Test de la voix {voice_name}",
                "voice_type": voice_type,
                "language": "fr",
                "speed": 1.0,
                "pitch": 1.0
            }
            
            response = requests.post("http://127.0.0.1:8000/tts/public/synthesize/", json=payload)
            if response.status_code == 200:
                print(f"  ✅ {voice_name}")
                success_count += 1
            else:
                print(f"  ❌ {voice_name} - Erreur {response.status_code}")
        except Exception as e:
            print(f"  ❌ {voice_name} - Erreur: {e}")
    
    print(f"\n📊 Résultat: {success_count}/{len(test_voices)} voix testées avec succès")
    return success_count > 0

def main():
    """Fonction principale"""
    print("🎯 Test de la page Simple Voice Test avec toutes les voix")
    print("=" * 60)
    
    # Test de l'API
    api_ok = test_api_voices()
    
    # Test du frontend
    frontend_ok = test_frontend()
    
    # Test de synthèse
    synthesis_ok = test_synthesis()
    
    print("\n" + "=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    print(f"🔧 API Backend: {'✅ OK' if api_ok else '❌ ERREUR'}")
    print(f"🌐 Frontend: {'✅ OK' if frontend_ok else '❌ ERREUR'}")
    print(f"🔊 Synthèse: {'✅ OK' if synthesis_ok else '❌ ERREUR'}")
    
    if api_ok and frontend_ok and synthesis_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("🌐 Page accessible: http://localhost:8081/simple-voice-test")
        print("📝 Fonctionnalités disponibles:")
        print("  • 11 voix TTS via l'API MeetVoice")
        print("  • Voix locales du navigateur")
        print("  • Contrôles de vitesse et tonalité")
        print("  • Test individuel de chaque voix")
        print("  • Reconnaissance vocale")
        print("  • Interface organisée par langue")
        print("  • Support des voix premium")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        if not api_ok:
            print("💡 Vérifiez que le serveur Django backend fonctionne")
        if not frontend_ok:
            print("💡 Vérifiez que le serveur Vue.js fonctionne")
        if not synthesis_ok:
            print("💡 Vérifiez la configuration TTS du backend")

if __name__ == "__main__":
    main()
