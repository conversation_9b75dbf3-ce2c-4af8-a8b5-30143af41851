<template>
  <main class="dashboard-container">
    <!-- Header adaptatif selon le type d'utilisateur -->
    <header class="dashboard-header">
      <section class="welcome-section">
        <h1>{{ welcomeMessage }}</h1>
        <p class="user-type-indicator">{{ userTypeDescription }}</p>
      </section>
      
      <nav class="quick-actions" aria-label="Actions rapides">
        <component 
          :is="quickActionsComponent" 
          @navigate="handleNavigation"
        />
      </nav>
    </header>

    <!-- Contenu principal adaptatif -->
    <section class="dashboard-content">
      <component 
        :is="dashboardComponent" 
        :user-data="userData"
        @update-data="handleDataUpdate"
      />
    </section>

    <!-- Sidebar commune -->
    <aside class="dashboard-sidebar">
      <section class="notifications-widget">
        <h2>Notifications</h2>
        <NotificationsList :notifications="notifications" />
      </section>
      
      <section class="subscription-widget" v-if="!subscription?.isPremium">
        <h2>Abonnement</h2>
        <SubscriptionCard :subscription="subscription" />
      </section>
    </aside>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

// Composants pour les actions rapides selon le type
import AmicalQuickActions from '@/components/dashboard/AmicalQuickActions.vue'
import AmoureuQuickActions from '@/components/dashboard/AmoureuQuickActions.vue'
import LibertinQuickActions from '@/components/dashboard/LibertinQuickActions.vue'

// Composants pour le contenu principal selon le type
import AmicalDashboard from '@/components/dashboard/AmicalDashboard.vue'
import AmoureuDashboard from '@/components/dashboard/AmoureuDashboard.vue'
import LibertinDashboard from '@/components/dashboard/LibertinDashboard.vue'

// Composants communs
import NotificationsList from '@/components/common/NotificationsList.vue'
import SubscriptionCard from '@/components/common/SubscriptionCard.vue'

export default {
  name: 'DashboardView',
  components: {
    AmicalQuickActions,
    AmoureuQuickActions,
    LibertinQuickActions,
    AmicalDashboard,
    AmoureuDashboard,
    LibertinDashboard,
    NotificationsList,
    SubscriptionCard
  },
  
  computed: {
    ...mapState(['userType', 'user', 'notifications', 'subscription']),
    
    welcomeMessage() {
      const name = this.user?.prenom || 'Utilisateur';
      const timeOfDay = this.getTimeOfDay();
      return `${timeOfDay} ${name} !`;
    },
    
    userTypeDescription() {
      const descriptions = {
        amical: 'Découvrez des événements et rencontrez de nouveaux amis',
        amoureux: 'Trouvez l\'amour grâce à des rencontres authentiques',
        libertin: 'Explorez une communauté ouverte et bienveillante'
      };
      return descriptions[this.userType] || '';
    },
    
    quickActionsComponent() {
      const components = {
        amical: 'AmicalQuickActions',
        amoureux: 'AmoureuQuickActions',
        libertin: 'LibertinQuickActions'
      };
      return components[this.userType] || 'AmicalQuickActions';
    },
    
    dashboardComponent() {
      const components = {
        amical: 'AmicalDashboard',
        amoureux: 'AmoureuDashboard',
        libertin: 'LibertinDashboard'
      };
      return components[this.userType] || 'AmicalDashboard';
    },
    
    userData() {
      return {
        userType: this.userType,
        user: this.user,
        // Données spécifiques selon le type
        ...(this.userType === 'amical' && { 
          events: this.$store.state.events,
          userEvents: this.$store.state.userEvents 
        }),
        ...(this.userType === 'amoureux' && { 
          profiles: this.$store.state.profiles,
          matches: this.$store.state.matches 
        }),
        ...(this.userType === 'libertin' && { 
          community: this.$store.state.community,
          parties: this.$store.state.parties 
        })
      };
    }
  },
  
  methods: {
    ...mapActions(['loadUserData', 'loadNotifications']),
    
    getTimeOfDay() {
      const hour = new Date().getHours();
      if (hour < 12) return 'Bonjour';
      if (hour < 18) return 'Bon après-midi';
      return 'Bonsoir';
    },
    
    handleNavigation(route) {
      this.$router.push(route);
    },
    
    handleDataUpdate(data) {
      // Gérer les mises à jour de données selon le type
      console.log('Mise à jour des données:', data);
    }
  },
  
  async mounted() {
    // Charger les données selon le type d'utilisateur
    await this.loadUserData();
    await this.loadNotifications();
    
    // Rediriger vers l'interface appropriée si nécessaire
    this.redirectToAppropriateInterface();
  },
  
  methods: {
    redirectToAppropriateInterface() {
      const routes = {
        amical: '/events',
        amoureux: '/profiles',
        libertin: '/community'
      };
      
      // Optionnel: redirection automatique vers l'interface principale
      // this.$router.push(routes[this.userType] || '/events');
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}

.dashboard-container {
  display: grid;
  grid-template-areas: 
    "header header"
    "content sidebar";
  grid-template-columns: 1fr 300px;
  grid-template-rows: auto 1fr;
  min-height: 100vh;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  color: var(--text-white);
  gap: 20px;
  padding: 20px;
}

.dashboard-header {
  grid-area: header;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-section h1 {
  font-size: 2rem;
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--accent-blue);
}

.user-type-indicator {
  color: var(--slogan-gray);
  font-size: 1.1rem;
  margin-bottom: 20px;
}

.quick-actions {
  margin-top: 20px;
}

.dashboard-content {
  grid-area: content;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.dashboard-sidebar {
  grid-area: sidebar;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notifications-widget,
.subscription-widget {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.notifications-widget h2,
.subscription-widget h2 {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
  color: var(--accent-purple);
}

/* Responsive */
@media (max-width: 1024px) {
  .dashboard-container {
    grid-template-areas: 
      "header"
      "content"
      "sidebar";
    grid-template-columns: 1fr;
    grid-template-rows: auto auto auto;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
    gap: 16px;
  }
  
  .dashboard-header,
  .dashboard-content,
  .notifications-widget,
  .subscription-widget {
    padding: 16px;
  }
  
  .welcome-section h1 {
    font-size: 1.5rem;
  }
}
</style>
