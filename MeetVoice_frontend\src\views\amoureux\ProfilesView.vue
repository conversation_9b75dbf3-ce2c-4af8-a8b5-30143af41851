<template>
  <main class="profiles-container">
    <!-- Header avec recherche -->
    <header class="profiles-header">
      <section class="header-content">
        <h1>Découvrez des profils</h1>
        <p class="subtitle">Trouvez l'amour grâce à des rencontres authentiques par la voix</p>
      </section>

      <nav class="profiles-actions" aria-label="Actions profils">
        <button
          @click="$router.push('/matches')"
          class="btn-matches"
        >
          <span aria-hidden="true">💕</span>
          Mes matchs ({{ matchesCount }})
        </button>

        <button
          @click="$router.push('/messages')"
          class="btn-messages"
        >
          <span aria-hidden="true">💬</span>
          Messages ({{ unreadCount }})
        </button>
      </nav>
    </header>

    <!-- Filtres de recherche -->
    <section class="search-filters" aria-label="Filtres de recherche">
      <div class="filters-container">
        <div class="filter-group">
          <label for="age-range">Âge :</label>
          <div class="age-range">
            <input
              id="age-min"
              v-model.number="filters.ageMin"
              type="number"
              min="18"
              max="99"
              placeholder="Min"
              class="age-input"
              @change="applyFilters"
            >
            <span>à</span>
            <input
              id="age-max"
              v-model.number="filters.ageMax"
              type="number"
              min="18"
              max="99"
              placeholder="Max"
              class="age-input"
              @change="applyFilters"
            >
          </div>
        </div>

        <div class="filter-group">
          <label for="location-filter">Localisation :</label>
          <input
            id="location-filter"
            v-model="filters.location"
            type="text"
            placeholder="Ville, département..."
            class="filter-input"
            @input="applyFilters"
          >
        </div>

        <div class="filter-group">
          <label for="distance-filter">Distance max :</label>
          <select
            id="distance-filter"
            v-model="filters.distance"
            class="filter-select"
            @change="applyFilters"
          >
            <option value="">Peu importe</option>
            <option value="10">10 km</option>
            <option value="25">25 km</option>
            <option value="50">50 km</option>
            <option value="100">100 km</option>
          </select>
        </div>

        <div class="filter-group">
          <label for="online-filter">Statut :</label>
          <select
            id="online-filter"
            v-model="filters.onlineStatus"
            class="filter-select"
            @change="applyFilters"
          >
            <option value="">Tous</option>
            <option value="online">En ligne</option>
            <option value="recent">Récemment connecté</option>
          </select>
        </div>

        <button
          @click="resetFilters"
          class="btn-reset"
        >
          Réinitialiser
        </button>
      </div>
    </section>

    <!-- Mode d'affichage -->
    <section class="view-controls">
      <div class="view-modes">
        <button
          @click="viewMode = 'cards'"
          :class="['btn-view', { active: viewMode === 'cards' }]"
        >
          <span aria-hidden="true">🃏</span>
          Cartes
        </button>
        <button
          @click="viewMode = 'swipe'"
          :class="['btn-view', { active: viewMode === 'swipe' }]"
        >
          <span aria-hidden="true">👆</span>
          Swipe
        </button>
        <button
          @click="viewMode = 'list'"
          :class="['btn-view', { active: viewMode === 'list' }]"
        >
          <span aria-hidden="true">📋</span>
          Liste
        </button>
      </div>

      <div class="sort-controls">
        <label for="sort-select">Trier par :</label>
        <select
          id="sort-select"
          v-model="sortBy"
          class="sort-select"
          @change="sortProfiles"
        >
          <option value="compatibility">Compatibilité</option>
          <option value="distance">Distance</option>
          <option value="lastConnection">Dernière connexion</option>
          <option value="age">Âge</option>
          <option value="newest">Nouveaux profils</option>
        </select>
      </div>
    </section>

    <!-- Affichage des profils -->
    <section class="profiles-content" aria-label="Profils disponibles">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Recherche de profils compatibles...</p>
      </div>

      <div v-else-if="filteredProfiles.length === 0" class="empty-state">
        <h2>Aucun profil trouvé</h2>
        <p>Essayez d'élargir vos critères de recherche</p>
        <button
          @click="resetFilters"
          class="btn-expand-search"
        >
          Élargir la recherche
        </button>
      </div>

      <!-- Mode cartes -->
      <div v-else-if="viewMode === 'cards'" class="profiles-grid">
        <article
          v-for="profile in paginatedProfiles"
          :key="profile.id"
          class="profile-card"
          @click="viewProfile(profile.id)"
          role="button"
          tabindex="0"
          @keydown.enter="viewProfile(profile.id)"
          @keydown.space.prevent="viewProfile(profile.id)"
        >
          <div class="profile-image-container">
            <img
              :src="profile.photos?.[0] || '/default-avatar.jpg'"
              :alt="`Photo de ${profile.prenom}`"
              class="profile-image"
              loading="lazy"
            >
            <div class="online-indicator" v-if="profile.isOnline"></div>
            <div class="compatibility-badge">
              {{ profile.compatibility }}% compatible
            </div>
          </div>

          <div class="profile-info">
            <header class="profile-header">
              <h3 class="profile-name">{{ profile.prenom }}</h3>
              <span class="profile-age">{{ calculateAge(profile.dateNaissance) }} ans</span>
            </header>

            <div class="profile-location">
              <span aria-hidden="true">📍</span>
              {{ profile.ville || 'Non spécifiée' }}
              <span v-if="profile.distance" class="distance">
                ({{ profile.distance }} km)
              </span>
            </div>

            <p class="profile-description">
              {{ profile.description || 'Aucune description' }}
            </p>

            <div class="profile-interests" v-if="profile.hobbies">
              <span
                v-for="hobby in profile.hobbies.split(',').slice(0, 3)"
                :key="hobby"
                class="interest-tag"
              >
                {{ hobby.trim() }}
              </span>
            </div>
          </div>

          <footer class="profile-actions">
            <button
              @click.stop="passProfile(profile.id)"
              class="btn-pass"
              aria-label="Passer ce profil"
            >
              <span aria-hidden="true">👎</span>
            </button>

            <button
              @click.stop="likeProfile(profile.id)"
              class="btn-like"
              aria-label="Aimer ce profil"
            >
              <span aria-hidden="true">💖</span>
            </button>

            <button
              @click.stop="superLikeProfile(profile.id)"
              class="btn-super-like"
              aria-label="Super like"
            >
              <span aria-hidden="true">⭐</span>
            </button>
          </footer>
        </article>
      </div>

      <!-- Mode swipe -->
      <div v-else-if="viewMode === 'swipe'" class="swipe-container">
        <div v-if="currentSwipeProfile" class="swipe-card">
          <div class="swipe-image-container">
            <img
              :src="currentSwipeProfile.photos?.[0] || '/default-avatar.jpg'"
              :alt="`Photo de ${currentSwipeProfile.prenom}`"
              class="swipe-image"
            >
            <div class="swipe-overlay">
              <div class="swipe-info">
                <h3>{{ currentSwipeProfile.prenom }}, {{ calculateAge(currentSwipeProfile.dateNaissance) }}</h3>
                <p>{{ currentSwipeProfile.ville }}</p>
              </div>
            </div>
          </div>

          <div class="swipe-actions">
            <button
              @click="swipeLeft"
              class="btn-swipe-left"
            >
              <span aria-hidden="true">👎</span>
              Passer
            </button>

            <button
              @click="swipeRight"
              class="btn-swipe-right"
            >
              <span aria-hidden="true">💖</span>
              J'aime
            </button>
          </div>
        </div>

        <div v-else class="no-more-profiles">
          <h3>Plus de profils à découvrir</h3>
          <p>Revenez plus tard pour de nouveaux profils !</p>
          <button
            @click="resetSwipe"
            class="btn-reset-swipe"
          >
            Recommencer
          </button>
        </div>
      </div>

      <!-- Mode liste -->
      <div v-else-if="viewMode === 'list'" class="profiles-list">
        <article
          v-for="profile in paginatedProfiles"
          :key="profile.id"
          class="profile-list-item"
          @click="viewProfile(profile.id)"
        >
          <div class="list-avatar">
            <img
              :src="profile.photos?.[0] || '/default-avatar.jpg'"
              :alt="`Photo de ${profile.prenom}`"
              class="avatar-image"
            >
            <div class="online-dot" v-if="profile.isOnline"></div>
          </div>

          <div class="list-info">
            <div class="list-header">
              <h3 class="list-name">{{ profile.prenom }}</h3>
              <span class="list-age">{{ calculateAge(profile.dateNaissance) }} ans</span>
              <span class="compatibility-score">{{ profile.compatibility }}%</span>
            </div>

            <div class="list-details">
              <span class="list-location">{{ profile.ville }}</span>
              <span v-if="profile.distance" class="list-distance">{{ profile.distance }} km</span>
              <span class="last-connection">{{ formatLastConnection(profile.lastConnection) }}</span>
            </div>
          </div>

          <div class="list-actions">
            <button
              @click.stop="likeProfile(profile.id)"
              class="btn-quick-like"
            >
              💖
            </button>
          </div>
        </article>
      </div>
    </section>

    <!-- Pagination -->
    <nav class="pagination" v-if="viewMode !== 'swipe' && totalPages > 1" aria-label="Navigation pagination">
      <button
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        class="btn-page"
      >
        Précédent
      </button>

      <span class="page-info">
        Page {{ currentPage }} sur {{ totalPages }}
      </span>

      <button
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="btn-page"
      >
        Suivant
      </button>
    </nav>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'ProfilesView',

  data() {
    return {
      loading: false,
      viewMode: 'cards', // cards, swipe, list
      sortBy: 'compatibility',
      currentPage: 1,
      profilesPerPage: 12,
      currentSwipeIndex: 0,

      filters: {
        ageMin: 18,
        ageMax: 65,
        location: '',
        distance: '',
        onlineStatus: ''
      }
    }
  },

  computed: {
    ...mapState(['profiles', 'matches', 'conversations', 'user']),

    matchesCount() {
      return this.matches?.length || 0;
    },

    unreadCount() {
      return this.conversations?.filter(c => c.unreadCount > 0).length || 0;
    },

    filteredProfiles() {
      let filtered = [...this.profiles];

      // Filtre par âge
      if (this.filters.ageMin || this.filters.ageMax) {
        filtered = filtered.filter(profile => {
          const age = this.calculateAge(profile.dateNaissance);
          const minAge = this.filters.ageMin || 0;
          const maxAge = this.filters.ageMax || 999;
          return age >= minAge && age <= maxAge;
        });
      }

      // Filtre par localisation
      if (this.filters.location) {
        filtered = filtered.filter(profile =>
          profile.ville?.toLowerCase().includes(this.filters.location.toLowerCase())
        );
      }

      // Filtre par distance
      if (this.filters.distance) {
        filtered = filtered.filter(profile =>
          profile.distance <= parseInt(this.filters.distance)
        );
      }

      // Filtre par statut en ligne
      if (this.filters.onlineStatus === 'online') {
        filtered = filtered.filter(profile => profile.isOnline);
      } else if (this.filters.onlineStatus === 'recent') {
        const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
        filtered = filtered.filter(profile =>
          new Date(profile.lastConnection) > oneHourAgo
        );
      }

      // Tri
      return this.sortProfilesArray(filtered);
    },

    paginatedProfiles() {
      const start = (this.currentPage - 1) * this.profilesPerPage;
      const end = start + this.profilesPerPage;
      return this.filteredProfiles.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredProfiles.length / this.profilesPerPage);
    },

    currentSwipeProfile() {
      return this.filteredProfiles[this.currentSwipeIndex] || null;
    }
  },

  methods: {
    ...mapActions(['loadProfiles', 'likeProfile', 'passProfile', 'superLikeProfile']),

    calculateAge(birthDate) {
      if (!birthDate) return 'N/A';
      const today = new Date();
      const birth = new Date(birthDate);
      let age = today.getFullYear() - birth.getFullYear();
      const monthDiff = today.getMonth() - birth.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
        age--;
      }

      return age;
    },

    formatLastConnection(lastConnection) {
      if (!lastConnection) return 'Jamais connecté';

      const now = new Date();
      const last = new Date(lastConnection);
      const diffMs = now - last;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);

      if (diffMins < 5) return 'En ligne';
      if (diffMins < 60) return `Il y a ${diffMins} min`;
      if (diffHours < 24) return `Il y a ${diffHours}h`;
      if (diffDays < 7) return `Il y a ${diffDays}j`;
      return last.toLocaleDateString('fr-FR');
    },

    sortProfilesArray(profiles) {
      return profiles.sort((a, b) => {
        switch (this.sortBy) {
          case 'compatibility':
            return (b.compatibility || 0) - (a.compatibility || 0);
          case 'distance':
            return (a.distance || 999) - (b.distance || 999);
          case 'lastConnection':
            return new Date(b.lastConnection || 0) - new Date(a.lastConnection || 0);
          case 'age':
            return this.calculateAge(a.dateNaissance) - this.calculateAge(b.dateNaissance);
          case 'newest':
            return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
          default:
            return 0;
        }
      });
    },

    applyFilters() {
      this.currentPage = 1;
    },

    resetFilters() {
      this.filters = {
        ageMin: 18,
        ageMax: 65,
        location: '',
        distance: '',
        onlineStatus: ''
      };
      this.currentPage = 1;
    },

    sortProfiles() {
      this.currentPage = 1;
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    },

    viewProfile(profileId) {
      this.$router.push(`/profile/${profileId}`);
    },

    async likeProfile(profileId) {
      try {
        await this.likeProfile(profileId);

        // Vérifier si c'est un match
        const profile = this.profiles.find(p => p.id === profileId);
        if (profile?.hasLikedBack) {
          this.$store.commit('addNotification', {
            type: 'success',
            message: `C'est un match avec ${profile.prenom} ! 💕`,
            timestamp: new Date().toISOString()
          });
        }
      } catch (error) {
        console.error('Erreur lors du like:', error);
      }
    },

    async passProfile(profileId) {
      try {
        await this.passProfile(profileId);
      } catch (error) {
        console.error('Erreur lors du pass:', error);
      }
    },

    async superLikeProfile(profileId) {
      try {
        await this.superLikeProfile(profileId);

        this.$store.commit('addNotification', {
          type: 'info',
          message: 'Super Like envoyé ! ⭐',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Erreur lors du super like:', error);
      }
    },

    swipeLeft() {
      if (this.currentSwipeProfile) {
        this.passProfile(this.currentSwipeProfile.id);
        this.nextSwipeProfile();
      }
    },

    swipeRight() {
      if (this.currentSwipeProfile) {
        this.likeProfile(this.currentSwipeProfile.id);
        this.nextSwipeProfile();
      }
    },

    nextSwipeProfile() {
      this.currentSwipeIndex++;
    },

    resetSwipe() {
      this.currentSwipeIndex = 0;
    }
  },

  async mounted() {
    this.loading = true;
    try {
      await this.loadProfiles();
    } catch (error) {
      console.error('Erreur lors du chargement des profils:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --love-pink: #FF69B4;
  --heart-red: #FF1744;
}

.profiles-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.profiles-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--love-pink);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.profiles-actions {
  display: flex;
  gap: 12px;
}

.btn-matches,
.btn-messages {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-matches:hover,
.btn-messages:hover {
  background: var(--heart-red);
  transform: translateY(-2px);
}

.search-filters {
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--slogan-gray);
  font-weight: 500;
}

.age-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.age-input {
  width: 60px;
  padding: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  text-align: center;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  min-width: 120px;
}

.filter-input:focus,
.filter-select:focus,
.age-input:focus {
  outline: none;
  border-color: var(--love-pink);
  background: rgba(255, 255, 255, 0.15);
}

.btn-reset {
  padding: 8px 16px;
  background: transparent;
  color: var(--love-pink);
  border: 1px solid var(--love-pink);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-reset:hover {
  background: var(--love-pink);
  color: var(--text-white);
}

.view-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.view-modes {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  overflow: hidden;
}

.btn-view {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: transparent;
  color: var(--slogan-gray);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view.active {
  background: var(--love-pink);
  color: var(--text-white);
}

.btn-view:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-controls label {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.sort-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.profiles-content {
  margin-bottom: 30px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--love-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-state h2 {
  color: var(--love-pink);
  margin-bottom: 16px;
}

.btn-expand-search {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-expand-search:hover {
  background: var(--heart-red);
}

/* Mode cartes */
.profiles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.profile-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.profile-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--love-pink);
  box-shadow: 0 20px 40px rgba(255, 105, 180, 0.2);
}

.profile-image-container {
  position: relative;
  height: 300px;
  overflow: hidden;
}

.profile-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.profile-card:hover .profile-image {
  transform: scale(1.05);
}

.online-indicator {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 12px;
  height: 12px;
  background: #4ade80;
  border: 2px solid var(--text-white);
  border-radius: 50%;
}

.compatibility-badge {
  position: absolute;
  bottom: 12px;
  left: 12px;
  padding: 6px 12px;
  background: var(--love-pink);
  color: var(--text-white);
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.profile-info {
  padding: 20px;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.profile-name {
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--love-pink);
  margin: 0;
}

.profile-age {
  color: var(--slogan-gray);
  font-size: 1rem;
}

.profile-location {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--slogan-gray);
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.distance {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.profile-description {
  color: var(--slogan-gray);
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.profile-interests {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.interest-tag {
  padding: 4px 8px;
  background: rgba(255, 105, 180, 0.2);
  color: var(--love-pink);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.profile-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-pass,
.btn-like,
.btn-super-like {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-pass {
  background: #6b7280;
  color: var(--text-white);
}

.btn-pass:hover {
  background: #4b5563;
  transform: scale(1.1);
}

.btn-like {
  background: var(--love-pink);
  color: var(--text-white);
}

.btn-like:hover {
  background: var(--heart-red);
  transform: scale(1.1);
}

.btn-super-like {
  background: #fbbf24;
  color: var(--text-white);
}

.btn-super-like:hover {
  background: #f59e0b;
  transform: scale(1.1);
}

/* Mode swipe */
.swipe-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
}

.swipe-card {
  width: 400px;
  height: 600px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.swipe-image-container {
  position: relative;
  height: 500px;
}

.swipe-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.swipe-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  padding: 30px 20px 20px;
}

.swipe-info h3 {
  color: var(--text-white);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.swipe-info p {
  color: var(--slogan-gray);
  font-size: 1rem;
}

.swipe-actions {
  display: flex;
  justify-content: center;
  gap: 40px;
  padding: 20px;
}

.btn-swipe-left,
.btn-swipe-right {
  width: 60px;
  height: 60px;
  border: none;
  border-radius: 50%;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.btn-swipe-left {
  background: #6b7280;
  color: var(--text-white);
}

.btn-swipe-left:hover {
  background: #4b5563;
  transform: scale(1.1);
}

.btn-swipe-right {
  background: var(--love-pink);
  color: var(--text-white);
}

.btn-swipe-right:hover {
  background: var(--heart-red);
  transform: scale(1.1);
}

.no-more-profiles {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.no-more-profiles h3 {
  color: var(--love-pink);
  margin-bottom: 16px;
}

.btn-reset-swipe {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-reset-swipe:hover {
  background: var(--heart-red);
}

/* Mode liste */
.profiles-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.profile-list-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.profile-list-item:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-2px);
}

.list-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--love-pink);
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #4ade80;
  border: 2px solid var(--text-white);
  border-radius: 50%;
}

.list-info {
  flex: 1;
}

.list-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
}

.list-name {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--love-pink);
  margin: 0;
}

.list-age {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.compatibility-score {
  padding: 2px 8px;
  background: var(--love-pink);
  color: var(--text-white);
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.list-details {
  display: flex;
  gap: 16px;
  color: var(--slogan-gray);
  font-size: 0.8rem;
}

.list-actions {
  flex-shrink: 0;
}

.btn-quick-like {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--love-pink);
  color: var(--text-white);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-quick-like:hover {
  background: var(--heart-red);
  transform: scale(1.1);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}

.btn-page {
  padding: 10px 20px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-page:hover:not(:disabled) {
  background: var(--heart-red);
}

.btn-page:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.page-info {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .profiles-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .view-controls {
    flex-direction: column;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .profiles-container {
    padding: 16px;
  }

  .profiles-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .profiles-grid {
    grid-template-columns: 1fr;
  }

  .swipe-card {
    width: 100%;
    max-width: 350px;
  }

  .profile-list-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .list-header {
    justify-content: center;
  }

  .list-details {
    justify-content: center;
  }
}
</style>