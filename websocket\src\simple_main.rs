// Version simplifiée pour tester la compilation de base

use anyhow::Result;
use axum::{
    extract::State,
    http::StatusCode,
    response::Json,
    routing::{get, post},
    Router,
};
use serde_json::{json, Value};
use std::sync::Arc;
use tokio::net::TcpListener;
use tower_http::cors::CorsLayer;
use tracing::{info, warn};

#[derive(Clone)]
pub struct SimpleAppState {
    pub name: String,
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialisation du logging
    tracing_subscriber::fmt()
        .with_env_filter("meetvoice_websocket=debug")
        .init();

    info!("🚀 Démarrage du serveur MeetVoice WebSocket (version simple)");

    // État partagé de l'application
    let app_state = SimpleAppState {
        name: "MeetVoice WebSocket Server".to_string(),
    };

    // Configuration des routes
    let app = Router::new()
        .route("/", get(health_check))
        .route("/health", get(health_check))
        .route("/test", get(test_endpoint))
        .layer(CorsLayer::permissive())
        .with_state(app_state);

    // Démarrage du serveur
    let addr = "0.0.0.0:8080";
    let listener = TcpListener::bind(addr).await?;
    
    info!("🌐 Serveur en écoute sur http://{}", addr);
    
    axum::serve(listener, app).await?;

    Ok(())
}

async fn health_check(State(state): State<SimpleAppState>) -> Json<Value> {
    Json(json!({
        "status": "healthy",
        "service": state.name,
        "version": env!("CARGO_PKG_VERSION"),
        "timestamp": chrono::Utc::now()
    }))
}

async fn test_endpoint() -> Result<Json<Value>, StatusCode> {
    Ok(Json(json!({
        "message": "Test endpoint fonctionne!",
        "features": [
            "WebSocket (à implémenter)",
            "P2P (à implémenter)",
            "Bases de données (à implémenter)"
        ]
    })))
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_health_check() {
        let state = SimpleAppState {
            name: "Test Server".to_string(),
        };
        
        let response = health_check(State(state)).await;
        let value = response.0;
        
        assert_eq!(value["status"], "healthy");
        assert_eq!(value["service"], "Test Server");
    }

    #[tokio::test]
    async fn test_basic_functionality() {
        // Test de base pour vérifier que Rust et les dépendances fonctionnent
        let uuid = uuid::Uuid::new_v4();
        let now = chrono::Utc::now();
        
        println!("UUID généré: {}", uuid);
        println!("Timestamp: {}", now);
        
        assert!(!uuid.to_string().is_empty());
    }
}
