use anyhow::Result;
use serde_json::json;
use std::time::Duration;
use tokio::time::sleep;
use uuid::Uuid;

// Tests d'intégration pour le serveur WebSocket MeetVoice

#[tokio::test]
async fn test_server_health() -> Result<()> {
    // Test de base pour vérifier que le serveur démarre et répond
    let client = reqwest::Client::new();
    
    // Note: En pratique, vous devriez démarrer le serveur dans un thread séparé
    // ou utiliser un framework de test comme `tower::test`
    
    let response = client
        .get("http://localhost:8080/health")
        .timeout(Duration::from_secs(5))
        .send()
        .await;
    
    match response {
        Ok(resp) => {
            assert_eq!(resp.status(), 200);
            let body: serde_json::Value = resp.json().await?;
            assert_eq!(body["status"], "healthy");
            println!("✅ Test de santé du serveur réussi");
        }
        Err(_) => {
            println!("⚠️  Serveur non disponible - test ignoré");
            // En mode CI/CD, vous pourriez vouloir faire échouer le test
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_websocket_connection() -> Result<()> {
    use tokio_tungstenite::{connect_async, tungstenite::Message};
    use futures_util::{SinkExt, StreamExt};
    
    // Test de connexion WebSocket
    let url = "ws://localhost:8080/ws";
    
    match connect_async(url).await {
        Ok((mut ws_stream, _)) => {
            // Test d'authentification
            let auth_message = json!({
                "type": "Auth",
                "data": {
                    "token": Uuid::new_v4().to_string()
                }
            });
            
            ws_stream.send(Message::Text(auth_message.to_string())).await?;
            
            // Attendre la réponse
            if let Some(msg) = ws_stream.next().await {
                match msg? {
                    Message::Text(text) => {
                        let response: serde_json::Value = serde_json::from_str(&text)?;
                        println!("📨 Réponse WebSocket: {}", response);
                        
                        // Vérifier le type de réponse
                        if response["type"] == "AuthError" {
                            println!("✅ Authentification échouée comme attendu (utilisateur inexistant)");
                        } else if response["type"] == "AuthSuccess" {
                            println!("✅ Authentification réussie");
                        }
                    }
                    _ => println!("❌ Type de message inattendu"),
                }
            }
            
            // Test de ping
            let ping_message = json!({
                "type": "Ping"
            });
            
            ws_stream.send(Message::Text(ping_message.to_string())).await?;
            
            // Attendre le pong
            if let Some(msg) = ws_stream.next().await {
                match msg? {
                    Message::Text(text) => {
                        let response: serde_json::Value = serde_json::from_str(&text)?;
                        if response["type"] == "Pong" {
                            println!("✅ Test ping/pong réussi");
                        }
                    }
                    _ => {}
                }
            }
            
            ws_stream.close(None).await?;
            println!("✅ Test de connexion WebSocket réussi");
        }
        Err(_) => {
            println!("⚠️  Serveur WebSocket non disponible - test ignoré");
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_database_connections() -> Result<()> {
    use meetvoice_websocket::config::AppConfig;
    use meetvoice_websocket::database::DatabaseManager;
    
    // Test des connexions aux bases de données
    let config = AppConfig::load()?;
    
    match DatabaseManager::new(&config).await {
        Ok(db_manager) => {
            // Test PostgreSQL
            match db_manager.postgres.health_check().await {
                Ok(true) => println!("✅ PostgreSQL connecté"),
                Ok(false) => println!("❌ PostgreSQL non disponible"),
                Err(e) => println!("❌ Erreur PostgreSQL: {}", e),
            }
            
            // Test MongoDB
            match db_manager.mongo.health_check().await {
                Ok(true) => println!("✅ MongoDB connecté"),
                Ok(false) => println!("❌ MongoDB non disponible"),
                Err(e) => println!("❌ Erreur MongoDB: {}", e),
            }
            
            println!("✅ Test des connexions aux bases de données terminé");
        }
        Err(e) => {
            println!("⚠️  Impossible de se connecter aux bases de données: {}", e);
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_p2p_initialization() -> Result<()> {
    use meetvoice_websocket::config::AppConfig;
    use meetvoice_websocket::p2p::P2PManager;
    
    // Test d'initialisation du gestionnaire P2P
    let config = AppConfig::load()?;
    
    match P2PManager::new(&config.p2p).await {
        Ok(p2p_manager) => {
            println!("✅ Gestionnaire P2P initialisé");
            println!("🆔 Peer ID: {}", p2p_manager.get_peer_id());
            
            // Test de récupération des peers connectés
            let peers = p2p_manager.get_connected_peers().await?;
            println!("👥 Peers connectés: {}", peers.len());
            
            println!("✅ Test d'initialisation P2P réussi");
        }
        Err(e) => {
            println!("❌ Erreur d'initialisation P2P: {}", e);
        }
    }
    
    Ok(())
}

#[tokio::test]
async fn test_message_serialization() -> Result<()> {
    use meetvoice_websocket::models::*;
    use chrono::Utc;
    
    // Test de sérialisation/désérialisation des messages
    let test_message = WebSocketMessage::SendMessage {
        room_id: "test-room".to_string(),
        content: "Hello, World!".to_string(),
        message_type: MessageType::Text,
    };
    
    let serialized = serde_json::to_string(&test_message)?;
    println!("📤 Message sérialisé: {}", serialized);
    
    let deserialized: WebSocketMessage = serde_json::from_str(&serialized)?;
    println!("📥 Message désérialisé: {:?}", deserialized);
    
    // Test avec un message de chat
    let chat_message = ChatMessage {
        id: None,
        user_id: Uuid::new_v4(),
        room_id: "test-room".to_string(),
        content: "Test message".to_string(),
        message_type: MessageType::Text,
        timestamp: Utc::now(),
        metadata: Some(json!({"test": true})),
    };
    
    let chat_serialized = serde_json::to_string(&chat_message)?;
    println!("💬 Message de chat sérialisé: {}", chat_serialized);
    
    let chat_deserialized: ChatMessage = serde_json::from_str(&chat_serialized)?;
    println!("💬 Message de chat désérialisé: {:?}", chat_deserialized);
    
    println!("✅ Test de sérialisation réussi");
    Ok(())
}

#[tokio::test]
async fn test_voice_protocol() -> Result<()> {
    use meetvoice_websocket::p2p::protocols::*;
    
    // Test d'encodage/décodage des paquets vocaux
    let metadata = VoiceStreamMetadata {
        codec: "opus".to_string(),
        sample_rate: 48000,
        channels: 2,
        bitrate: 128000,
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
    };
    
    let audio_data = vec![1, 2, 3, 4, 5]; // Données audio simulées
    
    let encoded = encode_voice_packet(metadata.clone(), audio_data.clone())?;
    println!("🎤 Paquet vocal encodé: {} bytes", encoded.len());
    
    let (decoded_metadata, decoded_data) = decode_voice_packet(encoded)?;
    
    assert_eq!(decoded_metadata.codec, metadata.codec);
    assert_eq!(decoded_metadata.sample_rate, metadata.sample_rate);
    assert_eq!(decoded_data, audio_data);
    
    println!("✅ Test du protocole vocal réussi");
    Ok(())
}

#[tokio::test]
async fn test_video_protocol() -> Result<()> {
    use meetvoice_websocket::p2p::protocols::*;
    
    // Test d'encodage/décodage des paquets vidéo
    let metadata = VideoStreamMetadata {
        codec: "h264".to_string(),
        width: 1920,
        height: 1080,
        fps: 30,
        bitrate: 2000000,
        timestamp: chrono::Utc::now().timestamp_millis() as u64,
    };
    
    let video_data = vec![10, 20, 30, 40, 50]; // Données vidéo simulées
    
    let encoded = encode_video_packet(metadata.clone(), video_data.clone())?;
    println!("📹 Paquet vidéo encodé: {} bytes", encoded.len());
    
    let (decoded_metadata, decoded_data) = decode_video_packet(encoded)?;
    
    assert_eq!(decoded_metadata.codec, metadata.codec);
    assert_eq!(decoded_metadata.width, metadata.width);
    assert_eq!(decoded_metadata.height, metadata.height);
    assert_eq!(decoded_data, video_data);
    
    println!("✅ Test du protocole vidéo réussi");
    Ok(())
}

// Test de performance basique
#[tokio::test]
async fn test_concurrent_connections() -> Result<()> {
    use std::sync::Arc;
    use std::sync::atomic::{AtomicU32, Ordering};
    use tokio::task::JoinSet;
    
    let success_count = Arc::new(AtomicU32::new(0));
    let mut join_set = JoinSet::new();
    
    // Simuler 10 connexions concurrentes
    for i in 0..10 {
        let success_count = success_count.clone();
        
        join_set.spawn(async move {
            // Simulation d'une connexion WebSocket
            sleep(Duration::from_millis(100)).await;
            
            // Simuler une opération réussie
            success_count.fetch_add(1, Ordering::Relaxed);
            
            println!("✅ Connexion {} simulée", i);
        });
    }
    
    // Attendre que toutes les tâches se terminent
    while let Some(_) = join_set.join_next().await {}
    
    let final_count = success_count.load(Ordering::Relaxed);
    assert_eq!(final_count, 10);
    
    println!("✅ Test de connexions concurrentes réussi: {}/10", final_count);
    Ok(())
}
