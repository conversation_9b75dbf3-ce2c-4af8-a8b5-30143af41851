<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Tests Automatiques - Intégration Vosk</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-container {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 18px;
            margin: 10px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 14px;
        }
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 16px;
            font-weight: bold;
            margin: 10px;
        }
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
        }
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: #3498db;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Tests Automatiques d'Intégration</h1>
        <p>Tests complets : Vosk Backend + Frontend Hybride + IA + TTS</p>
        <div id="overall-status">
            <span class="status warning">🟡 Prêt pour les tests</span>
        </div>
    </div>

    <div class="test-container">
        <h3>🚀 Lancement des Tests</h3>
        <button class="btn success" onclick="runFullIntegrationTests()">
            🧪 Lancer Tests Complets
        </button>
        <button class="btn" onclick="runQuickTests()">
            ⚡ Tests Rapides
        </button>
        <button class="btn danger" onclick="clearResults()">
            🗑️ Effacer
        </button>
        
        <div class="progress" id="progress-container" style="display: none;">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
        
        <div id="test-results" class="result" style="display: none;"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://127.0.0.1:8000';
        let testResults = [];
        let currentTest = 0;
        let totalTests = 0;

        // Tests complets d'intégration
        async function runFullIntegrationTests() {
            const resultDiv = document.getElementById('test-results');
            const progressContainer = document.getElementById('progress-container');
            const progressBar = document.getElementById('progress-bar');
            
            resultDiv.style.display = 'block';
            progressContainer.style.display = 'block';
            
            testResults = [];
            currentTest = 0;
            
            const tests = [
                { name: '🔗 Test Connectivité Backend', fn: testBackendConnectivity },
                { name: '🎯 Test API Vosk Status', fn: testVoskStatus },
                { name: '📋 Test API Vosk Languages', fn: testVoskLanguages },
                { name: '🎵 Test Upload Audio Vosk', fn: testVoskUpload },
                { name: '⏱️ Test Performance Vosk', fn: testVoskPerformance },
                { name: '🎤 Test Services Frontend', fn: testFrontendServices },
                { name: '🧠 Test IA Extraction', fn: testAIExtraction },
                { name: '🔊 Test TTS Sophie', fn: testTTSService },
                { name: '🚀 Test Intégration Complète', fn: testFullIntegration }
            ];
            
            totalTests = tests.length;
            
            resultDiv.textContent = '🚀 === DÉBUT DES TESTS D\'INTÉGRATION COMPLETS ===\n\n';
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                currentTest = i + 1;
                
                updateProgress();
                appendResult(`🔍 Test ${currentTest}/${totalTests}: ${test.name}...`);
                
                try {
                    const result = await test.fn();
                    testResults.push({ name: test.name, status: 'SUCCESS', result });
                    appendResult(`✅ ${test.name}: RÉUSSI\n`);
                } catch (error) {
                    testResults.push({ name: test.name, status: 'FAILED', error: error.message });
                    appendResult(`❌ ${test.name}: ÉCHEC - ${error.message}\n`);
                }
                
                // Petite pause entre les tests
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            displayFinalResults();
        }

        // Tests rapides
        async function runQuickTests() {
            const resultDiv = document.getElementById('test-results');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '⚡ === TESTS RAPIDES ===\n\n';
            
            try {
                // Test 1: Backend
                appendResult('🔍 Test backend...');
                const backendOk = await testBackendConnectivity();
                appendResult(`✅ Backend: ${backendOk ? 'OK' : 'KO'}\n`);
                
                // Test 2: Vosk Status
                appendResult('🔍 Test Vosk...');
                const voskOk = await testVoskStatus();
                appendResult(`✅ Vosk: ${voskOk ? 'OK' : 'KO'}\n`);
                
                // Test 3: Frontend
                appendResult('🔍 Test frontend...');
                const frontendOk = await testFrontendServices();
                appendResult(`✅ Frontend: ${frontendOk ? 'OK' : 'KO'}\n`);
                
                const score = [backendOk, voskOk, frontendOk].filter(Boolean).length;
                appendResult(`\n📊 Score: ${score}/3\n`);
                
                if (score === 3) {
                    appendResult('🎉 TOUS LES TESTS RAPIDES RÉUSSIS !');
                    updateOverallStatus('success', '🟢 Intégration OK');
                } else {
                    appendResult('⚠️ Certains tests ont échoué');
                    updateOverallStatus('warning', '🟡 Intégration partielle');
                }
                
            } catch (error) {
                appendResult(`❌ Erreur tests rapides: ${error.message}`);
                updateOverallStatus('error', '🔴 Erreur tests');
            }
        }

        // Test 1: Connectivité Backend
        async function testBackendConnectivity() {
            // Tester directement avec un endpoint qui existe
            const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
            if (!response.ok) throw new Error(`Backend inaccessible: ${response.status}`);
            return { status: response.status, message: 'Backend accessible via Vosk' };
        }

        // Test 2: API Vosk Status
        async function testVoskStatus() {
            const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
            if (!response.ok) throw new Error(`API Vosk indisponible: ${response.status}`);
            const data = await response.json();
            return { status: response.status, data, message: 'API Vosk disponible' };
        }

        // Test 3: API Vosk Languages
        async function testVoskLanguages() {
            const response = await fetch(`${BACKEND_URL}/api/vosk/languages/`);
            if (!response.ok) throw new Error(`Languages indisponibles: ${response.status}`);
            const data = await response.json();
            return { status: response.status, languages: data, message: 'Languages disponibles' };
        }

        // Test 4: Upload Audio Vosk
        async function testVoskUpload() {
            // Créer un vrai fichier WAV minimal (silence de 1 seconde)
            const audioBlob = createMinimalWavFile();
            const formData = new FormData();
            formData.append('audio', audioBlob, 'test.wav');
            formData.append('language', 'fr-FR');

            const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                method: 'POST',
                body: formData
            });

            // Analyser la réponse même si erreur 500
            let data = null;
            try {
                data = await response.json();
            } catch (e) {
                data = { error: 'Réponse non-JSON' };
            }

            if (!response.ok) {
                throw new Error(`Upload échec: ${response.status} - ${JSON.stringify(data)}`);
            }

            return { status: response.status, result: data, message: 'Upload audio réussi' };
        }

        // Test 5: Performance Vosk
        async function testVoskPerformance() {
            const tests = [];
            for (let i = 0; i < 3; i++) {
                const start = performance.now();
                const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
                const end = performance.now();
                if (response.ok) tests.push(end - start);
            }
            
            if (tests.length === 0) throw new Error('Aucun test de performance réussi');
            
            const average = tests.reduce((a, b) => a + b, 0) / tests.length;
            return { average: average.toFixed(2), tests: tests.length, message: 'Performance mesurée' };
        }

        // Test 6: Services Frontend
        async function testFrontendServices() {
            const checks = {
                mediaRecorder: !!window.MediaRecorder,
                getUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
                speechRecognition: !!(window.SpeechRecognition || window.webkitSpeechRecognition),
                fetch: !!window.fetch
            };
            
            const available = Object.values(checks).filter(Boolean).length;
            if (available < 3) throw new Error(`Services insuffisants: ${available}/4`);
            
            return { checks, available, message: 'Services frontend disponibles' };
        }

        // Test 7: IA Extraction (simulation)
        async function testAIExtraction() {
            // Simulation d'extraction IA
            const testText = "Bonjour, je m'appelle Serge et mon <NAME_EMAIL>";
            
            // Extraction simple côté frontend
            const names = testText.match(/\b[A-Z][a-z]+\b/g) || [];
            const emails = testText.match(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g) || [];
            
            return { 
                testText, 
                extractedNames: names, 
                extractedEmails: emails, 
                message: 'IA extraction simulée' 
            };
        }

        // Test 8: TTS Sophie (simulation)
        async function testTTSService() {
            // Test de base TTS
            if (!window.speechSynthesis) throw new Error('Speech Synthesis non disponible');
            
            const voices = speechSynthesis.getVoices();
            const sophieVoice = voices.find(v => v.name.toLowerCase().includes('sophie')) || 
                              voices.find(v => v.lang.startsWith('fr'));
            
            return { 
                totalVoices: voices.length, 
                sophieFound: !!sophieVoice, 
                message: 'TTS service disponible' 
            };
        }

        // Test 9: Intégration Complète
        async function testFullIntegration() {
            const integrationChecks = {
                backend: true,
                vosk: true,
                frontend: true,
                extraction: true,
                tts: true
            };
            
            // Vérifier que tous les tests précédents ont réussi
            const successfulTests = testResults.filter(t => t.status === 'SUCCESS').length;
            const totalPreviousTests = testResults.length;
            
            if (successfulTests < totalPreviousTests * 0.8) {
                throw new Error(`Intégration incomplète: ${successfulTests}/${totalPreviousTests} tests réussis`);
            }
            
            return { 
                integrationChecks, 
                score: `${successfulTests}/${totalPreviousTests}`, 
                message: 'Intégration complète validée' 
            };
        }

        // Créer un fichier WAV minimal valide
        function createMinimalWavFile() {
            const sampleRate = 16000;
            const duration = 1; // 1 seconde
            const numSamples = sampleRate * duration;
            const numChannels = 1;
            const bitsPerSample = 16;

            const buffer = new ArrayBuffer(44 + numSamples * 2);
            const view = new DataView(buffer);

            // Header WAV
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + numSamples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numChannels * bitsPerSample / 8, true);
            view.setUint16(32, numChannels * bitsPerSample / 8, true);
            view.setUint16(34, bitsPerSample, true);
            writeString(36, 'data');
            view.setUint32(40, numSamples * 2, true);

            // Données audio (silence)
            for (let i = 0; i < numSamples; i++) {
                view.setInt16(44 + i * 2, 0, true);
            }

            return new Blob([buffer], { type: 'audio/wav' });
        }

        // Fonctions utilitaires
        function updateProgress() {
            const progressBar = document.getElementById('progress-bar');
            const percentage = (currentTest / totalTests) * 100;
            progressBar.style.width = `${percentage}%`;
        }

        function appendResult(text) {
            const resultDiv = document.getElementById('test-results');
            resultDiv.textContent += text + '\n';
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }

        function displayFinalResults() {
            const successful = testResults.filter(t => t.status === 'SUCCESS').length;
            const failed = testResults.filter(t => t.status === 'FAILED').length;
            
            appendResult('\n🧪 === RÉSUMÉ FINAL ===');
            appendResult(`✅ Tests réussis: ${successful}`);
            appendResult(`❌ Tests échoués: ${failed}`);
            appendResult(`📊 Score: ${successful}/${testResults.length}`);
            
            if (successful === testResults.length) {
                appendResult('\n🎉 INTÉGRATION PARFAITE ! Système prêt pour production !');
                updateOverallStatus('success', '🟢 Intégration parfaite');
            } else if (successful >= testResults.length * 0.8) {
                appendResult('\n✅ INTÉGRATION BONNE ! Quelques optimisations possibles.');
                updateOverallStatus('warning', '🟡 Intégration bonne');
            } else {
                appendResult('\n⚠️ INTÉGRATION PARTIELLE ! Corrections nécessaires.');
                updateOverallStatus('error', '🔴 Intégration partielle');
            }
        }

        function updateOverallStatus(type, text) {
            const statusDiv = document.getElementById('overall-status');
            statusDiv.innerHTML = `<span class="status ${type}">${text}</span>`;
        }

        function clearResults() {
            const resultDiv = document.getElementById('test-results');
            const progressContainer = document.getElementById('progress-container');
            
            resultDiv.style.display = 'none';
            progressContainer.style.display = 'none';
            resultDiv.textContent = '';
            testResults = [];
            updateOverallStatus('warning', '🟡 Prêt pour les tests');
        }

        // Message de bienvenue
        console.log('🧪 Tests automatiques d\'intégration chargés !');
        console.log('🚀 Cliquez sur "Lancer Tests Complets" pour commencer.');
    </script>
</body>
</html>
