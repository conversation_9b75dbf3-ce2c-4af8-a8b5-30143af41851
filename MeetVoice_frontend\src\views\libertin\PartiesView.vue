<template>
  <main class="parties-container">
    <!-- Header -->
    <header class="parties-header">
      <section class="header-content">
        <h1>Soirées & Événements</h1>
        <p class="subtitle">Découvrez et organisez des soirées inoubliables</p>
      </section>
      
      <nav class="parties-actions" aria-label="Actions soirées">
        <button 
          @click="$router.push('/community')"
          class="btn-community"
        >
          <span aria-hidden="true">👥</span>
          Communauté
        </button>
        
        <button 
          @click="showCreateParty = true"
          class="btn-create-party"
        >
          <span aria-hidden="true">🎉</span>
          Organiser une soirée
        </button>
      </nav>
    </header>

    <!-- Filtres et recherche -->
    <section class="filters-section" aria-label="Filtres de recherche">
      <div class="filters-container">
        <div class="search-group">
          <input 
            v-model="searchQuery"
            type="text"
            placeholder="Rechercher une soirée..."
            class="search-input"
            @input="applyFilters"
          >
        </div>
        
        <div class="filter-group">
          <label for="date-filter">Date :</label>
          <select 
            id="date-filter"
            v-model="dateFilter"
            @change="applyFilters"
            class="filter-select"
          >
            <option value="">Toutes les dates</option>
            <option value="today">Aujourd'hui</option>
            <option value="tomorrow">Demain</option>
            <option value="weekend">Ce week-end</option>
            <option value="week">Cette semaine</option>
            <option value="month">Ce mois</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="type-filter">Type :</label>
          <select 
            id="type-filter"
            v-model="typeFilter"
            @change="applyFilters"
            class="filter-select"
          >
            <option value="">Tous les types</option>
            <option value="private">Soirée privée</option>
            <option value="club">Club libertin</option>
            <option value="outdoor">Extérieur</option>
            <option value="themed">Thématique</option>
            <option value="couples">Couples uniquement</option>
          </select>
        </div>
        
        <div class="filter-group">
          <label for="location-filter">Lieu :</label>
          <input 
            id="location-filter"
            v-model="locationFilter"
            type="text"
            placeholder="Ville, région..."
            class="filter-input"
            @input="applyFilters"
          >
        </div>
        
        <div class="filter-group">
          <label for="participants-filter">Participants :</label>
          <select 
            id="participants-filter"
            v-model="participantsFilter"
            @change="applyFilters"
            class="filter-select"
          >
            <option value="">Peu importe</option>
            <option value="small">Intime (2-10)</option>
            <option value="medium">Moyenne (10-30)</option>
            <option value="large">Grande (30+)</option>
          </select>
        </div>
        
        <button 
          @click="resetFilters"
          class="btn-reset-filters"
        >
          Réinitialiser
        </button>
      </div>
    </section>

    <!-- Navigation des onglets -->
    <section class="tabs-navigation">
      <div class="tabs-container">
        <button 
          @click="activeTab = 'upcoming'"
          :class="['tab-button', { active: activeTab === 'upcoming' }]"
        >
          <span aria-hidden="true">🔥</span>
          À venir ({{ upcomingCount }})
        </button>
        
        <button 
          @click="activeTab = 'my-parties'"
          :class="['tab-button', { active: activeTab === 'my-parties' }]"
        >
          <span aria-hidden="true">🎭</span>
          Mes soirées ({{ myPartiesCount }})
        </button>
        
        <button 
          @click="activeTab = 'favorites'"
          :class="['tab-button', { active: activeTab === 'favorites' }]"
        >
          <span aria-hidden="true">⭐</span>
          Favoris ({{ favoritesCount }})
        </button>
        
        <button 
          @click="activeTab = 'history'"
          :class="['tab-button', { active: activeTab === 'history' }]"
        >
          <span aria-hidden="true">📚</span>
          Historique
        </button>
      </div>
      
      <div class="sort-controls">
        <label for="sort-select">Trier par :</label>
        <select 
          id="sort-select"
          v-model="sortBy"
          @change="sortParties"
          class="sort-select"
        >
          <option value="date">Date</option>
          <option value="popularity">Popularité</option>
          <option value="distance">Distance</option>
          <option value="newest">Plus récentes</option>
        </select>
      </div>
    </section>

    <!-- Liste des soirées -->
    <section class="parties-content" aria-label="Liste des soirées">
      <div v-if="loading" class="loading-state">
        <div class="loading-spinner"></div>
        <p>Chargement des soirées...</p>
      </div>
      
      <div v-else-if="filteredParties.length === 0" class="empty-state">
        <div class="empty-icon">🎉</div>
        <h2>{{ getEmptyStateTitle() }}</h2>
        <p>{{ getEmptyStateMessage() }}</p>
        <button 
          v-if="activeTab === 'upcoming'"
          @click="showCreateParty = true"
          class="btn-create-first"
        >
          Organiser la première soirée
        </button>
      </div>
      
      <div v-else class="parties-grid">
        <article 
          v-for="party in paginatedParties" 
          :key="party.id"
          class="party-card"
          @click="viewParty(party.id)"
          role="button"
          tabindex="0"
          @keydown.enter="viewParty(party.id)"
          @keydown.space.prevent="viewParty(party.id)"
        >
          <div class="party-image-container">
            <img 
              :src="party.image || '/default-party.jpg'"
              :alt="`Image de ${party.title}`"
              class="party-image"
              loading="lazy"
            >
            
            <!-- Badges et indicateurs -->
            <div class="party-badges">
              <span v-if="party.isPrivate" class="badge private">🔒 Privée</span>
              <span v-if="party.isVerified" class="badge verified">✓ Vérifiée</span>
              <span v-if="party.isHot" class="badge hot">🔥 Populaire</span>
              <span v-if="party.isNew" class="badge new">✨ Nouveau</span>
            </div>
            
            <!-- Bouton favori -->
            <button 
              @click.stop="toggleFavorite(party.id)"
              :class="['btn-favorite', { active: party.isFavorite }]"
              :aria-label="party.isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'"
            >
              {{ party.isFavorite ? '⭐' : '☆' }}
            </button>
          </div>
          
          <div class="party-content">
            <header class="party-header">
              <div class="party-date-time">
                <time class="party-date">{{ formatPartyDate(party.date) }}</time>
                <span class="party-time">{{ formatPartyTime(party.time) }}</span>
              </div>
              <span :class="['party-type', party.type]">
                {{ getPartyTypeLabel(party.type) }}
              </span>
            </header>
            
            <h3 class="party-title">{{ party.title }}</h3>
            
            <div class="party-location">
              <span aria-hidden="true">📍</span>
              {{ party.location }}
              <span v-if="party.distance" class="distance">
                ({{ party.distance }} km)
              </span>
            </div>
            
            <p class="party-description">{{ party.description }}</p>
            
            <div class="party-details">
              <div class="participants-info">
                <span class="participants-count">
                  {{ party.participantsCount }}/{{ party.maxParticipants }} participants
                </span>
                <div class="participants-avatars">
                  <img 
                    v-for="participant in party.participants?.slice(0, 3)" 
                    :key="participant.id"
                    :src="participant.avatar || '/default-avatar.jpg'"
                    :alt="`Avatar de ${participant.username}`"
                    class="participant-avatar"
                  >
                  <span 
                    v-if="party.participants?.length > 3"
                    class="more-participants"
                  >
                    +{{ party.participants.length - 3 }}
                  </span>
                </div>
              </div>
              
              <div class="party-price" v-if="party.price">
                <span class="price-amount">{{ party.price }}€</span>
                <span class="price-label">par personne</span>
              </div>
            </div>
            
            <div class="party-tags" v-if="party.tags && party.tags.length > 0">
              <span 
                v-for="tag in party.tags.slice(0, 3)" 
                :key="tag"
                class="party-tag"
              >
                #{{ tag }}
              </span>
            </div>
          </div>
          
          <footer class="party-footer">
            <div class="party-organizer">
              <img 
                :src="party.organizer.avatar || '/default-avatar.jpg'"
                :alt="`Avatar de ${party.organizer.username}`"
                class="organizer-avatar"
              >
              <div class="organizer-info">
                <span class="organizer-name">{{ party.organizer.username }}</span>
                <span class="organizer-rating">⭐ {{ party.organizer.rating || 'N/A' }}</span>
              </div>
            </div>
            
            <div class="party-actions">
              <button 
                v-if="!isParticipating(party.id) && !isOrganizer(party)"
                @click.stop="joinParty(party.id)"
                :class="['btn-join', { disabled: isPartyFull(party) }]"
                :disabled="isPartyFull(party)"
              >
                {{ isPartyFull(party) ? 'Complet' : 'Participer' }}
              </button>
              
              <button 
                v-else-if="isParticipating(party.id)"
                @click.stop="leaveParty(party.id)"
                class="btn-leave"
              >
                Se désinscrire
              </button>
              
              <button 
                v-else-if="isOrganizer(party)"
                @click.stop="editParty(party.id)"
                class="btn-edit"
              >
                Modifier
              </button>
              
              <button 
                @click.stop="contactOrganizer(party.organizer.id)"
                class="btn-contact"
              >
                Contacter
              </button>
            </div>
          </footer>
        </article>
      </div>
    </section>

    <!-- Pagination -->
    <nav class="pagination" v-if="totalPages > 1" aria-label="Navigation pagination">
      <button 
        @click="changePage(currentPage - 1)"
        :disabled="currentPage === 1"
        class="btn-page"
      >
        Précédent
      </button>
      
      <span class="page-info">
        Page {{ currentPage }} sur {{ totalPages }}
      </span>
      
      <button 
        @click="changePage(currentPage + 1)"
        :disabled="currentPage === totalPages"
        class="btn-page"
      >
        Suivant
      </button>
    </nav>

    <!-- Modal de création de soirée -->
    <div v-if="showCreateParty" class="modal-overlay" @click="showCreateParty = false">
      <div class="modal-content" @click.stop>
        <header class="modal-header">
          <h2>Organiser une nouvelle soirée</h2>
          <button @click="showCreateParty = false" class="btn-close-modal">×</button>
        </header>
        
        <form @submit.prevent="createParty" class="party-form">
          <div class="form-row">
            <div class="form-group">
              <label for="party-title" class="required">Titre :</label>
              <input
                id="party-title"
                v-model="newParty.title"
                type="text"
                required
                placeholder="Soirée libertine entre amis..."
                class="form-input"
              >
            </div>
            
            <div class="form-group">
              <label for="party-type" class="required">Type :</label>
              <select
                id="party-type"
                v-model="newParty.type"
                required
                class="form-select"
              >
                <option value="">Choisir un type</option>
                <option value="private">Soirée privée</option>
                <option value="club">Club libertin</option>
                <option value="outdoor">Extérieur</option>
                <option value="themed">Thématique</option>
                <option value="couples">Couples uniquement</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label for="party-description" class="required">Description :</label>
            <textarea
              id="party-description"
              v-model="newParty.description"
              required
              rows="3"
              placeholder="Décrivez l'ambiance, les règles, ce qui est prévu..."
              class="form-textarea"
            ></textarea>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="party-date" class="required">Date :</label>
              <input
                id="party-date"
                v-model="newParty.date"
                type="date"
                required
                :min="minDate"
                class="form-input"
              >
            </div>
            
            <div class="form-group">
              <label for="party-time" class="required">Heure :</label>
              <input
                id="party-time"
                v-model="newParty.time"
                type="time"
                required
                class="form-input"
              >
            </div>
          </div>
          
          <div class="form-group">
            <label for="party-location" class="required">Lieu :</label>
            <input
              id="party-location"
              v-model="newParty.location"
              type="text"
              required
              placeholder="Adresse ou lieu de rendez-vous"
              class="form-input"
            >
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label for="max-participants" class="required">Participants max :</label>
              <input
                id="max-participants"
                v-model.number="newParty.maxParticipants"
                type="number"
                required
                min="2"
                max="100"
                class="form-input"
              >
            </div>
            
            <div class="form-group">
              <label for="party-price">Prix (€) :</label>
              <input
                id="party-price"
                v-model.number="newParty.price"
                type="number"
                min="0"
                step="5"
                placeholder="0"
                class="form-input"
              >
            </div>
          </div>
          
          <div class="form-group">
            <label for="party-tags">Tags (séparés par des virgules) :</label>
            <input
              id="party-tags"
              v-model="newParty.tags"
              type="text"
              placeholder="couple, débutant, expérimenté..."
              class="form-input"
            >
          </div>
          
          <div class="form-group">
            <label class="checkbox-label">
              <input
                v-model="newParty.isPrivate"
                type="checkbox"
                class="form-checkbox"
              >
              <span class="checkbox-text">Soirée privée (sur invitation)</span>
            </label>
          </div>
          
          <div class="form-actions">
            <button type="button" @click="showCreateParty = false" class="btn-cancel">
              Annuler
            </button>
            <button type="submit" class="btn-create" :disabled="!isFormValid">
              Créer la soirée
            </button>
          </div>
        </form>
      </div>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'PartiesView',

  data() {
    return {
      loading: false,
      activeTab: 'upcoming',
      searchQuery: '',
      dateFilter: '',
      typeFilter: '',
      locationFilter: '',
      participantsFilter: '',
      sortBy: 'date',
      currentPage: 1,
      partiesPerPage: 12,
      showCreateParty: false,

      newParty: {
        title: '',
        type: '',
        description: '',
        date: '',
        time: '',
        location: '',
        maxParticipants: 10,
        price: null,
        tags: '',
        isPrivate: false
      }
    }
  },

  computed: {
    ...mapState(['parties', 'user']),

    upcomingCount() {
      return this.parties?.filter(p => new Date(p.date) > new Date()).length || 0;
    },

    myPartiesCount() {
      return this.parties?.filter(p =>
        p.organizer.id === this.user?.id ||
        p.participants?.some(participant => participant.id === this.user?.id)
      ).length || 0;
    },

    favoritesCount() {
      return this.parties?.filter(p => p.isFavorite).length || 0;
    },

    filteredParties() {
      let filtered = [...(this.parties || [])];

      // Filtrage par onglet
      switch (this.activeTab) {
        case 'upcoming':
          filtered = filtered.filter(party => new Date(party.date) > new Date());
          break;
        case 'my-parties':
          filtered = filtered.filter(party =>
            party.organizer.id === this.user?.id ||
            party.participants?.some(participant => participant.id === this.user?.id)
          );
          break;
        case 'favorites':
          filtered = filtered.filter(party => party.isFavorite);
          break;
        case 'history':
          filtered = filtered.filter(party => new Date(party.date) <= new Date());
          break;
      }

      // Recherche textuelle
      if (this.searchQuery) {
        filtered = filtered.filter(party =>
          party.title.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          party.description.toLowerCase().includes(this.searchQuery.toLowerCase()) ||
          party.location.toLowerCase().includes(this.searchQuery.toLowerCase())
        );
      }

      // Filtre par date
      if (this.dateFilter) {
        filtered = this.filterByDateRange(filtered);
      }

      // Filtre par type
      if (this.typeFilter) {
        filtered = filtered.filter(party => party.type === this.typeFilter);
      }

      // Filtre par lieu
      if (this.locationFilter) {
        filtered = filtered.filter(party =>
          party.location.toLowerCase().includes(this.locationFilter.toLowerCase())
        );
      }

      // Filtre par nombre de participants
      if (this.participantsFilter) {
        filtered = filtered.filter(party => {
          const count = party.participantsCount || 0;
          switch (this.participantsFilter) {
            case 'small': return count <= 10;
            case 'medium': return count > 10 && count <= 30;
            case 'large': return count > 30;
            default: return true;
          }
        });
      }

      // Tri
      return this.sortPartiesArray(filtered);
    },

    paginatedParties() {
      const start = (this.currentPage - 1) * this.partiesPerPage;
      const end = start + this.partiesPerPage;
      return this.filteredParties.slice(start, end);
    },

    totalPages() {
      return Math.ceil(this.filteredParties.length / this.partiesPerPage);
    },

    minDate() {
      return new Date().toISOString().split('T')[0];
    },

    isFormValid() {
      return this.newParty.title &&
             this.newParty.type &&
             this.newParty.description &&
             this.newParty.date &&
             this.newParty.time &&
             this.newParty.location &&
             this.newParty.maxParticipants >= 2;
    }
  },

  methods: {
    ...mapActions(['loadParties', 'createParty', 'joinParty', 'leaveParty']),

    formatPartyDate(date) {
      return new Date(date).toLocaleDateString('fr-FR', {
        weekday: 'short',
        day: 'numeric',
        month: 'short'
      });
    },

    formatPartyTime(time) {
      return new Date(`2000-01-01T${time}`).toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
      });
    },

    getPartyTypeLabel(type) {
      const labels = {
        private: '🏠 Privée',
        club: '🏛️ Club',
        outdoor: '🌳 Extérieur',
        themed: '🎭 Thématique',
        couples: '💑 Couples'
      };
      return labels[type] || type;
    },

    getEmptyStateTitle() {
      switch (this.activeTab) {
        case 'upcoming': return 'Aucune soirée à venir';
        case 'my-parties': return 'Aucune soirée';
        case 'favorites': return 'Aucun favori';
        case 'history': return 'Aucun historique';
        default: return 'Aucune soirée trouvée';
      }
    },

    getEmptyStateMessage() {
      switch (this.activeTab) {
        case 'upcoming': return 'Soyez le premier à organiser une soirée !';
        case 'my-parties': return 'Organisez ou participez à des soirées pour les voir ici.';
        case 'favorites': return 'Ajoutez des soirées à vos favoris en cliquant sur l\'étoile.';
        case 'history': return 'Vos soirées passées apparaîtront ici.';
        default: return 'Essayez de modifier vos critères de recherche.';
      }
    },

    filterByDateRange(parties) {
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      return parties.filter(party => {
        const partyDate = new Date(party.date);

        switch (this.dateFilter) {
          case 'today':
            return partyDate.toDateString() === today.toDateString();
          case 'tomorrow':
            const tomorrow = new Date(today);
            tomorrow.setDate(tomorrow.getDate() + 1);
            return partyDate.toDateString() === tomorrow.toDateString();
          case 'weekend':
            const friday = new Date(today);
            friday.setDate(today.getDate() + (5 - today.getDay()));
            const sunday = new Date(friday);
            sunday.setDate(friday.getDate() + 2);
            return partyDate >= friday && partyDate <= sunday;
          case 'week':
            const weekEnd = new Date(today);
            weekEnd.setDate(weekEnd.getDate() + 7);
            return partyDate >= today && partyDate <= weekEnd;
          case 'month':
            const monthEnd = new Date(today);
            monthEnd.setMonth(monthEnd.getMonth() + 1);
            return partyDate >= today && partyDate <= monthEnd;
          default:
            return true;
        }
      });
    },

    sortPartiesArray(parties) {
      return parties.sort((a, b) => {
        switch (this.sortBy) {
          case 'date':
            return new Date(a.date) - new Date(b.date);
          case 'popularity':
            return (b.participantsCount || 0) - (a.participantsCount || 0);
          case 'distance':
            return (a.distance || 999) - (b.distance || 999);
          case 'newest':
            return new Date(b.createdAt || 0) - new Date(a.createdAt || 0);
          default:
            return 0;
        }
      });
    },

    applyFilters() {
      this.currentPage = 1;
    },

    resetFilters() {
      this.searchQuery = '';
      this.dateFilter = '';
      this.typeFilter = '';
      this.locationFilter = '';
      this.participantsFilter = '';
      this.currentPage = 1;
    },

    sortParties() {
      this.currentPage = 1;
    },

    changePage(page) {
      if (page >= 1 && page <= this.totalPages) {
        this.currentPage = page;
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    },

    viewParty(partyId) {
      this.$router.push(`/parties/${partyId}`);
    },

    isParticipating(partyId) {
      const party = this.parties?.find(p => p.id === partyId);
      return party?.participants?.some(p => p.id === this.user?.id);
    },

    isOrganizer(party) {
      return party.organizer.id === this.user?.id;
    },

    isPartyFull(party) {
      return (party.participantsCount || 0) >= party.maxParticipants;
    },

    async toggleFavorite(partyId) {
      try {
        // Implémenter la logique de favori
        console.log('Toggle favorite party:', partyId);
      } catch (error) {
        console.error('Erreur lors de la modification des favoris:', error);
      }
    },

    async joinParty(partyId) {
      try {
        await this.joinParty(partyId);

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Inscription confirmée !',
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        console.error('Erreur lors de l\'inscription:', error);
      }
    },

    async leaveParty(partyId) {
      if (confirm('Êtes-vous sûr de vouloir vous désinscrire ?')) {
        try {
          await this.leaveParty(partyId);

          this.$store.commit('addNotification', {
            type: 'info',
            message: 'Désinscription confirmée',
            timestamp: new Date().toISOString()
          });
        } catch (error) {
          console.error('Erreur lors de la désinscription:', error);
        }
      }
    },

    editParty(partyId) {
      this.$router.push(`/parties/${partyId}/edit`);
    },

    contactOrganizer(organizerId) {
      this.$router.push(`/messages/${organizerId}`);
    },

    async createParty() {
      if (!this.isFormValid) return;

      try {
        const partyData = {
          ...this.newParty,
          tags: this.newParty.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
          organizer: this.user,
          createdAt: new Date().toISOString(),
          participantsCount: 1,
          participants: [this.user]
        };

        await this.createParty(partyData);

        // Réinitialiser le formulaire
        this.newParty = {
          title: '',
          type: '',
          description: '',
          date: '',
          time: '',
          location: '',
          maxParticipants: 10,
          price: null,
          tags: '',
          isPrivate: false
        };

        this.showCreateParty = false;

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Soirée créée avec succès !',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors de la création de la soirée:', error);
        this.$store.commit('addNotification', {
          type: 'error',
          message: 'Erreur lors de la création',
          timestamp: new Date().toISOString()
        });
      }
    }
  },

  async mounted() {
    this.loading = true;
    try {
      await this.loadParties();
    } catch (error) {
      console.error('Erreur lors du chargement des soirées:', error);
    } finally {
      this.loading = false;
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --sensual-red: #DC143C;
  --passion-orange: #FF6347;
  --desire-purple: #9932CC;
  --intimate-pink: #FF1493;
  --party-gold: #FFD700;
}

.parties-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--desire-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.parties-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--intimate-pink);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.parties-actions {
  display: flex;
  gap: 12px;
}

.btn-community,
.btn-create-party {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--sensual-red);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-community:hover,
.btn-create-party:hover {
  background: var(--passion-orange);
  transform: translateY(-2px);
}

.filters-section {
  margin-bottom: 20px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.filters-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
}

.search-group,
.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-group label {
  font-size: 0.9rem;
  color: var(--slogan-gray);
  font-weight: 500;
}

.search-input,
.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  min-width: 120px;
}

.search-input {
  min-width: 200px;
}

.search-input:focus,
.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: var(--intimate-pink);
  background: rgba(255, 255, 255, 0.15);
}

.btn-reset-filters {
  padding: 8px 16px;
  background: transparent;
  color: var(--intimate-pink);
  border: 1px solid var(--intimate-pink);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-reset-filters:hover {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.tabs-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.tabs-container {
  display: flex;
  gap: 8px;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 10px 16px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.tab-button.active {
  background: var(--intimate-pink);
  color: var(--text-white);
  border-color: var(--intimate-pink);
}

.tab-button:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.sort-controls label {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

.sort-select {
  padding: 6px 10px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.parties-content {
  margin-bottom: 40px;
}

.loading-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.1);
  border-left: 4px solid var(--intimate-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: var(--slogan-gray);
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 20px;
}

.empty-state h2 {
  color: var(--intimate-pink);
  margin-bottom: 16px;
  font-size: 1.5rem;
}

.btn-create-first {
  margin-top: 20px;
  padding: 12px 24px;
  background: var(--intimate-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-create-first:hover {
  background: var(--sensual-red);
}

.parties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.party-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.party-card:hover {
  transform: translateY(-8px);
  background: rgba(255, 255, 255, 0.15);
  border-color: var(--intimate-pink);
  box-shadow: 0 20px 40px rgba(255, 20, 147, 0.2);
}

.party-image-container {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.party-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.party-card:hover .party-image {
  transform: scale(1.05);
}

.party-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

.badge.private {
  background: rgba(220, 20, 60, 0.9);
  color: var(--text-white);
}

.badge.verified {
  background: rgba(0, 207, 255, 0.9);
  color: var(--text-white);
}

.badge.hot {
  background: rgba(255, 99, 71, 0.9);
  color: var(--text-white);
}

.badge.new {
  background: rgba(255, 215, 0, 0.9);
  color: var(--header-bg);
}

.btn-favorite {
  position: absolute;
  top: 12px;
  right: 12px;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.5);
  color: var(--text-white);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.btn-favorite:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: scale(1.1);
}

.btn-favorite.active {
  color: var(--party-gold);
}

.party-content {
  padding: 20px;
}

.party-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.party-date-time {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.party-date {
  color: var(--intimate-pink);
  font-weight: 600;
  font-size: 0.9rem;
}

.party-time {
  color: var(--slogan-gray);
  font-size: 0.8rem;
}

.party-type {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 600;
}

.party-type.private { background: rgba(220, 20, 60, 0.2); color: var(--sensual-red); }
.party-type.club { background: rgba(153, 50, 204, 0.2); color: var(--desire-purple); }
.party-type.outdoor { background: rgba(34, 197, 94, 0.2); color: #22c55e; }
.party-type.themed { background: rgba(255, 20, 147, 0.2); color: var(--intimate-pink); }
.party-type.couples { background: rgba(255, 99, 71, 0.2); color: var(--passion-orange); }

.party-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--intimate-pink);
  margin: 0 0 12px 0;
  line-height: 1.3;
}

.party-location {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--slogan-gray);
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.distance {
  color: var(--icon-color);
  font-size: 0.8rem;
}

.party-description {
  color: var(--slogan-gray);
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.party-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.participants-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.participants-count {
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.participants-avatars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.participant-avatar {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  border: 2px solid var(--intimate-pink);
  object-fit: cover;
}

.more-participants {
  font-size: 0.7rem;
  color: var(--slogan-gray);
  margin-left: 4px;
}

.party-price {
  text-align: right;
}

.price-amount {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--party-gold);
}

.price-label {
  display: block;
  font-size: 0.7rem;
  color: var(--slogan-gray);
}

.party-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 16px;
}

.party-tag {
  padding: 4px 8px;
  background: rgba(255, 20, 147, 0.2);
  color: var(--intimate-pink);
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: 500;
}

.party-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.party-organizer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.organizer-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--intimate-pink);
}

.organizer-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.organizer-name {
  color: var(--intimate-pink);
  font-weight: 600;
  font-size: 0.9rem;
}

.organizer-rating {
  color: var(--slogan-gray);
  font-size: 0.7rem;
}

.party-actions {
  display: flex;
  gap: 8px;
}

.btn-join,
.btn-leave,
.btn-edit,
.btn-contact {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.8rem;
  font-weight: 500;
}

.btn-join {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-join.disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.btn-join:hover:not(.disabled) {
  background: var(--sensual-red);
}

.btn-leave {
  background: transparent;
  color: #ff6b6b;
  border: 1px solid #ff6b6b;
}

.btn-leave:hover {
  background: #ff6b6b;
  color: var(--text-white);
}

.btn-edit {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-edit:hover {
  background: var(--sweater-purple);
}

.btn-contact {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-contact:hover {
  background: var(--accent-purple);
}

.btn-join:hover,
.btn-edit:hover,
.btn-contact:hover {
  transform: translateY(-1px);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 30px;
}

.btn-page {
  padding: 10px 20px;
  background: var(--intimate-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-page:hover:not(:disabled) {
  background: var(--sensual-red);
}

.btn-page:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.page-info {
  color: var(--slogan-gray);
  font-size: 0.9rem;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--header-bg);
  border-radius: 12px;
  padding: 24px;
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h2 {
  color: var(--intimate-pink);
  font-size: 1.3rem;
  font-weight: 600;
}

.btn-close-modal {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.party-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  color: var(--slogan-gray);
  font-weight: 500;
  font-size: 0.9rem;
}

.required::after {
  content: ' *';
  color: var(--sensual-red);
}

.form-input,
.form-select,
.form-textarea {
  padding: 10px 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 1rem;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--intimate-pink);
  background: rgba(255, 255, 255, 0.15);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  margin-bottom: 0;
}

.form-checkbox {
  width: auto;
  margin: 0;
}

.checkbox-text {
  color: var(--slogan-gray);
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.btn-cancel,
.btn-create {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-cancel {
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-create {
  background: var(--intimate-pink);
  color: var(--text-white);
}

.btn-create:hover:not(:disabled) {
  background: var(--sensual-red);
}

.btn-create:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 1024px) {
  .parties-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }

  .tabs-navigation {
    flex-direction: column;
    gap: 16px;
  }

  .tabs-container {
    flex-wrap: wrap;
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .parties-container {
    padding: 16px;
  }

  .parties-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .filters-container {
    flex-direction: column;
    align-items: stretch;
  }

  .parties-grid {
    grid-template-columns: 1fr;
  }

  .tab-button {
    flex: 1;
    justify-content: center;
  }

  .party-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .party-actions {
    justify-content: center;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
  }
}
</style>
