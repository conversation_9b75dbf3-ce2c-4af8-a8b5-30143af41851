<template>
  <div class="video-call-interface" :class="{ 'in-call': isInCall }">
    <!-- Interface d'appel entrant -->
    <div v-if="hasIncomingCall && !isInCall" class="incoming-call-modal">
      <div class="incoming-call-content">
        <div class="caller-info">
          <div class="caller-avatar">
            <img :src="incomingCall.callerAvatar || '/images/default-avatar.png'" :alt="incomingCall.callerName" />
          </div>
          <h3>{{ incomingCall.callerName || 'Utilisateur inconnu' }}</h3>
          <p>Appel {{ getCallTypeText(incomingCall.offerType) }} entrant...</p>
        </div>
        
        <div class="call-actions">
          <button @click="declineCall" class="btn-decline">
            <span class="icon">📞</span>
            Refuser
          </button>
          
          <button @click="acceptCall('voice')" class="btn-accept-voice" v-if="incomingCall.offerType === 'Voice'">
            <span class="icon">🎤</span>
            Répondre
          </button>
          
          <button @click="acceptCall('video')" class="btn-accept-video" v-if="incomingCall.offerType === 'Video'">
            <span class="icon">📹</span>
            Répondre
          </button>
        </div>
      </div>
    </div>

    <!-- Interface d'appel en cours -->
    <div v-if="isInCall" class="active-call-interface">
      <!-- Vidéos -->
      <div class="video-container">
        <!-- Vidéo distante (principale) -->
        <div class="remote-video-container">
          <video
            v-for="[userId, stream] in remoteStreamsArray"
            :key="userId"
            :ref="`remoteVideo-${userId}`"
            class="remote-video"
            autoplay
            playsinline
          ></video>
          
          <!-- Placeholder si pas de vidéo distante -->
          <div v-if="remoteStreamsArray.length === 0" class="video-placeholder">
            <div class="placeholder-avatar">
              <span class="icon">👤</span>
            </div>
            <p>En attente de la connexion...</p>
          </div>
        </div>
        
        <!-- Vidéo locale (petite fenêtre) -->
        <div class="local-video-container" v-if="localStream">
          <video
            ref="localVideo"
            class="local-video"
            autoplay
            playsinline
            muted
          ></video>
          
          <div class="local-video-controls">
            <button @click="switchCamera" class="btn-switch-camera" v-if="isCameraEnabled">
              <span class="icon">🔄</span>
            </button>
          </div>
        </div>
      </div>

      <!-- Contrôles d'appel -->
      <div class="call-controls">
        <div class="controls-row">
          <!-- Microphone -->
          <button
            @click="toggleMicrophone"
            class="control-btn"
            :class="{ 'disabled': !isMicrophoneEnabled }"
          >
            <span class="icon">{{ isMicrophoneEnabled ? '🎤' : '🔇' }}</span>
            <span class="label">Micro</span>
          </button>

          <!-- Caméra -->
          <button
            @click="toggleCamera"
            class="control-btn"
            :class="{ 'disabled': !isCameraEnabled }"
          >
            <span class="icon">{{ isCameraEnabled ? '📹' : '📷' }}</span>
            <span class="label">Caméra</span>
          </button>

          <!-- Partage d'écran -->
          <button
            @click="toggleScreenShare"
            class="control-btn"
            :class="{ 'active': isScreenSharing }"
          >
            <span class="icon">🖥️</span>
            <span class="label">Écran</span>
          </button>

          <!-- Raccrocher -->
          <button @click="endCall" class="control-btn btn-end-call">
            <span class="icon">📞</span>
            <span class="label">Raccrocher</span>
          </button>
        </div>

        <!-- Informations d'appel -->
        <div class="call-info">
          <div class="call-duration">{{ formattedCallDuration }}</div>
          <div class="call-quality">
            <span class="quality-indicator" :class="callQuality"></span>
            {{ getQualityText(callQuality) }}
          </div>
          <div class="participants-count" v-if="participantsCount > 1">
            {{ participantsCount }} participants
          </div>
        </div>
      </div>
    </div>

    <!-- Bouton d'appel flottant (quand pas en appel) -->
    <div v-if="!isInCall && !hasIncomingCall" class="floating-call-button">
      <button @click="showCallOptions = true" class="btn-start-call">
        <span class="icon">📞</span>
      </button>
    </div>

    <!-- Modal d'options d'appel -->
    <div v-if="showCallOptions" class="call-options-modal" @click="showCallOptions = false">
      <div class="call-options-content" @click.stop>
        <h3>Démarrer un appel</h3>
        
        <div class="call-type-options">
          <button @click="startCall('voice')" class="call-type-btn">
            <span class="icon">🎤</span>
            <span>Appel vocal</span>
          </button>
          
          <button @click="startCall('video')" class="call-type-btn">
            <span class="icon">📹</span>
            <span>Appel vidéo</span>
          </button>
        </div>
        
        <button @click="showCallOptions = false" class="btn-cancel">Annuler</button>
      </div>
    </div>

    <!-- Statistiques d'appel (mode debug) -->
    <div v-if="showStats && isInCall" class="call-stats">
      <h4>Statistiques d'appel</h4>
      <div v-for="(stats, userId) in callStats" :key="userId" class="user-stats">
        <h5>{{ userId }}</h5>
        <div class="stats-grid">
          <div class="stat">
            <label>Audio Bitrate:</label>
            <span>{{ Math.round(stats.audio?.bitrate || 0) }} kbps</span>
          </div>
          <div class="stat">
            <label>Video Bitrate:</label>
            <span>{{ Math.round(stats.video?.bitrate || 0) }} kbps</span>
          </div>
          <div class="stat">
            <label>Packets Lost:</label>
            <span>{{ (stats.audio?.packetsLost || 0) + (stats.video?.packetsLost || 0) }}</span>
          </div>
          <div class="stat">
            <label>Frame Rate:</label>
            <span>{{ Math.round(stats.video?.frameRate || 0) }} fps</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'VideoCallInterface',
  
  props: {
    targetUserId: {
      type: String,
      default: null
    },
    
    showStats: {
      type: Boolean,
      default: false
    }
  },
  
  data() {
    return {
      showCallOptions: false,
      callStartTime: null,
      callDurationInterval: null,
      callDuration: 0
    }
  },
  
  computed: {
    ...mapState('websocket', [
      'isInCall',
      'localStream',
      'callStats',
      'isMicrophoneEnabled',
      'isCameraEnabled',
      'isScreenSharing',
      'participantsCount'
    ]),
    
    ...mapGetters('websocket', [
      'hasIncomingCall',
      'incomingCall',
      'remoteStreamsArray',
      'callQuality'
    ]),
    
    formattedCallDuration() {
      const minutes = Math.floor(this.callDuration / 60)
      const seconds = this.callDuration % 60
      return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
  },
  
  watch: {
    isInCall(newValue) {
      if (newValue) {
        this.startCallTimer()
      } else {
        this.stopCallTimer()
      }
    },
    
    localStream(newStream) {
      if (newStream && this.$refs.localVideo) {
        this.$refs.localVideo.srcObject = newStream
      }
    },
    
    remoteStreamsArray: {
      handler(newStreams) {
        this.$nextTick(() => {
          newStreams.forEach(([userId, stream]) => {
            const videoElement = this.$refs[`remoteVideo-${userId}`]
            if (videoElement && videoElement[0]) {
              videoElement[0].srcObject = stream
            }
          })
        })
      },
      deep: true
    }
  },
  
  mounted() {
    // Initialiser les streams vidéo si déjà en appel
    if (this.localStream && this.$refs.localVideo) {
      this.$refs.localVideo.srcObject = this.localStream
    }
  },
  
  beforeUnmount() {
    this.stopCallTimer()
  },
  
  methods: {
    ...mapActions('websocket', [
      'startCall',
      'answerCall',
      'endCall',
      'toggleMicrophone',
      'toggleCamera',
      'switchCamera',
      'shareScreen',
      'getCallStats'
    ]),
    
    async startCall(callType) {
      if (!this.targetUserId) {
        console.error('Aucun utilisateur cible spécifié')
        return
      }
      
      try {
        await this.startCall({
          targetUserId: this.targetUserId,
          callType
        })
        
        this.showCallOptions = false
      } catch (error) {
        console.error('Erreur lors du démarrage de l\'appel:', error)
        alert('Impossible de démarrer l\'appel')
      }
    },
    
    async acceptCall(callType) {
      try {
        await this.answerCall({
          accept: true,
          callType
        })
      } catch (error) {
        console.error('Erreur lors de l\'acceptation de l\'appel:', error)
        alert('Impossible d\'accepter l\'appel')
      }
    },
    
    async declineCall() {
      try {
        await this.answerCall({
          accept: false
        })
      } catch (error) {
        console.error('Erreur lors du refus de l\'appel:', error)
      }
    },
    
    async toggleScreenShare() {
      try {
        if (this.isScreenSharing) {
          // Arrêter le partage d'écran
          // TODO: Implémenter l'arrêt du partage d'écran
        } else {
          await this.shareScreen()
        }
      } catch (error) {
        console.error('Erreur lors du partage d\'écran:', error)
        alert('Impossible de partager l\'écran')
      }
    },
    
    startCallTimer() {
      this.callStartTime = Date.now()
      this.callDuration = 0
      
      this.callDurationInterval = setInterval(() => {
        this.callDuration = Math.floor((Date.now() - this.callStartTime) / 1000)
      }, 1000)
    },
    
    stopCallTimer() {
      if (this.callDurationInterval) {
        clearInterval(this.callDurationInterval)
        this.callDurationInterval = null
      }
      this.callDuration = 0
    },
    
    getCallTypeText(offerType) {
      const types = {
        'Voice': 'vocal',
        'Video': 'vidéo',
        'Screen': 'partage d\'écran'
      }
      return types[offerType] || 'inconnu'
    },
    
    getQualityText(quality) {
      const qualities = {
        'excellent': 'Excellente',
        'good': 'Bonne',
        'fair': 'Correcte',
        'poor': 'Mauvaise',
        'unknown': 'Inconnue'
      }
      return qualities[quality] || 'Inconnue'
    },
    
    // Mettre à jour les statistiques périodiquement
    async updateStats() {
      if (this.isInCall) {
        try {
          await this.getCallStats()
        } catch (error) {
          console.error('Erreur lors de la mise à jour des statistiques:', error)
        }
      }
    }
  },
  
  created() {
    // Mettre à jour les statistiques toutes les 5 secondes
    this.statsInterval = setInterval(this.updateStats, 5000)
  },
  
  beforeUnmount() {
    if (this.statsInterval) {
      clearInterval(this.statsInterval)
    }
  }
}
</script>

<style scoped>
.video-call-interface {
  position: relative;
  width: 100%;
  height: 100vh;
  background: #000;
  color: white;
  overflow: hidden;
}

/* Interface d'appel entrant */
.incoming-call-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.incoming-call-content {
  background: white;
  color: #333;
  padding: 40px;
  border-radius: 20px;
  text-align: center;
  max-width: 400px;
  width: 90%;
}

.caller-info {
  margin-bottom: 30px;
}

.caller-avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: 0 auto 20px;
  overflow: hidden;
}

.caller-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.call-actions {
  display: flex;
  gap: 20px;
  justify-content: center;
}

.btn-decline,
.btn-accept-voice,
.btn-accept-video {
  padding: 15px 25px;
  border: none;
  border-radius: 50px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: all 0.3s ease;
}

.btn-decline {
  background: #dc3545;
  color: white;
}

.btn-accept-voice,
.btn-accept-video {
  background: #28a745;
  color: white;
}

/* Interface d'appel actif */
.active-call-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.video-container {
  flex: 1;
  position: relative;
  background: #1a1a1a;
}

.remote-video-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.remote-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.placeholder-avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: #333;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 60px;
  margin-bottom: 20px;
}

.local-video-container {
  position: absolute;
  top: 20px;
  right: 20px;
  width: 200px;
  height: 150px;
  border-radius: 10px;
  overflow: hidden;
  background: #333;
  border: 2px solid #555;
}

.local-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.local-video-controls {
  position: absolute;
  bottom: 10px;
  right: 10px;
}

.btn-switch-camera {
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  color: white;
  cursor: pointer;
  font-size: 16px;
}

/* Contrôles d'appel */
.call-controls {
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  backdrop-filter: blur(10px);
}

.controls-row {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 15px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50px;
  padding: 15px 20px;
  color: white;
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  transition: all 0.3s ease;
  min-width: 80px;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.control-btn.disabled {
  background: rgba(255, 0, 0, 0.3);
  border-color: rgba(255, 0, 0, 0.5);
}

.control-btn.active {
  background: rgba(0, 123, 255, 0.3);
  border-color: rgba(0, 123, 255, 0.5);
}

.btn-end-call {
  background: rgba(220, 53, 69, 0.3);
  border-color: rgba(220, 53, 69, 0.5);
}

.btn-end-call:hover {
  background: rgba(220, 53, 69, 0.5);
}

.control-btn .icon {
  font-size: 24px;
}

.control-btn .label {
  font-size: 12px;
  font-weight: 500;
}

.call-info {
  display: flex;
  justify-content: center;
  gap: 30px;
  font-size: 14px;
  color: #ccc;
}

.quality-indicator {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 5px;
}

.quality-indicator.excellent {
  background: #28a745;
}

.quality-indicator.good {
  background: #ffc107;
}

.quality-indicator.fair {
  background: #fd7e14;
}

.quality-indicator.poor {
  background: #dc3545;
}

.quality-indicator.unknown {
  background: #6c757d;
}

/* Bouton d'appel flottant */
.floating-call-button {
  position: fixed;
  bottom: 30px;
  right: 30px;
  z-index: 100;
}

.btn-start-call {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #28a745;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.4);
  transition: all 0.3s ease;
}

.btn-start-call:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 20px rgba(40, 167, 69, 0.6);
}

/* Modal d'options d'appel */
.call-options-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.call-options-content {
  background: white;
  color: #333;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  max-width: 300px;
  width: 90%;
}

.call-type-options {
  display: flex;
  flex-direction: column;
  gap: 15px;
  margin: 20px 0;
}

.call-type-btn {
  padding: 15px;
  border: 2px solid #007bff;
  border-radius: 10px;
  background: transparent;
  color: #007bff;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  justify-content: center;
  transition: all 0.3s ease;
}

.call-type-btn:hover {
  background: #007bff;
  color: white;
}

.btn-cancel {
  padding: 10px 20px;
  background: #6c757d;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  margin-top: 15px;
}

/* Statistiques d'appel */
.call-stats {
  position: absolute;
  top: 20px;
  left: 20px;
  background: rgba(0, 0, 0, 0.8);
  padding: 15px;
  border-radius: 10px;
  font-size: 12px;
  max-width: 300px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-top: 10px;
}

.stat {
  display: flex;
  justify-content: space-between;
}

/* Responsive */
@media (max-width: 768px) {
  .local-video-container {
    width: 120px;
    height: 90px;
    top: 10px;
    right: 10px;
  }
  
  .controls-row {
    gap: 10px;
  }
  
  .control-btn {
    min-width: 60px;
    padding: 10px 15px;
  }
  
  .control-btn .icon {
    font-size: 20px;
  }
  
  .call-info {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }
}
</style>
