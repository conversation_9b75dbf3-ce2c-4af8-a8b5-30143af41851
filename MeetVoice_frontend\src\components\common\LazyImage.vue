<template>
  <div 
    class="lazy-image-container"
    :class="{ 'loading': loading, 'error': error, 'loaded': loaded }"
    :style="{ aspectRatio: aspectRatio }"
  >
    <!-- Image de placeholder pendant le chargement -->
    <div v-if="loading" class="placeholder">
      <div class="placeholder-shimmer"></div>
      <div class="placeholder-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/>
        </svg>
      </div>
    </div>

    <!-- Image d'erreur -->
    <div v-else-if="error" class="error-state">
      <div class="error-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
        </svg>
      </div>
      <p class="error-text">Image non disponible</p>
      <button v-if="retryable" @click="retry" class="retry-button">
        Réessayer
      </button>
    </div>

    <!-- Image principale -->
    <img
      v-else
      ref="image"
      :src="currentSrc"
      :alt="alt"
      :class="{ 'fade-in': loaded }"
      @load="onLoad"
      @error="onError"
      :loading="nativeLazyLoading ? 'lazy' : undefined"
    />

    <!-- Overlay optionnel -->
    <div v-if="overlay && loaded" class="image-overlay">
      <slot name="overlay"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'LazyImage',
  
  props: {
    src: {
      type: String,
      required: true
    },
    
    alt: {
      type: String,
      default: ''
    },
    
    placeholder: {
      type: String,
      default: null
    },
    
    aspectRatio: {
      type: String,
      default: 'auto'
    },
    
    lazy: {
      type: Boolean,
      default: true
    },
    
    retryable: {
      type: Boolean,
      default: true
    },
    
    overlay: {
      type: Boolean,
      default: false
    },
    
    // Utiliser le lazy loading natif du navigateur
    nativeLazyLoading: {
      type: Boolean,
      default: true
    },
    
    // Seuil pour l'intersection observer (en pixels)
    threshold: {
      type: Number,
      default: 100
    }
  },
  
  data() {
    return {
      loading: true,
      loaded: false,
      error: false,
      currentSrc: this.placeholder || '',
      observer: null,
      retryCount: 0,
      maxRetries: 3
    }
  },
  
  mounted() {
    if (this.lazy && !this.nativeLazyLoading) {
      this.setupIntersectionObserver()
    } else {
      this.loadImage()
    }
  },
  
  beforeUnmount() {
    if (this.observer) {
      this.observer.disconnect()
    }
  },
  
  watch: {
    src(newSrc) {
      this.reset()
      if (!this.lazy || this.nativeLazyLoading) {
        this.loadImage()
      }
    }
  },
  
  methods: {
    setupIntersectionObserver() {
      if (!('IntersectionObserver' in window)) {
        // Fallback pour les navigateurs qui ne supportent pas IntersectionObserver
        this.loadImage()
        return
      }
      
      this.observer = new IntersectionObserver(
        (entries) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              this.loadImage()
              this.observer.disconnect()
            }
          })
        },
        {
          rootMargin: `${this.threshold}px`
        }
      )
      
      this.observer.observe(this.$el)
    },
    
    loadImage() {
      if (!this.src) {
        this.error = true
        this.loading = false
        return
      }
      
      this.loading = true
      this.error = false
      this.currentSrc = this.src
    },
    
    onLoad() {
      this.loading = false
      this.loaded = true
      this.error = false
      this.retryCount = 0
      this.$emit('load')
    },
    
    onError() {
      this.loading = false
      this.loaded = false
      this.error = true
      this.$emit('error')
    },
    
    retry() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        this.reset()
        
        // Attendre un peu avant de réessayer
        setTimeout(() => {
          this.loadImage()
        }, 1000 * this.retryCount)
      }
    },
    
    reset() {
      this.loading = true
      this.loaded = false
      this.error = false
      this.currentSrc = this.placeholder || ''
    }
  }
}
</script>

<style scoped>
.lazy-image-container {
  position: relative;
  overflow: hidden;
  background-color: #f5f5f5;
  border-radius: 8px;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.lazy-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image-container img.fade-in {
  opacity: 1;
}

/* Placeholder */
.placeholder {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
}

.placeholder-shimmer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent 25%, rgba(255,255,255,0.5) 50%, transparent 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.placeholder-icon {
  width: 48px;
  height: 48px;
  color: #ccc;
  z-index: 1;
}

.placeholder-icon svg {
  width: 100%;
  height: 100%;
}

/* État d'erreur */
.error-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  color: #666;
  padding: 20px;
  text-align: center;
}

.error-icon {
  width: 32px;
  height: 32px;
  color: #ff6b6b;
  margin-bottom: 8px;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.error-text {
  font-size: 14px;
  margin: 0 0 12px 0;
  color: #666;
}

.retry-button {
  padding: 6px 12px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.retry-button:hover {
  background-color: #0056b3;
}

/* Overlay */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.lazy-image-container:hover .image-overlay {
  opacity: 1;
}

/* Responsive */
@media (max-width: 768px) {
  .placeholder-icon {
    width: 32px;
    height: 32px;
  }
  
  .error-icon {
    width: 24px;
    height: 24px;
  }
  
  .error-text {
    font-size: 12px;
  }
  
  .retry-button {
    font-size: 11px;
    padding: 4px 8px;
  }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  .lazy-image-container img,
  .image-overlay,
  .retry-button {
    transition: none;
  }
  
  .placeholder-shimmer {
    animation: none;
  }
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .lazy-image-container {
    background-color: #2a2a2a;
  }
  
  .placeholder {
    background: linear-gradient(90deg, #2a2a2a 25%, #3a3a3a 50%, #2a2a2a 75%);
  }
  
  .placeholder-icon {
    color: #666;
  }
  
  .error-state {
    background-color: #2a2a2a;
    color: #ccc;
  }
  
  .error-text {
    color: #ccc;
  }
}
</style>
