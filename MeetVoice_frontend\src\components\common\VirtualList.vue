<template>
  <div 
    ref="container"
    class="virtual-list-container"
    @scroll="onScroll"
    :style="{ height: containerHeight }"
  >
    <!-- Spacer du haut pour maintenir la position de scroll -->
    <div 
      class="virtual-spacer-top"
      :style="{ height: `${offsetTop}px` }"
    ></div>
    
    <!-- Items visibles -->
    <div
      v-for="item in visibleItems"
      :key="getItemKey(item)"
      class="virtual-list-item"
      :style="{ height: `${itemHeight}px` }"
    >
      <slot :item="item" :index="item.index"></slot>
    </div>
    
    <!-- Spacer du bas pour maintenir la hauteur totale -->
    <div 
      class="virtual-spacer-bottom"
      :style="{ height: `${offsetBottom}px` }"
    ></div>
    
    <!-- Indicateur de chargement -->
    <div v-if="loading" class="virtual-loading">
      <div class="loading-spinner"></div>
      <p>Chargement...</p>
    </div>
    
    <!-- Message quand la liste est vide -->
    <div v-if="!loading && items.length === 0" class="virtual-empty">
      <slot name="empty">
        <p>Aucun élément à afficher</p>
      </slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'VirtualList',
  
  props: {
    items: {
      type: Array,
      required: true
    },
    
    itemHeight: {
      type: Number,
      default: 100
    },
    
    containerHeight: {
      type: String,
      default: '400px'
    },
    
    buffer: {
      type: Number,
      default: 5
    },
    
    keyField: {
      type: String,
      default: 'id'
    },
    
    loading: {
      type: Boolean,
      default: false
    },
    
    // Fonction pour le chargement infini
    loadMore: {
      type: Function,
      default: null
    },
    
    // Distance du bas pour déclencher le chargement
    loadThreshold: {
      type: Number,
      default: 200
    }
  },
  
  data() {
    return {
      scrollTop: 0,
      containerHeightPx: 400,
      isLoadingMore: false
    }
  },
  
  computed: {
    totalHeight() {
      return this.items.length * this.itemHeight
    },
    
    visibleCount() {
      return Math.ceil(this.containerHeightPx / this.itemHeight) + this.buffer * 2
    },
    
    startIndex() {
      const index = Math.floor(this.scrollTop / this.itemHeight) - this.buffer
      return Math.max(0, index)
    },
    
    endIndex() {
      const index = this.startIndex + this.visibleCount
      return Math.min(this.items.length, index)
    },
    
    visibleItems() {
      return this.items.slice(this.startIndex, this.endIndex).map((item, i) => ({
        ...item,
        index: this.startIndex + i
      }))
    },
    
    offsetTop() {
      return this.startIndex * this.itemHeight
    },
    
    offsetBottom() {
      return (this.items.length - this.endIndex) * this.itemHeight
    }
  },
  
  mounted() {
    this.updateContainerHeight()
    window.addEventListener('resize', this.updateContainerHeight)
    
    // Observer pour détecter les changements de taille du conteneur
    if ('ResizeObserver' in window) {
      this.resizeObserver = new ResizeObserver(this.updateContainerHeight)
      this.resizeObserver.observe(this.$refs.container)
    }
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.updateContainerHeight)
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
    }
  },
  
  watch: {
    items: {
      handler() {
        // Réinitialiser le scroll si les items changent complètement
        this.$nextTick(() => {
          if (this.$refs.container) {
            this.$refs.container.scrollTop = 0
            this.scrollTop = 0
          }
        })
      },
      deep: false
    }
  },
  
  methods: {
    onScroll(event) {
      this.scrollTop = event.target.scrollTop
      
      // Vérifier si on doit charger plus d'éléments
      if (this.loadMore && !this.isLoadingMore) {
        const { scrollTop, scrollHeight, clientHeight } = event.target
        const distanceFromBottom = scrollHeight - scrollTop - clientHeight
        
        if (distanceFromBottom < this.loadThreshold) {
          this.handleLoadMore()
        }
      }
      
      this.$emit('scroll', {
        scrollTop: this.scrollTop,
        startIndex: this.startIndex,
        endIndex: this.endIndex
      })
    },
    
    async handleLoadMore() {
      if (this.isLoadingMore || !this.loadMore) return
      
      this.isLoadingMore = true
      
      try {
        await this.loadMore()
      } catch (error) {
        console.error('Erreur lors du chargement de plus d\'éléments:', error)
        this.$emit('load-error', error)
      } finally {
        this.isLoadingMore = false
      }
    },
    
    updateContainerHeight() {
      if (this.$refs.container) {
        this.containerHeightPx = this.$refs.container.clientHeight
      }
    },
    
    getItemKey(item) {
      return item[this.keyField] || item.index
    },
    
    scrollToIndex(index) {
      if (this.$refs.container) {
        const scrollTop = index * this.itemHeight
        this.$refs.container.scrollTop = scrollTop
        this.scrollTop = scrollTop
      }
    },
    
    scrollToTop() {
      this.scrollToIndex(0)
    },
    
    scrollToBottom() {
      this.scrollToIndex(this.items.length - 1)
    }
  }
}
</script>

<style scoped>
.virtual-list-container {
  overflow-y: auto;
  overflow-x: hidden;
  position: relative;
  width: 100%;
}

.virtual-list-item {
  width: 100%;
  box-sizing: border-box;
}

.virtual-spacer-top,
.virtual-spacer-bottom {
  width: 100%;
  flex-shrink: 0;
}

.virtual-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.virtual-empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
  text-align: center;
}

.virtual-empty p {
  margin: 0;
  font-size: 16px;
}

/* Scrollbar personnalisée */
.virtual-list-container::-webkit-scrollbar {
  width: 8px;
}

.virtual-list-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-list-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.virtual-list-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Mode sombre */
@media (prefers-color-scheme: dark) {
  .virtual-loading,
  .virtual-empty {
    color: #ccc;
  }
  
  .loading-spinner {
    border-color: #444;
    border-top-color: #007bff;
  }
  
  .virtual-list-container::-webkit-scrollbar-track {
    background: #2a2a2a;
  }
  
  .virtual-list-container::-webkit-scrollbar-thumb {
    background: #555;
  }
  
  .virtual-list-container::-webkit-scrollbar-thumb:hover {
    background: #666;
  }
}

/* Accessibilité */
@media (prefers-reduced-motion: reduce) {
  .loading-spinner {
    animation: none;
  }
}
</style>
