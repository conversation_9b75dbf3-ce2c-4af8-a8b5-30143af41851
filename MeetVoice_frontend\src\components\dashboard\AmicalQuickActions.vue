<template>
  <div class="amical-quick-actions">
    <h3>Actions rapides - Amical</h3>
    <div class="actions-grid">
      <router-link to="/amical/events" class="action-card">
        <div class="action-icon">🎉</div>
        <h4>Événements</h4>
        <p>Découvrir les événements amicaux</p>
      </router-link>
      
      <router-link to="/amical/groups" class="action-card">
        <div class="action-icon">👥</div>
        <h4>Groupes</h4>
        <p>Rejoindre des groupes d'amis</p>
      </router-link>
      
      <router-link to="/amical/activities" class="action-card">
        <div class="action-icon">🎯</div>
        <h4>Activités</h4>
        <p>Proposer une activité</p>
      </router-link>
      
      <router-link to="/chat" class="action-card">
        <div class="action-icon">💬</div>
        <h4>Chat</h4>
        <p>Discuter avec des amis</p>
      </router-link>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AmicalQuickActions'
}
</script>

<style scoped>
.amical-quick-actions {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.amical-quick-actions h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.action-card {
  display: block;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.action-card:hover {
  background: #e3f2fd;
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.2);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 12px;
}

.action-card h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.action-card p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .action-card {
    padding: 16px;
  }
  
  .action-icon {
    font-size: 24px;
  }
}
</style>
