# 🎤 Guide des voix locales - MeetVoice

## 🎯 Pourquoi les voix locales ?

- ✅ **100% gratuit** - <PERSON><PERSON><PERSON> coût, aucune limite
- ✅ **Fonctionne hors ligne** - Pas besoin d'internet
- ✅ **Rapide** - Pas de latence réseau
- ✅ **Privé** - Aucune donnée envoyée sur internet
- ✅ **Qualité** - Souvent très bonne sur les systèmes modernes

## 🔧 Comment améliorer les voix sur votre système

### Windows 10/11

1. **Ouvrir les paramètres de voix :**
   - Paramètres > Heure et langue > Voix
   - Ou tapez "Voix" dans la recherche Windows

2. **Ajouter des voix françaises :**
   - Cliquez sur "Ajouter des voix"
   - Recherchez "Français"
   - Téléchargez les voix disponibles :
     - **Hortense** (Femme, qualité élevée)
     - **Claude** (Homme, qualité élevée)

3. **Configurer la vitesse :**
   - Ajustez la vitesse de la voix dans les paramètres
   - Testez avec l'aperçu

### macOS

1. **Ouvrir les préférences de voix :**
   - Préférences Système > Accessibilité > Contenu parlé
   - Ou Préférences Système > Voix système

2. **Télécharger des voix françaises :**
   - Cliquez sur "Voix système"
   - Sélectionnez "Français (France)"
   - Téléchargez les voix disponibles :
     - **Amélie** (Femme, haute qualité)
     - **Thomas** (Homme, haute qualité)

3. **Tester les voix :**
   - Sélectionnez une voix et cliquez "Lire le texte"

### Linux (Ubuntu/Debian)

1. **Installer espeak-ng :**
   ```bash
   sudo apt update
   sudo apt install espeak-ng espeak-ng-data
   ```

2. **Installer les voix françaises :**
   ```bash
   sudo apt install espeak-ng-data-fr
   ```

3. **Tester :**
   ```bash
   espeak-ng -v fr "Bonjour, ceci est un test"
   ```

## 🎛️ Optimisation dans MeetVoice

### Paramètres recommandés

1. **Vitesse :** 0.9 (légèrement plus lent = plus naturel)
2. **Tonalité :** 1.1 (légèrement plus aigu = plus agréable)
3. **Volume :** 0.8 (évite la saturation)

### Sélection des voix

1. **Privilégiez les voix locales** (icône 🏠)
2. **Testez chaque voix** avec le bouton "Test"
3. **Choisissez selon vos préférences** (homme/femme)

## 🔍 Identifier les meilleures voix

### Dans MeetVoice

1. Allez sur : http://localhost:8082/simple-voice-test
2. Regardez la liste des voix disponibles
3. Les voix avec 🏠 sont locales (meilleures)
4. Les voix avec ☁️ sont en ligne (qualité variable)

### Voix recommandées par système

#### Windows
- **Hortense** - Voix féminine française de haute qualité
- **Claude** - Voix masculine française de haute qualité

#### macOS
- **Amélie** - Voix féminine française naturelle
- **Thomas** - Voix masculine française naturelle

#### Linux
- **espeak-ng fr** - Voix française basique mais fonctionnelle

## 🎤 Test et validation

### Test rapide

1. Ouvrez : http://localhost:8082/simple-voice-test
2. Sélectionnez une voix locale (🏠)
3. Ajustez vitesse à 0.9 et tonalité à 1.1
4. Testez avec le texte par défaut
5. Comparez différentes voix

### Texte de test recommandé

```
Bonjour et bienvenue sur MeetVoice ! Cette application utilise les voix locales de votre système pour offrir une synthèse vocale de qualité, gratuite et respectueuse de votre vie privée. Comment trouvez-vous cette voix ?
```

## 🚀 Utilisation dans MeetVoice

### Pages où la synthèse vocale est utilisée

1. **Test vocal** : http://localhost:8082/simple-voice-test
2. **Configuration médias** : http://localhost:8082/media-config
3. **Interview vocal** : Dans l'inscription (étape 5)

### Intégration dans l'interview vocal

L'interview vocal utilise automatiquement :
- La meilleure voix française disponible
- Les paramètres optimisés (vitesse 0.9, tonalité 1.1)
- Les voix locales en priorité

## 💡 Conseils d'utilisation

### Pour les développeurs

- Toujours vérifier `voice.localService` pour prioriser les voix locales
- Implémenter un fallback si aucune voix française n'est disponible
- Permettre à l'utilisateur de choisir sa voix préférée

### Pour les utilisateurs

- Installez des voix de qualité sur votre système
- Testez régulièrement les nouvelles voix disponibles
- Ajustez les paramètres selon vos préférences

## 🔧 Dépannage

### Aucune voix française détectée

1. **Vérifiez les paramètres système** (voir sections Windows/macOS/Linux)
2. **Redémarrez le navigateur** après installation de nouvelles voix
3. **Testez dans un autre navigateur** (Chrome recommandé)

### Voix robotique

1. **Réduisez la vitesse** à 0.8 ou 0.9
2. **Ajustez la tonalité** entre 1.0 et 1.2
3. **Essayez une autre voix** locale

### Pas de son

1. **Vérifiez le volume** système et navigateur
2. **Testez les haut-parleurs** dans la configuration médias
3. **Autorisez l'audio** dans les paramètres du navigateur

## 🎯 Résultat attendu

Avec une configuration optimale, vous devriez obtenir :
- ✅ Une voix française naturelle et agréable
- ✅ Une synthèse fluide sans coupures
- ✅ Un rendu professionnel pour l'interview vocal
- ✅ Une expérience utilisateur de qualité

---

**Les voix locales offrent la meilleure solution gratuite pour MeetVoice ! 🎉**
