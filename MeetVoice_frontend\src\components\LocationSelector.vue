<!-- @format -->

<template>
  <div class="location-selector">
    <div class="location-input-group">
      <label for="location-search">Localisation</label>
      <div class="input-with-button">
        <input
          id="location-search"
          type="text"
          v-model="searchQuery"
          @input="onSearchInput"
          @focus="showSuggestions = true"
          placeholder="Rechercher une ville..."
          :disabled="isLoading"
        />
        <button
          type="button"
          @click="detectCurrentLocation"
          :disabled="isDetecting"
          class="detect-btn"
          title="Détecter ma position actuelle"
        >
          {{ isDetecting ? '📍' : '🎯' }}
        </button>
      </div>
      
      <!-- Suggestions de recherche -->
      <div v-if="showSuggestions && suggestions.length > 0" class="suggestions-dropdown">
        <div
          v-for="(suggestion, index) in suggestions"
          :key="index"
          @click="selectSuggestion(suggestion)"
          class="suggestion-item"
        >
          <div class="suggestion-main">{{ suggestion.address.city }}</div>
          <div class="suggestion-detail">{{ suggestion.display_name }}</div>
        </div>
      </div>
      
      <!-- Localisation actuelle -->
      <div v-if="selectedLocation" class="current-location">
        <div class="location-info">
          <span class="location-icon">📍</span>
          <div class="location-details">
            <div class="location-city">{{ selectedLocation.address.city }}</div>
            <div class="location-full">{{ selectedLocation.display_name }}</div>
          </div>
          <button type="button" @click="clearLocation" class="clear-btn">✕</button>
        </div>
        
        <!-- Validation de cohérence -->
        <div v-if="validationResult && !validationResult.isConsistent" class="validation-warning">
          ⚠️ {{ validationResult.warning }}
        </div>
      </div>
      
      <!-- Messages d'erreur -->
      <div v-if="error" class="error-message">{{ error }}</div>
      
      <!-- Indicateur de chargement -->
      <div v-if="isLoading" class="loading-indicator">
        Recherche en cours...
      </div>
    </div>
  </div>
</template>

<script>
import { geolocationService } from '@/_services/geolocation.service';

export default {
  name: 'LocationSelector',
  props: {
    modelValue: {
      type: Object,
      default: null
    },
    validateWithIP: {
      type: Boolean,
      default: false
    },
    required: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'location-selected', 'validation-result'],
  data() {
    return {
      searchQuery: '',
      suggestions: [],
      selectedLocation: null,
      showSuggestions: false,
      isLoading: false,
      isDetecting: false,
      error: '',
      searchTimeout: null,
      validationResult: null
    };
  },
  
  mounted() {
    // Initialiser avec la valeur du modèle si elle existe
    if (this.modelValue) {
      this.selectedLocation = this.modelValue;
      this.searchQuery = this.modelValue.address?.city || '';
    }
    
    // Fermer les suggestions si on clique ailleurs
    document.addEventListener('click', this.handleClickOutside);
  },
  
  beforeUnmount() {
    document.removeEventListener('click', this.handleClickOutside);
    if (this.searchTimeout) {
      clearTimeout(this.searchTimeout);
    }
  },
  
  watch: {
    modelValue(newValue) {
      if (newValue && newValue !== this.selectedLocation) {
        this.selectedLocation = newValue;
        this.searchQuery = newValue.address?.city || '';
      }
    }
  },
  
  methods: {
    onSearchInput() {
      this.error = '';
      
      // Débounce la recherche
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      
      this.searchTimeout = setTimeout(() => {
        this.searchAddresses();
      }, 300);
    },
    
    async searchAddresses() {
      if (!this.searchQuery || this.searchQuery.length < 3) {
        this.suggestions = [];
        return;
      }
      
      try {
        this.isLoading = true;
        this.suggestions = await geolocationService.searchAddresses(this.searchQuery, {
          limit: 5,
          countrycodes: 'fr'
        });
        this.showSuggestions = true;
      } catch (error) {
        console.error('Erreur lors de la recherche:', error);
        this.error = 'Erreur lors de la recherche d\'adresses';
        this.suggestions = [];
      } finally {
        this.isLoading = false;
      }
    },
    
    async selectSuggestion(suggestion) {
      this.selectedLocation = suggestion;
      this.searchQuery = suggestion.address.city;
      this.suggestions = [];
      this.showSuggestions = false;
      this.error = '';
      
      // Émettre les événements
      this.$emit('update:modelValue', suggestion);
      this.$emit('location-selected', suggestion);
      
      // Valider avec l'IP si demandé
      if (this.validateWithIP) {
        await this.validateLocation();
      }
    },
    
    async detectCurrentLocation() {
      try {
        this.isDetecting = true;
        this.error = '';
        
        // Obtenir la position actuelle
        const position = await geolocationService.getCurrentPosition();
        
        // Convertir en adresse
        const locationData = await geolocationService.reverseGeocode(
          position.latitude,
          position.longitude
        );
        
        this.selectedLocation = locationData;
        this.searchQuery = locationData.address.city;
        this.suggestions = [];
        this.showSuggestions = false;
        
        // Émettre les événements
        this.$emit('update:modelValue', locationData);
        this.$emit('location-selected', locationData);
        
        // Pas besoin de valider avec l'IP car c'est la position réelle
        this.validationResult = { isConsistent: true };
        this.$emit('validation-result', this.validationResult);
        
      } catch (error) {
        console.error('Erreur lors de la détection:', error);
        this.error = error.message;
      } finally {
        this.isDetecting = false;
      }
    },
    
    async validateLocation() {
      if (!this.selectedLocation) return;
      
      try {
        this.validationResult = await geolocationService.validateLocationConsistency(
          this.selectedLocation.coordinates
        );
        
        this.$emit('validation-result', this.validationResult);
      } catch (error) {
        console.error('Erreur lors de la validation:', error);
      }
    },
    
    clearLocation() {
      this.selectedLocation = null;
      this.searchQuery = '';
      this.suggestions = [];
      this.showSuggestions = false;
      this.error = '';
      this.validationResult = null;
      
      this.$emit('update:modelValue', null);
      this.$emit('location-selected', null);
    },
    
    handleClickOutside(event) {
      if (!this.$el.contains(event.target)) {
        this.showSuggestions = false;
      }
    }
  }
};
</script>

<style scoped>
.location-selector {
  position: relative;
}

.location-input-group {
  margin-bottom: 15px;
}

.location-input-group label {
  display: block;
  margin-bottom: 5px;
  color: #ffffff;
  font-weight: 500;
}

.input-with-button {
  display: flex;
  gap: 5px;
}

.input-with-button input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 16px;
}

.detect-btn {
  padding: 10px 15px;
  background-color: #1f8ea5;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
}

.detect-btn:hover:not(:disabled) {
  background-color: #1a7a94;
}

.detect-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.suggestion-item {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
}

.suggestion-item:hover {
  background-color: #f5f5f5;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-main {
  font-weight: bold;
  color: #333;
}

.suggestion-detail {
  font-size: 12px;
  color: #666;
  margin-top: 2px;
}

.current-location {
  margin-top: 10px;
}

.location-info {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: rgba(31, 142, 165, 0.1);
  border-radius: 4px;
  border: 1px solid #1f8ea5;
}

.location-icon {
  font-size: 18px;
}

.location-details {
  flex: 1;
}

.location-city {
  font-weight: bold;
  color: #1f8ea5;
}

.location-full {
  font-size: 12px;
  color: #ccc;
  margin-top: 2px;
}

.clear-btn {
  background: none;
  border: none;
  color: #ff6b6b;
  cursor: pointer;
  font-size: 16px;
  padding: 5px;
}

.clear-btn:hover {
  color: #ff5252;
}

.validation-warning {
  margin-top: 5px;
  padding: 8px;
  background-color: rgba(255, 193, 7, 0.1);
  border: 1px solid #ffc107;
  border-radius: 4px;
  color: #ffc107;
  font-size: 12px;
}

.error-message {
  margin-top: 5px;
  color: #ff6b6b;
  font-size: 12px;
}

.loading-indicator {
  margin-top: 5px;
  color: #1f8ea5;
  font-size: 12px;
  font-style: italic;
}
</style>
