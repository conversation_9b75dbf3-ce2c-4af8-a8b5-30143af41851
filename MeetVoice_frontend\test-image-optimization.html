<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Optimisation Images - MeetVoice</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-image {
            max-width: 300px;
            margin: 10px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        
        .image-info {
            background: #f8f9fa;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .webp-support {
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            font-weight: bold;
        }
        
        .webp-supported {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .webp-not-supported {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .performance-metrics {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🖼️ Test d'Optimisation des Images MeetVoice</h1>
    
    <!-- Support WebP -->
    <div id="webp-support" class="webp-support">
        <span id="webp-status">🔄 Vérification du support WebP...</span>
    </div>
    
    <!-- Test des images -->
    <div class="test-section">
        <h2>📸 Test des Images avec Picture Element</h2>
        
        <h3>Image d'accueil</h3>
        <picture>
            <source srcset="src/assets/Accueil.webp" type="image/webp">
            <img src="src/assets/Accueil.jpg" alt="Image d'accueil" class="test-image" onload="logImageLoad(this)">
        </picture>
        <div class="image-info" id="accueil-info">Chargement...</div>
        
        <h3>Logo principal</h3>
        <picture>
            <source srcset="src/assets/logo.webp" type="image/webp">
            <img src="src/assets/logo.jpg" alt="Logo MeetVoice" class="test-image" onload="logImageLoad(this)">
        </picture>
        <div class="image-info" id="logo-info">Chargement...</div>
        
        <h3>Image de présentation</h3>
        <picture>
            <source srcset="src/assets/pseudo-site-rencontre-Pr.webp" type="image/webp">
            <img src="src/assets/pseudo-site-rencontre-Pr.jpg" alt="Présentation site" class="test-image" onload="logImageLoad(this)">
        </picture>
        <div class="image-info" id="presentation-info">Chargement...</div>
    </div>
    
    <!-- Métriques de performance -->
    <div class="performance-metrics">
        <h3>📊 Métriques de Performance</h3>
        <div id="performance-data">
            <p>🔄 Collecte des données de performance...</p>
        </div>
    </div>
    
    <!-- Informations techniques -->
    <div class="test-section">
        <h2>🔧 Informations Techniques</h2>
        <div id="tech-info">
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
            <p><strong>Support WebP:</strong> <span id="webp-support-detail"></span></p>
            <p><strong>Connexion:</strong> <span id="connection-info"></span></p>
        </div>
    </div>

    <script>
        // Variables globales
        let webpSupported = false;
        let imageLoadTimes = {};
        
        // Détecter le support WebP
        async function detectWebPSupport() {
            return new Promise((resolve) => {
                const webpTestImage = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';
                const img = new Image();
                
                img.onload = () => resolve(img.width > 0 && img.height > 0);
                img.onerror = () => resolve(false);
                img.src = webpTestImage;
            });
        }
        
        // Logger le chargement des images
        function logImageLoad(img) {
            const startTime = performance.now();
            const isWebP = img.currentSrc ? img.currentSrc.includes('.webp') : false;
            const format = isWebP ? 'WebP' : 'Fallback';
            
            // Obtenir les informations de l'image
            const naturalSize = `${img.naturalWidth}x${img.naturalHeight}`;
            const displaySize = `${img.width}x${img.height}`;
            
            // Mettre à jour l'info correspondante
            const infoId = getInfoId(img.alt);
            if (infoId) {
                document.getElementById(infoId).innerHTML = `
                    ✅ <strong>Format:</strong> ${format}<br>
                    📐 <strong>Taille naturelle:</strong> ${naturalSize}<br>
                    📱 <strong>Taille affichée:</strong> ${displaySize}<br>
                    🔗 <strong>URL:</strong> ${img.currentSrc || img.src}
                `;
            }
            
            console.log(`📸 Image chargée: ${img.alt} (${format})`);
        }
        
        // Obtenir l'ID de l'info correspondant à l'image
        function getInfoId(alt) {
            if (alt.includes('accueil')) return 'accueil-info';
            if (alt.includes('Logo')) return 'logo-info';
            if (alt.includes('Présentation')) return 'presentation-info';
            return null;
        }
        
        // Mesurer les performances
        function measurePerformance() {
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    const entries = list.getEntries();
                    let webpCount = 0;
                    let fallbackCount = 0;
                    let totalWebPTime = 0;
                    let totalFallbackTime = 0;
                    
                    entries.forEach((entry) => {
                        if (entry.initiatorType === 'img') {
                            const isWebP = entry.name.includes('.webp');
                            const loadTime = entry.responseEnd - entry.startTime;
                            
                            if (isWebP) {
                                webpCount++;
                                totalWebPTime += loadTime;
                            } else {
                                fallbackCount++;
                                totalFallbackTime += loadTime;
                            }
                        }
                    });
                    
                    // Afficher les métriques
                    const performanceData = document.getElementById('performance-data');
                    performanceData.innerHTML = `
                        <p>📊 <strong>Images WebP:</strong> ${webpCount} (temps moyen: ${webpCount > 0 ? Math.round(totalWebPTime / webpCount) : 0}ms)</p>
                        <p>📊 <strong>Images Fallback:</strong> ${fallbackCount} (temps moyen: ${fallbackCount > 0 ? Math.round(totalFallbackTime / fallbackCount) : 0}ms)</p>
                        <p>🚀 <strong>Gain de performance:</strong> ${webpCount > 0 && fallbackCount > 0 ? Math.round(((totalFallbackTime / fallbackCount) - (totalWebPTime / webpCount)) / (totalFallbackTime / fallbackCount) * 100) : 0}%</p>
                    `;
                });
                
                observer.observe({ entryTypes: ['resource'] });
            }
        }
        
        // Initialisation
        async function init() {
            // Détecter le support WebP
            webpSupported = await detectWebPSupport();
            
            const supportDiv = document.getElementById('webp-support');
            const statusSpan = document.getElementById('webp-status');
            
            if (webpSupported) {
                supportDiv.className = 'webp-support webp-supported';
                statusSpan.textContent = '✅ WebP supporté - Images optimisées activées';
                document.documentElement.classList.add('webp');
            } else {
                supportDiv.className = 'webp-support webp-not-supported';
                statusSpan.textContent = '⚠️ WebP non supporté - Utilisation des images de fallback';
                document.documentElement.classList.add('no-webp');
            }
            
            // Remplir les informations techniques
            document.getElementById('user-agent').textContent = navigator.userAgent;
            document.getElementById('webp-support-detail').textContent = webpSupported ? 'Oui' : 'Non';
            
            // Informations de connexion
            if ('connection' in navigator) {
                const conn = navigator.connection;
                document.getElementById('connection-info').textContent = 
                    `${conn.effectiveType || 'Inconnue'} (${conn.downlink || '?'}Mbps)`;
            } else {
                document.getElementById('connection-info').textContent = 'Non disponible';
            }
            
            // Démarrer la mesure de performance
            measurePerformance();
        }
        
        // Lancer l'initialisation
        init();
    </script>
</body>
</html>
