module.exports = {
  // Environnement de test
  testEnvironment: 'jsdom',
  
  // Extensions de fichiers à traiter
  moduleFileExtensions: [
    'js',
    'jsx',
    'json',
    'vue',
    'ts',
    'tsx'
  ],
  
  // Transformation des fichiers
  transform: {
    '^.+\\.vue$': '@vue/vue3-jest',
    '.+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$': 'jest-transform-stub',
    '^.+\\.(js|jsx)$': 'babel-jest',
    '^.+\\.(ts|tsx)$': 'ts-jest'
  },
  
  // Patterns de fichiers de test
  testMatch: [
    '**/tests/unit/**/*.spec.(js|jsx|ts|tsx)|**/__tests__/*.(js|jsx|ts|tsx)'
  ],
  
  // Alias de modules
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/src/$1',
    '^@/_services$': '<rootDir>/src/_services/index.js'
  },
  
  // Fichiers à ignorer lors de la transformation
  transformIgnorePatterns: [
    'node_modules/(?!(axios)/)'
  ],
  
  // Configuration de la couverture de code
  collectCoverage: true,
  collectCoverageFrom: [
    'src/**/*.{js,vue}',
    '!src/main.js',
    '!src/router/index.js',
    '!**/node_modules/**',
    '!**/vendor/**'
  ],
  
  // Répertoire de sortie pour la couverture
  coverageDirectory: 'coverage',
  
  // Formats de rapport de couverture
  coverageReporters: [
    'html',
    'text',
    'lcov',
    'clover'
  ],
  
  // Seuils de couverture
  coverageThreshold: {
    global: {
      branches: 70,
      functions: 70,
      lines: 70,
      statements: 70
    }
  },
  
  // Configuration de l'environnement de test
  setupFilesAfterEnv: [
    '<rootDir>/tests/unit/setup.js'
  ],
  
  // Variables d'environnement pour les tests
  testEnvironmentOptions: {
    customExportConditions: ['node', 'node-addons']
  },
  
  // Timeout global pour les tests
  testTimeout: 10000,
  
  // Nettoyage automatique des mocks
  clearMocks: true,
  restoreMocks: true,
  
  // Configuration pour les tests en parallèle
  maxWorkers: '50%',
  
  // Verbose output
  verbose: true,
  
  // Patterns de fichiers à ignorer
  testPathIgnorePatterns: [
    '/node_modules/',
    '/dist/',
    '/coverage/'
  ],
  
  // Configuration spécifique pour Vue
  globals: {
    'vue-jest': {
      pug: {
        doctype: 'html'
      }
    }
  }
}
