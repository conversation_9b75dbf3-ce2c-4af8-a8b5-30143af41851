import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import RegisterView from '../views/RegisterView.vue'
import EnhancedRegisterView from '../views/EnhancedRegisterView.vue'
import NewRegisterView from '../views/NewRegisterView.vue'
import ActualiteView from '../views/ActualiteView.vue'
import ArticleDetailView from '../views/ArticleDetailView.vue'
import AccessDeniedView from '../views/AccessDeniedView.vue'
import DlView from '../views/DlView.vue'
import FormuleView from '../views/FormuleView'
import ProfileEditView from '../views/ProfileEditView.vue'
import VoskTestView from '../views/VoskTestView.vue'

// Pages légales
import MentionsLegalesView from '../views/legal/MentionsLegalesView.vue'
import DonneesPersonnellesView from '../views/legal/DonneesPersonnellesView.vue'

const routes = [
  {
    path: '/',
    name: 'home',
    component: HomeView
  },
  {
    path: '/actualite',
    name: 'Actualite',
    component: ActualiteView
  },
  {
    path: '/article/:slug/:id?',
    name: 'article-detail-slug',
    component: ArticleDetailView
  },
  {
    path: '/article/:id',
    name: 'article-detail',
    component: ArticleDetailView
  },
  {
    path: '/dl',
    name: 'Dl',
    component: DlView,
    meta: { requiresAuth: true }
  },
  {
    path: '/formule',
    name: 'formule',
    component: FormuleView,
    meta: { requiresAuth: true }
  },
  {
    path: '/login',
    name: 'login',
    component: LoginView
  },
  {
    path: '/register',
    name: 'register',
    component: EnhancedRegisterView
  },
  {
    path: '/register-enhanced',
    name: 'register-enhanced',
    component: NewRegisterView
  },
  {
    path: '/access-denied',
    name: 'access-denied',
    component: AccessDeniedView
  },
  {
    path: '/profile/edit',
    name: 'profile-edit',
    component: ProfileEditView,
    meta: { requiresAuth: true }
  },

  // Routes légales
  {
    path: '/mentions-legales',
    name: 'mentions-legales',
    component: MentionsLegalesView
  },
  {
    path: '/donnees-personnelles',
    name: 'donnees-personnelles',
    component: DonneesPersonnellesView
  },
  {
    path: '/cgu',
    name: 'cgu',
    component: () => import('../views/legal/CGUView.vue')
  },
  {
    path: '/responsabilite',
    name: 'responsabilite',
    component: () => import('../views/legal/ResponsabiliteView.vue')
  },
  {
    path: '/propriete-intellectuelle',
    name: 'propriete-intellectuelle',
    component: () => import('../views/legal/ProprieteIntellectuelleView.vue')
  },
  {
    path: '/mediation',
    name: 'mediation',
    component: () => import('../views/legal/MediationView.vue')
  },
  {
    path: '/contact',
    name: 'contact',
    component: () => import('../views/ContactView.vue')
  },
  {
    path: '/vosk-test',
    name: 'vosk-test',
    component: VoskTestView
  },

  // Routes pour les différents types d'interfaces
  {
    path: '/dashboard',
    name: 'dashboard',
    component: () => import('../views/DashboardView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Tableau de bord - MeetVoice',
      description: 'Votre espace personnel MeetVoice'
    }
  },

  // Interface Amical (OnVaSortir style)
  {
    path: '/events',
    name: 'events',
    component: () => import('../views/amical/EventsView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amical',
      title: 'Événements - MeetVoice',
      description: 'Découvrez et participez aux événements près de chez vous'
    }
  },
  {
    path: '/events/create',
    name: 'create-event',
    component: () => import('../views/amical/CreateEventView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amical',
      title: 'Créer un événement - MeetVoice'
    }
  },
  {
    path: '/events/:id',
    name: 'event-detail',
    component: () => import('../views/amical/EventDetailView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amical'
    }
  },
  {
    path: '/calendar',
    name: 'calendar',
    component: () => import('../views/amical/CalendarView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amical',
      title: 'Mon calendrier - MeetVoice'
    }
  },

  // Interface Amoureux (Meetic style)
  {
    path: '/profiles',
    name: 'profiles',
    component: () => import('../views/amoureux/ProfilesView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amoureux',
      title: 'Profils - MeetVoice',
      description: 'Découvrez des profils compatibles avec vous'
    }
  },
  {
    path: '/matches',
    name: 'matches',
    component: () => import('../views/amoureux/MatchesView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amoureux',
      title: 'Mes matchs - MeetVoice'
    }
  },
  {
    path: '/messages',
    name: 'messages',
    component: () => import('../views/amoureux/MessagesView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'amoureux',
      title: 'Messages - MeetVoice'
    }
  },
  {
    path: '/messages/:id',
    name: 'conversation',
    component: () => import('../views/amoureux/MessagesView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Conversation - MeetVoice'
    }
  },

  // Interface Libertin (Wyylde style)
  {
    path: '/community',
    name: 'community',
    component: () => import('../views/libertin/CommunityView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin',
      title: 'Communauté - MeetVoice'
    }
  },
  {
    path: '/parties',
    name: 'parties',
    component: () => import('../views/libertin/PartiesView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin',
      title: 'Soirées - MeetVoice'
    }
  },
  {
    path: '/parties/create',
    name: 'create-party',
    component: () => import('../views/libertin/CreatePartyView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin',
      title: 'Créer une soirée - MeetVoice'
    }
  },
  {
    path: '/parties/:id',
    name: 'party-detail',
    component: () => import('../views/libertin/PartyDetailView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin'
    }
  },
  {
    path: '/parties/:id/edit',
    name: 'edit-party',
    component: () => import('../views/libertin/EditPartyView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin'
    }
  },
  {
    path: '/groups',
    name: 'groups',
    component: () => import('../views/libertin/GroupsView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin',
      title: 'Groupes - MeetVoice'
    }
  },
  {
    path: '/groups/:id',
    name: 'group-detail',
    component: () => import('../views/libertin/GroupDetailView.vue'),
    meta: {
      requiresAuth: true,
      userType: 'libertin'
    }
  },

  // Routes communes
  {
    path: '/wall',
    name: 'wall',
    component: () => import('../views/common/WallView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Mur - MeetVoice'
    }
  },
  {
    path: '/subscription',
    name: 'subscription',
    component: () => import('../views/common/SubscriptionView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Abonnement - MeetVoice'
    }
  },
  {
    path: '/profile/:id',
    name: 'profile-view',
    component: () => import('../views/common/ProfileView.vue'),
    meta: {
      requiresAuth: true
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: () => import('../views/common/SettingsView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Paramètres - MeetVoice'
    }
  },
  {
    path: '/notifications',
    name: 'notifications',
    component: () => import('../views/common/NotificationsView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Notifications - MeetVoice'
    }
  },
  {
    path: '/search',
    name: 'search',
    component: () => import('../views/common/SearchView.vue'),
    meta: {
      requiresAuth: true,
      title: 'Recherche - MeetVoice'
    }
  },
  // Route de fallback pour les 404
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('../views/NotFoundView.vue'),
    meta: {
      title: 'Page non trouvée - MeetVoice'
    }
  }

]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Guard de navigation pour vérifier l'authentification et le type d'utilisateur
router.beforeEach((to, from, next) => {
  // Gestion du SEO - Mettre à jour le titre et les meta tags
  if (to.meta.title) {
    document.title = to.meta.title;
  }

  if (to.meta.description) {
    let metaDescription = document.querySelector('meta[name="description"]');
    if (!metaDescription) {
      metaDescription = document.createElement('meta');
      metaDescription.setAttribute('name', 'description');
      document.head.appendChild(metaDescription);
    }
    metaDescription.setAttribute('content', to.meta.description);
  }

  if (to.meta.keywords) {
    let metaKeywords = document.querySelector('meta[name="keywords"]');
    if (!metaKeywords) {
      metaKeywords = document.createElement('meta');
      metaKeywords.setAttribute('name', 'keywords');
      document.head.appendChild(metaKeywords);
    }
    metaKeywords.setAttribute('content', to.meta.keywords);
  }

  // Vérifier si la route nécessite une authentification
  if (to.matched.some(record => record.meta.requiresAuth)) {
    const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';

    if (!isLoggedIn) {
      next('/access-denied');
      return;
    }

    // Vérifier le type d'utilisateur pour les routes spécifiques
    if (to.meta.userType) {
      const userType = localStorage.getItem('userType') || 'amical';

      if (to.meta.userType !== userType) {
        // Rediriger vers l'interface appropriée
        switch (userType) {
          case 'amical':
            next('/events');
            break;
          case 'amoureux':
            next('/profiles');
            break;
          case 'libertin':
            next('/community');
            break;
          default:
            next('/dashboard');
        }
        return;
      }
    }

    next(); // Utilisateur connecté et autorisé
  } else {
    next(); // Route publique
  }
});

export default router
