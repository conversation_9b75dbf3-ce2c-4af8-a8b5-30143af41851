# 🎯 MeetVoice Frontend

Application Vue.js moderne et optimisée pour MeetVoice - Plateforme de rencontres et articles.

## ✨ Fonctionnalités

- 📱 **Interface moderne** : Design responsive et intuitif
- 🚀 **Performance optimisée** : Service Worker, compression, CDN
- 🔍 **SEO avancé** : Pre-rendering, données structurées, sitemap automatique
- 📊 **Analytics** : Suivi des performances et métriques
- 🔐 **Sécurisé** : Headers de sécurité et protection CSRF

## 🛠️ Technologies

- **Vue.js 3** - Framework JavaScript progressif
- **Vue Router** - Routage SPA
- **Vuex** - Gestion d'état
- **Bootstrap 5** - Framework CSS
- **Webpack** - Bundler avec optimisations avancées

## 🚀 Installation

```bash
# Cloner le repository
git clone <repository-url>
cd front_meetvoice

# Installer les dépendances
cd MeetVoice_frontend
npm install

# Démarrer le serveur de développement
npm run serve
```

## 📋 Scripts disponibles

```bash
# Développement
npm run serve                 # Serveur de développement (port 8081)

# Build
npm run build                 # Build standard
npm run build:optimized       # Build avec toutes les optimisations
npm run build:production      # Build de production avec pre-rendering

# SEO et Performance
npm run generate-sitemap      # Générer le sitemap
npm run audit:seo            # Audit SEO automatique
npm run test:seo             # Tests SEO
npm run analyze:bundle       # Analyser la taille des bundles

# Tests et déploiement
npm run serve:dist           # Servir la version de production
npm run performance:test     # Test de performance complet
```

## 🔧 Configuration

### Variables d'environnement

Créer un fichier `.env.local` :

```env
VUE_APP_API_URL=http://127.0.0.1:8000
VUE_APP_CDN_URL=https://cdn.meetvoice.fr
VUE_APP_ENABLE_ANALYTICS=true
```

### API Backend

L'application communique avec l'API Django sur `http://127.0.0.1:8000`

Endpoints principaux :
- `/actualite/api/articles/published/` - Articles publiés
- `/actualite/api/articles/{id}/` - Article par ID
- `/actualite/api/articles/by-slug/{slug}/` - Article par slug

## 📊 Optimisations SEO

### ✅ Implémentées

- **Pre-rendering SPA** : Pages statiques pour Google
- **URLs SEO-friendly** : `/article/titre-de-l-article`
- **Meta tags dynamiques** : Titre, description, Open Graph
- **Données structurées** : Schema.org Article
- **Sitemap automatique** : Généré depuis l'API
- **Breadcrumbs** : Navigation contextuelle
- **Images optimisées** : Alt, lazy loading, dimensions

### 📈 Performance

- **Service Worker** : Mise en cache intelligente
- **Compression** : Gzip + Brotli
- **Code splitting** : Bundles optimisés
- **CDN ready** : Distribution d'assets
- **Core Web Vitals** : Optimisé pour Google

## 🌐 Déploiement

### Nginx (Recommandé)

Utiliser la configuration fournie dans `nginx.conf` :

```bash
# Copier la configuration
sudo cp nginx.conf /etc/nginx/sites-available/meetvoice
sudo ln -s /etc/nginx/sites-available/meetvoice /etc/nginx/sites-enabled/

# Build optimisé
npm run build:optimized

# Copier les fichiers
sudo cp -r dist/* /var/www/meetvoice/

# Redémarrer nginx
sudo systemctl reload nginx
```

### Autres plateformes

- **Vercel** : `vercel --prod`
- **Netlify** : Drag & drop du dossier `dist/`
- **AWS S3** : Upload avec CloudFront
- **GitHub Pages** : Push vers branche `gh-pages`

## 📁 Structure du projet

```
MeetVoice_frontend/
├── public/                   # Fichiers statiques
│   ├── sw.js                # Service Worker
│   ├── robots.txt           # Directives robots
│   └── sitemap.xml          # Sitemap généré
├── src/
│   ├── components/          # Composants Vue
│   ├── views/               # Pages principales
│   ├── router/              # Configuration routage
│   ├── store/               # Gestion d'état Vuex
│   └── utils/               # Utilitaires
│       ├── seo.js          # Gestionnaire SEO
│       ├── seo-advanced.js # SEO avancé
│       ├── cdn.js          # Gestionnaire CDN
│       └── slug.js         # Gestion des slugs
├── scripts/                 # Scripts de build
│   ├── generate-sitemap.js # Génération sitemap
│   ├── build-optimized.js  # Build optimisé
│   └── seo-audit.js        # Audit SEO
└── nginx.conf              # Configuration nginx
```

## 🔍 Monitoring

### SEO

```bash
# Audit complet
npm run audit:seo

# Vérifier les données structurées
# https://search.google.com/test/rich-results

# Performance
# https://pagespeed.web.dev/
```

### Analytics

- Google Analytics 4
- Google Search Console
- Core Web Vitals monitoring

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/AmazingFeature`)
3. Commit les changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 📞 Support

Pour toute question ou problème :
- 📧 Email : <EMAIL>
- 🐛 Issues : [GitHub Issues](repository-url/issues)
- 📖 Documentation : [Wiki](repository-url/wiki)

---

**MeetVoice** - Connecter les cœurs, partager les histoires ❤️
