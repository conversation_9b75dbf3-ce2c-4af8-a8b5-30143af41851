<template>
  <div class="simple-stt-container">
    <div class="stt-header">
      <h2>🎤 Speech-to-Text Dynamique</h2>
      <p><PERSON><PERSON><PERSON> et voyez votre texte apparaître en temps réel</p>
    </div>

    <!-- Contrôles -->
    <div class="stt-controls">
      <button 
        @click="toggleListening" 
        :class="['mic-button', { 'listening': isListening, 'error': hasError }]"
        :disabled="!isSupported"
      >
        <span v-if="isListening">🛑 Arrêter</span>
        <span v-else>🎤 Commencer</span>
      </button>

      <button @click="clearText" class="clear-button">
        🗑️ Effacer
      </button>

      <select v-model="selectedLanguage" @change="changeLanguage" class="language-select">
        <option value="fr-FR">🇫🇷 Français</option>
        <option value="en-US">🇺🇸 English</option>
        <option value="es-ES">🇪🇸 Español</option>
      </select>
    </div>

    <!-- Status -->
    <div class="stt-status">
      <div v-if="!isSupported" class="status error">
        ❌ Speech Recognition non supporté par ce navigateur
      </div>
      <div v-else-if="isListening" class="status listening">
        🎤 Écoute en cours... Parlez maintenant !
      </div>
      <div v-else-if="hasError" class="status error">
        ❌ {{ errorMessage }}
      </div>
      <div v-else class="status ready">
        ✅ Prêt à écouter
      </div>
    </div>

    <!-- Zone de texte dynamique -->
    <div class="text-display">
      <div class="text-area">
        <!-- Texte final (confirmé) -->
        <span class="final-text">{{ finalText }}</span>
        <!-- Texte intermédiaire (en cours) -->
        <span class="interim-text">{{ interimText }}</span>
        <!-- Curseur clignotant si écoute active -->
        <span v-if="isListening" class="cursor">|</span>
      </div>
    </div>

    <!-- Statistiques -->
    <div class="stt-stats">
      <div class="stat">
        <strong>Mots:</strong> {{ wordCount }}
      </div>
      <div class="stat">
        <strong>Caractères:</strong> {{ charCount }}
      </div>
      <div class="stat">
        <strong>Temps:</strong> {{ listeningTime }}s
      </div>
    </div>
  </div>
</template>

<script>
import { simpleSttService } from '@/_services/simple-stt.service';

export default {
  name: 'SimpleSpeechToText',
  
  data() {
    return {
      isListening: false,
      isSupported: true,
      hasError: false,
      errorMessage: '',
      finalText: '',
      interimText: '',
      selectedLanguage: 'fr-FR',
      startTime: null,
      listeningTime: 0,
      timer: null
    };
  },

  computed: {
    wordCount() {
      return this.finalText.trim().split(/\s+/).filter(word => word.length > 0).length;
    },
    
    charCount() {
      return this.finalText.length;
    }
  },

  mounted() {
    this.isSupported = simpleSttService.checkSupport();
    console.log('🎤 Composant STT monté, support:', this.isSupported);
  },

  beforeUnmount() {
    this.stopListening();
  },

  methods: {
    toggleListening() {
      if (this.isListening) {
        this.stopListening();
      } else {
        this.startListening();
      }
    },

    startListening() {
      if (!this.isSupported) {
        this.showError('Speech Recognition non supporté');
        return;
      }

      console.log('🚀 Démarrage écoute STT...');
      this.hasError = false;
      this.startTime = Date.now();
      this.startTimer();

      simpleSttService.startListening({
        onStart: () => {
          this.isListening = true;
          console.log('✅ Écoute démarrée');
        },

        onResult: (finalText, interimText) => {
          this.finalText = finalText;
          this.interimText = interimText;
          console.log('📝 Résultat:', { final: finalText, interim: interimText });
        },

        onInterim: (interimText) => {
          this.interimText = interimText;
        },

        onEnd: () => {
          this.isListening = false;
          this.stopTimer();
          console.log('🛑 Écoute terminée');
        },

        onError: (error) => {
          this.isListening = false;
          this.stopTimer();
          this.showError(error);
        }
      }).catch(error => {
        this.isListening = false;
        this.stopTimer();
        this.showError(error.message);
      });
    },

    stopListening() {
      console.log('🛑 Arrêt écoute STT...');
      simpleSttService.stopListening();
      this.isListening = false;
      this.interimText = '';
      this.stopTimer();
    },

    clearText() {
      this.finalText = '';
      this.interimText = '';
      this.hasError = false;
      this.errorMessage = '';
      console.log('🗑️ Texte effacé');
    },

    changeLanguage() {
      simpleSttService.setLanguage(this.selectedLanguage);
      console.log('🌐 Langue changée:', this.selectedLanguage);
    },

    showError(message) {
      this.hasError = true;
      this.errorMessage = message;
      console.error('❌ Erreur STT:', message);
    },

    startTimer() {
      this.timer = setInterval(() => {
        if (this.startTime) {
          this.listeningTime = Math.floor((Date.now() - this.startTime) / 1000);
        }
      }, 1000);
    },

    stopTimer() {
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
    }
  }
};
</script>

<style scoped>
.simple-stt-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.stt-header {
  text-align: center;
  margin-bottom: 30px;
}

.stt-header h2 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.stt-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  flex-wrap: wrap;
}

.mic-button {
  background: #3498db;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.mic-button:hover {
  background: #2980b9;
  transform: translateY(-2px);
}

.mic-button.listening {
  background: #e74c3c;
  animation: pulse 1.5s infinite;
}

.mic-button.error {
  background: #95a5a6;
  cursor: not-allowed;
}

.clear-button {
  background: #95a5a6;
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-button:hover {
  background: #7f8c8d;
}

.language-select {
  padding: 10px 15px;
  border: 2px solid #bdc3c7;
  border-radius: 20px;
  font-size: 14px;
  background: white;
}

.stt-status {
  text-align: center;
  margin-bottom: 20px;
}

.status {
  padding: 10px 20px;
  border-radius: 20px;
  font-weight: bold;
  display: inline-block;
}

.status.ready {
  background: #d5f4e6;
  color: #27ae60;
}

.status.listening {
  background: #ffeaa7;
  color: #e17055;
  animation: pulse 2s infinite;
}

.status.error {
  background: #fab1a0;
  color: #e74c3c;
}

.text-display {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 20px;
  min-height: 200px;
  margin-bottom: 20px;
}

.text-area {
  font-size: 18px;
  line-height: 1.6;
  color: #2c3e50;
}

.final-text {
  color: #2c3e50;
}

.interim-text {
  color: #7f8c8d;
  font-style: italic;
}

.cursor {
  animation: blink 1s infinite;
  font-weight: bold;
  color: #3498db;
}

.stt-stats {
  display: flex;
  justify-content: space-around;
  background: #ecf0f1;
  padding: 15px;
  border-radius: 10px;
  flex-wrap: wrap;
  gap: 10px;
}

.stat {
  text-align: center;
  color: #2c3e50;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

@media (max-width: 600px) {
  .stt-controls {
    flex-direction: column;
  }
  
  .stt-stats {
    flex-direction: column;
    text-align: center;
  }
}
</style>
