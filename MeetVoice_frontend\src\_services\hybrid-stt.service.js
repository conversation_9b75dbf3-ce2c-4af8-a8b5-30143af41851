/**
 * Service STT Hybride - Vosk + IA d'extraction
 * Combine la reconnaissance vocale Vosk avec l'extraction IA
 * Système intelligent et autonome
 */

import voskSttService from './vosk-stt-fixed.service.js';
import aiExtractionService from './ai-extraction.service.js';
import simpleSttService from './simple-stt.service.js';

class HybridSTTService {
  constructor() {
    this.currentEngine = 'simple'; // 'simple' ou 'vosk'
    this.isListening = false;
    this.autoExtraction = true;
    this.fallbackEnabled = true;
    
    console.log('🚀 Service STT Hybride initialisé');
  }

  /**
   * Démarrer l'écoute hybride intelligente
   * @param {Object} options - Options d'écoute
   */
  async startListening(options = {}) {
    try {
      console.log('🚀 Démarrage STT hybride...');

      // Essayer d'abord Vosk si disponible
      if (this.shouldUseVosk()) {
        try {
          console.log('🎯 Tentative avec Vosk...');
          await this.startVoskListening(options);
          this.currentEngine = 'vosk';
          return;
        } catch (error) {
          console.warn('⚠️ Vosk indisponible, fallback vers simple:', error.message);
        }
      }

      // Fallback vers STT simple
      console.log('🎤 Utilisation STT simple...');
      await this.startSimpleListening(options);
      this.currentEngine = 'simple';

    } catch (error) {
      console.error('❌ Erreur STT hybride:', error);
      this.isListening = false;
      if (options.onError) options.onError(error.message);
      throw error;
    }
  }

  /**
   * Démarrer l'écoute avec Vosk
   */
  async startVoskListening(options) {
    return await voskSttService.startListening({
      language: options.language || 'fr-FR',
      
      onStart: () => {
        this.isListening = true;
        console.log('✅ Vosk STT démarré');
        if (options.onStart) options.onStart();
      },

      onResult: async (finalText, interimText) => {
        console.log('📝 Vosk résultat:', finalText);
        
        // Extraction IA automatique
        if (this.autoExtraction && finalText.trim()) {
          try {
            const extracted = await aiExtractionService.extractInformation(finalText);
            console.log('🧠 Informations extraites:', extracted);
          } catch (error) {
            console.warn('⚠️ Erreur extraction IA:', error.message);
          }
        }
        
        if (options.onResult) {
          options.onResult(finalText, interimText);
        }
      },

      onInterim: (interimText) => {
        console.log('📝 Vosk partiel:', interimText);
        if (options.onInterim) {
          options.onInterim(interimText);
        }
      },

      onEnd: () => {
        this.isListening = false;
        console.log('🛑 Vosk STT terminé');
        if (options.onEnd) options.onEnd();
      },

      onError: (error) => {
        this.isListening = false;
        console.error('❌ Erreur Vosk STT:', error);
        if (options.onError) options.onError(error);
      }
    });
  }

  /**
   * Démarrer l'écoute avec STT simple
   */
  async startSimpleListening(options) {
    return await simpleSttService.startListening({
      language: options.language || 'fr-FR',
      
      onStart: () => {
        this.isListening = true;
        console.log('✅ STT simple démarré');
        if (options.onStart) options.onStart();
      },

      onResult: async (finalText, interimText) => {
        console.log('📝 STT simple résultat:', finalText);
        
        // Extraction IA automatique
        if (this.autoExtraction && finalText.trim()) {
          try {
            const extracted = await aiExtractionService.extractInformation(finalText);
            console.log('🧠 Informations extraites:', extracted);
          } catch (error) {
            console.warn('⚠️ Erreur extraction IA:', error.message);
          }
        }
        
        if (options.onResult) {
          options.onResult(finalText, interimText);
        }
      },

      onInterim: (interimText) => {
        console.log('📝 STT simple partiel:', interimText);
        if (options.onInterim) {
          options.onInterim(interimText);
        }
      },

      onEnd: () => {
        this.isListening = false;
        console.log('🛑 STT simple terminé');
        if (options.onEnd) options.onEnd();
      },

      onError: (error) => {
        this.isListening = false;
        console.error('❌ Erreur STT simple:', error);
        if (options.onError) options.onError(error);
      }
    });
  }

  /**
   * Arrêter l'écoute hybride
   */
  stopListening() {
    console.log('🛑 Arrêt STT hybride...');
    
    if (this.currentEngine === 'vosk') {
      voskSttService.stopListening();
    } else {
      simpleSttService.stopListening();
    }
    
    this.isListening = false;
    console.log('✅ STT hybride arrêté');
  }

  /**
   * Vérifier si Vosk doit être utilisé
   */
  shouldUseVosk() {
    // Utiliser Vosk si :
    // 1. Il est supporté
    // 2. L'utilisateur n'a pas désactivé Vosk
    // 3. La connexion est stable
    return voskSttService.isSupported() && 
           navigator.onLine && 
           !this.isVoskDisabled();
  }

  /**
   * Vérifier si Vosk est désactivé
   */
  isVoskDisabled() {
    // Vérifier les préférences utilisateur ou les erreurs précédentes
    return localStorage.getItem('vosk_disabled') === 'true';
  }

  /**
   * Activer/désactiver Vosk
   */
  setVoskEnabled(enabled) {
    localStorage.setItem('vosk_disabled', enabled ? 'false' : 'true');
    console.log(`🎯 Vosk ${enabled ? 'activé' : 'désactivé'}`);
  }

  /**
   * Activer/désactiver l'extraction automatique
   */
  setAutoExtraction(enabled) {
    this.autoExtraction = enabled;
    console.log(`🧠 Extraction automatique ${enabled ? 'activée' : 'désactivée'}`);
  }

  /**
   * Obtenir le statut du service hybride
   */
  getStatus() {
    return {
      isListening: this.isListening,
      currentEngine: this.currentEngine,
      autoExtraction: this.autoExtraction,
      voskAvailable: voskSttService.isSupported(),
      voskReady: voskSttService.isReady(),
      simpleAvailable: simpleSttService.isSupported(),
      extractedInfo: aiExtractionService.getAllExtractedInfo()
    };
  }

  /**
   * Obtenir les informations extraites
   */
  getExtractedInfo() {
    return aiExtractionService.getAllExtractedInfo();
  }

  /**
   * Obtenir un résumé des informations
   */
  getInfoSummary() {
    return aiExtractionService.generateSummary();
  }

  /**
   * Réinitialiser le service hybride
   */
  reset() {
    console.log('🔄 Reset STT hybride...');
    this.stopListening();
    voskSttService.reset();
    simpleSttService.reset();
    aiExtractionService.reset();
    this.currentEngine = 'simple';
  }

  /**
   * Forcer l'utilisation d'un moteur spécifique
   */
  forceEngine(engine) {
    if (engine === 'vosk' || engine === 'simple') {
      this.currentEngine = engine;
      console.log(`🔧 Moteur forcé: ${engine}`);
    }
  }

  /**
   * Vérifier si l'écoute est active
   */
  isListeningActive() {
    return this.isListening;
  }
}

// Export de l'instance unique
export const hybridSttService = new HybridSTTService();
export default hybridSttService;
