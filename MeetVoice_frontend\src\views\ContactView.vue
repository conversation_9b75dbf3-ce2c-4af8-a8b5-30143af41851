<!-- @format -->

<template>
  <div class="contact-page">
    <div class="contact-container">
      <div class="contact-header">
        <h1>Centre d'aide</h1>
        <p class="subtitle">Nous sommes là pour vous aider</p>
      </div>

      <div class="contact-content">
        <!-- Moyens de contact -->
        <section class="contact-methods">
          <h2>Contactez-nous</h2>
          
          <div class="contact-cards">
            <div class="contact-card">
              <div class="contact-icon">
                <i class="fas fa-envelope"></i>
              </div>
              <h3>Email</h3>
              <p>Pour toute question ou assistance</p>
              <a href="mailto:<EMAIL>" class="contact-link">
                <EMAIL>
              </a>
            </div>
            
            <div class="contact-card">
              <div class="contact-icon">
                <i class="fas fa-clock"></i>
              </div>
              <h3><PERSON><PERSON><PERSON> de réponse</h3>
              <p>Nous répondons généralement sous</p>
              <span class="response-time">24-48 heures</span>
            </div>
            
            <div class="contact-card">
              <div class="contact-icon">
                <i class="fas fa-shield-alt"></i>
              </div>
              <h3>Support sécurisé</h3>
              <p>Vos données sont protégées</p>
              <span class="security-note">Communication chiffrée</span>
            </div>
          </div>
        </section>

        <!-- FAQ rapide -->
        <section class="quick-faq">
          <h2>Questions fréquentes</h2>
          
          <div class="faq-items">
            <div class="faq-item">
              <h3>Comment créer mon profil ?</h3>
              <p>Utilisez notre interview vocal innovant ou remplissez le formulaire classique pour créer votre profil en quelques minutes.</p>
            </div>
            
            <div class="faq-item">
              <h3>Comment fonctionne le matching ?</h3>
              <p>Notre algorithme analyse vos préférences et votre personnalité pour vous proposer des profils compatibles.</p>
            </div>
            
            <div class="faq-item">
              <h3>Mes données sont-elles sécurisées ?</h3>
              <p>Oui, nous respectons le RGPD et utilisons un chiffrement de niveau bancaire pour protéger vos informations.</p>
            </div>
            
            <div class="faq-item">
              <h3>Comment supprimer mon compte ?</h3>
              <p>Vous pouvez supprimer votre compte à tout moment depuis les paramètres de votre profil ou en nous contactant.</p>
            </div>
          </div>
        </section>

        <!-- Formulaire de contact -->
        <section class="contact-form-section">
          <h2>Envoyez-nous un message</h2>
          
          <form @submit.prevent="submitContactForm" class="contact-form">
            <div class="form-group">
              <label for="contact-name">Nom complet</label>
              <input
                type="text"
                id="contact-name"
                v-model="contactForm.name"
                required
                placeholder="Votre nom et prénom"
              />
            </div>
            
            <div class="form-group">
              <label for="contact-email">Email</label>
              <input
                type="email"
                id="contact-email"
                v-model="contactForm.email"
                required
                placeholder="<EMAIL>"
              />
            </div>
            
            <div class="form-group">
              <label for="contact-subject">Sujet</label>
              <select id="contact-subject" v-model="contactForm.subject" required>
                <option value="">Sélectionnez un sujet</option>
                <option value="account">Problème de compte</option>
                <option value="technical">Problème technique</option>
                <option value="billing">Facturation</option>
                <option value="privacy">Confidentialité</option>
                <option value="report">Signalement</option>
                <option value="suggestion">Suggestion</option>
                <option value="other">Autre</option>
              </select>
            </div>
            
            <div class="form-group">
              <label for="contact-message">Message</label>
              <textarea
                id="contact-message"
                v-model="contactForm.message"
                required
                rows="5"
                placeholder="Décrivez votre demande en détail..."
              ></textarea>
            </div>
            
            <button type="submit" class="submit-btn" :disabled="isSubmitting">
              <i class="fas fa-paper-plane"></i>
              {{ isSubmitting ? 'Envoi en cours...' : 'Envoyer le message' }}
            </button>
          </form>
          
          <div v-if="submitMessage" class="submit-message" :class="submitSuccess ? 'success' : 'error'">
            {{ submitMessage }}
          </div>
        </section>

        <!-- Liens utiles -->
        <section class="useful-links">
          <h2>Liens utiles</h2>
          
          <div class="links-grid">
            <router-link to="/cgu" class="useful-link">
              <i class="fas fa-file-contract"></i>
              <span>Conditions d'utilisation</span>
            </router-link>
            
            <router-link to="/donnees-personnelles" class="useful-link">
              <i class="fas fa-shield-alt"></i>
              <span>Protection des données</span>
            </router-link>
            
            <router-link to="/mentions-legales" class="useful-link">
              <i class="fas fa-info-circle"></i>
              <span>Mentions légales</span>
            </router-link>
            
            <router-link to="/mediation" class="useful-link">
              <i class="fas fa-handshake"></i>
              <span>Médiation</span>
            </router-link>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ContactView',
  data() {
    return {
      contactForm: {
        name: '',
        email: '',
        subject: '',
        message: ''
      },
      isSubmitting: false,
      submitMessage: '',
      submitSuccess: false
    };
  },
  methods: {
    async submitContactForm() {
      this.isSubmitting = true;
      this.submitMessage = '';
      
      try {
        // Simulation d'envoi (à remplacer par un vrai appel API)
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Ici, vous pourriez envoyer les données à votre backend
        console.log('Formulaire de contact soumis:', this.contactForm);
        
        this.submitSuccess = true;
        this.submitMessage = 'Votre message a été envoyé avec succès ! Nous vous répondrons dans les plus brefs délais.';
        
        // Réinitialiser le formulaire
        this.contactForm = {
          name: '',
          email: '',
          subject: '',
          message: ''
        };
        
      } catch (error) {
        this.submitSuccess = false;
        this.submitMessage = 'Une erreur est survenue lors de l\'envoi. Veuillez réessayer ou nous contacter directement par email.';
        console.error('Erreur envoi formulaire:', error);
      } finally {
        this.isSubmitting = false;
      }
    }
  },
  mounted() {
    window.scrollTo(0, 0);
  }
};
</script>

<style scoped>
.contact-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 80px 0 40px;
}

.contact-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 20px;
}

.contact-header {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 40px;
  margin-bottom: 30px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.subtitle {
  color: #667eea;
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0;
}

.contact-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.contact-content section {
  margin-bottom: 50px;
}

.contact-content section:last-child {
  margin-bottom: 0;
}

.contact-content h2 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin-bottom: 25px;
  padding-bottom: 10px;
  border-bottom: 2px solid #667eea;
  font-weight: 600;
}

/* Contact cards */
.contact-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
}

.contact-card {
  background: #f8f9fa;
  padding: 30px;
  border-radius: 12px;
  text-align: center;
  border: 1px solid #e9ecef;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.contact-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}

.contact-icon i {
  font-size: 24px;
  color: white;
}

.contact-card h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.contact-card p {
  color: #6c757d;
  margin-bottom: 15px;
  font-size: 14px;
}

.contact-link {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
}

.contact-link:hover {
  text-decoration: underline;
}

.response-time,
.security-note {
  color: #28a745;
  font-weight: 600;
  font-size: 16px;
}

/* FAQ */
.faq-items {
  display: grid;
  gap: 20px;
}

.faq-item {
  background: #f8f9fa;
  padding: 25px;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.faq-item h3 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.faq-item p {
  color: #495057;
  line-height: 1.6;
  margin: 0;
}

/* Formulaire */
.contact-form {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 25px;
}

.form-group label {
  display: block;
  color: #2c3e50;
  font-weight: 600;
  margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #667eea;
}

.submit-btn {
  background: linear-gradient(45deg, #667eea, #764ba2);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0 auto;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.submit-message {
  margin-top: 20px;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
  font-weight: 500;
}

.submit-message.success {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.submit-message.error {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

/* Liens utiles */
.links-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.useful-link {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  text-decoration: none;
  color: #2c3e50;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.useful-link:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.useful-link i {
  font-size: 20px;
  color: #667eea;
}

.useful-link:hover i {
  color: white;
}

/* Responsive */
@media (max-width: 768px) {
  .contact-page {
    padding: 60px 0 20px;
  }
  
  .contact-container {
    padding: 0 15px;
  }
  
  .contact-header,
  .contact-content {
    padding: 25px;
  }
  
  .contact-header h1 {
    font-size: 2rem;
  }
  
  .contact-cards {
    grid-template-columns: 1fr;
  }
  
  .links-grid {
    grid-template-columns: 1fr;
  }
}
</style>
