<template>
  <main class="not-found-container">
    <div class="not-found-content">
      <div class="error-animation">
        <div class="error-number">404</div>
        <div class="error-icon">🔍</div>
      </div>
      
      <h1 class="error-title">Page non trouvée</h1>
      <p class="error-message">
        D<PERSON>ol<PERSON>, la page que vous recherchez n'existe pas ou a été déplacée.
      </p>
      
      <div class="error-actions">
        <button @click="goHome" class="btn-home">
          <span aria-hidden="true">🏠</span>
          Retour à l'accueil
        </button>
        
        <button @click="goBack" class="btn-back">
          <span aria-hidden="true">←</span>
          Page précédente
        </button>
        
        <button @click="goToDashboard" class="btn-dashboard" v-if="isLoggedIn">
          <span aria-hidden="true">📊</span>
          Tableau de bord
        </button>
      </div>
      
      <div class="suggestions" v-if="isLoggedIn">
        <h2>Suggestions</h2>
        <div class="suggestions-grid">
          <router-link 
            v-if="userType === 'amical'"
            to="/events" 
            class="suggestion-card"
          >
            <span class="suggestion-icon">🎉</span>
            <span class="suggestion-text">Événements</span>
          </router-link>
          
          <router-link 
            v-if="userType === 'amoureux'"
            to="/profiles" 
            class="suggestion-card"
          >
            <span class="suggestion-icon">💕</span>
            <span class="suggestion-text">Profils</span>
          </router-link>
          
          <router-link 
            v-if="userType === 'libertin'"
            to="/community" 
            class="suggestion-card"
          >
            <span class="suggestion-icon">👥</span>
            <span class="suggestion-text">Communauté</span>
          </router-link>
          
          <router-link to="/wall" class="suggestion-card">
            <span class="suggestion-icon">📰</span>
            <span class="suggestion-text">Mur social</span>
          </router-link>
          
          <router-link to="/subscription" class="suggestion-card">
            <span class="suggestion-icon">⭐</span>
            <span class="suggestion-text">Premium</span>
          </router-link>
          
          <router-link to="/settings" class="suggestion-card">
            <span class="suggestion-icon">⚙️</span>
            <span class="suggestion-text">Paramètres</span>
          </router-link>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
export default {
  name: 'NotFoundView',
  
  computed: {
    isLoggedIn() {
      return localStorage.getItem('isLoggedIn') === 'true';
    },
    
    userType() {
      return localStorage.getItem('userType') || 'amical';
    }
  },
  
  methods: {
    goHome() {
      this.$router.push('/');
    },
    
    goBack() {
      this.$router.go(-1);
    },
    
    goToDashboard() {
      if (this.isLoggedIn) {
        this.$router.push('/dashboard');
      }
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
}

.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  color: var(--text-white);
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  width: 100%;
}

.error-animation {
  position: relative;
  margin-bottom: 40px;
}

.error-number {
  font-size: 8rem;
  font-weight: 900;
  color: var(--accent-blue);
  text-shadow: 0 0 20px rgba(0, 207, 255, 0.5);
  animation: pulse 2s infinite;
  margin-bottom: 20px;
}

.error-icon {
  font-size: 3rem;
  animation: bounce 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.error-title {
  font-size: 2.5rem;
  font-weight: 600;
  color: var(--accent-purple);
  margin-bottom: 16px;
}

.error-message {
  font-size: 1.2rem;
  color: var(--slogan-gray);
  line-height: 1.6;
  margin-bottom: 40px;
}

.error-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
  margin-bottom: 60px;
}

.btn-home,
.btn-back,
.btn-dashboard {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  text-decoration: none;
}

.btn-home {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-back {
  background: var(--wall-color);
  color: var(--text-white);
}

.btn-dashboard {
  background: var(--accent-purple);
  color: var(--text-white);
}

.btn-home:hover,
.btn-back:hover,
.btn-dashboard:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.suggestions {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 30px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.suggestions h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
}

.suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.suggestion-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  text-decoration: none;
  color: var(--text-white);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.suggestion-card:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4px);
  border-color: var(--accent-blue);
}

.suggestion-icon {
  font-size: 2rem;
  margin-bottom: 8px;
}

.suggestion-text {
  font-weight: 500;
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .error-number {
    font-size: 6rem;
  }
  
  .error-title {
    font-size: 2rem;
  }
  
  .error-message {
    font-size: 1rem;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .btn-home,
  .btn-back,
  .btn-dashboard {
    width: 200px;
    justify-content: center;
  }
  
  .suggestions-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .suggestions-grid {
    grid-template-columns: 1fr;
  }
}
</style>
