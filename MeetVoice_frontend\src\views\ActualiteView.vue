<!-- @format -->

<template>
  <div class="modern-articles-page">
 



    <!-- Modern Loading State -->
    <div v-if="loading" class="loading-section">
      <div class="container">
        <div class="loading-content">
          <div class="loading-spinner">
            <div class="spinner-circle"></div>
            <div class="spinner-dots">
              <div class="dot dot-1"></div>
              <div class="dot dot-2"></div>
              <div class="dot dot-3"></div>
            </div>
          </div>
          <h3 class="loading-title">Chargement des articles...</h3>
          <p class="loading-subtitle">Nous préparons le meilleur contenu pour vous</p>
        </div>
      </div>
    </div>

    <!-- Articles Grid -->
    <div v-else class="articles-section">
      <div class="container">
        <!-- Featured Articles -->
        <div v-if="featuredArticles.length > 0" class="featured-section mb-5">
          <h2 class="section-title">
            <i class="fas fa-star text-warning me-2"></i>
            Articles mis en avant
          </h2>
          <div class="row">
            <div
              v-for="article in featuredArticles"
              :key="'featured-' + article.id"
              class="col-lg-6 mb-4"
            >
              <div class="featured-card" @click="openArticle(article)">
                <div class="featured-image">
                  <img
                    :src="article.photo || '/api/static/default-article.jpg'"
                    :alt="`${article.titre} - Article sur ${article.theme}`"
                    class="img-fluid"
                    loading="lazy"
                    :width="600"
                    :height="400"
                  />
                  <div class="featured-badge">
                    <i class="fas fa-star"></i>
                  </div>
                </div>
                <div class="featured-content">
                  <div class="article-meta">
                    <span
                      class="category-badge"
                      :class="getCategoryClass(article.theme)"
                    >
                      {{ article.theme }}
                    </span>                   
                  </div>
                  <h3 class="featured-title">{{ article.titre }}</h3>
                  <p v-if="article.sous_titre" class="featured-subtitle">
                    {{ article.sous_titre }}
                  </p>
                  <p class="featured-description">
                    {{ article.petit_description }}
                  </p>
                  <div class="article-footer">
                    <div class="author-info">
                      <i class="fas fa-user me-1"></i>
                      {{ article.auteur.full_name }}
                    </div>
                    <div class="article-stats">
                      <span class="views">
                        <i class="fas fa-eye me-1"></i>
                        {{ article.access_count }}
                      </span>
                      <span class="reading-time">
                        <i class="fas fa-clock me-1"></i>
                        {{ article.reading_time }} min
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- All Articles -->
        <div class="all-articles">
          <h2 class="section-title">
            <i class="fas fa-newspaper me-2"></i>
            Tous les articles
          </h2>

          <div v-if="articles.length === 0" class="no-articles">
            <div class="text-center py-5">
              <i class="fas fa-newspaper fa-3x text-muted mb-3"></i>
              <h4>Aucun article disponible</h4>
              <p class="text-muted">
                Les articles seront bientôt disponibles
              </p>
            </div>
          </div>

          <div v-else class="row">
            <div
              v-for="article in articles"
              :key="article.id"
              class="col-lg-4 col-md-6 mb-4"
            >
              <div class="article-card" @click="openArticle(article)">
                <div class="article-image">
                  <img
                    :src="article.photo || '/api/static/default-article.jpg'"
                    :alt="`${article.titre} - Article sur ${article.theme}`"
                    class="img-fluid"
                    loading="lazy"
                    :width="400"
                    :height="300"
                  />
                  <div class="article-overlay">
                    <button class="btn btn-primary btn-sm">
                      <i class="fas fa-eye me-1"></i>
                      Lire l'article
                    </button>
                  </div>
                </div>
                <div class="article-content">
                  <div class="article-meta">
                    <span
                      class="category-badge"
                      :class="getCategoryClass(article.theme)"
                    >
                      {{ article.theme }}
                    </span>
                  </div>
                  <h4 class="article-title">{{ article.titre }}</h4>
                  <p
                    v-if="article.petit_description"
                    class="article-description"
                  >
                    {{ article.petit_description }}
                  </p>
                  <div class="article-footer">
                    <div class="article-stats-bottom">
                      <span class="views-bottom">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z" stroke="currentColor" stroke-width="2"/>
                          <circle cx="12" cy="12" r="3" stroke="currentColor" stroke-width="2"/>
                        </svg>
                        {{ article.access_count }}
                      </span>
                      <span class="date-bottom">{{
                        formatDate(article.date_publication)
                      }}</span>
                    </div>
                  </div>

                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>


  </div>
</template>

<script>
import { accountService } from "@/_services";
import SEOManager from '@/utils/seo.js';
import { generateArticleUrl } from '@/utils/slug.js';

export default {
  name: "ActualiteView",
  data() {
    return {
      articles: [],
      featuredArticles: [],
      loading: true,
    };
  },

  async mounted() {
    await this.loadArticles();
    this.updateSEO();
  },
  methods: {
    async loadArticles() {
      try {
        this.loading = true;

        // Charger les articles depuis l'API réelle
        const response = await fetch(
          "http://127.0.0.1:8000/actualite/api/articles/published/",
          {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            mode: 'cors', // Permet les requêtes cross-origin
          }
        );

        if (!response.ok) {
          throw new Error(`Erreur HTTP: ${response.status}`);
        }

        const data = await response.json();
        console.log("Réponse complète de l'API:", data);

        // L'API retourne un objet avec 'results' contenant les articles
        const articles = data.results || data;
        console.log("Articles extraits:", articles);



        this.articles = articles;

        // Séparer les articles mis en avant
        this.featuredArticles = this.articles.filter(
          (article) => article.mis_en_avant
        );

      } catch (error) {
        console.error("Erreur lors du chargement des articles:", error);

        // En cas d'erreur, afficher un message détaillé
        this.articles = [];
        this.featuredArticles = [];

        // Message d'erreur plus informatif
        if (error.name === 'TypeError' && error.message.includes('fetch')) {
          alert("❌ Impossible de se connecter à l'API.\n\n🔧 Vérifiez que :\n- L'API Django est démarrée sur le port 8000\n- L'URL est correcte : http://127.0.0.1:8000/actualite/api/articles/published/");
        } else {
          alert(`❌ Erreur lors du chargement des articles :\n${error.message}`);
        }
      } finally {
        this.loading = false;
      }
    },





    openArticle(article) {
      // Naviguer vers la page de détail de l'article avec slug SEO
      const articleUrl = generateArticleUrl(article);
      this.$router.push(articleUrl);

      // Incrémenter le compteur de vues
      this.incrementViews(article.id);
    },

    async incrementViews(articleId) {
      try {
        await fetch(
          `http://127.0.0.1:8000/actualite/api/articles/${articleId}/increment_views/`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // Mettre à jour le compteur localement
        const article = this.articles.find((a) => a.id === articleId);
        if (article) {
          article.access_count++;
        }
      } catch (error) {
        console.error("Erreur lors de l'incrémentation des vues:", error);
      }
    },

    updateSEO() {
      SEOManager.setPageSEO(
        'Actualités et Articles',
        'Découvrez nos derniers articles et actualités sur les rencontres, relations et tendances actuelles. Restez informé avec MeetVoice.'
      );
    },

    getCategoryClass(category) {
      return {
        "category-recruteur": category === "recruteur",
        "category-culture": category === "culture",
      };
    },



    formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleDateString("fr-FR", {
        year: "numeric",
        month: "long",
        day: "numeric",
      });
    },
  },
};
</script>

<style scoped>
/* Modern CSS Variables */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --accent-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  --primary-color: #667eea;
  --secondary-color: #f093fb;
  --accent-color: #4facfe;
  --dark-color: #2c3e50;
  --light-color: #ffffff;
  --gray-light: #f8fafc;
  --gray-medium: #64748b;
  --gray-dark: #334155;

  --border-radius-sm: 8px;
  --border-radius: 16px;
  --border-radius-lg: 24px;
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.05);
  --shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 40px rgba(0, 0, 0, 0.15);
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Main Container */
.modern-articles-page {
  min-height: 100vh;
  background: #f8fafc;
  padding-top: 0; /* Supprimé car .main-content a déjà padding-top: 80px */
  overflow-y: auto;
}

/* Simple Header */
.simple-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 60px 0;
  text-align: center;
}

.page-title {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 16px;
}

.page-subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  margin: 0;
}

/* Hero Banner */
.hero-banner {
  position: relative;
  background: var(--primary-gradient);
  padding: 80px 0 60px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0.1;
}

.floating-shapes {
  position: absolute;
  width: 100%;
  height: 100%;
}

.shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.shape-1 {
  width: 80px;
  height: 80px;
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.shape-2 {
  width: 120px;
  height: 120px;
  top: 60%;
  right: 15%;
  animation-delay: 2s;
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 30%;
  left: 20%;
  animation-delay: 4s;
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 10%;
  right: 30%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
  position: relative;
  z-index: 2;
}

.hero-text {
  text-align: center;
  margin-bottom: 40px;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: 800;
  margin-bottom: 20px;
  line-height: 1.1;
}

.title-highlight {
  color: rgba(255, 255, 255, 0.9);
  display: block;
  font-size: 2rem;
  font-weight: 400;
  margin-bottom: 10px;
}

.title-main {
  color: white;
  background: linear-gradient(45deg, #fff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.85);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Modern Search */
.search-container {
  max-width: 600px;
  margin: 0 auto;
}

.search-wrapper {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-lg);
  padding: 4px;
  box-shadow: var(--shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-medium);
  z-index: 2;
}

.search-input {
  width: 100%;
  padding: 16px 50px 16px 50px;
  border: none;
  background: transparent;
  font-size: 1.1rem;
  color: var(--dark-color);
  border-radius: var(--border-radius);
  outline: none;
  transition: var(--transition);
}

.search-input::placeholder {
  color: var(--gray-medium);
}

.search-input:focus {
  background: white;
}

.clear-search {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-medium);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: var(--transition);
}

.clear-search:hover {
  background: var(--gray-light);
  color: var(--dark-color);
}

/* Modern Filters Section */
.filters-section {
  padding: 40px 0;
  background: white;
}

.filters-container {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 30px;
  box-shadow: var(--shadow);
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.filters-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--gray-light);
}

.filters-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin: 0;
}

.results-count {
  background: var(--accent-gradient);
  color: white;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 0.9rem;
  font-weight: 600;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
}

.filter-label {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--gray-dark);
  margin-bottom: 8px;
}

.custom-select {
  position: relative;
}

.filter-select {
  width: 100%;
  padding: 12px 40px 12px 16px;
  border: 2px solid var(--gray-light);
  border-radius: var(--border-radius);
  background: white;
  font-size: 1rem;
  color: var(--dark-color);
  cursor: pointer;
  transition: var(--transition);
  appearance: none;
}

.filter-select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.select-arrow {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-medium);
  pointer-events: none;
}

.reset-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--secondary-gradient);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  width: 100%;
  justify-content: center;
}

.reset-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow);
}

/* Modern Loading */
.loading-section {
  padding: 80px 0;
  text-align: center;
}

.loading-content {
  max-width: 400px;
  margin: 0 auto;
}

.loading-spinner {
  position: relative;
  width: 80px;
  height: 80px;
  margin: 0 auto 30px;
}

.spinner-circle {
  width: 80px;
  height: 80px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.spinner-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  gap: 4px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #667eea;
  animation: pulse 1.5s ease-in-out infinite;
}

.dot-1 {
  animation-delay: 0s;
}

.dot-2 {
  animation-delay: 0.3s;
  background: #f093fb;
}

.dot-3 {
  animation-delay: 0.6s;
  background: #4facfe;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1.2);
    opacity: 1;
  }
}

.loading-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 10px;
}

.loading-subtitle {
  color: var(--gray-medium);
  font-size: 1rem;
}

/* Articles Section */
.articles-section {
  padding: 60px 0;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--dark-color);
  margin-bottom: 40px;
  display: flex;
  align-items: center;
  gap: 15px;
}

.section-title i {
  font-size: 2rem;
}

.article-count {
  font-size: 1rem;
  color: var(--gray-medium);
  font-weight: 500;
  background: var(--gray-light);
  padding: 4px 12px;
  border-radius: var(--border-radius);
}

/* Featured Articles */
.featured-section {
  margin-bottom: 80px;
}

.featured-card {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.featured-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-lg);
}

.featured-image {
  position: relative;
  height: 280px;
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.featured-card:hover .featured-image img {
  transform: scale(1.08);
}

.featured-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--secondary-gradient);
  color: white;
  padding: 12px;
  border-radius: 50%;
  font-size: 1rem;
  box-shadow: var(--shadow);
  display: flex;
  align-items: center;
  justify-content: center;
}

.featured-content {
  padding: 30px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.featured-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 12px;
  line-height: 1.3;
}

.featured-subtitle {
  font-size: 1.2rem;
  color: var(--gray-medium);
  margin-bottom: 16px;
  font-weight: 500;
}

.featured-description {
  color: var(--gray-medium);
  margin-bottom: 24px;
  flex: 1;
  line-height: 1.6;
  font-size: 1.05rem;
}

/* Regular Articles */
.article-card {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  cursor: pointer;
  height: 100%;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.article-card:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-lg);
}

.article-image {
  position: relative;
  height: 220px;
  overflow: hidden;
}

.article-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.article-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(240, 147, 251, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: var(--transition);
}

.article-card:hover .article-overlay {
  opacity: 1;
}

.article-card:hover .article-image img {
  transform: scale(1.08);
}

.article-overlay .btn {
  background: white;
  color: var(--primary-color);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 600;
  transform: translateY(20px);
  transition: var(--transition);
}

.article-card:hover .article-overlay .btn {
  transform: translateY(0);
}

.article-content {
  padding: 24px;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.article-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: var(--dark-color);
  margin-bottom: 12px;
  line-height: 1.3;
}

.article-description {
  color: var(--gray-medium);
  margin-bottom: 20px;
  flex: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.6;
}

/* Article Meta */
.article-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.category-badge, .category-tag {
  padding: 6px 16px;
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 700;
  text-transform: capitalize; /* Première lettre en majuscule */
  letter-spacing: 0.5px;
  border: 2px solid rgba(255, 255, 255, 0.3); /* Encadrement */
}

.category-recruteur {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
}

.category-culture {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  color: white;
}

.theme-badge, .theme-tag {
  padding: 6px 16px;
  background: var(--gray-light);
  color: var(--gray-dark);
  border-radius: var(--border-radius);
  font-size: 0.8rem;
  font-weight: 600;
}

.date {
  font-size: 0.9rem;
  color: var(--gray-medium);
  font-weight: 500;
}

/* Article Footer */
.article-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  padding-top: 20px;
}

.article-stats-bottom {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  font-size: 0.9rem;
}

.date-bottom {
  color: var(--gray-medium);
  font-weight: 500;
}

.views-bottom {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--gray-medium);
  font-weight: 500;
}

.views-bottom svg {
  color: var(--primary-color);
  flex-shrink: 0;
}



.author-name {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--dark-color);
}

.publish-date {
  font-size: 0.8rem;
  color: var(--gray-medium);
}

.article-stats {
  display: flex;
  gap: 16px;
  font-size: 0.9rem;
  color: var(--gray-medium);
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 500;
}



/* Modern Pagination */
.pagination-section {
  margin-top: 60px;
  text-align: center;
}

.pagination {
  display: inline-flex;
  gap: 8px;
  background: white;
  padding: 16px;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow);
}

.pagination .page-link {
  border: none;
  color: var(--dark-color);
  padding: 12px 16px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-weight: 600;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pagination .page-link:hover {
  background: var(--primary-gradient);
  color: white;
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-sm);
}

.pagination .page-item.disabled .page-link {
  color: var(--gray-medium);
  cursor: not-allowed;
  opacity: 0.5;
}

/* Empty State */
.no-articles {
  background: white;
  border-radius: var(--border-radius-lg);
  padding: 60px 40px;
  text-align: center;
  box-shadow: var(--shadow);
  margin: 40px 0;
}

.no-articles i {
  color: var(--gray-medium);
  margin-bottom: 20px;
}

.no-articles h4 {
  color: var(--dark-color);
  font-weight: 700;
  margin-bottom: 12px;
}

.no-articles p {
  color: var(--gray-medium);
  font-size: 1.1rem;
}

/* Modal de détail d'article */
.article-detail {
  max-width: none;
}

.article-detail-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: var(--dark-color);
  margin-bottom: 1rem;
  line-height: 1.2;
}

.article-detail-subtitle {
  font-size: 1.5rem;
  color: #6c757d;
  margin-bottom: 2rem;
  font-weight: normal;
}

.article-detail-meta {
  background: var(--light-color);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.article-detail-meta > div {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #6c757d;
}

.article-detail-image {
  margin: 2rem 0;
  text-align: center;
}

.article-detail-image img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}

.article-detail-content {
  font-size: 1.1rem;
  line-height: 1.8;
  color: #333;
  margin-bottom: 2rem;
}

.article-detail-content h1,
.article-detail-content h2,
.article-detail-content h3,
.article-detail-content h4,
.article-detail-content h5,
.article-detail-content h6 {
  color: var(--dark-color);
  margin-top: 2rem;
  margin-bottom: 1rem;
}

.article-detail-content p {
  margin-bottom: 1.5rem;
}

.article-detail-content img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
  margin: 1rem 0;
}

.article-detail-tags,
.article-detail-collaborators {
  background: var(--light-color);
  padding: 1.5rem;
  border-radius: var(--border-radius);
  margin-top: 2rem;
}

.article-detail-tags h5,
.article-detail-collaborators h5 {
  margin-bottom: 1rem;
  color: var(--dark-color);
}

.article-modal-meta {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .filters-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .hero-title {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .modern-articles-page {
    padding-top: 0; /* Supprimé car .main-content a déjà padding-top: 80px */
  }

  .hero-banner {
    padding: 60px 0 40px;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .title-highlight {
    font-size: 1.5rem;
  }

  .hero-description {
    font-size: 1.1rem;
  }

  .search-container {
    margin-top: 30px;
  }

  .filters-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .filters-container {
    padding: 20px;
  }

  .section-title {
    font-size: 2rem;
  }

  .featured-card {
    margin-bottom: 30px;
  }

  .featured-image {
    height: 240px;
  }

  .featured-content {
    padding: 24px;
  }

  .featured-title {
    font-size: 1.5rem;
  }

  .article-image {
    height: 200px;
  }

  .article-content {
    padding: 20px;
  }

  .article-title {
    font-size: 1.25rem;
  }

  .article-footer {
    justify-content: flex-end;
  }

  .article-stats-bottom {
    gap: 8px;
    font-size: 0.85rem;
  }
}

@media (max-width: 576px) {
  .hero-banner {
    padding: 40px 0 30px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .title-highlight {
    font-size: 1.25rem;
  }

  .hero-description {
    font-size: 1rem;
  }

  .search-wrapper {
    padding: 3px;
  }

  .search-input {
    padding: 14px 40px 14px 40px;
    font-size: 1rem;
  }

  .filters-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .filters-title {
    font-size: 1.25rem;
  }

  .section-title {
    font-size: 1.75rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .featured-image {
    height: 200px;
  }

  .featured-content {
    padding: 20px;
  }

  .featured-title {
    font-size: 1.35rem;
  }

  .article-image {
    height: 180px;
  }

  .article-content {
    padding: 16px;
  }

  .article-title {
    font-size: 1.2rem;
  }



  .pagination {
    padding: 12px;
    gap: 4px;
  }

  .pagination .page-link {
    padding: 8px 12px;
    min-width: 36px;
  }

  .loading-spinner {
    width: 60px;
    height: 60px;
  }

  .loading-title {
    font-size: 1.25rem;
  }

  .shape {
    display: none;
  }
}

@media (max-width: 480px) {
  .container {
    padding-left: 16px;
    padding-right: 16px;
  }

  .filters-container {
    padding: 16px;
  }

  .featured-content,
  .article-content {
    padding: 16px;
  }

  .hero-description {
    font-size: 0.95rem;
  }
}
</style>
