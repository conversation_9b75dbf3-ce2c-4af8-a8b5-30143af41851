import { config } from '@vue/test-utils'

// Configuration globale pour Vue Test Utils
config.global.mocks = {
  $t: (key) => key, // Mock pour i18n
  $tc: (key) => key,
  $te: () => true,
  $d: (date) => date,
  $n: (number) => number
}

// Mock des APIs du navigateur
global.navigator = {
  ...global.navigator,
  onLine: true,
  serviceWorker: {
    register: jest.fn(() => Promise.resolve()),
    ready: Promise.resolve({
      showNotification: jest.fn()
    })
  },
  permissions: {
    query: jest.fn(() => Promise.resolve({ state: 'granted' }))
  }
}

// Mock de localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
global.localStorage = localStorageMock

// Mock de sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
global.sessionStorage = sessionStorageMock

// Mock de fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob())
  })
)

// Mock de WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  readyState: 1, // OPEN
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3
}))

// Mock de IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Mock de ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn()
}))

// Mock de MutationObserver
global.MutationObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  disconnect: jest.fn()
}))

// Mock de matchMedia
global.matchMedia = jest.fn().mockImplementation(query => ({
  matches: false,
  media: query,
  onchange: null,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn()
}))

// Mock de URL
global.URL = {
  createObjectURL: jest.fn(() => 'mock-url'),
  revokeObjectURL: jest.fn()
}

// Mock de FileReader
global.FileReader = jest.fn().mockImplementation(() => ({
  readAsDataURL: jest.fn(),
  readAsText: jest.fn(),
  readAsArrayBuffer: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  result: null,
  error: null,
  readyState: 0
}))

// Mock de Notification
global.Notification = jest.fn().mockImplementation(() => ({
  close: jest.fn()
}))
global.Notification.permission = 'granted'
global.Notification.requestPermission = jest.fn(() => Promise.resolve('granted'))

// Mock de Audio
global.Audio = jest.fn().mockImplementation(() => ({
  play: jest.fn(() => Promise.resolve()),
  pause: jest.fn(),
  load: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  volume: 1,
  currentTime: 0,
  duration: 0,
  paused: true,
  ended: false
}))

// Mock de console pour réduire le bruit dans les tests
const originalConsole = global.console
global.console = {
  ...originalConsole,
  log: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
}

// Restaurer console.error pour les vrais erreurs de test
const originalError = originalConsole.error
global.console.error = (...args) => {
  // Ignorer certains warnings Vue dans les tests
  const message = args[0]
  if (
    typeof message === 'string' &&
    (message.includes('[Vue warn]') ||
     message.includes('Download the Vue Devtools'))
  ) {
    return
  }
  originalError(...args)
}

// Mock de window.location
delete window.location
window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  protocol: 'http:',
  host: 'localhost:3000',
  hostname: 'localhost',
  port: '3000',
  pathname: '/',
  search: '',
  hash: '',
  assign: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn()
}

// Mock de window.history
window.history = {
  pushState: jest.fn(),
  replaceState: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  go: jest.fn(),
  length: 1,
  state: null
}

// Mock de document.cookie
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: ''
})

// Mock de document.hidden
Object.defineProperty(document, 'hidden', {
  writable: true,
  value: false
})

// Mock de document.visibilityState
Object.defineProperty(document, 'visibilityState', {
  writable: true,
  value: 'visible'
})

// Helpers pour les tests
global.flushPromises = () => new Promise(resolve => setImmediate(resolve))

global.nextTick = () => new Promise(resolve => process.nextTick(resolve))

// Mock de requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 16))
global.cancelAnimationFrame = jest.fn(id => clearTimeout(id))

// Mock de requestIdleCallback
global.requestIdleCallback = jest.fn(cb => setTimeout(cb, 1))
global.cancelIdleCallback = jest.fn(id => clearTimeout(id))

// Configuration pour les tests async
jest.setTimeout(10000)

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks()
  localStorage.clear()
  sessionStorage.clear()
  
  // Réinitialiser les mocks de fetch
  fetch.mockClear()
  
  // Réinitialiser les timers si utilisés
  if (jest.isMockFunction(setTimeout)) {
    jest.runOnlyPendingTimers()
    jest.useRealTimers()
  }
})

// Configuration pour les tests de composants Vue
beforeEach(() => {
  // Réinitialiser les mocks avant chaque test
  jest.clearAllMocks()
})
