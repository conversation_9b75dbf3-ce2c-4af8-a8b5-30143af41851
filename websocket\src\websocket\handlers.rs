use anyhow::Result;
use chrono::Utc;
use tracing::{info, warn, error};
use uuid::Uuid;

use crate::models::*;
use crate::websocket::connection::WebSocketConnection;
use crate::AppState;

pub struct MessageHandler {
    state: AppState,
}

impl MessageHandler {
    pub fn new(state: AppState) -> Self {
        Self { state }
    }
    
    pub async fn handle_message(
        &mut self,
        message: WebSocketMessage,
        connection: &WebSocketConnection,
        authenticated_user_id: Option<Uuid>,
    ) -> Result<Option<Uuid>> {
        match message {
            // Authentification
            WebSocketMessage::Auth { token } => {
                self.handle_auth(token, connection).await
            }
            
            // Messages de chat
            WebSocketMessage::SendMessage { room_id, content, message_type } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_send_message(user_id, room_id, content, message_type, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            // Sessions vocales
            WebSocketMessage::JoinVoiceSession { session_id } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_join_voice_session(user_id, session_id, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            WebSocketMessage::LeaveVoiceSession { session_id } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_leave_voice_session(user_id, session_id, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            // WebRTC
            WebSocketMessage::WebRTCOffer { to_user, offer_type, sdp } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_webrtc_offer(user_id, to_user, offer_type, sdp, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            WebSocketMessage::WebRTCAnswer { to_user, sdp } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_webrtc_answer(user_id, to_user, sdp, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            WebSocketMessage::ICECandidate { to_user, candidate } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_ice_candidate(user_id, to_user, candidate, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            // P2P
            WebSocketMessage::P2PConnect { peer_id } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_p2p_connect(user_id, peer_id, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            WebSocketMessage::P2PDisconnect { peer_id } => {
                if let Some(user_id) = authenticated_user_id {
                    self.handle_p2p_disconnect(user_id, peer_id, connection).await?;
                } else {
                    self.send_auth_error(connection).await?;
                }
                Ok(authenticated_user_id)
            }
            
            // Système
            WebSocketMessage::Ping => {
                connection.send_message(WebSocketMessage::Pong).await?;
                Ok(authenticated_user_id)
            }
            
            _ => {
                warn!("Message non géré: {:?}", message);
                Ok(authenticated_user_id)
            }
        }
    }
    
    async fn handle_auth(&self, token: String, connection: &WebSocketConnection) -> Result<Option<Uuid>> {
        // Simulation d'authentification - en production, vérifiez le JWT
        // Pour ce demo, nous utilisons le token comme user_id
        match Uuid::parse_str(&token) {
            Ok(user_id) => {
                // Vérifier si l'utilisateur existe dans PostgreSQL
                match self.state.db.postgres.get_user_by_id(user_id).await {
                    Ok(Some(user)) => {
                        info!("✅ Utilisateur authentifié: {}", user.username);
                        
                        // Mettre à jour l'activité utilisateur
                        let _ = self.state.db.postgres.update_user_activity(user_id).await;
                        
                        connection.send_message(WebSocketMessage::AuthSuccess { user_id }).await?;
                        Ok(Some(user_id))
                    }
                    Ok(None) => {
                        warn!("❌ Utilisateur non trouvé: {}", user_id);
                        connection.send_message(WebSocketMessage::AuthError {
                            message: "Utilisateur non trouvé".to_string(),
                        }).await?;
                        Ok(None)
                    }
                    Err(e) => {
                        error!("❌ Erreur d'authentification: {}", e);
                        connection.send_message(WebSocketMessage::AuthError {
                            message: "Erreur de base de données".to_string(),
                        }).await?;
                        Ok(None)
                    }
                }
            }
            Err(_) => {
                warn!("❌ Token invalide: {}", token);
                connection.send_message(WebSocketMessage::AuthError {
                    message: "Token invalide".to_string(),
                }).await?;
                Ok(None)
            }
        }
    }
    
    async fn handle_send_message(
        &self,
        user_id: Uuid,
        room_id: String,
        content: String,
        message_type: MessageType,
        connection: &WebSocketConnection,
    ) -> Result<()> {
        let message = ChatMessage {
            id: None,
            user_id,
            room_id: room_id.clone(),
            content,
            message_type: message_type.clone(),
            timestamp: Utc::now(),
            metadata: None,
        };
        
        // Sauvegarder en MongoDB
        self.state.db.save_chat_message(&message).await?;
        
        // Joindre la salle si pas déjà fait
        self.state.websocket.join_room(user_id, room_id.clone()).await;
        
        // Diffuser le message à tous les utilisateurs de la salle
        let broadcast_message = WebSocketMessage::MessageReceived(message);
        self.state.websocket.broadcast_to_room(&room_id, broadcast_message, Some(user_id)).await?;
        
        info!("💬 Message envoyé par {} dans la salle {}", user_id, room_id);
        Ok(())
    }
    
    async fn handle_join_voice_session(
        &self,
        user_id: Uuid,
        session_id: String,
        connection: &WebSocketConnection,
    ) -> Result<()> {
        // Joindre la session vocale
        self.state.websocket.join_voice_session(user_id, session_id.clone()).await;
        
        // Obtenir la liste des participants
        let participants = self.state.websocket.get_voice_session_users(&session_id);
        
        // Créer ou mettre à jour la session en MongoDB
        let voice_session = VoiceSession {
            id: None,
            session_id: session_id.clone(),
            participants: participants.clone(),
            status: SessionStatus::Active,
            started_at: Utc::now(),
            ended_at: None,
            quality_metrics: None,
        };
        
        self.state.db.create_voice_session(&voice_session).await?;
        
        // Notifier l'utilisateur
        connection.send_message(WebSocketMessage::VoiceSessionJoined {
            session_id: session_id.clone(),
            participants: participants.clone(),
        }).await?;
        
        // Notifier les autres participants
        let notification = WebSocketMessage::VoiceSessionJoined {
            session_id: session_id.clone(),
            participants,
        };
        self.state.websocket.broadcast_to_voice_session(&session_id, notification, Some(user_id)).await?;
        
        info!("🎤 Utilisateur {} a rejoint la session vocale {}", user_id, session_id);
        Ok(())
    }
    
    async fn handle_leave_voice_session(
        &self,
        user_id: Uuid,
        session_id: String,
        connection: &WebSocketConnection,
    ) -> Result<()> {
        // Quitter la session vocale
        self.state.websocket.leave_voice_session(user_id, session_id.clone()).await;
        
        // Mettre à jour le statut en MongoDB
        let remaining_users = self.state.websocket.get_voice_session_users(&session_id);
        if remaining_users.is_empty() {
            self.state.db.update_voice_session_status(&session_id, SessionStatus::Ended).await?;
        }
        
        // Notifier l'utilisateur
        connection.send_message(WebSocketMessage::VoiceSessionLeft {
            session_id: session_id.clone(),
        }).await?;
        
        // Notifier les autres participants
        let notification = WebSocketMessage::VoiceSessionLeft {
            session_id: session_id.clone(),
        };
        self.state.websocket.broadcast_to_voice_session(&session_id, notification, Some(user_id)).await?;
        
        info!("🎤 Utilisateur {} a quitté la session vocale {}", user_id, session_id);
        Ok(())
    }
    
    async fn handle_webrtc_offer(
        &self,
        from_user: Uuid,
        to_user: Uuid,
        offer_type: OfferType,
        sdp: String,
        _connection: &WebSocketConnection,
    ) -> Result<()> {
        // Créer l'offre WebRTC
        let offer = WebRTCOffer {
            id: None,
            from_user,
            to_user,
            offer_type: offer_type.clone(),
            sdp: sdp.clone(),
            ice_candidates: Vec::new(),
            created_at: Utc::now(),
            expires_at: Utc::now() + chrono::Duration::minutes(5), // Expire dans 5 minutes
        };
        
        // Sauvegarder en MongoDB
        self.state.db.save_webrtc_offer(&offer).await?;
        
        // Transmettre l'offre au destinataire
        let message = WebSocketMessage::WebRTCOffer {
            to_user,
            offer_type,
            sdp,
        };
        self.state.websocket.send_to_user(to_user, message).await?;
        
        info!("📞 Offre WebRTC envoyée de {} vers {}", from_user, to_user);
        Ok(())
    }
    
    async fn handle_webrtc_answer(
        &self,
        from_user: Uuid,
        to_user: Uuid,
        sdp: String,
        _connection: &WebSocketConnection,
    ) -> Result<()> {
        // Transmettre la réponse
        let message = WebSocketMessage::WebRTCAnswer { to_user, sdp };
        self.state.websocket.send_to_user(to_user, message).await?;
        
        info!("📞 Réponse WebRTC envoyée de {} vers {}", from_user, to_user);
        Ok(())
    }
    
    async fn handle_ice_candidate(
        &self,
        from_user: Uuid,
        to_user: Uuid,
        candidate: String,
        _connection: &WebSocketConnection,
    ) -> Result<()> {
        // Transmettre le candidat ICE
        let message = WebSocketMessage::ICECandidate { to_user, candidate };
        self.state.websocket.send_to_user(to_user, message).await?;
        
        info!("🧊 Candidat ICE envoyé de {} vers {}", from_user, to_user);
        Ok(())
    }
    
    async fn handle_p2p_connect(
        &self,
        user_id: Uuid,
        peer_id: String,
        connection: &WebSocketConnection,
    ) -> Result<()> {
        // Déléguer au gestionnaire P2P
        match self.state.p2p.connect_to_peer(&peer_id).await {
            Ok(_) => {
                info!("🔗 Connexion P2P établie pour l'utilisateur {} vers {}", user_id, peer_id);
            }
            Err(e) => {
                warn!("❌ Erreur de connexion P2P pour {}: {}", user_id, e);
                connection.send_message(WebSocketMessage::Error {
                    message: format!("Erreur de connexion P2P: {}", e),
                }).await?;
            }
        }
        Ok(())
    }
    
    async fn handle_p2p_disconnect(
        &self,
        user_id: Uuid,
        peer_id: String,
        _connection: &WebSocketConnection,
    ) -> Result<()> {
        // Déléguer au gestionnaire P2P
        match self.state.p2p.disconnect_from_peer(&peer_id).await {
            Ok(_) => {
                info!("🔗 Déconnexion P2P pour l'utilisateur {} de {}", user_id, peer_id);
            }
            Err(e) => {
                warn!("❌ Erreur de déconnexion P2P pour {}: {}", user_id, e);
            }
        }
        Ok(())
    }
    
    async fn send_auth_error(&self, connection: &WebSocketConnection) -> Result<()> {
        connection.send_message(WebSocketMessage::AuthError {
            message: "Authentification requise".to_string(),
        }).await
    }
}
