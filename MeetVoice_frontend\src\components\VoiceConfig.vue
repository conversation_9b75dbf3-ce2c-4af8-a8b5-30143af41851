<template>
  <div class="voice-config-container">
    <div class="config-panel">
      <header class="config-header">
        <h2>Configuration Audio</h2>
        <p>Configurez votre voix et microphone avant de commencer l'interview</p>
      </header>

      <main class="config-content">
        
        <!-- Configuration simplifiée - Sophie utilise vitesse fixe 1.2 -->

        <!-- Configuration Microphone -->
        <section class="microphone-section">
          <h3>🎤 Configuration Microphone</h3>
          
          <div class="mic-config">
            <div class="config-item">
              <label for="mic-select">Microphone :</label>
              <select 
                id="mic-select"
                v-model="selectedMicrophoneId" 
                @change="saveMicrophoneConfig"
                class="config-select"
              >
                <option value="">Microphone par défaut</option>
                <option 
                  v-for="mic in availableMicrophones" 
                  :key="mic.deviceId" 
                  :value="mic.deviceId"
                >
                  {{ mic.label }}
                </option>
              </select>

            </div>
            
            <div class="mic-buttons">
              <button @click="testMicrophone" class="btn-test-mic" :disabled="isTestingMic">
                {{ isTestingMic ? '🔧 Test...' : '🔧 Tester' }}
              </button>
              <button @click="refreshMicrophones" class="btn-refresh-mic">
                🔄 Actualiser
              </button>
            </div>
            
            <div v-if="micTestResult" class="test-result">
              <div :class="['test-status', micTestResult.success ? 'success' : 'error']">
                {{ micTestResult.message }}
              </div>
            </div>
          </div>
        </section>

        <!-- Email -->
        <section class="email-section">
          <h3>📧 Votre Email</h3>
          <div class="email-input">
            <label for="email">Adresse email :</label>
            <input
              id="email"
              type="email"
              v-model="userEmail"
              @blur="validateEmail"
              @input="clearEmailValidation"
              placeholder="<EMAIL>"
              class="config-input"
              :class="{ 'error': emailError, 'valid': emailValid }"
              required
            />
            <div v-if="isCheckingEmail" class="email-checking">🔍 Vérification de l'email...</div>
            <div v-if="emailError" class="email-error">❌ {{ emailError }}</div>
            <div v-if="emailValid" class="email-success">✅ Email disponible</div>
          </div>
        </section>

        <!-- Mot de passe -->
        <section class="password-section">
          <h3>🔒 Votre Mot de Passe</h3>
          <div class="password-input">
            <label for="password">Mot de passe :</label>
            <input
              id="password"
              type="password"
              v-model="userPassword"
              @input="validatePassword"
              placeholder="Minimum 8 caractères"
              class="config-input"
              :class="{ 'error': passwordError, 'valid': passwordValid }"
              required
            />
            <div v-if="passwordError" class="password-error">❌ {{ passwordError }}</div>
            <div v-if="passwordValid" class="password-success">✅ Mot de passe valide</div>

            <div class="password-requirements">
              <p class="requirements-title">Le mot de passe doit contenir :</p>
              <ul class="requirements-list">
                <li :class="{ 'valid': hasMinLength }">✓ Au moins 8 caractères</li>
                <li :class="{ 'valid': hasUppercase }">✓ Une majuscule</li>
                <li :class="{ 'valid': hasLowercase }">✓ Une minuscule</li>
                <li :class="{ 'valid': hasNumber }">✓ Un chiffre</li>
              </ul>
            </div>
          </div>
        </section>

      </main>

      <footer class="config-footer">
        <button 
          @click="startInterview" 
          :disabled="!canStartInterview"
          class="btn-start-interview"
        >
          🎤 Commencer l'Interview Vocale
        </button>
        
        <p v-if="!canStartInterview" class="config-warning">
          Veuillez configurer votre microphone, saisir un email valide et créer un mot de passe sécurisé
        </p>
      </footer>

    </div>
  </div>
</template>

<script>
import { voiceService, ttsService } from '@/_services';

export default {
  name: 'VoiceConfig',
  
  data() {
    return {
      availableMicrophones: [],
      selectedMicrophoneId: '',
      userEmail: '',
      userPassword: '',
      isTestingVoice: false,
      isTestingMic: false,
      micTestResult: null,

      // Validation email
      emailError: '',
      emailValid: false,
      isCheckingEmail: false,

      // Validation mot de passe
      passwordError: '',
      passwordValid: false
    };
  },

  computed: {
    voiceConfig() {
      return this.$store.getters.getVoiceConfig;
    },
    
    availableVoices() {
      return this.$store.getters.getAvailableVoices;
    },
    
    canStartInterview() {
      return this.userEmail && this.userPassword && this.selectedMicrophoneId &&
             this.emailValid && !this.emailError && this.passwordValid && !this.passwordError;
    },

    // Validation mot de passe
    hasMinLength() {
      return this.userPassword.length >= 8;
    },

    hasUppercase() {
      return /[A-Z]/.test(this.userPassword);
    },

    hasLowercase() {
      return /[a-z]/.test(this.userPassword);
    },

    hasNumber() {
      return /\d/.test(this.userPassword);
    }
  },

  async mounted() {
    await this.initializeConfig();
  },

  methods: {
    async initializeConfig() {
      try {
        // Charger les configs sauvegardées
        this.$store.dispatch('loadSavedConfigs');
        
        // Charger les voix disponibles
        await this.$store.dispatch('loadAvailableVoices');
        
        // Charger les microphones
        await this.loadMicrophones();
        
        // Charger l'email sauvegardé
        this.userEmail = localStorage.getItem('meetvoice_email') || '';
        
        console.log('✅ Configuration initialisée');
        
      } catch (error) {
        console.error('❌ Erreur initialisation:', error);
      }
    },

    async loadMicrophones() {
      try {
        await navigator.mediaDevices.getUserMedia({ audio: true });
        const devices = await navigator.mediaDevices.enumerateDevices();
        
        this.availableMicrophones = devices
          .filter(device => device.kind === 'audioinput')
          .map(device => ({
            deviceId: device.deviceId,
            label: device.label || `Microphone ${device.deviceId.substring(0, 8)}`
          }));

        // Charger le microphone sauvegardé
        const savedMic = this.$store.getters.getSelectedMicrophone;
        if (savedMic && this.availableMicrophones.find(m => m.deviceId === savedMic)) {
          this.selectedMicrophoneId = savedMic;
        } else if (this.availableMicrophones.length > 0) {
          this.selectedMicrophoneId = this.availableMicrophones[0].deviceId;
        }

      } catch (error) {
        console.error('❌ Erreur microphones:', error);
      }
    },

    refreshMicrophones() {
      this.loadMicrophones();
    },

    // Configuration voix supprimée - Sophie utilise vitesse fixe 1.2

    async testMicrophone() {
      try {
        this.isTestingMic = true;
        this.micTestResult = null;
        
        const result = await voiceService.testMicrophone(this.selectedMicrophoneId);
        
        if (result.success) {
          this.micTestResult = {
            success: true,
            message: `✅ Microphone OK ! Niveau: ${(result.testResults.maxAudioLevel * 100).toFixed(1)}%`
          };
        } else {
          this.micTestResult = {
            success: false,
            message: `❌ Problème microphone: ${result.message}`
          };
        }
      } catch (error) {
        this.micTestResult = {
          success: false,
          message: `❌ Erreur test: ${error.message}`
        };
      } finally {
        this.isTestingMic = false;
      }
    },

    saveMicrophoneConfig() {
      this.$store.dispatch('updateMicrophoneConfig', {
        selectedMicrophoneId: this.selectedMicrophoneId
      });
    },

    // Vérifier email - version simplifiée
    async checkEmail(email) {
      const response = await fetch(`http://127.0.0.1:8000/api/check-email/?email=${email}`);
      const data = await response.json();
      return data.exists; // true si existe déjà
    },

    async validateEmail() {
      if (!this.userEmail) {
        this.emailError = '';
        this.emailValid = false;
        return;
      }

      // Validation format email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(this.userEmail)) {
        this.emailError = 'Format email invalide';
        this.emailValid = false;
        return;
      }

      try {
        this.isCheckingEmail = true;
        this.emailError = '';
        this.emailValid = false;

        // Utiliser la fonction simplifiée
        const exists = await this.checkEmail(this.userEmail);

        if (exists) {
          this.emailError = 'Cet email est déjà utilisé';
          this.emailValid = false;
        } else {
          this.emailValid = true;
          this.emailError = '';
        }

      } catch (error) {
        console.error('❌ Erreur validation email:', error);
        this.emailError = 'Erreur de connexion au serveur';
        this.emailValid = false;
      } finally {
        this.isCheckingEmail = false;
      }
    },

    clearEmailValidation() {
      this.emailError = '';
      this.emailValid = false;
      this.isCheckingEmail = false;
    },

    // Vérifier téléphone - version simplifiée
    async checkPhone(phone) {
      const response = await fetch(`http://127.0.0.1:8000/api/check-phone/?phone=${phone}`);
      const data = await response.json();
      return data.exists; // true si existe déjà
    },

    validatePassword() {
      if (!this.userPassword) {
        this.passwordError = '';
        this.passwordValid = false;
        return;
      }

      const errors = [];

      if (!this.hasMinLength) {
        errors.push('8 caractères minimum');
      }

      if (!this.hasUppercase) {
        errors.push('une majuscule');
      }

      if (!this.hasLowercase) {
        errors.push('une minuscule');
      }

      if (!this.hasNumber) {
        errors.push('un chiffre');
      }

      if (errors.length > 0) {
        this.passwordError = `Manque: ${errors.join(', ')}`;
        this.passwordValid = false;
      } else {
        this.passwordError = '';
        this.passwordValid = true;
      }
    },

    startInterview() {
      // Sauvegarder l'email et le mot de passe
      localStorage.setItem('meetvoice_email', this.userEmail);
      localStorage.setItem('meetvoice_password', this.userPassword);

      // Sauvegarder la config microphone finale
      this.saveMicrophoneConfig();

      // Émettre l'événement pour démarrer l'interview
      this.$emit('config-complete', {
        email: this.userEmail,
        password: this.userPassword,
        voiceConfig: this.voiceConfig,
        microphoneId: this.selectedMicrophoneId
      });
    }
  }
};
</script>

<style scoped>
.voice-config-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.config-panel {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 40px;
  max-width: 600px;
  width: 100%;
  color: white;
  backdrop-filter: blur(10px);
}

.config-header {
  text-align: center;
  margin-bottom: 40px;
}

.config-header h2 {
  font-size: 28px;
  margin-bottom: 10px;
}

.voice-section, .microphone-section, .email-section, .password-section {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.config-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.config-item label {
  font-size: 14px;
  font-weight: 500;
}

.config-select, .config-input {
  padding: 10px;
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.config-slider {
  width: 100%;
  height: 6px;
  background: #e5e7eb;
  border-radius: 3px;
  outline: none;
}

.btn-test-voice {
  padding: 12px 24px;
  background: #8b5cf6;
  border: none;
  border-radius: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-test-voice:hover {
  background: #7c3aed;
  transform: translateY(-1px);
}

.mic-buttons {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.btn-test-mic, .btn-refresh-mic {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.btn-test-mic {
  background: #8b5cf6;
}

.btn-test-mic:hover {
  background: #7c3aed;
}

.btn-refresh-mic {
  background: #6366f1;
}

.btn-refresh-mic:hover {
  background: #4f46e5;
}

.btn-start-interview {
  width: 100%;
  padding: 15px;
  background: #22c55e;
  border: none;
  border-radius: 12px;
  color: white;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-start-interview:hover:not(:disabled) {
  background: #16a34a;
  transform: translateY(-2px);
}

.btn-start-interview:disabled {
  background: #6b7280;
  cursor: not-allowed;
}

.config-warning {
  text-align: center;
  color: #fbbf24;
  margin-top: 10px;
  font-size: 14px;
}

.test-result {
  margin-top: 15px;
}

.test-status {
  padding: 10px;
  border-radius: 6px;
  font-size: 14px;
}

.test-status.success {
  background: rgba(34, 197, 94, 0.2);
  border: 1px solid #22c55e;
}

.test-status.error {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid #ef4444;
}

.voice-info {
  padding: 10px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
  margin: 0;
  font-weight: 500;
  color: white;
}

/* Validation email */
.config-input.error {
  border-color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

.config-input.valid {
  border-color: #22c55e;
  background: rgba(34, 197, 94, 0.1);
}

.email-checking {
  margin-top: 8px;
  color: #6366f1;
  font-size: 14px;
}

.email-error {
  margin-top: 8px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.email-success {
  margin-top: 8px;
  color: #22c55e;
  font-size: 14px;
  font-weight: 500;
}

/* Validation mot de passe */
.password-error {
  margin-top: 8px;
  color: #ef4444;
  font-size: 14px;
  font-weight: 500;
}

.password-success {
  margin-top: 8px;
  color: #22c55e;
  font-size: 14px;
  font-weight: 500;
}

.password-requirements {
  margin-top: 15px;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.requirements-title {
  margin: 0 0 10px 0;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.requirements-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.requirements-list li {
  padding: 4px 0;
  font-size: 13px;
  color: #ef4444;
  transition: color 0.3s ease;
}

.requirements-list li.valid {
  color: #22c55e;
}
</style>
