/**
 * Utilitaires SEO avancés pour MeetVoice
 */

export class AdvancedSEO {
  
  /**
   * Ajouter des liens internes intelligents
   */
  static addInternalLinks(content, articles) {
    if (!content || !articles) return content;
    
    let processedContent = content;
    
    // Mots-clés à lier automatiquement
    const keywords = {
      'rencontre': '/actualite?category=rencontre',
      'culture': '/actualite?category=culture',
      'tendance': '/actualite?category=tendance',
      'actualité': '/actualite',
      'MeetVoice': '/'
    };
    
    // Ajouter des liens vers d'autres articles
    articles.slice(0, 3).forEach(article => {
      const regex = new RegExp(`\\b${article.titre.substring(0, 20)}`, 'gi');
      if (regex.test(processedContent)) {
        processedContent = processedContent.replace(
          regex, 
          `<a href="/article/${article.slug || article.id}" class="internal-link">$&</a>`
        );
      }
    });
    
    return processedContent;
  }

  /**
   * Générer des FAQ automatiques
   */
  static generateFAQ(article) {
    const faqs = [
      {
        question: `Qu'est-ce que ${article.titre} ?`,
        answer: article.petit_description || article.titre
      },
      {
        question: `Quand a été publié cet article sur ${article.theme} ?`,
        answer: `Cet article a été publié le ${new Date(article.date_creation).toLocaleDateString('fr-FR')}`
      }
    ];

    return {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": faqs.map(faq => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    };
  }

  /**
   * Optimiser les méta-descriptions
   */
  static optimizeMetaDescription(article) {
    const baseDesc = article.petit_description || article.titre;
    const theme = article.theme;
    const readTime = Math.ceil((article.contenu?.length || 1000) / 1000);
    
    return `${baseDesc} | Article ${theme} - Lecture ${readTime} min | MeetVoice`;
  }

  /**
   * Générer des données structurées pour les articles liés
   */
  static generateRelatedArticlesSchema(articles) {
    return {
      "@context": "https://schema.org",
      "@type": "ItemList",
      "itemListElement": articles.map((article, index) => ({
        "@type": "ListItem",
        "position": index + 1,
        "item": {
          "@type": "Article",
          "@id": `${window.location.origin}/article/${article.slug || article.id}`,
          "headline": article.titre,
          "image": article.photo,
          "datePublished": article.date_creation
        }
      }))
    };
  }

  /**
   * Calculer le temps de lecture
   */
  static calculateReadingTime(content) {
    const wordsPerMinute = 200;
    const wordCount = content ? content.split(' ').length : 0;
    return Math.ceil(wordCount / wordsPerMinute);
  }

  /**
   * Générer des tags Open Graph optimisés
   */
  static generateOptimizedOGTags(article) {
    const readTime = this.calculateReadingTime(article.contenu);
    
    return {
      'og:type': 'article',
      'og:title': `${article.titre} | MeetVoice`,
      'og:description': this.optimizeMetaDescription(article),
      'og:image': article.photo,
      'og:url': window.location.href,
      'og:site_name': 'MeetVoice',
      'article:author': article.auteur?.full_name || 'MeetVoice',
      'article:published_time': article.date_creation,
      'article:modified_time': article.date_modification || article.date_creation,
      'article:section': article.theme,
      'article:tag': article.tags_list?.join(',') || article.theme,
      'twitter:card': 'summary_large_image',
      'twitter:title': article.titre,
      'twitter:description': this.optimizeMetaDescription(article),
      'twitter:image': article.photo,
      'twitter:label1': 'Temps de lecture',
      'twitter:data1': `${readTime} min`,
      'twitter:label2': 'Catégorie',
      'twitter:data2': article.theme
    };
  }

  /**
   * Ajouter des données de performance Web Vitals
   */
  static trackWebVitals() {
    if ('web-vital' in window) {
      import('web-vitals').then(({ getCLS, getFID, getFCP, getLCP, getTTFB }) => {
        getCLS(console.log);
        getFID(console.log);
        getFCP(console.log);
        getLCP(console.log);
        getTTFB(console.log);
      });
    }
  }

  /**
   * Optimiser les images pour le SEO
   */
  static optimizeImageSEO(img, article) {
    if (!img || !article) return;
    
    // Alt text optimisé
    img.alt = `${article.titre} - Image illustrant l'article sur ${article.theme}`;
    
    // Lazy loading
    img.loading = 'lazy';
    
    // Dimensions pour éviter le CLS
    if (!img.width) img.width = '800';
    if (!img.height) img.height = '600';
    
    // Titre pour l'accessibilité
    img.title = article.titre;
  }

  /**
   * Générer un sitemap dynamique côté client
   */
  static generateClientSitemap(articles) {
    const urls = [
      { loc: '/', priority: '1.0', changefreq: 'daily' },
      { loc: '/actualite', priority: '0.9', changefreq: 'daily' }
    ];
    
    articles.forEach(article => {
      urls.push({
        loc: `/article/${article.slug || article.id}`,
        priority: '0.8',
        changefreq: 'weekly',
        lastmod: article.date_modification || article.date_creation
      });
    });
    
    return urls;
  }

  /**
   * Analyser la densité des mots-clés
   */
  static analyzeKeywordDensity(content, keyword) {
    if (!content || !keyword) return 0;
    
    const words = content.toLowerCase().split(/\s+/);
    const keywordCount = words.filter(word => 
      word.includes(keyword.toLowerCase())
    ).length;
    
    return (keywordCount / words.length) * 100;
  }

  /**
   * Suggestions d'amélioration SEO
   */
  static getSEOSuggestions(article) {
    const suggestions = [];
    
    // Vérifier la longueur du titre
    if (article.titre.length < 30) {
      suggestions.push('Titre trop court (< 30 caractères)');
    }
    if (article.titre.length > 60) {
      suggestions.push('Titre trop long (> 60 caractères)');
    }
    
    // Vérifier la méta-description
    const metaDesc = article.petit_description || '';
    if (metaDesc.length < 120) {
      suggestions.push('Méta-description trop courte (< 120 caractères)');
    }
    if (metaDesc.length > 160) {
      suggestions.push('Méta-description trop longue (> 160 caractères)');
    }
    
    // Vérifier la présence d'image
    if (!article.photo) {
      suggestions.push('Aucune image principale définie');
    }
    
    // Vérifier le contenu
    const contentLength = article.contenu?.length || 0;
    if (contentLength < 300) {
      suggestions.push('Contenu trop court (< 300 caractères)');
    }
    
    return suggestions;
  }
}

export default AdvancedSEO;
