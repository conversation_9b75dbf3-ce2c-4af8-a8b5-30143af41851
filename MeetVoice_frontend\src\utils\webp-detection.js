/**
 * Détection du support WebP et optimisation des images
 * Ajoute automatiquement les classes CSS appropriées
 */

export class WebPDetector {
  
  /**
   * Détecter le support WebP du navigateur
   */
  static async detectWebPSupport() {
    return new Promise((resolve) => {
      // Test avec une image WebP minimale
      const webpTestImage = 'data:image/webp;base64,UklGRiIAAABXRUJQVlA4IBYAAAAwAQCdASoBAAEADsD+JaQAA3AAAAAA';
      
      const img = new Image();
      
      img.onload = () => {
        // Si l'image se charge, WebP est supporté
        resolve(img.width > 0 && img.height > 0);
      };
      
      img.onerror = () => {
        // Si l'image ne se charge pas, WebP n'est pas supporté
        resolve(false);
      };
      
      img.src = webpTestImage;
    });
  }
  
  /**
   * Initialiser la détection WebP et ajouter les classes CSS
   */
  static async initWebPDetection() {
    try {
      const supportsWebP = await this.detectWebPSupport();
      
      // Ajouter la classe appropriée au document
      if (supportsWebP) {
        document.documentElement.classList.add('webp');
        document.documentElement.classList.remove('no-webp');
        console.log('✅ WebP supporté - Images optimisées activées');
      } else {
        document.documentElement.classList.add('no-webp');
        document.documentElement.classList.remove('webp');
        console.log('⚠️ WebP non supporté - Fallback vers images classiques');
      }
      
      // Stocker le résultat pour éviter de refaire le test
      window.webpSupported = supportsWebP;
      
      return supportsWebP;
    } catch (error) {
      console.error('Erreur lors de la détection WebP:', error);
      
      // En cas d'erreur, utiliser le fallback
      document.documentElement.classList.add('no-webp');
      window.webpSupported = false;
      
      return false;
    }
  }
  
  /**
   * Obtenir l'URL optimisée d'une image
   */
  static getOptimizedImageUrl(originalUrl, options = {}) {
    const { 
      quality = 85, 
      width = null, 
      height = null,
      format = 'auto'
    } = options;
    
    // Si WebP n'est pas supporté, retourner l'URL originale
    if (!window.webpSupported) {
      return originalUrl;
    }
    
    // Si l'image est déjà en WebP, la retourner
    if (originalUrl.includes('.webp')) {
      return originalUrl;
    }
    
    // Convertir l'extension en WebP
    const webpUrl = originalUrl.replace(/\.(jpg|jpeg|png|gif)$/i, '.webp');
    
    return webpUrl;
  }
  
  /**
   * Optimiser toutes les images existantes dans le DOM
   */
  static optimizeExistingImages() {
    const images = document.querySelectorAll('img[data-optimize]');
    
    images.forEach(img => {
      const originalSrc = img.getAttribute('data-src') || img.src;
      const optimizedSrc = this.getOptimizedImageUrl(originalSrc);
      
      if (optimizedSrc !== originalSrc) {
        // Créer un élément picture pour le fallback
        const picture = document.createElement('picture');
        
        // Source WebP
        if (window.webpSupported) {
          const sourceWebP = document.createElement('source');
          sourceWebP.srcset = optimizedSrc;
          sourceWebP.type = 'image/webp';
          picture.appendChild(sourceWebP);
        }
        
        // Fallback
        const imgClone = img.cloneNode(true);
        imgClone.src = originalSrc;
        picture.appendChild(imgClone);
        
        // Remplacer l'image originale
        img.parentNode.replaceChild(picture, img);
      }
    });
  }
  
  /**
   * Précharger les images critiques
   */
  static preloadCriticalImages(imageUrls) {
    imageUrls.forEach(url => {
      const link = document.createElement('link');
      link.rel = 'preload';
      link.as = 'image';
      
      // Utiliser la version optimisée si disponible
      link.href = this.getOptimizedImageUrl(url);
      
      if (window.webpSupported && url.includes('.webp')) {
        link.type = 'image/webp';
      }
      
      document.head.appendChild(link);
    });
  }
  
  /**
   * Lazy loading intelligent avec WebP
   */
  static setupIntelligentLazyLoading() {
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            const src = img.getAttribute('data-src');
            
            if (src) {
              // Utiliser l'URL optimisée
              img.src = this.getOptimizedImageUrl(src);
              img.removeAttribute('data-src');
              img.classList.remove('lazy');
              img.classList.add('loaded');
              
              observer.unobserve(img);
            }
          }
        });
      }, {
        rootMargin: '50px 0px',
        threshold: 0.01
      });
      
      // Observer toutes les images lazy
      document.querySelectorAll('img[data-src]').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }
  
  /**
   * Mesurer les performances des images
   */
  static measureImagePerformance() {
    if ('PerformanceObserver' in window) {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (entry.initiatorType === 'img') {
            const isWebP = entry.name.includes('.webp');
            const loadTime = entry.responseEnd - entry.startTime;
            
            console.log(`📊 Image ${isWebP ? 'WebP' : 'classique'}: ${entry.name}`);
            console.log(`   ⏱️ Temps de chargement: ${Math.round(loadTime)}ms`);
            
            // Envoyer les métriques à Google Analytics si disponible
            if (typeof gtag !== 'undefined') {
              gtag('event', 'image_load_time', {
                event_category: 'performance',
                event_label: isWebP ? 'webp' : 'fallback',
                value: Math.round(loadTime)
              });
            }
          }
        });
      });
      
      observer.observe({ entryTypes: ['resource'] });
    }
  }
}

// Auto-initialisation
if (typeof window !== 'undefined') {
  // Initialiser dès que possible
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      WebPDetector.initWebPDetection().then(() => {
        WebPDetector.setupIntelligentLazyLoading();
        WebPDetector.measureImagePerformance();
      });
    });
  } else {
    WebPDetector.initWebPDetection().then(() => {
      WebPDetector.setupIntelligentLazyLoading();
      WebPDetector.measureImagePerformance();
    });
  }
}

export default WebPDetector;
