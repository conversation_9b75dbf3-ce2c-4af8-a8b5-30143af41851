#!/usr/bin/env python3
"""
Test de la correction du filtrage des hints visuels
"""

def test_visual_hint_detection():
    """Test de la fonction isVisualHint (simulation)"""
    print("🔍 Test de détection des hints visuels...")
    print("=" * 50)
    
    # Simulation de la fonction isVisualHint
    def is_visual_hint(hint):
        import re
        visual_patterns = [
            r'^💡',  # Commence par l'emoji ampoule
            r'^🔍',  # Commence par l'emoji loupe
            r'^ℹ️',  # Commence par l'emoji info
            r'^⚠️',  # Commence par l'emoji warning
            r'^📝',  # Commence par l'emoji note
            r'^👆',  # Commence par l'emoji pointeur
            r'^Dites\s+(votre|votre nom|simplement)',  # Instructions directes courtes
            r'^Choisissez\s+un',  # Instructions de choix courtes
        ]
        
        return any(re.search(pattern, hint.strip(), re.IGNORECASE) for pattern in visual_patterns)
    
    # Tests de différents hints
    test_cases = [
        # Hints visuels (ne doivent PAS être lus)
        ("💡 Dites votre nom de famille", True, "Emoji ampoule"),
        ("🔍 Cherchez dans la liste", True, "Emoji loupe"),
        ("Dites votre prénom", True, "Instruction directe courte"),
        ("Dites simplement oui ou non", True, "Instruction directe courte"),
        ("Choisissez un nom d'utilisateur", True, "Instruction de choix"),
        
        # Hints normaux (DOIVENT être lus)
        ("Dites votre email en épelant arobase et point", False, "Instruction détaillée"),
        ("Répondez simplement oui ou non", False, "Instruction normale"),
        ("Dites votre âge en années", False, "Instruction normale"),
        ("Indiquez vos préférences de rencontre", False, "Instruction normale"),
        ("Vous pouvez épeler les caractères spéciaux", False, "Conseil utile"),
    ]
    
    success_count = 0
    total_count = len(test_cases)
    
    for hint, expected_visual, description in test_cases:
        result = is_visual_hint(hint)
        status = "✅" if result == expected_visual else "❌"
        action = "Filtré" if result else "Lu à voix haute"
        
        print(f"{status} {description}")
        print(f"   Hint: '{hint}'")
        print(f"   Action: {action}")
        
        if result == expected_visual:
            success_count += 1
        
        print()
    
    print(f"📊 Résultat: {success_count}/{total_count} tests réussis")
    return success_count == total_count

def test_question_construction():
    """Test de construction des questions avec filtrage"""
    print("\n🎤 Test de construction des questions...")
    print("=" * 50)
    
    # Simulation de la logique de construction
    def build_question_text(question_index, question_text, hint):
        import re
        
        def is_visual_hint(hint):
            visual_patterns = [
                r'^💡',  # Commence par l'emoji ampoule
                r'^🔍',  # Commence par l'emoji loupe
                r'^ℹ️',  # Commence par l'emoji info
                r'^⚠️',  # Commence par l'emoji warning
                r'^📝',  # Commence par l'emoji note
                r'^👆',  # Commence par l'emoji pointeur
                r'^Dites\s+(votre|votre nom|simplement)',  # Instructions directes courtes
                r'^Choisissez\s+un',  # Instructions de choix courtes
            ]
            
            return any(re.search(pattern, hint.strip(), re.IGNORECASE) for pattern in visual_patterns)
        
        # Construire le texte à lire
        text_to_read = f"Question {question_index + 1}. {question_text}"
        
        # Ajouter le hint seulement s'il ne commence pas par un emoji ou des instructions visuelles
        if hint and not is_visual_hint(hint):
            text_to_read += f" Conseil : {hint}"
        
        return text_to_read
    
    # Test avec les vraies questions
    questions = [
        {
            "text": "Bonjour ! Pouvez-vous me dire votre prénom ?",
            "hint": "Dites simplement votre prénom",
            "expected": "Question 1. Bonjour ! Pouvez-vous me dire votre prénom ?"
        },
        {
            "text": "Et votre nom de famille ?",
            "hint": "💡 Dites votre nom de famille",
            "expected": "Question 2. Et votre nom de famille ?"
        },
        {
            "text": "Quelle est votre adresse email ? Vous pouvez épeler les caractères spéciaux.",
            "hint": "Dites votre email en épelant arobase et point",
            "expected": "Question 3. Quelle est votre adresse email ? Vous pouvez épeler les caractères spéciaux. Conseil : Dites votre email en épelant arobase et point"
        },
        {
            "text": "Quel âge avez-vous ?",
            "hint": "Dites votre âge en années",
            "expected": "Question 4. Quel âge avez-vous ? Conseil : Dites votre âge en années"
        }
    ]
    
    success_count = 0
    
    for i, question in enumerate(questions):
        result = build_question_text(i, question["text"], question["hint"])
        expected = question["expected"]
        
        status = "✅" if result == expected else "❌"
        print(f"{status} Question {i + 1}")
        print(f"   Texte lu: '{result}'")
        print(f"   Attendu:  '{expected}'")
        
        if result == expected:
            success_count += 1
        
        print()
    
    print(f"📊 Résultat: {success_count}/{len(questions)} questions correctes")
    return success_count == len(questions)

def main():
    """Fonction principale"""
    print("🎯 Test de la correction du filtrage des hints")
    print("=" * 60)
    
    # Test de détection des hints visuels
    visual_test_ok = test_visual_hint_detection()
    
    # Test de construction des questions
    question_test_ok = test_question_construction()
    
    print("=" * 60)
    print("📋 RÉSUMÉ DES TESTS")
    print("=" * 60)
    
    print(f"🔍 Détection hints visuels: {'✅ OK' if visual_test_ok else '❌ ERREUR'}")
    print(f"🎤 Construction questions: {'✅ OK' if question_test_ok else '❌ ERREUR'}")
    
    if visual_test_ok and question_test_ok:
        print("\n🎉 TOUS LES TESTS RÉUSSIS !")
        print("✅ Les hints visuels ne seront plus lus à voix haute")
        print("✅ La question 'Et votre nom de famille ?' s'arrêtera à la question")
        print("✅ Les hints utiles continueront d'être lus")
    else:
        print("\n⚠️  CERTAINS TESTS ONT ÉCHOUÉ")
        print("💡 Vérifiez la logique de filtrage des hints")

if __name__ == "__main__":
    main()
