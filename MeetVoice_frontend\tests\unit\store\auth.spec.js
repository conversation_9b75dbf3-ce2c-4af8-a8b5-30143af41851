import { createStore } from 'vuex'
import auth from '@/store/modules/auth'
import { accountService } from '@/_services'

// Mock du service d'authentification
jest.mock('@/_services', () => ({
  accountService: {
    login: jest.fn(),
    register: jest.fn(),
    logout: jest.fn(),
    initializeAuth: jest.fn(),
    refreshUserData: jest.fn(),
    updateProfile: jest.fn(),
    changePassword: jest.fn(),
    saveToken: jest.fn(),
    saveUser: jest.fn(),
    getUser: jest.fn(),
    getToken: jest.fn()
  }
}))

describe('Store Auth Module', () => {
  let store

  beforeEach(() => {
    store = createStore({
      modules: {
        auth: {
          ...auth,
          namespaced: true
        }
      }
    })
    
    // Réinitialiser les mocks
    jest.clearAllMocks()
    
    // Mock localStorage
    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn()
      },
      writable: true
    })
  })

  describe('Mutations', () => {
    it('should set token', () => {
      const token = 'test-token'
      store.commit('auth/setToken', token)
      
      expect(store.state.auth.token).toBe(token)
    })

    it('should set logged in status', () => {
      store.commit('auth/setLoggedIn', true)
      
      expect(store.state.auth.isLoggedIn).toBe(true)
    })

    it('should set user', () => {
      const user = { id: 1, username: 'testuser' }
      store.commit('auth/setUser', user)
      
      expect(store.state.auth.user).toEqual(user)
    })

    it('should set error', () => {
      const error = 'Test error'
      store.commit('auth/setError', error)
      
      expect(store.state.auth.error).toBe(error)
    })

    it('should clear error', () => {
      store.commit('auth/setError', 'Test error')
      store.commit('auth/clearError')
      
      expect(store.state.auth.error).toBeNull()
    })

    it('should set loading state', () => {
      store.commit('auth/setLoading', true)
      
      expect(store.state.auth.loading).toBe(true)
    })

    it('should set user type', () => {
      const userType = 'amoureux'
      store.commit('auth/setUserType', userType)
      
      expect(store.state.auth.userType).toBe(userType)
      expect(localStorage.setItem).toHaveBeenCalledWith('userType', userType)
    })
  })

  describe('Actions', () => {
    describe('login', () => {
      it('should login successfully', async () => {
        const credentials = { email: '<EMAIL>', password: 'password' }
        const mockResponse = {
          data: {
            access: 'test-token',
            user: { id: 1, username: 'testuser' }
          }
        }

        accountService.login.mockResolvedValue(mockResponse)

        await store.dispatch('auth/login', credentials)

        expect(accountService.login).toHaveBeenCalledWith(credentials)
        expect(store.state.auth.token).toBe('test-token')
        expect(store.state.auth.user).toEqual(mockResponse.data.user)
        expect(store.state.auth.isLoggedIn).toBe(true)
        expect(store.state.auth.loading).toBe(false)
      })

      it('should handle login error', async () => {
        const credentials = { email: '<EMAIL>', password: 'wrong' }
        const error = new Error('Invalid credentials')
        error.response = { data: { message: 'Invalid credentials' } }

        accountService.login.mockRejectedValue(error)

        await expect(store.dispatch('auth/login', credentials)).rejects.toThrow()
        
        expect(store.state.auth.error).toBe('Invalid credentials')
        expect(store.state.auth.loading).toBe(false)
      })
    })

    describe('register', () => {
      it('should register successfully', async () => {
        const userData = {
          email: '<EMAIL>',
          password: 'password',
          username: 'testuser'
        }
        const mockResponse = {
          data: {
            access: 'test-token',
            user: { id: 1, username: 'testuser' }
          }
        }

        accountService.register.mockResolvedValue(mockResponse)

        await store.dispatch('auth/register', userData)

        expect(accountService.register).toHaveBeenCalledWith(userData)
        expect(store.state.auth.token).toBe('test-token')
        expect(store.state.auth.user).toEqual(mockResponse.data.user)
        expect(store.state.auth.isLoggedIn).toBe(true)
      })

      it('should handle registration error', async () => {
        const userData = { email: 'invalid-email' }
        const error = new Error('Invalid email')
        error.response = { data: { message: 'Invalid email' } }

        accountService.register.mockRejectedValue(error)

        await expect(store.dispatch('auth/register', userData)).rejects.toThrow()
        
        expect(store.state.auth.error).toBe('Invalid email')
      })
    })

    describe('logout', () => {
      it('should logout successfully', () => {
        // Définir un état initial connecté
        store.commit('auth/setLoggedIn', true)
        store.commit('auth/setUser', { id: 1 })
        store.commit('auth/setToken', 'token')

        store.dispatch('auth/logout')

        expect(store.state.auth.isLoggedIn).toBe(false)
        expect(store.state.auth.user).toBeNull()
        expect(store.state.auth.token).toBeNull()
        expect(store.state.auth.error).toBeNull()
        expect(accountService.logout).toHaveBeenCalled()
      })
    })

    describe('initializeAuth', () => {
      it('should initialize auth when user is authenticated', async () => {
        const mockUser = { id: 1, username: 'testuser' }
        const mockToken = 'test-token'

        accountService.initializeAuth.mockReturnValue(true)
        accountService.getUser.mockReturnValue(mockUser)
        accountService.getToken.mockReturnValue(mockToken)
        accountService.refreshUserData.mockResolvedValue(mockUser)

        const result = await store.dispatch('auth/initializeAuth')

        expect(result).toBe(true)
        expect(store.state.auth.isLoggedIn).toBe(true)
        expect(store.state.auth.user).toEqual(mockUser)
        expect(store.state.auth.token).toBe(mockToken)
      })

      it('should handle initialization when user is not authenticated', async () => {
        accountService.initializeAuth.mockReturnValue(false)

        const result = await store.dispatch('auth/initializeAuth')

        expect(result).toBe(false)
        expect(store.state.auth.isLoggedIn).toBe(false)
      })
    })

    describe('updateProfile', () => {
      it('should update profile successfully', async () => {
        const profileData = { username: 'newusername' }
        const mockResponse = { data: { id: 1, username: 'newusername' } }

        accountService.updateProfile.mockResolvedValue(mockResponse)

        const result = await store.dispatch('auth/updateProfile', profileData)

        expect(accountService.updateProfile).toHaveBeenCalledWith(profileData)
        expect(store.state.auth.user).toEqual(mockResponse.data)
        expect(result).toEqual(mockResponse.data)
      })

      it('should handle profile update error', async () => {
        const profileData = { username: 'invalid' }
        const error = new Error('Update failed')

        accountService.updateProfile.mockRejectedValue(error)

        await expect(store.dispatch('auth/updateProfile', profileData)).rejects.toThrow()
        
        expect(store.state.auth.error).toBe('Erreur lors de la mise à jour du profil')
      })
    })
  })

  describe('Getters', () => {
    it('should get token', () => {
      store.commit('auth/setToken', 'test-token')
      
      expect(store.getters['auth/token']).toBe('test-token')
    })

    it('should get user', () => {
      const user = { id: 1, username: 'testuser' }
      store.commit('auth/setUser', user)
      
      expect(store.getters['auth/user']).toEqual(user)
    })

    it('should get authentication status', () => {
      store.commit('auth/setLoggedIn', true)
      
      expect(store.getters['auth/isAuthenticated']).toBe(true)
    })

    it('should get user full name', () => {
      const user = { first_name: 'John', last_name: 'Doe' }
      store.commit('auth/setUser', user)
      
      expect(store.getters['auth/userFullName']).toBe('John Doe')
    })

    it('should fallback to username when no first/last name', () => {
      const user = { username: 'johndoe' }
      store.commit('auth/setUser', user)
      
      expect(store.getters['auth/userFullName']).toBe('johndoe')
    })

    it('should return null when no user', () => {
      expect(store.getters['auth/userFullName']).toBeNull()
    })
  })
})
