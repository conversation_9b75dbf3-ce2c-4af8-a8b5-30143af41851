<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🧪 Tests Intégration Vosk - MeetVoice</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        .header {
            text-align: center;
            background: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            background: white;
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
            transition: all 0.3s ease;
        }
        .btn:hover {
            background: #2980b9;
            transform: translateY(-2px);
        }
        .btn.success {
            background: #27ae60;
        }
        .btn.danger {
            background: #e74c3c;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            margin: 5px;
        }
        .status.success {
            background: #d5f4e6;
            color: #27ae60;
        }
        .status.error {
            background: #fadbd8;
            color: #e74c3c;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Tests d'Intégration Vosk</h1>
        <p>Interface de test pour l'intégration complète Vosk + IA + TTS</p>
        <div id="overall-status">
            <span class="status warning">🟡 En attente de tests</span>
        </div>
    </div>

    <div class="grid">
        <div class="test-section">
            <h3>🎯 Test API Vosk Backend</h3>
            <button class="btn" onclick="testVoskAPI()">Tester API Vosk</button>
            <div id="vosk-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔗 Test Endpoints</h3>
            <button class="btn" onclick="testEndpoints()">Tester Endpoints</button>
            <div id="endpoints-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎵 Test Upload Audio</h3>
            <button class="btn" onclick="testAudioUpload()">Tester Upload</button>
            <div id="upload-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>⏱️ Test Performance</h3>
            <button class="btn" onclick="testPerformance()">Tester Performance</button>
            <div id="performance-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <div class="test-section">
        <h3>🚀 Tests Complets</h3>
        <button class="btn success" onclick="runAllTests()">🧪 Lancer Tous les Tests</button>
        <button class="btn" onclick="quickTest()">⚡ Test Rapide</button>
        <button class="btn danger" onclick="clearResults()">🗑️ Effacer</button>
        <div id="complete-result" class="result" style="display: none;"></div>
    </div>

    <script>
        // Variables globales
        let testResults = {};
        const BACKEND_URL = 'http://127.0.0.1:8000';

        // Test API Vosk
        async function testVoskAPI() {
            const resultDiv = document.getElementById('vosk-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 Test en cours...';

            try {
                const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `✅ API Vosk disponible\nStatus: ${response.status}\nData: ${JSON.stringify(data, null, 2)}`;
                    testResults.vosk = true;
                } else {
                    resultDiv.textContent = `❌ API Vosk indisponible\nStatus: ${response.status}`;
                    testResults.vosk = false;
                }
            } catch (error) {
                resultDiv.textContent = `❌ Erreur API Vosk: ${error.message}`;
                testResults.vosk = false;
            }
            updateOverallStatus();
        }

        // Test Endpoints
        async function testEndpoints() {
            const resultDiv = document.getElementById('endpoints-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 Test des endpoints...';

            const endpoints = [
                { path: `${BACKEND_URL}/api/vosk/status/`, method: 'GET' },
                { path: `${BACKEND_URL}/api/vosk/speech-to-text/`, method: 'POST' },
                { path: `${BACKEND_URL}/api/vosk/languages/`, method: 'GET' }
            ];

            let results = '';
            let allOk = true;

            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(endpoint.path, { method: 'HEAD' });
                    const status = response.ok ? '✅' : '❌';
                    results += `${status} ${endpoint.method} ${endpoint.path} - Status: ${response.status}\n`;
                    if (!response.ok) allOk = false;
                } catch (error) {
                    results += `❌ ${endpoint.method} ${endpoint.path} - Erreur: ${error.message}\n`;
                    allOk = false;
                }
            }

            resultDiv.textContent = results;
            testResults.endpoints = allOk;
            updateOverallStatus();
        }

        // Test Upload Audio
        async function testAudioUpload() {
            const resultDiv = document.getElementById('upload-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 Test upload audio...';

            try {
                // Créer un blob audio de test
                const audioBlob = new Blob(['test audio data'], { type: 'audio/webm' });
                const formData = new FormData();
                formData.append('audio', audioBlob, 'test.webm');
                formData.append('language', 'fr-FR');

                const response = await fetch(`${BACKEND_URL}/api/vosk/speech-to-text/`, {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    resultDiv.textContent = `✅ Upload audio réussi\nStatus: ${response.status}\nRésultat: ${JSON.stringify(result, null, 2)}`;
                    testResults.upload = true;
                } else {
                    resultDiv.textContent = `❌ Upload audio échec\nStatus: ${response.status}`;
                    testResults.upload = false;
                }
            } catch (error) {
                resultDiv.textContent = `❌ Erreur upload: ${error.message}`;
                testResults.upload = false;
            }
            updateOverallStatus();
        }

        // Test Performance
        async function testPerformance() {
            const resultDiv = document.getElementById('performance-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🔄 Test de performance...';

            const tests = [];
            
            // Test 5 fois pour avoir une moyenne
            for (let i = 0; i < 5; i++) {
                const startTime = performance.now();
                try {
                    const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    tests.push(duration);
                } catch (error) {
                    tests.push(null);
                }
            }

            const validTests = tests.filter(t => t !== null);
            if (validTests.length > 0) {
                const average = validTests.reduce((a, b) => a + b, 0) / validTests.length;
                const min = Math.min(...validTests);
                const max = Math.max(...validTests);

                let performance_status = '';
                if (average < 100) {
                    performance_status = '🚀 Excellente';
                } else if (average < 500) {
                    performance_status = '✅ Bonne';
                } else {
                    performance_status = '⚠️ Lente';
                }

                resultDiv.textContent = `${performance_status}\nMoyenne: ${average.toFixed(2)}ms\nMin: ${min.toFixed(2)}ms\nMax: ${max.toFixed(2)}ms\nTests réussis: ${validTests.length}/5`;
                testResults.performance = average < 1000;
            } else {
                resultDiv.textContent = '❌ Tous les tests de performance ont échoué';
                testResults.performance = false;
            }
            updateOverallStatus();
        }

        // Test rapide
        async function quickTest() {
            const resultDiv = document.getElementById('complete-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '⚡ Test rapide en cours...';

            try {
                const response = await fetch(`${BACKEND_URL}/api/vosk/status/`);
                if (response.ok) {
                    const data = await response.json();
                    resultDiv.textContent = `⚡ Test rapide: ✅ RÉUSSI\nAPI Vosk opérationnelle\nData: ${JSON.stringify(data, null, 2)}`;
                } else {
                    resultDiv.textContent = `⚡ Test rapide: ❌ ÉCHEC\nStatus: ${response.status}`;
                }
            } catch (error) {
                resultDiv.textContent = `⚡ Test rapide: ❌ ERREUR\n${error.message}`;
            }
        }

        // Lancer tous les tests
        async function runAllTests() {
            const resultDiv = document.getElementById('complete-result');
            resultDiv.style.display = 'block';
            resultDiv.textContent = '🧪 Lancement de tous les tests...\n';

            await testVoskAPI();
            await testEndpoints();
            await testAudioUpload();
            await testPerformance();

            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(r => r === true).length;

            let summary = `\n🧪 === RÉSUMÉ DES TESTS ===\n`;
            summary += `✅ Tests réussis: ${successfulTests}\n`;
            summary += `❌ Tests échoués: ${totalTests - successfulTests}\n`;
            summary += `📊 Score: ${successfulTests}/${totalTests}\n\n`;

            if (successfulTests === totalTests) {
                summary += '🎉 TOUS LES TESTS RÉUSSIS ! Intégration parfaite !';
            } else if (successfulTests >= totalTests * 0.8) {
                summary += '✅ INTÉGRATION BONNE ! Quelques optimisations possibles.';
            } else {
                summary += '⚠️ INTÉGRATION PARTIELLE ! Corrections nécessaires.';
            }

            resultDiv.textContent += summary;
        }

        // Effacer les résultats
        function clearResults() {
            const resultDivs = document.querySelectorAll('.result');
            resultDivs.forEach(div => {
                div.style.display = 'none';
                div.textContent = '';
            });
            testResults = {};
            updateOverallStatus();
        }

        // Mettre à jour le statut global
        function updateOverallStatus() {
            const statusDiv = document.getElementById('overall-status');
            const totalTests = Object.keys(testResults).length;
            const successfulTests = Object.values(testResults).filter(r => r === true).length;

            if (totalTests === 0) {
                statusDiv.innerHTML = '<span class="status warning">🟡 En attente de tests</span>';
            } else if (successfulTests === totalTests) {
                statusDiv.innerHTML = '<span class="status success">🟢 Tous les tests réussis</span>';
            } else if (successfulTests > 0) {
                statusDiv.innerHTML = `<span class="status warning">🟡 ${successfulTests}/${totalTests} tests réussis</span>`;
            } else {
                statusDiv.innerHTML = '<span class="status error">🔴 Tous les tests échoués</span>';
            }
        }

        // Message de bienvenue
        console.log('🧪 Interface de test Vosk chargée !');
        console.log('🚀 Cliquez sur les boutons pour lancer les tests.');
    </script>
</body>
</html>
