#!/usr/bin/env python3
"""
Test pour vérifier que les voix sont bien différentes
"""

import requests
import hashlib

def test_voice_differences():
    """Test que les voix produisent des audios différents"""
    print("🎤 Test des différences entre les voix...")
    print("=" * 50)
    
    # Même texte pour toutes les voix
    test_text = "Bonjour, ceci est un test de voix."
    
    voices_to_test = [
        ('female_young', '<PERSON>'),
        ('male_young', '<PERSON>'), 
        ('female_mature', '<PERSON>'),
        ('male_mature', '<PERSON>'),
        ('neutral', '<PERSON>')
    ]
    
    audio_hashes = {}
    
    for voice_type, voice_name in voices_to_test:
        try:
            payload = {
                'text': test_text,
                'voice_type': voice_type,
                'language': 'fr',
                'speed': 1.0,
                'pitch': 1.0
            }
            
            response = requests.post('http://127.0.0.1:8000/tts/public/synthesize/', json=payload)
            if response.status_code == 200:
                # Calculer le hash du contenu audio
                audio_hash = hashlib.md5(response.content).hexdigest()
                audio_hashes[voice_type] = {
                    'name': voice_name,
                    'hash': audio_hash,
                    'size': len(response.content)
                }
                print(f"✅ {voice_name} ({voice_type}): {len(response.content)} bytes, hash: {audio_hash[:8]}...")
            else:
                print(f"❌ {voice_name} ({voice_type}): Erreur {response.status_code}")
                
        except Exception as e:
            print(f"❌ {voice_name} ({voice_type}): Erreur {e}")
    
    # Vérifier que les hashs sont différents
    print("\n🔍 Analyse des différences:")
    print("-" * 30)
    
    unique_hashes = set(data['hash'] for data in audio_hashes.values())
    
    if len(unique_hashes) == len(audio_hashes):
        print("✅ TOUTES LES VOIX SONT DIFFÉRENTES")
        print(f"   {len(audio_hashes)} voix testées, {len(unique_hashes)} hashs uniques")
    else:
        print("⚠️  CERTAINES VOIX SONT IDENTIQUES")
        print(f"   {len(audio_hashes)} voix testées, {len(unique_hashes)} hashs uniques")
        
        # Trouver les doublons
        hash_to_voices = {}
        for voice_type, data in audio_hashes.items():
            hash_val = data['hash']
            if hash_val not in hash_to_voices:
                hash_to_voices[hash_val] = []
            hash_to_voices[hash_val].append((voice_type, data['name']))
        
        for hash_val, voices in hash_to_voices.items():
            if len(voices) > 1:
                voice_names = [f"{name} ({vtype})" for vtype, name in voices]
                print(f"   🔄 Même hash: {', '.join(voice_names)}")
    
    return len(unique_hashes) == len(audio_hashes)

if __name__ == "__main__":
    success = test_voice_differences()
    
    if success:
        print("\n🎉 RÉSULTAT: Les voix API sont bien différentes")
        print("💡 Le problème vient probablement du frontend Vue.js")
        print("🔧 Vérifiez la console du navigateur pour les messages de debug")
    else:
        print("\n⚠️  RÉSULTAT: Certaines voix API sont identiques")
        print("💡 Le problème vient du backend - certaines voix ne sont pas configurées")
