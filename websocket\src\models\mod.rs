use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

// Modèles pour PostgreSQL (lecture des infos utilisateurs)
#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct User {
    pub id: Uuid,
    pub email: String,
    pub username: String,
    pub created_at: DateTime<Utc>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize, sqlx::FromRow)]
pub struct UserProfile {
    pub user_id: Uuid,
    pub display_name: String,
    pub avatar_url: Option<String>,
    pub bio: Option<String>,
    pub preferences: serde_json::Value,
}

// Modèles pour MongoDB (données en temps réel)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<mongodb::bson::oid::ObjectId>,
    pub user_id: Uuid,
    pub room_id: String,
    pub content: String,
    pub message_type: MessageType,
    pub timestamp: DateTime<Utc>,
    pub metadata: Option<serde_json::Value>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VoiceSession {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<mongodb::bson::oid::ObjectId>,
    pub session_id: String,
    pub participants: Vec<Uuid>,
    pub status: SessionStatus,
    pub started_at: DateTime<Utc>,
    pub ended_at: Option<DateTime<Utc>>,
    pub quality_metrics: Option<QualityMetrics>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WebRTCOffer {
    #[serde(rename = "_id", skip_serializing_if = "Option::is_none")]
    pub id: Option<mongodb::bson::oid::ObjectId>,
    pub from_user: Uuid,
    pub to_user: Uuid,
    pub offer_type: OfferType,
    pub sdp: String,
    pub ice_candidates: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub expires_at: DateTime<Utc>,
}

// Énumérations
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum MessageType {
    Text,
    Voice,
    Video,
    System,
    WebrtcOffer,
    WebrtcAnswer,
    IceCandidate,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum SessionStatus {
    Waiting,
    Active,
    Ended,
    Failed,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "snake_case")]
pub enum OfferType {
    Voice,
    Video,
    Screen,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct QualityMetrics {
    pub avg_latency_ms: f64,
    pub packet_loss_rate: f64,
    pub bitrate_kbps: u32,
    pub jitter_ms: f64,
}

// Messages WebSocket
#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(tag = "type", content = "data")]
pub enum WebSocketMessage {
    // Authentification
    Auth { token: String },
    AuthSuccess { user_id: Uuid },
    AuthError { message: String },
    
    // Chat
    SendMessage { room_id: String, content: String, message_type: MessageType },
    MessageReceived(ChatMessage),
    
    // Voice/Video
    JoinVoiceSession { session_id: String },
    LeaveVoiceSession { session_id: String },
    VoiceSessionJoined { session_id: String, participants: Vec<Uuid> },
    VoiceSessionLeft { session_id: String },
    
    // WebRTC
    WebRTCOffer { to_user: Uuid, offer_type: OfferType, sdp: String },
    WebRTCAnswer { to_user: Uuid, sdp: String },
    ICECandidate { to_user: Uuid, candidate: String },
    
    // P2P
    P2PConnect { peer_id: String },
    P2PDisconnect { peer_id: String },
    
    // Système
    Ping,
    Pong,
    Error { message: String },
}

// Structures pour les réponses API
#[derive(Debug, Serialize, Deserialize)]
pub struct ApiResponse<T> {
    pub success: bool,
    pub data: Option<T>,
    pub error: Option<String>,
    pub timestamp: DateTime<Utc>,
}

impl<T> ApiResponse<T> {
    pub fn success(data: T) -> Self {
        Self {
            success: true,
            data: Some(data),
            error: None,
            timestamp: Utc::now(),
        }
    }
    
    pub fn error(message: String) -> Self {
        Self {
            success: false,
            data: None,
            error: Some(message),
            timestamp: Utc::now(),
        }
    }
}
