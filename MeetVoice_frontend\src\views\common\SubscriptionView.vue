<template>
  <main class="subscription-container">
    <!-- Header -->
    <header class="subscription-header">
      <section class="header-content">
        <h1>Abonnements Premium</h1>
        <p class="subtitle"><PERSON><PERSON><PERSON><PERSON><PERSON> toutes les fonctionnalités de MeetVoice</p>
      </section>
      
      <div class="current-plan" v-if="currentSubscription">
        <div class="plan-badge">
          <span class="plan-name">{{ currentSubscription.planName }}</span>
          <span class="plan-status">{{ getStatusLabel(currentSubscription.status) }}</span>
        </div>
      </div>
    </header>

    <!-- Avantages Premium -->
    <section class="benefits-section">
      <h2>Pourquoi passer Premium ?</h2>
      <div class="benefits-grid">
        <div class="benefit-card">
          <div class="benefit-icon">🎤</div>
          <h3>Messages vocaux illimités</h3>
          <p>Envoyez autant de messages vocaux que vous voulez</p>
        </div>
        
        <div class="benefit-card">
          <div class="benefit-icon">👁️</div>
          <h3>Voir qui vous a liké</h3>
          <p>Découvrez qui s'intéresse à votre profil</p>
        </div>
        
        <div class="benefit-card">
          <div class="benefit-icon">⭐</div>
          <h3>Super Likes illimités</h3>
          <p>Montrez votre intérêt avec des Super Likes</p>
        </div>
        
        <div class="benefit-card">
          <div class="benefit-icon">🔍</div>
          <h3>Filtres avancés</h3>
          <p>Recherchez avec des critères précis</p>
        </div>
        
        <div class="benefit-card">
          <div class="benefit-icon">🚫</div>
          <h3>Sans publicité</h3>
          <p>Profitez d'une expérience sans interruption</p>
        </div>
        
        <div class="benefit-card">
          <div class="benefit-icon">🎯</div>
          <h3>Boost de profil</h3>
          <p>Augmentez votre visibilité</p>
        </div>
      </div>
    </section>

    <!-- Plans d'abonnement -->
    <section class="plans-section">
      <h2>Choisissez votre plan</h2>
      
      <div class="plans-toggle">
        <button 
          @click="billingCycle = 'monthly'"
          :class="['toggle-btn', { active: billingCycle === 'monthly' }]"
        >
          Mensuel
        </button>
        <button 
          @click="billingCycle = 'yearly'"
          :class="['toggle-btn', { active: billingCycle === 'yearly' }]"
        >
          Annuel
          <span class="discount-badge">-20%</span>
        </button>
      </div>
      
      <div class="plans-grid">
        <article 
          v-for="plan in filteredPlans" 
          :key="plan.id"
          :class="['plan-card', { 
            popular: plan.isPopular,
            current: isCurrentPlan(plan.id)
          }]"
        >
          <div v-if="plan.isPopular" class="popular-badge">
            ⭐ Le plus populaire
          </div>
          
          <header class="plan-header">
            <h3 class="plan-name">{{ plan.name }}</h3>
            <div class="plan-price">
              <span class="price-amount">{{ plan.price }}€</span>
              <span class="price-period">/{{ billingCycle === 'monthly' ? 'mois' : 'an' }}</span>
            </div>
            <div v-if="billingCycle === 'yearly' && plan.monthlyPrice" class="price-comparison">
              Soit {{ (plan.price / 12).toFixed(2) }}€/mois
              <span class="savings">Économisez {{ ((plan.monthlyPrice * 12) - plan.price).toFixed(0) }}€</span>
            </div>
          </header>
          
          <div class="plan-features">
            <ul class="features-list">
              <li 
                v-for="feature in plan.features" 
                :key="feature"
                class="feature-item"
              >
                <span class="feature-icon">✓</span>
                {{ feature }}
              </li>
            </ul>
          </div>
          
          <footer class="plan-footer">
            <button 
              v-if="!isCurrentPlan(plan.id)"
              @click="selectPlan(plan)"
              :class="['btn-select-plan', { popular: plan.isPopular }]"
            >
              {{ currentSubscription ? 'Changer de plan' : 'Choisir ce plan' }}
            </button>
            
            <div v-else class="current-plan-indicator">
              <span class="current-icon">✓</span>
              Plan actuel
            </div>
          </footer>
        </article>
      </div>
    </section>

    <!-- Gestion de l'abonnement actuel -->
    <section v-if="currentSubscription" class="current-subscription">
      <h2>Gérer mon abonnement</h2>
      
      <div class="subscription-details">
        <div class="detail-card">
          <h3>Informations de facturation</h3>
          <div class="billing-info">
            <div class="info-row">
              <span class="label">Plan actuel :</span>
              <span class="value">{{ currentSubscription.planName }}</span>
            </div>
            <div class="info-row">
              <span class="label">Prix :</span>
              <span class="value">{{ currentSubscription.price }}€/{{ currentSubscription.cycle }}</span>
            </div>
            <div class="info-row">
              <span class="label">Prochaine facturation :</span>
              <span class="value">{{ formatDate(currentSubscription.nextBilling) }}</span>
            </div>
            <div class="info-row">
              <span class="label">Statut :</span>
              <span :class="['value', 'status', currentSubscription.status]">
                {{ getStatusLabel(currentSubscription.status) }}
              </span>
            </div>
          </div>
        </div>
        
        <div class="detail-card">
          <h3>Actions</h3>
          <div class="subscription-actions">
            <button 
              v-if="currentSubscription.status === 'active'"
              @click="pauseSubscription"
              class="btn-action pause"
            >
              Suspendre l'abonnement
            </button>
            
            <button 
              v-if="currentSubscription.status === 'paused'"
              @click="resumeSubscription"
              class="btn-action resume"
            >
              Reprendre l'abonnement
            </button>
            
            <button 
              @click="cancelSubscription"
              class="btn-action cancel"
            >
              {{ currentSubscription.status === 'active' ? 'Annuler l\'abonnement' : 'Supprimer définitivement' }}
            </button>
            
            <button 
              @click="updatePaymentMethod"
              class="btn-action update"
            >
              Modifier le moyen de paiement
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Historique des paiements -->
    <section v-if="paymentHistory.length > 0" class="payment-history">
      <h2>Historique des paiements</h2>
      
      <div class="history-table">
        <div class="table-header">
          <span>Date</span>
          <span>Plan</span>
          <span>Montant</span>
          <span>Statut</span>
          <span>Actions</span>
        </div>
        
        <div 
          v-for="payment in paymentHistory" 
          :key="payment.id"
          class="table-row"
        >
          <span class="payment-date">{{ formatDate(payment.date) }}</span>
          <span class="payment-plan">{{ payment.planName }}</span>
          <span class="payment-amount">{{ payment.amount }}€</span>
          <span :class="['payment-status', payment.status]">
            {{ getPaymentStatusLabel(payment.status) }}
          </span>
          <div class="payment-actions">
            <button 
              @click="downloadInvoice(payment.id)"
              class="btn-download"
            >
              📄 Facture
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ -->
    <section class="faq-section">
      <h2>Questions fréquentes</h2>
      
      <div class="faq-list">
        <div 
          v-for="(faq, index) in faqs" 
          :key="index"
          class="faq-item"
        >
          <button 
            @click="toggleFaq(index)"
            class="faq-question"
          >
            {{ faq.question }}
            <span class="faq-icon">{{ openFaqs.includes(index) ? '−' : '+' }}</span>
          </button>
          
          <div v-if="openFaqs.includes(index)" class="faq-answer">
            {{ faq.answer }}
          </div>
        </div>
      </div>
    </section>

    <!-- Modal de sélection de plan -->
    <div v-if="selectedPlan" class="modal-overlay" @click="selectedPlan = null">
      <div class="modal-content" @click.stop>
        <header class="modal-header">
          <h2>Confirmer l'abonnement</h2>
          <button @click="selectedPlan = null" class="btn-close-modal">×</button>
        </header>
        
        <div class="plan-summary">
          <h3>{{ selectedPlan.name }}</h3>
          <div class="summary-price">
            {{ selectedPlan.price }}€/{{ billingCycle === 'monthly' ? 'mois' : 'an' }}
          </div>
          
          <div class="payment-method">
            <h4>Moyen de paiement</h4>
            <div class="payment-options">
              <label class="payment-option">
                <input type="radio" name="payment" value="card" v-model="paymentMethod">
                <span>💳 Carte bancaire</span>
              </label>
              <label class="payment-option">
                <input type="radio" name="payment" value="paypal" v-model="paymentMethod">
                <span>🅿️ PayPal</span>
              </label>
            </div>
          </div>
          
          <div class="terms-acceptance">
            <label class="checkbox-label">
              <input type="checkbox" v-model="acceptTerms">
              <span>J'accepte les <a href="/terms" target="_blank">conditions d'utilisation</a></span>
            </label>
          </div>
        </div>
        
        <div class="modal-actions">
          <button @click="selectedPlan = null" class="btn-cancel">
            Annuler
          </button>
          <button 
            @click="confirmSubscription"
            :disabled="!paymentMethod || !acceptTerms"
            class="btn-confirm"
          >
            Confirmer l'abonnement
          </button>
        </div>
      </div>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'SubscriptionView',

  data() {
    return {
      billingCycle: 'monthly',
      selectedPlan: null,
      paymentMethod: '',
      acceptTerms: false,
      openFaqs: [],

      plans: [
        {
          id: 'basic',
          name: 'Basic',
          price: 9.99,
          monthlyPrice: 9.99,
          isPopular: false,
          features: [
            'Messages vocaux illimités',
            'Voir qui vous a liké',
            'Filtres de base',
            'Support par email'
          ]
        },
        {
          id: 'premium',
          name: 'Premium',
          price: 19.99,
          monthlyPrice: 19.99,
          isPopular: true,
          features: [
            'Toutes les fonctionnalités Basic',
            'Super Likes illimités',
            'Filtres avancés',
            'Boost de profil mensuel',
            'Sans publicité',
            'Support prioritaire'
          ]
        },
        {
          id: 'vip',
          name: 'VIP',
          price: 39.99,
          monthlyPrice: 39.99,
          isPopular: false,
          features: [
            'Toutes les fonctionnalités Premium',
            'Boost de profil illimité',
            'Badge VIP sur le profil',
            'Accès aux événements exclusifs',
            'Support téléphonique 24/7',
            'Conseiller personnel'
          ]
        }
      ],

      faqs: [
        {
          question: 'Puis-je annuler mon abonnement à tout moment ?',
          answer: 'Oui, vous pouvez annuler votre abonnement à tout moment. L\'annulation prendra effet à la fin de votre période de facturation actuelle.'
        },
        {
          question: 'Que se passe-t-il si j\'annule mon abonnement ?',
          answer: 'Vous conserverez l\'accès aux fonctionnalités premium jusqu\'à la fin de votre période payée, puis votre compte repassera en version gratuite.'
        },
        {
          question: 'Puis-je changer de plan ?',
          answer: 'Oui, vous pouvez passer à un plan supérieur ou inférieur à tout moment. Les changements sont proratisés.'
        },
        {
          question: 'Les paiements sont-ils sécurisés ?',
          answer: 'Oui, tous les paiements sont traités de manière sécurisée via des partenaires certifiés PCI DSS.'
        }
      ]
    }
  },

  computed: {
    ...mapState(['subscription']),

    currentSubscription() {
      return this.subscription?.current || null;
    },

    paymentHistory() {
      return this.subscription?.history || [];
    },

    filteredPlans() {
      return this.plans.map(plan => ({
        ...plan,
        price: this.billingCycle === 'yearly' ? plan.price * 12 * 0.8 : plan.price
      }));
    }
  },

  methods: {
    ...mapActions(['loadSubscription', 'subscribeToPlan', 'cancelSubscription']),

    isCurrentPlan(planId) {
      return this.currentSubscription?.planId === planId;
    },

    getStatusLabel(status) {
      const labels = {
        active: 'Actif',
        paused: 'Suspendu',
        cancelled: 'Annulé',
        expired: 'Expiré'
      };
      return labels[status] || status;
    },

    getPaymentStatusLabel(status) {
      const labels = {
        success: 'Payé',
        pending: 'En attente',
        failed: 'Échec',
        refunded: 'Remboursé'
      };
      return labels[status] || status;
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleDateString('fr-FR');
    },

    selectPlan(plan) {
      this.selectedPlan = plan;
      this.paymentMethod = '';
      this.acceptTerms = false;
    },

    async confirmSubscription() {
      if (!this.paymentMethod || !this.acceptTerms) return;

      try {
        await this.subscribeToPlan({
          planId: this.selectedPlan.id,
          cycle: this.billingCycle,
          paymentMethod: this.paymentMethod
        });

        this.selectedPlan = null;

        this.$store.commit('addNotification', {
          type: 'success',
          message: 'Abonnement activé avec succès !',
          timestamp: new Date().toISOString()
        });

      } catch (error) {
        console.error('Erreur lors de l\'abonnement:', error);
        this.$store.commit('addNotification', {
          type: 'error',
          message: 'Erreur lors de l\'activation de l\'abonnement',
          timestamp: new Date().toISOString()
        });
      }
    },

    async pauseSubscription() {
      if (confirm('Êtes-vous sûr de vouloir suspendre votre abonnement ?')) {
        try {
          // Implémenter la suspension
          console.log('Suspendre abonnement');
        } catch (error) {
          console.error('Erreur lors de la suspension:', error);
        }
      }
    },

    async resumeSubscription() {
      try {
        // Implémenter la reprise
        console.log('Reprendre abonnement');
      } catch (error) {
        console.error('Erreur lors de la reprise:', error);
      }
    },

    async cancelSubscription() {
      if (confirm('Êtes-vous sûr de vouloir annuler votre abonnement ?')) {
        try {
          await this.cancelSubscription();

          this.$store.commit('addNotification', {
            type: 'info',
            message: 'Abonnement annulé',
            timestamp: new Date().toISOString()
          });

        } catch (error) {
          console.error('Erreur lors de l\'annulation:', error);
        }
      }
    },

    updatePaymentMethod() {
      // Implémenter la mise à jour du moyen de paiement
      console.log('Mettre à jour le moyen de paiement');
    },

    downloadInvoice(paymentId) {
      // Implémenter le téléchargement de facture
      console.log('Télécharger facture:', paymentId);
    },

    toggleFaq(index) {
      if (this.openFaqs.includes(index)) {
        this.openFaqs = this.openFaqs.filter(i => i !== index);
      } else {
        this.openFaqs.push(index);
      }
    }
  },

  async mounted() {
    try {
      await this.loadSubscription();
    } catch (error) {
      console.error('Erreur lors du chargement de l\'abonnement:', error);
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --premium-gold: #FFD700;
  --success-green: #22c55e;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;
}

.subscription-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--premium-gold);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.current-plan {
  display: flex;
  align-items: center;
}

.plan-badge {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 20px;
  background: var(--premium-gold);
  color: var(--header-bg);
  border-radius: 8px;
  font-weight: 600;
}

.plan-name {
  font-size: 1.1rem;
}

.plan-status {
  font-size: 0.8rem;
  opacity: 0.8;
}

.benefits-section {
  margin-bottom: 40px;
}

.benefits-section h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
}

.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.benefit-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.benefit-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-4px);
}

.benefit-icon {
  font-size: 2.5rem;
  margin-bottom: 16px;
}

.benefit-card h3 {
  color: var(--accent-blue);
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.benefit-card p {
  color: var(--slogan-gray);
  line-height: 1.5;
}

.plans-section {
  margin-bottom: 40px;
}

.plans-section h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
  text-align: center;
}

.plans-toggle {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 4px;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
}

.toggle-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: transparent;
  color: var(--slogan-gray);
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.toggle-btn.active {
  background: var(--accent-blue);
  color: var(--text-white);
}

.discount-badge {
  padding: 2px 6px;
  background: var(--premium-gold);
  color: var(--header-bg);
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: 600;
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.plan-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.plan-card:hover {
  background: rgba(255, 255, 255, 0.15);
  transform: translateY(-8px);
}

.plan-card.popular {
  border-color: var(--premium-gold);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.plan-card.current {
  border-color: var(--success-green);
  background: rgba(34, 197, 94, 0.1);
}

.popular-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--premium-gold);
  color: var(--header-bg);
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 24px;
}

.plan-name {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 12px;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  margin-bottom: 8px;
}

.price-amount {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--premium-gold);
}

.price-period {
  color: var(--slogan-gray);
  font-size: 1rem;
}

.price-comparison {
  font-size: 0.9rem;
  color: var(--slogan-gray);
}

.savings {
  display: block;
  color: var(--success-green);
  font-weight: 600;
  font-size: 0.8rem;
}

.plan-features {
  margin-bottom: 24px;
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
  color: var(--text-white);
}

.feature-icon {
  color: var(--success-green);
  font-weight: bold;
}

.plan-footer {
  text-align: center;
}

.btn-select-plan {
  width: 100%;
  padding: 12px 24px;
  background: var(--accent-blue);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-select-plan.popular {
  background: var(--premium-gold);
  color: var(--header-bg);
}

.btn-select-plan:hover {
  transform: translateY(-2px);
}

.current-plan-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 24px;
  background: var(--success-green);
  color: var(--text-white);
  border-radius: 8px;
  font-weight: 600;
}

.current-icon {
  font-size: 1.2rem;
}

.current-subscription {
  margin-bottom: 40px;
}

.current-subscription h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
}

.subscription-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.detail-card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 24px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-card h3 {
  color: var(--accent-purple);
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.billing-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  color: var(--slogan-gray);
}

.value {
  color: var(--text-white);
  font-weight: 500;
}

.value.status.active {
  color: var(--success-green);
}

.value.status.paused {
  color: var(--warning-orange);
}

.value.status.cancelled {
  color: var(--error-red);
}

.subscription-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.btn-action {
  padding: 10px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-action.pause {
  background: var(--warning-orange);
  color: var(--text-white);
}

.btn-action.resume {
  background: var(--success-green);
  color: var(--text-white);
}

.btn-action.cancel {
  background: var(--error-red);
  color: var(--text-white);
}

.btn-action.update {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-action:hover {
  transform: translateY(-1px);
}

.payment-history {
  margin-bottom: 40px;
}

.payment-history h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
}

.history-table {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px 20px;
  background: rgba(255, 255, 255, 0.1);
  font-weight: 600;
  color: var(--accent-blue);
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
  gap: 16px;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.payment-status.success {
  color: var(--success-green);
}

.payment-status.pending {
  color: var(--warning-orange);
}

.payment-status.failed {
  color: var(--error-red);
}

.btn-download {
  padding: 6px 12px;
  background: var(--accent-blue);
  color: var(--text-white);
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.btn-download:hover {
  background: var(--accent-purple);
}

.faq-section {
  margin-bottom: 40px;
}

.faq-section h2 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 24px;
}

.faq-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.faq-item {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.faq-question {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: transparent;
  color: var(--text-white);
  border: none;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  text-align: left;
  transition: all 0.3s ease;
}

.faq-question:hover {
  background: rgba(255, 255, 255, 0.1);
}

.faq-icon {
  color: var(--accent-blue);
  font-size: 1.2rem;
  font-weight: bold;
}

.faq-answer {
  padding: 0 20px 16px;
  color: var(--slogan-gray);
  line-height: 1.6;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--header-bg);
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.modal-header h2 {
  color: var(--premium-gold);
  font-size: 1.3rem;
  font-weight: 600;
}

.btn-close-modal {
  background: transparent;
  border: none;
  color: var(--slogan-gray);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.btn-close-modal:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.plan-summary {
  text-align: center;
  margin-bottom: 24px;
}

.plan-summary h3 {
  color: var(--accent-blue);
  font-size: 1.5rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.summary-price {
  font-size: 2rem;
  font-weight: 700;
  color: var(--premium-gold);
  margin-bottom: 24px;
}

.payment-method {
  margin-bottom: 20px;
}

.payment-method h4 {
  color: var(--accent-purple);
  margin-bottom: 12px;
}

.payment-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.payment-option {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.payment-option:hover {
  background: rgba(255, 255, 255, 0.15);
}

.terms-acceptance {
  margin-bottom: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  color: var(--slogan-gray);
}

.checkbox-label a {
  color: var(--accent-blue);
  text-decoration: none;
}

.checkbox-label a:hover {
  text-decoration: underline;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.btn-cancel,
.btn-confirm {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-cancel {
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-cancel:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.btn-confirm {
  background: var(--premium-gold);
  color: var(--header-bg);
}

.btn-confirm:hover:not(:disabled) {
  background: var(--accent-blue);
  color: var(--text-white);
}

.btn-confirm:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

/* Responsive */
@media (max-width: 1024px) {
  .benefits-grid,
  .plans-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .subscription-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .subscription-container {
    padding: 16px;
  }

  .subscription-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 8px;
  }

  .table-header {
    display: none;
  }

  .table-row {
    display: flex;
    flex-direction: column;
    align-items: stretch;
  }

  .modal-content {
    width: 95%;
    padding: 16px;
  }
}
</style>
