import { accountService } from '@/_services/account.service'
import store from '@/store'

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
}
global.localStorage = localStorageMock

// Mock Axios
jest.mock('@/_services/caller.service', () => ({
  post: jest.fn(),
  get: jest.fn()
}))

import Axios from '@/_services/caller.service'

describe('AccountService', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    localStorageMock.getItem.mockClear()
    localStorageMock.setItem.mockClear()
    localStorageMock.removeItem.mockClear()
  })

  describe('login', () => {
    it('should call login API', async () => {
      const credentials = { username: 'testuser', password: 'password' }
      const mockResponse = { data: { access: 'token123', user: { id: 1, username: 'test' } } }
      
      Axios.post.mockResolvedValue(mockResponse)
      
      const result = await accountService.login(credentials)
      
      expect(Axios.post).toHaveBeenCalledWith('/api/login/', credentials)
      expect(result).toEqual(mockResponse)
    })
  })

  describe('logout', () => {
    it('should remove all user data from localStorage', () => {
      accountService.logout()
      
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('userProfile')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('userPreferences')
    })
  })

  describe('token management', () => {
    it('should save token to localStorage', () => {
      const token = 'test-token'
      
      accountService.saveToken(token)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', token)
    })

    it('should get token from localStorage', () => {
      const token = 'test-token'
      localStorageMock.getItem.mockReturnValue(token)
      
      const result = accountService.getToken()
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('token')
      expect(result).toBe(token)
    })
  })

  describe('user management', () => {
    it('should save user to localStorage', () => {
      const user = { id: 1, username: 'test', email: '<EMAIL>' }
      
      accountService.saveUser(user)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('user', JSON.stringify(user))
    })

    it('should get user from localStorage', () => {
      const user = { id: 1, username: 'test', email: '<EMAIL>' }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(user))
      
      const result = accountService.getUser()
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('user')
      expect(result).toEqual(user)
    })

    it('should return null when no user in localStorage', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const result = accountService.getUser()
      
      expect(result).toBeNull()
    })
  })

  describe('user profile management', () => {
    it('should save user profile to localStorage', () => {
      const profile = { bio: 'Test bio', avatar: 'avatar.jpg' }
      
      accountService.saveUserProfile(profile)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('userProfile', JSON.stringify(profile))
    })

    it('should get user profile from localStorage', () => {
      const profile = { bio: 'Test bio', avatar: 'avatar.jpg' }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(profile))
      
      const result = accountService.getUserProfile()
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('userProfile')
      expect(result).toEqual(profile)
    })
  })

  describe('user preferences management', () => {
    it('should save user preferences to localStorage', () => {
      const preferences = { theme: 'dark', language: 'fr' }
      
      accountService.saveUserPreferences(preferences)
      
      expect(localStorageMock.setItem).toHaveBeenCalledWith('userPreferences', JSON.stringify(preferences))
    })

    it('should get user preferences from localStorage', () => {
      const preferences = { theme: 'dark', language: 'fr' }
      localStorageMock.getItem.mockReturnValue(JSON.stringify(preferences))
      
      const result = accountService.getUserPreferences()
      
      expect(localStorageMock.getItem).toHaveBeenCalledWith('userPreferences')
      expect(result).toEqual(preferences)
    })

    it('should return empty object when no preferences', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const result = accountService.getUserPreferences()
      
      expect(result).toEqual({})
    })
  })

  describe('isLogged', () => {
    it('should return true when token exists', () => {
      localStorageMock.getItem.mockReturnValue('test-token')
      
      const result = accountService.isLogged()
      
      expect(result).toBe(true)
    })

    it('should return false when no token', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const result = accountService.isLogged()
      
      expect(result).toBe(false)
    })
  })

  describe('initializeAuth', () => {
    it('should return true when token and user exist', () => {
      const token = 'test-token'
      const user = { id: 1, username: 'test' }
      
      localStorageMock.getItem
        .mockReturnValueOnce(token) // getToken call
        .mockReturnValueOnce(JSON.stringify(user)) // getUser call
      
      const result = accountService.initializeAuth()
      
      expect(result).toBe(true)
    })

    it('should return false and cleanup when no token', () => {
      localStorageMock.getItem.mockReturnValue(null)
      
      const result = accountService.initializeAuth()
      
      expect(result).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')
    })

    it('should return false and cleanup when no user', () => {
      localStorageMock.getItem
        .mockReturnValueOnce('test-token') // getToken call
        .mockReturnValueOnce(null) // getUser call
      
      const result = accountService.initializeAuth()
      
      expect(result).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
    })
  })

  describe('refreshUserData', () => {
    it('should fetch and save updated user data', async () => {
      const userData = { id: 1, username: 'updated', email: '<EMAIL>' }
      const mockResponse = { data: userData }
      
      Axios.get.mockResolvedValue(mockResponse)
      
      const result = await accountService.refreshUserData()
      
      expect(Axios.get).toHaveBeenCalledWith('/api/user/profile/')
      expect(localStorageMock.setItem).toHaveBeenCalledWith('user', JSON.stringify(userData))
      expect(result).toEqual(userData)
    })

    it('should handle API errors', async () => {
      const error = new Error('API Error')
      error.response = { status: 401 }
      
      Axios.get.mockRejectedValue(error)
      
      await expect(accountService.refreshUserData()).rejects.toThrow('API Error')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
    })

    it('should not logout on non-401 errors', async () => {
      const error = new Error('Server Error')
      error.response = { status: 500 }
      
      Axios.get.mockRejectedValue(error)
      
      await expect(accountService.refreshUserData()).rejects.toThrow('Server Error')
      expect(localStorageMock.removeItem).not.toHaveBeenCalled()
    })
  })
})
