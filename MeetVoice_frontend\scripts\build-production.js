const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

/**
 * Script de build optimisé pour la production avec pre-rendering
 */

console.log('🚀 Démarrage du build de production avec SEO optimisé...\n');

try {
  // Étape 1: Nettoyer le dossier dist
  console.log('📁 Nettoyage du dossier dist...');
  const distPath = path.join(__dirname, '../dist');
  if (fs.existsSync(distPath)) {
    fs.rmSync(distPath, { recursive: true, force: true });
    console.log('✅ Dossier dist nettoyé\n');
  }

  // Étape 2: Générer le sitemap et mettre à jour la config webpack
  console.log('🗺️  Génération du sitemap dynamique...');
  execSync('npm run generate-sitemap', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ Sitemap généré avec succès\n');

  // Étape 3: Build de production avec pre-rendering
  console.log('🏗️  Build de production avec pre-rendering...');
  execSync('npm run build', { 
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  console.log('✅ Build terminé avec succès\n');

  // Étape 4: Vérifications post-build
  console.log('🔍 Vérifications post-build...');
  
  const distFiles = [
    'index.html',
    'sitemap.xml',
    'robots.txt',
    'manifest.json'
  ];

  let allFilesExist = true;
  distFiles.forEach(file => {
    const filePath = path.join(distPath, file);
    if (fs.existsSync(filePath)) {
      console.log(`✅ ${file} présent`);
    } else {
      console.log(`❌ ${file} manquant`);
      allFilesExist = false;
    }
  });

  // Vérifier les pages pre-rendues
  const prerenderPaths = [
    'actualite/index.html',
    'article'
  ];

  prerenderPaths.forEach(pathToCheck => {
    const fullPath = path.join(distPath, pathToCheck);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ Page pre-rendue: ${pathToCheck}`);
    } else {
      console.log(`⚠️  Page pre-rendue manquante: ${pathToCheck}`);
    }
  });

  // Étape 5: Statistiques du build
  console.log('\n📊 Statistiques du build:');
  
  const indexPath = path.join(distPath, 'index.html');
  if (fs.existsSync(indexPath)) {
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    const hasMetaTags = indexContent.includes('<meta name="description"');
    const hasOpenGraph = indexContent.includes('<meta property="og:');
    const hasStructuredData = indexContent.includes('application/ld+json');
    
    console.log(`📄 Meta tags: ${hasMetaTags ? '✅' : '❌'}`);
    console.log(`📱 Open Graph: ${hasOpenGraph ? '✅' : '❌'}`);
    console.log(`🏷️  Données structurées: ${hasStructuredData ? '✅' : '❌'}`);
  }

  if (allFilesExist) {
    console.log('\n🎉 Build de production terminé avec succès !');
    console.log('\n📋 Prochaines étapes:');
    console.log('1. Tester le build localement: npm run serve:dist');
    console.log('2. Déployer sur votre serveur de production');
    console.log('3. Vérifier l\'indexation Google avec Search Console');
    console.log('4. Tester les URLs avec l\'outil de test des données structurées de Google');
  } else {
    console.log('\n⚠️  Build terminé avec des avertissements');
  }

} catch (error) {
  console.error('\n❌ Erreur lors du build:', error.message);
  process.exit(1);
}
