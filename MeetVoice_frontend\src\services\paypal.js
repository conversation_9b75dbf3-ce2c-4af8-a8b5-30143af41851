class PayPalService {
  constructor() {
    this.paypal = null
    this.isInitialized = false
    this.clientId = process.env.VUE_APP_PAYPAL_CLIENT_ID
    this.currency = 'EUR'
    this.environment = process.env.NODE_ENV === 'production' ? 'production' : 'sandbox'
  }

  /**
   * Initialiser PayPal SDK
   */
  async initialize() {
    if (this.isInitialized && window.paypal) {
      return window.paypal
    }

    if (!this.clientId) {
      throw new Error('Client ID PayPal manquant. Vérifiez VUE_APP_PAYPAL_CLIENT_ID')
    }

    try {
      // Charger le SDK PayPal
      await this.loadPayPalSDK()
      this.paypal = window.paypal
      this.isInitialized = true
      console.log('✅ PayPal initialisé avec succès')
      return this.paypal
    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation de PayPal:', error)
      throw error
    }
  }

  /**
   * Charger le SDK PayPal dynamiquement
   */
  loadPayPalSDK() {
    return new Promise((resolve, reject) => {
      // Vérifier si le script est déjà chargé
      if (window.paypal) {
        resolve(window.paypal)
        return
      }

      const script = document.createElement('script')
      script.src = `https://www.paypal.com/sdk/js?client-id=${this.clientId}&currency=${this.currency}&intent=capture&components=buttons,marks,funding-eligibility`
      script.async = true
      
      script.onload = () => {
        if (window.paypal) {
          resolve(window.paypal)
        } else {
          reject(new Error('PayPal SDK non disponible'))
        }
      }
      
      script.onerror = () => {
        reject(new Error('Erreur lors du chargement du SDK PayPal'))
      }
      
      document.head.appendChild(script)
    })
  }

  /**
   * Créer un bouton PayPal
   */
  createButton(container, options = {}) {
    if (!this.paypal) {
      throw new Error('PayPal non initialisé. Appelez initialize() d\'abord.')
    }

    const defaultOptions = {
      style: {
        layout: 'vertical',
        color: 'gold',
        shape: 'rect',
        label: 'paypal',
        height: 40
      },
      funding: {
        allowed: [this.paypal.FUNDING.PAYPAL, this.paypal.FUNDING.CARD],
        disallowed: []
      }
    }

    const buttonOptions = {
      ...defaultOptions,
      ...options,
      createOrder: this.createOrder.bind(this, options.orderData),
      onApprove: this.onApprove.bind(this, options.onSuccess),
      onError: this.onError.bind(this, options.onError),
      onCancel: this.onCancel.bind(this, options.onCancel)
    }

    return this.paypal.Buttons(buttonOptions).render(container)
  }

  /**
   * Créer une commande PayPal
   */
  async createOrder(orderData) {
    try {
      const response = await fetch('/api/abonnement/paypal/create-order/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          amount: orderData.amount,
          currency: orderData.currency || this.currency,
          description: orderData.description,
          plan_id: orderData.planId,
          metadata: orderData.metadata || {}
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la création de la commande PayPal')
      }

      const order = await response.json()
      return order.id
    } catch (error) {
      console.error('Erreur création commande PayPal:', error)
      throw error
    }
  }

  /**
   * Gérer l'approbation du paiement
   */
  async onApprove(onSuccessCallback, data) {
    try {
      const response = await fetch('/api/abonnement/paypal/capture-order/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          order_id: data.orderID
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la capture du paiement')
      }

      const result = await response.json()
      
      if (result.status === 'COMPLETED') {
        if (onSuccessCallback) {
          onSuccessCallback(result)
        }
        return result
      } else {
        throw new Error('Le paiement n\'a pas pu être finalisé')
      }
    } catch (error) {
      console.error('Erreur approbation PayPal:', error)
      throw error
    }
  }

  /**
   * Gérer les erreurs de paiement
   */
  onError(onErrorCallback, error) {
    console.error('Erreur PayPal:', error)
    if (onErrorCallback) {
      onErrorCallback(this.formatError(error))
    }
  }

  /**
   * Gérer l'annulation du paiement
   */
  onCancel(onCancelCallback, data) {
    console.log('Paiement PayPal annulé:', data)
    if (onCancelCallback) {
      onCancelCallback(data)
    }
  }

  /**
   * Créer un abonnement PayPal
   */
  async createSubscription(planId, subscriberInfo = {}) {
    try {
      const response = await fetch('/api/abonnement/paypal/create-subscription/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          plan_id: planId,
          subscriber: subscriberInfo
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la création de l\'abonnement PayPal')
      }

      const subscription = await response.json()
      return subscription
    } catch (error) {
      console.error('Erreur création abonnement PayPal:', error)
      throw error
    }
  }

  /**
   * Annuler un abonnement PayPal
   */
  async cancelSubscription(subscriptionId, reason = 'User requested cancellation') {
    try {
      const response = await fetch('/api/abonnement/paypal/cancel-subscription/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: JSON.stringify({
          subscription_id: subscriptionId,
          reason: reason
        })
      })

      if (!response.ok) {
        throw new Error('Erreur lors de l\'annulation de l\'abonnement')
      }

      const result = await response.json()
      return result
    } catch (error) {
      console.error('Erreur annulation abonnement PayPal:', error)
      throw error
    }
  }

  /**
   * Obtenir les détails d'un abonnement
   */
  async getSubscriptionDetails(subscriptionId) {
    try {
      const response = await fetch(`/api/abonnement/paypal/subscription/${subscriptionId}/`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (!response.ok) {
        throw new Error('Erreur lors de la récupération des détails de l\'abonnement')
      }

      const subscription = await response.json()
      return subscription
    } catch (error) {
      console.error('Erreur détails abonnement PayPal:', error)
      throw error
    }
  }

  /**
   * Vérifier l'éligibilité des méthodes de financement
   */
  checkFundingEligibility() {
    if (!this.paypal) {
      return {}
    }

    return {
      paypal: this.paypal.isFundingEligible(this.paypal.FUNDING.PAYPAL),
      card: this.paypal.isFundingEligible(this.paypal.FUNDING.CARD),
      venmo: this.paypal.isFundingEligible(this.paypal.FUNDING.VENMO),
      applepay: this.paypal.isFundingEligible(this.paypal.FUNDING.APPLEPAY),
      googlepay: this.paypal.isFundingEligible(this.paypal.FUNDING.GOOGLEPAY)
    }
  }

  /**
   * Formater les erreurs PayPal
   */
  formatError(error) {
    const errorMessages = {
      'INSTRUMENT_DECLINED': 'Votre méthode de paiement a été refusée.',
      'PAYER_ACCOUNT_RESTRICTED': 'Votre compte PayPal est restreint.',
      'PAYER_CANNOT_PAY': 'Impossible de traiter le paiement avec ce compte PayPal.',
      'PAYEE_ACCOUNT_RESTRICTED': 'Le compte marchand est restreint.',
      'PAYER_ACCOUNT_LOCKED_OR_CLOSED': 'Votre compte PayPal est verrouillé ou fermé.',
      'PAYEE_ACCOUNT_LOCKED_OR_CLOSED': 'Le compte marchand est verrouillé ou fermé.',
      'TRANSACTION_REFUSED': 'La transaction a été refusée.',
      'TRANSACTION_LIMIT_EXCEEDED': 'Limite de transaction dépassée.'
    }

    if (error && error.details && error.details.length > 0) {
      const detail = error.details[0]
      return errorMessages[detail.issue] || detail.description || 'Erreur de paiement PayPal'
    }

    return error.message || 'Une erreur PayPal s\'est produite'
  }

  /**
   * Nettoyer les ressources
   */
  destroy() {
    // PayPal ne nécessite pas de nettoyage spécifique
    this.paypal = null
    this.isInitialized = false
  }
}

// Instance singleton
const paypalService = new PayPalService()

export default paypalService
