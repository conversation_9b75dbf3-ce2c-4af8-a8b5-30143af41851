use anyhow::Result;
use async_trait::async_trait;
use futures::{AsyncRead, AsyncWrite, AsyncWriteExt, AsyncReadExt};
use libp2p::{
    core::ProtocolName,
    request_response::{Codec, ProtocolName as RequestResponseProtocolName},
    StreamProtocol,
};
use std::io;
use tracing::{debug, error};

// Protocole pour les données vocales
#[derive(Debu<PERSON>, <PERSON>lone)]
pub struct VoiceProtocol {
    protocol_name: StreamProtocol,
}

impl VoiceProtocol {
    pub fn new() -> Self {
        Self {
            protocol_name: StreamProtocol::new("/meetvoice/voice/1.0.0"),
        }
    }
    
    pub fn protocol_name(&self) -> StreamProtocol {
        self.protocol_name.clone()
    }
}

impl Default for VoiceProtocol {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Codec for VoiceProtocol {
    type Protocol = StreamProtocol;
    type Request = Vec<u8>;
    type Response = Vec<u8>;

    async fn read_request<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
    ) -> io::Result<Self::Request>
    where
        T: AsyncRead + Unpin + Send,
    {
        let mut length_bytes = [0u8; 4];
        io.read_exact(&mut length_bytes).await?;
        let length = u32::from_be_bytes(length_bytes) as usize;
        
        if length > 1024 * 1024 {  // Limite de 1MB pour les données vocales
            return Err(io::Error::new(
                io::ErrorKind::InvalidData,
                "Données vocales trop volumineuses",
            ));
        }
        
        let mut buffer = vec![0u8; length];
        io.read_exact(&mut buffer).await?;
        
        debug!("📥 Données vocales reçues: {} bytes", length);
        Ok(buffer)
    }

    async fn read_response<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
    ) -> io::Result<Self::Response>
    where
        T: AsyncRead + Unpin + Send,
    {
        let mut length_bytes = [0u8; 4];
        io.read_exact(&mut length_bytes).await?;
        let length = u32::from_be_bytes(length_bytes) as usize;
        
        if length > 1024 {  // Réponse limitée à 1KB
            return Err(io::Error::new(
                io::ErrorKind::InvalidData,
                "Réponse trop volumineuse",
            ));
        }
        
        let mut buffer = vec![0u8; length];
        io.read_exact(&mut buffer).await?;
        
        debug!("📥 Réponse vocale reçue: {} bytes", length);
        Ok(buffer)
    }

    async fn write_request<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
        req: Self::Request,
    ) -> io::Result<()>
    where
        T: AsyncWrite + Unpin + Send,
    {
        let length = req.len() as u32;
        io.write_all(&length.to_be_bytes()).await?;
        io.write_all(&req).await?;
        io.flush().await?;
        
        debug!("📤 Données vocales envoyées: {} bytes", req.len());
        Ok(())
    }

    async fn write_response<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
        res: Self::Response,
    ) -> io::Result<()>
    where
        T: AsyncWrite + Unpin + Send,
    {
        let length = res.len() as u32;
        io.write_all(&length.to_be_bytes()).await?;
        io.write_all(&res).await?;
        io.flush().await?;
        
        debug!("📤 Réponse vocale envoyée: {} bytes", res.len());
        Ok(())
    }
}

impl ProtocolName for VoiceProtocol {
    fn protocol_name(&self) -> &[u8] {
        self.protocol_name.as_ref().as_bytes()
    }
}

// Protocole pour les données vidéo
#[derive(Debug, Clone)]
pub struct VideoProtocol {
    protocol_name: StreamProtocol,
}

impl VideoProtocol {
    pub fn new() -> Self {
        Self {
            protocol_name: StreamProtocol::new("/meetvoice/video/1.0.0"),
        }
    }
    
    pub fn protocol_name(&self) -> StreamProtocol {
        self.protocol_name.clone()
    }
}

impl Default for VideoProtocol {
    fn default() -> Self {
        Self::new()
    }
}

#[async_trait]
impl Codec for VideoProtocol {
    type Protocol = StreamProtocol;
    type Request = Vec<u8>;
    type Response = Vec<u8>;

    async fn read_request<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
    ) -> io::Result<Self::Request>
    where
        T: AsyncRead + Unpin + Send,
    {
        let mut length_bytes = [0u8; 4];
        io.read_exact(&mut length_bytes).await?;
        let length = u32::from_be_bytes(length_bytes) as usize;
        
        if length > 10 * 1024 * 1024 {  // Limite de 10MB pour les données vidéo
            return Err(io::Error::new(
                io::ErrorKind::InvalidData,
                "Données vidéo trop volumineuses",
            ));
        }
        
        let mut buffer = vec![0u8; length];
        io.read_exact(&mut buffer).await?;
        
        debug!("📥 Données vidéo reçues: {} bytes", length);
        Ok(buffer)
    }

    async fn read_response<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
    ) -> io::Result<Self::Response>
    where
        T: AsyncRead + Unpin + Send,
    {
        let mut length_bytes = [0u8; 4];
        io.read_exact(&mut length_bytes).await?;
        let length = u32::from_be_bytes(length_bytes) as usize;
        
        if length > 1024 {  // Réponse limitée à 1KB
            return Err(io::Error::new(
                io::ErrorKind::InvalidData,
                "Réponse trop volumineuse",
            ));
        }
        
        let mut buffer = vec![0u8; length];
        io.read_exact(&mut buffer).await?;
        
        debug!("📥 Réponse vidéo reçue: {} bytes", length);
        Ok(buffer)
    }

    async fn write_request<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
        req: Self::Request,
    ) -> io::Result<()>
    where
        T: AsyncWrite + Unpin + Send,
    {
        let length = req.len() as u32;
        io.write_all(&length.to_be_bytes()).await?;
        io.write_all(&req).await?;
        io.flush().await?;
        
        debug!("📤 Données vidéo envoyées: {} bytes", req.len());
        Ok(())
    }

    async fn write_response<T>(
        &mut self,
        _protocol: &Self::Protocol,
        io: &mut T,
        res: Self::Response,
    ) -> io::Result<()>
    where
        T: AsyncWrite + Unpin + Send,
    {
        let length = res.len() as u32;
        io.write_all(&length.to_be_bytes()).await?;
        io.write_all(&res).await?;
        io.flush().await?;
        
        debug!("📤 Réponse vidéo envoyée: {} bytes", res.len());
        Ok(())
    }
}

impl ProtocolName for VideoProtocol {
    fn protocol_name(&self) -> &[u8] {
        self.protocol_name.as_ref().as_bytes()
    }
}

// Structures pour les métadonnées des streams
#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct VoiceStreamMetadata {
    pub codec: String,
    pub sample_rate: u32,
    pub channels: u8,
    pub bitrate: u32,
    pub timestamp: u64,
}

#[derive(Debug, Clone, serde::Serialize, serde::Deserialize)]
pub struct VideoStreamMetadata {
    pub codec: String,
    pub width: u32,
    pub height: u32,
    pub fps: u8,
    pub bitrate: u32,
    pub timestamp: u64,
}

// Utilitaires pour l'encodage/décodage des métadonnées
pub fn encode_voice_packet(metadata: VoiceStreamMetadata, data: Vec<u8>) -> Result<Vec<u8>> {
    let metadata_json = serde_json::to_string(&metadata)?;
    let metadata_bytes = metadata_json.as_bytes();
    let metadata_len = metadata_bytes.len() as u32;
    
    let mut packet = Vec::new();
    packet.extend_from_slice(&metadata_len.to_be_bytes());
    packet.extend_from_slice(metadata_bytes);
    packet.extend_from_slice(&data);
    
    Ok(packet)
}

pub fn decode_voice_packet(packet: Vec<u8>) -> Result<(VoiceStreamMetadata, Vec<u8>)> {
    if packet.len() < 4 {
        return Err(anyhow::anyhow!("Paquet trop court"));
    }
    
    let metadata_len = u32::from_be_bytes([packet[0], packet[1], packet[2], packet[3]]) as usize;
    
    if packet.len() < 4 + metadata_len {
        return Err(anyhow::anyhow!("Paquet incomplet"));
    }
    
    let metadata_bytes = &packet[4..4 + metadata_len];
    let metadata_json = std::str::from_utf8(metadata_bytes)?;
    let metadata: VoiceStreamMetadata = serde_json::from_str(metadata_json)?;
    
    let data = packet[4 + metadata_len..].to_vec();
    
    Ok((metadata, data))
}

pub fn encode_video_packet(metadata: VideoStreamMetadata, data: Vec<u8>) -> Result<Vec<u8>> {
    let metadata_json = serde_json::to_string(&metadata)?;
    let metadata_bytes = metadata_json.as_bytes();
    let metadata_len = metadata_bytes.len() as u32;
    
    let mut packet = Vec::new();
    packet.extend_from_slice(&metadata_len.to_be_bytes());
    packet.extend_from_slice(metadata_bytes);
    packet.extend_from_slice(&data);
    
    Ok(packet)
}

pub fn decode_video_packet(packet: Vec<u8>) -> Result<(VideoStreamMetadata, Vec<u8>)> {
    if packet.len() < 4 {
        return Err(anyhow::anyhow!("Paquet trop court"));
    }
    
    let metadata_len = u32::from_be_bytes([packet[0], packet[1], packet[2], packet[3]]) as usize;
    
    if packet.len() < 4 + metadata_len {
        return Err(anyhow::anyhow!("Paquet incomplet"));
    }
    
    let metadata_bytes = &packet[4..4 + metadata_len];
    let metadata_json = std::str::from_utf8(metadata_bytes)?;
    let metadata: VideoStreamMetadata = serde_json::from_str(metadata_json)?;
    
    let data = packet[4 + metadata_len..].to_vec();
    
    Ok((metadata, data))
}
