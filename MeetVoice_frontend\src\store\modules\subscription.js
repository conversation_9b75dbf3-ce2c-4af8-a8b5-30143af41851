import axios from 'axios'

const API_BASE_URL = process.env.VUE_APP_API_URL || 'http://127.0.0.1:8000/api'

export default {
  namespaced: true,
  
  state: {
    // Plans d'abonnement disponibles
    plans: [],
    
    // Abonnements de l'utilisateur
    userSubscriptions: [],
    
    // Factures de l'utilisateur
    userInvoices: [],
    
    // Abonnement actuel
    currentSubscription: null,
    
    // État de chargement
    loading: false,
    error: null,
    
    // Checkout Stripe
    checkoutLoading: false,
    checkoutError: null
  },
  
  mutations: {
    setPlans(state, plans) {
      state.plans = plans
    },
    
    setUserSubscriptions(state, subscriptions) {
      state.userSubscriptions = subscriptions
    },
    
    setUserInvoices(state, invoices) {
      state.userInvoices = invoices
    },
    
    setCurrentSubscription(state, subscription) {
      state.currentSubscription = subscription
    },
    
    addSubscription(state, subscription) {
      state.userSubscriptions.unshift(subscription)
      
      // Mettre à jour l'abonnement actuel si c'est le plus récent
      if (subscription.status === 'active') {
        state.currentSubscription = subscription
      }
    },
    
    updateSubscription(state, updatedSubscription) {
      const index = state.userSubscriptions.findIndex(s => s.id === updatedSubscription.id)
      if (index !== -1) {
        state.userSubscriptions.splice(index, 1, updatedSubscription)
      }
      
      // Mettre à jour l'abonnement actuel si nécessaire
      if (state.currentSubscription && state.currentSubscription.id === updatedSubscription.id) {
        state.currentSubscription = updatedSubscription
      }
    },
    
    addInvoice(state, invoice) {
      state.userInvoices.unshift(invoice)
    },
    
    setLoading(state, loading) {
      state.loading = loading
    },
    
    setError(state, error) {
      state.error = error
    },
    
    clearError(state) {
      state.error = null
    },
    
    setCheckoutLoading(state, loading) {
      state.checkoutLoading = loading
    },
    
    setCheckoutError(state, error) {
      state.checkoutError = error
    },
    
    clearCheckoutError(state) {
      state.checkoutError = null
    }
  },
  
  actions: {
    // Charger tous les plans d'abonnement disponibles
    async loadPlans({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/abonnement/api/abonnements/`)
        commit('setPlans', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des plans')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Charger les abonnements de l'utilisateur
    async loadUserSubscriptions({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/abonnement/api/abonnements-utilisateurs/`)
        commit('setUserSubscriptions', response.data.results || response.data)
        
        // Trouver l'abonnement actuel
        const activeSubscription = (response.data.results || response.data)
          .find(sub => sub.status === 'active')
        
        if (activeSubscription) {
          commit('setCurrentSubscription', activeSubscription)
        }
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des abonnements')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Charger les factures de l'utilisateur
    async loadUserInvoices({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.get(`${API_BASE_URL}/abonnement/api/factures/`)
        commit('setUserInvoices', response.data.results || response.data)
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors du chargement des factures')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Créer une session de checkout Stripe
    async createCheckoutSession({ commit }, { planId, successUrl, cancelUrl }) {
      try {
        commit('setCheckoutLoading', true)
        commit('clearCheckoutError')
        
        const response = await axios.post(`${API_BASE_URL}/abonnement/checkout/`, {
          plan_id: planId,
          success_url: successUrl || `${window.location.origin}/abonnement/success/`,
          cancel_url: cancelUrl || `${window.location.origin}/abonnement/cancel/`
        })
        
        // Rediriger vers Stripe Checkout
        if (response.data.checkout_url) {
          window.location.href = response.data.checkout_url
        }
        
        return response.data
      } catch (error) {
        const errorMessage = error.response?.data?.error || 'Erreur lors de la création du checkout'
        commit('setCheckoutError', errorMessage)
        throw error
      } finally {
        commit('setCheckoutLoading', false)
      }
    },
    
    // Annuler un abonnement
    async cancelSubscription({ commit }, subscriptionId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/abonnement/annuler/${subscriptionId}/`)
        commit('updateSubscription', response.data)
        
        // Si c'était l'abonnement actuel, le retirer
        if (response.data.status === 'cancelled') {
          commit('setCurrentSubscription', null)
        }
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de l\'annulation de l\'abonnement')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Synchroniser avec Stripe (admin)
    async syncWithStripe({ commit }) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/abonnement/api/sync-stripe/`)
        
        // Recharger les données après la synchronisation
        await this.dispatch('subscription/loadUserSubscriptions')
        await this.dispatch('subscription/loadUserInvoices')
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la synchronisation avec Stripe')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    // Créer un produit Stripe pour un plan (admin)
    async createStripeProduct({ commit }, planId) {
      try {
        commit('setLoading', true)
        commit('clearError')
        
        const response = await axios.post(`${API_BASE_URL}/abonnement/api/create-stripe-product/${planId}/`)
        
        // Recharger les plans après la création
        await this.dispatch('subscription/loadPlans')
        
        return response.data
      } catch (error) {
        commit('setError', 'Erreur lors de la création du produit Stripe')
        throw error
      } finally {
        commit('setLoading', false)
      }
    },
    
    clearError({ commit }) {
      commit('clearError')
    },
    
    clearCheckoutError({ commit }) {
      commit('clearCheckoutError')
    }
  },
  
  getters: {
    plans: state => state.plans,
    userSubscriptions: state => state.userSubscriptions,
    userInvoices: state => state.userInvoices,
    currentSubscription: state => state.currentSubscription,
    loading: state => state.loading,
    error: state => state.error,
    checkoutLoading: state => state.checkoutLoading,
    checkoutError: state => state.checkoutError,
    
    // Vérifier si l'utilisateur a un abonnement actif
    hasActiveSubscription: state => {
      return state.currentSubscription && state.currentSubscription.status === 'active'
    },
    
    // Obtenir les plans par type de facturation
    plansByBilling: state => {
      const grouped = {}
      state.plans.forEach(plan => {
        if (!grouped[plan.billing_cycle]) {
          grouped[plan.billing_cycle] = []
        }
        grouped[plan.billing_cycle].push(plan)
      })
      return grouped
    },
    
    // Obtenir les factures payées
    paidInvoices: state => {
      return state.userInvoices.filter(invoice => invoice.status === 'paid')
    },
    
    // Obtenir les factures en attente
    pendingInvoices: state => {
      return state.userInvoices.filter(invoice => invoice.status === 'pending')
    },
    
    // Calculer le total dépensé
    totalSpent: state => {
      return state.userInvoices
        .filter(invoice => invoice.status === 'paid')
        .reduce((total, invoice) => total + parseFloat(invoice.amount || 0), 0)
    },
    
    // Vérifier si l'abonnement expire bientôt (dans les 7 jours)
    subscriptionExpiringSoon: state => {
      if (!state.currentSubscription || !state.currentSubscription.expires_at) {
        return false
      }
      
      const expirationDate = new Date(state.currentSubscription.expires_at)
      const now = new Date()
      const daysUntilExpiration = Math.ceil((expirationDate - now) / (1000 * 60 * 60 * 24))
      
      return daysUntilExpiration <= 7 && daysUntilExpiration > 0
    }
  }
}
