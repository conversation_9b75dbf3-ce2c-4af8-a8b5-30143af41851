/**
 * Service de Test pour l'API Vosk
 * Tests complets de l'API Vosk backend
 */

import Axios from './caller.service.js';

class VoskTestService {
  constructor() {
    this.baseUrl = '/api';
    this.testResults = [];
    
    console.log('🧪 Service de Test Vosk initialisé');
  }

  /**
   * Exécuter tous les tests de l'API Vosk
   */
  async runAllTests() {
    console.log('🧪 === DÉBUT DES TESTS API VOSK ===');
    this.testResults = [];

    const tests = [
      { name: 'Test de connectivité', fn: () => this.testConnectivity() },
      { name: 'Test du statut Vosk', fn: () => this.testVoskStatus() },
      { name: 'Test des endpoints disponibles', fn: () => this.testEndpoints() },
      { name: 'Test d\'upload audio', fn: () => this.testAudioUpload() },
      { name: 'Test de reconnaissance vocale', fn: () => this.testSpeechRecognition() }
    ];

    for (const test of tests) {
      try {
        console.log(`🔍 Exécution: ${test.name}...`);
        const result = await test.fn();
        this.testResults.push({
          name: test.name,
          status: 'SUCCESS',
          result: result,
          timestamp: new Date().toISOString()
        });
        console.log(`✅ ${test.name}: RÉUSSI`);
      } catch (error) {
        this.testResults.push({
          name: test.name,
          status: 'FAILED',
          error: error.message,
          timestamp: new Date().toISOString()
        });
        console.error(`❌ ${test.name}: ÉCHEC -`, error.message);
      }
    }

    console.log('🧪 === FIN DES TESTS API VOSK ===');
    this.displayTestSummary();
    return this.testResults;
  }

  /**
   * Test 1: Connectivité de base
   */
  async testConnectivity() {
    console.log('📡 Test de connectivité backend...');
    
    try {
      const response = await Axios.get('/');
      return {
        status: response.status,
        message: 'Backend accessible',
        baseUrl: this.baseUrl
      };
    } catch (error) {
      throw new Error(`Backend inaccessible: ${error.message}`);
    }
  }

  /**
   * Test 2: Statut de l'API Vosk
   */
  async testVoskStatus() {
    console.log('🎯 Test du statut Vosk...');
    
    try {
      const response = await Axios.get(`${this.baseUrl}/vosk/status/`);
      return {
        status: response.status,
        data: response.data,
        message: 'API Vosk disponible'
      };
    } catch (error) {
      if (error.response) {
        throw new Error(`API Vosk erreur ${error.response.status}: ${error.response.data?.detail || error.message}`);
      } else {
        throw new Error(`API Vosk inaccessible: ${error.message}`);
      }
    }
  }

  /**
   * Test 3: Endpoints disponibles
   */
  async testEndpoints() {
    console.log('🔗 Test des endpoints Vosk...');
    
    const endpoints = [
      { path: '/vosk/status/', method: 'GET' },
      { path: '/vosk/speech-to-text/', method: 'POST' },
      { path: '/vosk/languages/', method: 'GET' }
    ];

    const results = [];

    for (const endpoint of endpoints) {
      try {
        if (endpoint.method === 'GET') {
          const response = await Axios.get(`${this.baseUrl}${endpoint.path}`);
          results.push({
            endpoint: endpoint.path,
            method: endpoint.method,
            status: response.status,
            available: true
          });
        } else {
          // Pour POST, on teste juste l'existence (sans données)
          results.push({
            endpoint: endpoint.path,
            method: endpoint.method,
            status: 'NOT_TESTED',
            available: 'UNKNOWN'
          });
        }
      } catch (error) {
        results.push({
          endpoint: endpoint.path,
          method: endpoint.method,
          status: error.response?.status || 'ERROR',
          available: false,
          error: error.message
        });
      }
    }

    return results;
  }

  /**
   * Test 4: Upload audio de test
   */
  async testAudioUpload() {
    console.log('🎵 Test d\'upload audio...');
    
    try {
      // Créer un fichier audio de test (silence de 1 seconde)
      const audioBlob = this.createTestAudioBlob();
      
      const formData = new FormData();
      formData.append('audio', audioBlob, 'test-audio.webm');
      formData.append('language', 'fr-FR');

      const response = await Axios.post(`${this.baseUrl}/vosk/speech-to-text/`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        },
        timeout: 10000 // 10 secondes timeout
      });

      return {
        status: response.status,
        data: response.data,
        message: 'Upload audio réussi'
      };
    } catch (error) {
      if (error.response) {
        throw new Error(`Upload audio échec ${error.response.status}: ${error.response.data?.detail || error.message}`);
      } else {
        throw new Error(`Upload audio erreur: ${error.message}`);
      }
    }
  }

  /**
   * Test 5: Reconnaissance vocale avec audio réel
   */
  async testSpeechRecognition() {
    console.log('🎤 Test de reconnaissance vocale...');
    
    return new Promise((resolve, reject) => {
      // Test avec microphone réel
      navigator.mediaDevices.getUserMedia({ audio: true })
        .then(stream => {
          const mediaRecorder = new MediaRecorder(stream);
          const audioChunks = [];

          mediaRecorder.ondataavailable = (event) => {
            audioChunks.push(event.data);
          };

          mediaRecorder.onstop = async () => {
            stream.getTracks().forEach(track => track.stop());
            
            try {
              const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
              
              const formData = new FormData();
              formData.append('audio', audioBlob, 'test-speech.webm');
              formData.append('language', 'fr-FR');

              const response = await Axios.post(`${this.baseUrl}/vosk/speech-to-text/`, formData, {
                headers: {
                  'Content-Type': 'multipart/form-data'
                },
                timeout: 15000
              });

              resolve({
                status: response.status,
                data: response.data,
                message: 'Reconnaissance vocale testée',
                audioSize: audioBlob.size
              });
            } catch (error) {
              reject(new Error(`Reconnaissance vocale échec: ${error.message}`));
            }
          };

          // Enregistrer 3 secondes
          mediaRecorder.start();
          setTimeout(() => {
            mediaRecorder.stop();
          }, 3000);
        })
        .catch(error => {
          reject(new Error(`Accès microphone refusé: ${error.message}`));
        });
    });
  }

  /**
   * Créer un blob audio de test (silence)
   */
  createTestAudioBlob() {
    // Créer un buffer audio vide (silence)
    const sampleRate = 16000;
    const duration = 1; // 1 seconde
    const numSamples = sampleRate * duration;
    
    const audioBuffer = new ArrayBuffer(numSamples * 2);
    const view = new DataView(audioBuffer);
    
    // Remplir avec du silence (zéros)
    for (let i = 0; i < numSamples; i++) {
      view.setInt16(i * 2, 0, true);
    }
    
    return new Blob([audioBuffer], { type: 'audio/webm' });
  }

  /**
   * Afficher le résumé des tests
   */
  displayTestSummary() {
    console.log('\n🧪 === RÉSUMÉ DES TESTS VOSK ===');
    
    const successful = this.testResults.filter(t => t.status === 'SUCCESS').length;
    const failed = this.testResults.filter(t => t.status === 'FAILED').length;
    
    console.log(`✅ Tests réussis: ${successful}`);
    console.log(`❌ Tests échoués: ${failed}`);
    console.log(`📊 Total: ${this.testResults.length}`);
    
    if (failed > 0) {
      console.log('\n❌ ÉCHECS DÉTAILLÉS:');
      this.testResults
        .filter(t => t.status === 'FAILED')
        .forEach(test => {
          console.log(`- ${test.name}: ${test.error}`);
        });
    }
    
    console.log('\n🧪 === FIN DU RÉSUMÉ ===\n');
  }

  /**
   * Obtenir les résultats des tests
   */
  getTestResults() {
    return this.testResults;
  }

  /**
   * Test rapide de l'API Vosk
   */
  async quickTest() {
    console.log('⚡ Test rapide API Vosk...');
    
    try {
      const response = await Axios.get(`${this.baseUrl}/vosk/status/`);
      console.log('✅ API Vosk disponible:', response.data);
      return true;
    } catch (error) {
      console.error('❌ API Vosk indisponible:', error.message);
      return false;
    }
  }
}

// Export de l'instance unique
export const voskTestService = new VoskTestService();
export default voskTestService;
