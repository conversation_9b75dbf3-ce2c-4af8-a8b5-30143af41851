# MeetVoice WebSocket Server

Serveur WebSocket haute performance en Rust pour la plateforme de rencontres vocales MeetVoice, avec support P2P pour les communications audio/vidéo.

## 🚀 Fonctionnalités

- **WebSocket haute performance** avec Tokio + Axum
- **Communication P2P** avec libp2p pour audio/vidéo
- **Bases de données duales** : PostgreSQL (données utilisateurs) + MongoDB (temps réel)
- **Protocoles personnalisés** pour streaming vocal et vidéo
- **Architecture modulaire** et extensible
- **Tests d'intégration** complets
- **Déploiement Docker** prêt pour la production

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   WebSocket     │    │      P2P        │    │   Databases     │
│   (Axum/Tokio)  │    │   (libp2p)      │    │ PostgreSQL +    │
│                 │    │                 │    │   MongoDB       │
│ • Chat          │    │ • Voice Stream  │    │                 │
│ • Signaling     │    │ • Video Stream  │    │ • Users         │
│ • Auth          │    │ • Discovery     │    │ • Messages      │
│ • Rooms         │    │ • Relay         │    │ • Sessions      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Technologies

- **Rust** - Langage principal
- **Tokio** - Runtime asynchrone
- **Axum** - Framework web moderne
- **libp2p** - Réseau peer-to-peer
- **PostgreSQL** - Base de données relationnelle
- **MongoDB** - Base de données NoSQL
- **Docker** - Conteneurisation

## 📦 Installation

### Prérequis

- Rust 1.75+
- PostgreSQL 15+
- MongoDB 7+
- Docker & Docker Compose (optionnel)

### Installation locale

1. **Cloner le projet**
```bash
git clone <repository-url>
cd meetvoice-websocket
```

2. **Configurer l'environnement**
```bash
cp .env.example .env
# Éditer .env avec vos paramètres
```

3. **Démarrer les bases de données**
```bash
docker-compose up -d postgres mongodb
```

4. **Compiler et lancer**
```bash
cargo build --release
cargo run
```

### Installation avec Docker

```bash
# Démarrer tous les services
docker-compose up -d

# Voir les logs
docker-compose logs -f meetvoice-websocket
```

## 🔧 Configuration

Variables d'environnement principales :

```env
# Serveur
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# PostgreSQL (données utilisateurs)
POSTGRES_URL=postgresql://postgres:password@localhost:5432/meetvoice

# MongoDB (données temps réel)
MONGODB_URL=mongodb://localhost:27017
MONGODB_DATABASE=meetvoice

# P2P
P2P_PORT=9090
P2P_ENABLE_MDNS=true
P2P_ENABLE_RELAY=true
```

## 🌐 API WebSocket

### Connexion
```
ws://localhost:8080/ws
```

### Messages supportés

#### Authentification
```json
{
  "type": "Auth",
  "data": {
    "token": "user-uuid-or-jwt-token"
  }
}
```

#### Chat
```json
{
  "type": "SendMessage",
  "data": {
    "room_id": "room-123",
    "content": "Hello world!",
    "message_type": "Text"
  }
}
```

#### Sessions vocales
```json
{
  "type": "JoinVoiceSession",
  "data": {
    "session_id": "session-456"
  }
}
```

#### WebRTC
```json
{
  "type": "WebRTCOffer",
  "data": {
    "to_user": "target-user-uuid",
    "offer_type": "Voice",
    "sdp": "webrtc-sdp-data"
  }
}
```

## 🔗 API REST

### Santé du serveur
```bash
GET /health
```

### Peers P2P
```bash
GET /p2p/peers
POST /p2p/connect
```

## 🧪 Tests

### Tests unitaires
```bash
cargo test
```

### Tests d'intégration
```bash
cargo test --test integration_tests
```

### Tests avec bases de données
```bash
# Démarrer les services de test
docker-compose up -d postgres mongodb

# Lancer les tests
cargo test test_database_connections
```

## 📊 Monitoring

Le serveur expose plusieurs métriques :

- Connexions WebSocket actives
- Sessions vocales en cours
- Peers P2P connectés
- Statistiques des bases de données

## 🔒 Sécurité

- Authentification par token/JWT
- Validation des messages
- Limites de taille des paquets
- Nettoyage automatique des sessions expirées

## 🚀 Déploiement

### Production avec Docker

```bash
# Build de l'image
docker build -t meetvoice-websocket .

# Déploiement
docker-compose -f docker-compose.prod.yml up -d
```

### Variables d'environnement de production

```env
RUST_LOG=meetvoice_websocket=info,libp2p=warn
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
# ... autres variables sécurisées
```

## 📈 Performance

- **Connexions simultanées** : 10,000+ WebSocket
- **Latence P2P** : <50ms en local
- **Throughput** : 1GB/s+ pour le streaming
- **Mémoire** : ~50MB base + 1KB par connexion

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit les changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

- **Issues** : GitHub Issues
- **Documentation** : `/docs`
- **Chat** : Discord/Slack de l'équipe

## 🔄 Roadmap

- [ ] Authentification JWT complète
- [ ] Métriques Prometheus
- [ ] Load balancing multi-instances
- [ ] Chiffrement E2E pour P2P
- [ ] API GraphQL
- [ ] Support WebRTC TURN/STUN
- [ ] Clustering Redis
- [ ] Tests de charge automatisés

---

**MeetVoice** - Connecter les cœurs par la voix 💕🎤
