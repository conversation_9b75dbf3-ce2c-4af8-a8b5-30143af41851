#!/usr/bin/env node

/**
 * Test de l'API TTS backend MeetVoice
 * Usage: node test-tts-backend.js
 */

const http = require('http');
const { URL } = require('url');

// Configuration
const CONFIG = {
    backendUrl: 'http://127.0.0.1:8000',
    ttsEndpoint: '/tts/public/synthesize/',
    timeout: 15000
};

// Tests de synthèse vocale
const TTS_TESTS = [
    {
        name: 'Question simple',
        text: 'Bonjour ! Pouvez-vous me dire votre prénom ?',
        voice_id: 'female_young',
        language: 'fr',
        speed: 0.85,
        pitch: 1.0
    },
    {
        name: 'Question avec email',
        text: 'Quelle est votre adresse email ? Vous pouvez épeler les caractères spéciaux.',
        voice_id: 'female_young',
        language: 'fr',
        speed: 0.85,
        pitch: 1.0
    },
    {
        name: 'Question personnalisée',
        text: '<PERSON>, êtes-vous non-binaire ?',
        voice_id: 'female_young',
        language: 'fr',
        speed: 0.85,
        pitch: 1.0
    },
    {
        name: 'Test voix masculine',
        text: 'Que recherchez-vous sur MeetVoice ?',
        voice_id: 'male_young',
        language: 'fr',
        speed: 0.85,
        pitch: 1.0
    },
    {
        name: 'Test vitesse lente',
        text: 'Pour finir, pouvez-vous vous décrire en quelques mots ?',
        voice_id: 'female_young',
        language: 'fr',
        speed: 0.7,
        pitch: 1.0
    }
];

// Couleurs console
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function makeRequest(url, options, data = null) {
    return new Promise((resolve, reject) => {
        const urlObj = new URL(url);
        
        const requestOptions = {
            hostname: urlObj.hostname,
            port: urlObj.port || 80,
            path: urlObj.pathname + urlObj.search,
            method: options.method || 'GET',
            headers: options.headers || {},
            timeout: CONFIG.timeout
        };
        
        const req = http.request(requestOptions, (res) => {
            let responseData = Buffer.alloc(0);
            
            res.on('data', (chunk) => {
                responseData = Buffer.concat([responseData, chunk]);
            });
            
            res.on('end', () => {
                resolve({
                    statusCode: res.statusCode,
                    headers: res.headers,
                    data: responseData,
                    size: responseData.length
                });
            });
        });
        
        req.on('error', (error) => {
            reject(error);
        });
        
        req.on('timeout', () => {
            req.destroy();
            reject(new Error('Request timeout'));
        });
        
        if (data) {
            req.write(data);
        }
        
        req.end();
    });
}

async function testTTSEndpoint() {
    log('\n🔍 Test 1: Vérification de l\'endpoint TTS', 'cyan');
    
    try {
        const testPayload = {
            text: 'Test de connexion',
            voice_id: 'female_young',
            language: 'fr',
            speed: 1.0,
            pitch: 1.0
        };
        
        const response = await makeRequest(
            `${CONFIG.backendUrl}${CONFIG.ttsEndpoint}`,
            {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Content-Length': Buffer.byteLength(JSON.stringify(testPayload))
                }
            },
            JSON.stringify(testPayload)
        );
        
        if (response.statusCode === 200) {
            log('✅ Endpoint TTS accessible', 'green');
            log(`📊 Taille de la réponse: ${response.size} bytes`, 'blue');
            
            // Vérifier le type de contenu
            const contentType = response.headers['content-type'];
            if (contentType && contentType.includes('audio')) {
                log(`🎵 Type audio détecté: ${contentType}`, 'green');
                return true;
            } else {
                log(`⚠️  Type de contenu inattendu: ${contentType}`, 'yellow');
                return false;
            }
        } else {
            log(`❌ Erreur HTTP: ${response.statusCode}`, 'red');
            return false;
        }
    } catch (error) {
        log(`❌ Erreur de connexion: ${error.message}`, 'red');
        return false;
    }
}

async function testVoiceTypes() {
    log('\n🎭 Test 2: Test des différents types de voix', 'cyan');
    
    const voiceTypes = ['female_young', 'female_mature', 'male_young', 'male_mature'];
    const results = {};
    
    for (const voiceType of voiceTypes) {
        try {
            log(`🎤 Test de la voix: ${voiceType}`, 'blue');
            
            const payload = {
                text: `Test de la voix ${voiceType}`,
                voice_id: voiceType,
                language: 'fr',
                speed: 0.85,
                pitch: 1.0
            };
            
            const response = await makeRequest(
                `${CONFIG.backendUrl}${CONFIG.ttsEndpoint}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                },
                JSON.stringify(payload)
            );
            
            if (response.statusCode === 200 && response.size > 0) {
                log(`✅ ${voiceType}: OK (${response.size} bytes)`, 'green');
                results[voiceType] = true;
            } else {
                log(`❌ ${voiceType}: ERREUR (${response.statusCode})`, 'red');
                results[voiceType] = false;
            }
            
            // Petite pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 500));
            
        } catch (error) {
            log(`❌ ${voiceType}: ERREUR (${error.message})`, 'red');
            results[voiceType] = false;
        }
    }
    
    const successCount = Object.values(results).filter(Boolean).length;
    log(`\n📊 Voix fonctionnelles: ${successCount}/${voiceTypes.length}`, 'cyan');
    
    return results;
}

async function testInterviewQuestions() {
    log('\n📝 Test 3: Test des questions d\'interview', 'cyan');
    
    const results = [];
    
    for (let i = 0; i < TTS_TESTS.length; i++) {
        const test = TTS_TESTS[i];
        
        try {
            log(`🎤 ${i + 1}/${TTS_TESTS.length}: ${test.name}`, 'blue');
            
            const startTime = Date.now();
            
            const response = await makeRequest(
                `${CONFIG.backendUrl}${CONFIG.ttsEndpoint}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                },
                JSON.stringify({
                    text: test.text,
                    voice_id: test.voice_id,
                    language: test.language,
                    speed: test.speed,
                    pitch: test.pitch
                })
            );
            
            const duration = Date.now() - startTime;
            
            if (response.statusCode === 200 && response.size > 0) {
                log(`✅ ${test.name}: OK (${response.size} bytes, ${duration}ms)`, 'green');
                results.push({
                    name: test.name,
                    success: true,
                    size: response.size,
                    duration: duration
                });
            } else {
                log(`❌ ${test.name}: ERREUR (${response.statusCode})`, 'red');
                results.push({
                    name: test.name,
                    success: false,
                    status: response.statusCode
                });
            }
            
            // Pause entre les tests
            await new Promise(resolve => setTimeout(resolve, 300));
            
        } catch (error) {
            log(`❌ ${test.name}: ERREUR (${error.message})`, 'red');
            results.push({
                name: test.name,
                success: false,
                error: error.message
            });
        }
    }
    
    return results;
}

async function testPerformance() {
    log('\n⚡ Test 4: Test de performance', 'cyan');
    
    const testText = 'Question de performance pour mesurer la vitesse de génération audio.';
    const iterations = 5;
    const durations = [];
    
    for (let i = 0; i < iterations; i++) {
        try {
            log(`📊 Test ${i + 1}/${iterations}`, 'blue');
            
            const startTime = Date.now();
            
            const response = await makeRequest(
                `${CONFIG.backendUrl}${CONFIG.ttsEndpoint}`,
                {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                },
                JSON.stringify({
                    text: testText,
                    voice_id: 'female_young',
                    language: 'fr',
                    speed: 0.85,
                    pitch: 1.0
                })
            );
            
            const duration = Date.now() - startTime;
            durations.push(duration);
            
            if (response.statusCode === 200) {
                log(`✅ Test ${i + 1}: ${duration}ms`, 'green');
            } else {
                log(`❌ Test ${i + 1}: ERREUR`, 'red');
            }
            
            await new Promise(resolve => setTimeout(resolve, 200));
            
        } catch (error) {
            log(`❌ Test ${i + 1}: ERREUR (${error.message})`, 'red');
        }
    }
    
    if (durations.length > 0) {
        const avgDuration = durations.reduce((a, b) => a + b, 0) / durations.length;
        const minDuration = Math.min(...durations);
        const maxDuration = Math.max(...durations);
        
        log(`\n📈 Statistiques de performance:`, 'cyan');
        log(`   Moyenne: ${avgDuration.toFixed(0)}ms`, 'blue');
        log(`   Minimum: ${minDuration}ms`, 'blue');
        log(`   Maximum: ${maxDuration}ms`, 'blue');
        
        return {
            average: avgDuration,
            min: minDuration,
            max: maxDuration,
            samples: durations.length
        };
    }
    
    return null;
}

async function runAllTTSTests() {
    log('🎤 DÉBUT DES TESTS API TTS BACKEND', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    const results = {
        endpoint: false,
        voices: {},
        questions: [],
        performance: null
    };
    
    // Test 1: Endpoint TTS
    results.endpoint = await testTTSEndpoint();
    
    if (results.endpoint) {
        // Test 2: Types de voix
        results.voices = await testVoiceTypes();
        
        // Test 3: Questions d'interview
        results.questions = await testInterviewQuestions();
        
        // Test 4: Performance
        results.performance = await testPerformance();
    } else {
        log('\n⏭️  Tests suivants ignorés (endpoint inaccessible)', 'yellow');
    }
    
    // Résumé final
    log('\n📊 RÉSUMÉ DES TESTS TTS', 'magenta');
    log('=' .repeat(50), 'magenta');
    
    // Endpoint
    const endpointStatus = results.endpoint ? '✅ ACCESSIBLE' : '❌ INACCESSIBLE';
    const endpointColor = results.endpoint ? 'green' : 'red';
    log(`Endpoint TTS: ${endpointStatus}`, endpointColor);
    
    // Voix
    if (Object.keys(results.voices).length > 0) {
        const voiceCount = Object.values(results.voices).filter(Boolean).length;
        const totalVoices = Object.keys(results.voices).length;
        log(`Voix disponibles: ${voiceCount}/${totalVoices}`, voiceCount > 0 ? 'green' : 'red');
    }
    
    // Questions
    if (results.questions.length > 0) {
        const successfulQuestions = results.questions.filter(q => q.success).length;
        log(`Questions testées: ${successfulQuestions}/${results.questions.length}`, successfulQuestions > 0 ? 'green' : 'red');
    }
    
    // Performance
    if (results.performance) {
        const avgTime = results.performance.average.toFixed(0);
        const perfColor = results.performance.average < 2000 ? 'green' : results.performance.average < 5000 ? 'yellow' : 'red';
        log(`Performance moyenne: ${avgTime}ms`, perfColor);
    }
    
    if (results.endpoint && Object.values(results.voices).some(Boolean)) {
        log('\n🎉 API TTS BACKEND FONCTIONNELLE!', 'green');
        log('L\'interview vocal peut utiliser la synthèse vocale.', 'green');
    } else {
        log('\n⚠️  PROBLÈMES DÉTECTÉS AVEC L\'API TTS', 'yellow');
        log('Vérifiez la configuration du backend TTS.', 'yellow');
    }
    
    return results;
}

// Exécution des tests
if (require.main === module) {
    runAllTTSTests().catch(error => {
        log(`\n💥 ERREUR FATALE: ${error.message}`, 'red');
        process.exit(1);
    });
}

module.exports = { runAllTTSTests, CONFIG, TTS_TESTS };
