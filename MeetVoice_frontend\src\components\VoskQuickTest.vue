<template>
  <div class="vosk-quick-test">
    <div class="test-header">
      <h3>🧪 Test API Vosk</h3>
      <div class="status-indicator" :class="statusClass">
        {{ statusText }}
      </div>
    </div>

    <div class="test-actions">
      <button 
        @click="runQuickTest" 
        :disabled="isRunning"
        class="btn btn-test"
      >
        {{ isRunning ? '⏳ Test...' : '⚡ Test Rapide' }}
      </button>
      
      <router-link to="/vosk-test" class="btn btn-detailed">
        🧪 Tests Détaillés
      </router-link>
    </div>

    <div v-if="lastResult" class="test-result">
      <div class="result-header">
        <span class="result-icon">
          {{ lastResult.success ? '✅' : '❌' }}
        </span>
        <span class="result-text">
          {{ lastResult.message }}
        </span>
        <span class="result-time">
          {{ formatTime(lastResult.timestamp) }}
        </span>
      </div>
      
      <div v-if="lastResult.details" class="result-details">
        <pre>{{ JSON.stringify(lastResult.details, null, 2) }}</pre>
      </div>
    </div>

    <div class="api-endpoints">
      <h4>🔗 Endpoints API</h4>
      <ul>
        <li><code>GET /api/vosk/status/</code></li>
        <li><code>POST /api/vosk/speech-to-text/</code></li>
      </ul>
    </div>
  </div>
</template>

<script>
import voskTestService from '@/_services/vosk-test.service.js';

export default {
  name: 'VoskQuickTest',
  data() {
    return {
      isRunning: false,
      lastResult: null,
      apiStatus: 'unknown' // 'available', 'unavailable', 'unknown'
    };
  },
  computed: {
    statusClass() {
      return {
        'status-available': this.apiStatus === 'available',
        'status-unavailable': this.apiStatus === 'unavailable',
        'status-unknown': this.apiStatus === 'unknown'
      };
    },
    statusText() {
      switch (this.apiStatus) {
        case 'available': return '🟢 API Disponible';
        case 'unavailable': return '🔴 API Indisponible';
        default: return '🟡 Statut Inconnu';
      }
    }
  },
  methods: {
    async runQuickTest() {
      this.isRunning = true;
      console.log('⚡ Test rapide API Vosk...');
      
      try {
        const success = await voskTestService.quickTest();
        
        this.lastResult = {
          success: success,
          message: success ? 'API Vosk disponible et fonctionnelle' : 'API Vosk indisponible',
          timestamp: new Date(),
          details: success ? { status: 'OK', endpoint: '/api/vosk/status/' } : null
        };
        
        this.apiStatus = success ? 'available' : 'unavailable';
        
      } catch (error) {
        console.error('Erreur test rapide:', error);
        
        this.lastResult = {
          success: false,
          message: `Erreur: ${error.message}`,
          timestamp: new Date(),
          details: null
        };
        
        this.apiStatus = 'unavailable';
      } finally {
        this.isRunning = false;
      }
    },

    formatTime(timestamp) {
      return timestamp.toLocaleTimeString();
    },

    async checkApiStatus() {
      try {
        const success = await voskTestService.quickTest();
        this.apiStatus = success ? 'available' : 'unavailable';
      } catch (error) {
        this.apiStatus = 'unavailable';
      }
    }
  },
  async mounted() {
    console.log('🧪 Composant test Vosk chargé');
    // Vérifier le statut au chargement
    await this.checkApiStatus();
  }
};
</script>

<style scoped>
.vosk-quick-test {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.test-header h3 {
  margin: 0;
  color: #2c3e50;
}

.status-indicator {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.9em;
  font-weight: 600;
}

.status-available {
  background: #d5f4e6;
  color: #27ae60;
}

.status-unavailable {
  background: #fadbd8;
  color: #e74c3c;
}

.status-unknown {
  background: #fff3cd;
  color: #856404;
}

.test-actions {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-test {
  background: #3498db;
  color: white;
}

.btn-detailed {
  background: #27ae60;
  color: white;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.test-result {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
}

.result-icon {
  font-size: 1.2em;
}

.result-text {
  flex: 1;
  font-weight: 600;
}

.result-time {
  color: #666;
  font-size: 0.9em;
}

.result-details {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 10px;
  margin-top: 10px;
}

.result-details pre {
  margin: 0;
  font-family: 'Courier New', monospace;
  font-size: 0.8em;
  white-space: pre-wrap;
}

.api-endpoints {
  border-top: 1px solid #dee2e6;
  padding-top: 15px;
}

.api-endpoints h4 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 1em;
}

.api-endpoints ul {
  margin: 0;
  padding-left: 20px;
}

.api-endpoints li {
  margin: 5px 0;
}

.api-endpoints code {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9em;
}
</style>
