# 🧪 TESTS DIAGNOSTIQUES - BOUTON MICROPHONE GAUCHE

## 🎯 OBJECTIF
Diagnostiquer pourquoi le bouton microphone principal (gauche/gris) ne fonctionne pas.

## 📋 SÉRIE DE TESTS À EFFECTUER

### **TEST 1: Vérification Console**
1. Ouvrir F12 → Console
2. Cliquer sur le bouton microphone gauche
3. **Vérifier les messages :**
   - ✅ "🎤 Démarrage de l'écoute MANUELLE (comme bouton bleu)..."
   - ✅ "🎯 Microphone sélectionné: [ID]"
   - ✅ "✅ Écoute démarrée - Cliquez à nouveau pour arrêter"
   - ❌ Messages d'erreur ?

### **TEST 2: État des Variables**
Dans la console, taper :
```javascript
// Vérifier l'état du composant
$vm0.isListening
$vm0.isProcessing  
$vm0.isSupported
$vm0.selectedMicrophoneId
```

### **TEST 3: Permissions Microphone**
1. Vérifier l'icône microphone dans la barre d'adresse
2. Autoriser l'accès si demandé
3. Recharger la page si nécessaire

### **TEST 4: Comparaison avec Bouton Bleu**
1. Tester le bouton bleu (fonctionne ?)
2. Comparer les paramètres dans voiceService.startListening()
3. Vérifier les différences

### **TEST 5: Service Voice**
Dans la console :
```javascript
// Vérifier le service
voiceService.isSupported()
voiceService.getAvailableDevices()
```

### **TEST 6: Paramètres d'Écoute**
Vérifier dans le code que les paramètres sont corrects :
- `timeout: 0` (pas de timeout)
- `silenceTimeout: 0` (pas de silence timeout)  
- `continuous: true` (mode continu)
- `microphoneId: this.$store.getters.getSelectedMicrophone`

## 🔍 RÉSULTATS ATTENDUS

### **Si ça marche :**
- Console : Messages de démarrage
- Interface : Bouton change d'état (listening)
- Audio : Niveau sonore détecté
- Transcription : Texte apparaît

### **Si ça ne marche pas :**
- Console : Messages d'erreur
- Interface : Bouton reste inactif
- Audio : Pas de détection
- Transcription : Rien

## 🛠️ SOLUTIONS POSSIBLES

### **Erreur Permission :**
```
❌ Permission microphone refusée
→ Cliquer sur l'icône microphone dans l'URL
→ Autoriser l'accès
```

### **Erreur Service :**
```
❌ voiceService undefined
→ Vérifier l'import du service
→ Redémarrer le serveur
```

### **Erreur Paramètres :**
```
❌ Paramètres invalides
→ Vérifier timeout: 0
→ Vérifier continuous: true
```

### **Erreur Microphone :**
```
❌ Microphone non sélectionné
→ Vérifier selectedMicrophoneId
→ Actualiser la liste des micros
```

## 📝 RAPPORT DE TEST

**Date :** ___________
**Navigateur :** ___________

### Test 1 - Console :
- [ ] Messages de démarrage visibles
- [ ] Pas d'erreurs
- [ ] Microphone ID affiché

### Test 2 - Variables :
- [ ] isSupported: true
- [ ] selectedMicrophoneId: [valeur]
- [ ] isListening change d'état

### Test 3 - Permissions :
- [ ] Accès microphone autorisé
- [ ] Pas de blocage navigateur

### Test 4 - Comparaison :
- [ ] Bouton bleu fonctionne
- [ ] Paramètres identiques

### Test 5 - Service :
- [ ] voiceService disponible
- [ ] Devices détectés

### Test 6 - Paramètres :
- [ ] timeout: 0
- [ ] continuous: true
- [ ] microphoneId valide

## 🎯 CONCLUSION

**Problème identifié :** ___________
**Solution appliquée :** ___________
**Résultat :** ___________
