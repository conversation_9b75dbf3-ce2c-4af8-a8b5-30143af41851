/** @format */

import { voiceService } from '@/_services/voice.service';

// Mock des APIs Web Speech
const mockSpeechRecognition = {
  start: jest.fn(),
  stop: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  continuous: false,
  interimResults: false,
  lang: 'fr-FR',
  maxAlternatives: 1,
  onstart: null,
  onresult: null,
  onend: null,
  onerror: null,
};

const mockSpeechSynthesis = {
  speak: jest.fn(),
  cancel: jest.fn(),
  pause: jest.fn(),
  resume: jest.fn(),
  getVoices: jest.fn(() => [
    { lang: 'fr-FR', name: 'French Voice' },
    { lang: 'en-US', name: 'English Voice' },
  ]),
  speaking: false,
  pending: false,
  paused: false,
  addEventListener: jest.fn(),
};

const mockSpeechSynthesisUtterance = jest.fn().mockImplementation((text) => ({
  text,
  lang: 'fr-FR',
  rate: 1,
  pitch: 1,
  volume: 1,
  voice: null,
  onstart: null,
  onend: null,
  onerror: null,
}));

// Mock des APIs navigator
global.navigator = {
  ...global.navigator,
  mediaDevices: {
    getUserMedia: jest.fn(),
  },
};

global.window = {
  ...global.window,
  SpeechRecognition: jest.fn(() => mockSpeechRecognition),
  webkitSpeechRecognition: jest.fn(() => mockSpeechRecognition),
  speechSynthesis: mockSpeechSynthesis,
  SpeechSynthesisUtterance: mockSpeechSynthesisUtterance,
  AudioContext: jest.fn(),
  webkitAudioContext: jest.fn(),
};

describe('VoiceService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockSpeechRecognition.start.mockClear();
    mockSpeechRecognition.stop.mockClear();
    mockSpeechSynthesis.speak.mockClear();
    mockSpeechSynthesis.cancel.mockClear();
    mockSpeechSynthesis.getVoices.mockReturnValue([
      { lang: 'fr-FR', name: 'French Voice' },
      { lang: 'en-US', name: 'English Voice' },
    ]);
  });

  describe('checkSupport', () => {
    it('should return true when speech APIs are supported', () => {
      const isSupported = voiceService.checkSupport();
      expect(isSupported).toBe(true);
    });

    it('should return false when SpeechRecognition is not supported', () => {
      const originalSpeechRecognition = window.SpeechRecognition;
      const originalWebkitSpeechRecognition = window.webkitSpeechRecognition;
      
      delete window.SpeechRecognition;
      delete window.webkitSpeechRecognition;

      const isSupported = voiceService.checkSupport();
      expect(isSupported).toBe(false);

      window.SpeechRecognition = originalSpeechRecognition;
      window.webkitSpeechRecognition = originalWebkitSpeechRecognition;
    });
  });

  describe('getFrenchVoice', () => {
    it('should return a French voice when available', () => {
      const frenchVoice = voiceService.getFrenchVoice();
      expect(frenchVoice).toEqual({ lang: 'fr-FR', name: 'French Voice' });
    });

    it('should return first voice when no French voice available', () => {
      mockSpeechSynthesis.getVoices.mockReturnValue([
        { lang: 'en-US', name: 'English Voice' },
        { lang: 'es-ES', name: 'Spanish Voice' },
      ]);
      
      voiceService.loadVoices(); // Recharger les voix
      const voice = voiceService.getFrenchVoice();
      expect(voice).toEqual({ lang: 'en-US', name: 'English Voice' });
    });
  });

  describe('initRecognition', () => {
    it('should initialize speech recognition with default options', async () => {
      const recognition = await voiceService.initRecognition();
      
      expect(window.SpeechRecognition).toHaveBeenCalled();
      expect(recognition.continuous).toBe(false);
      expect(recognition.interimResults).toBe(true);
      expect(recognition.lang).toBe('fr-FR');
      expect(recognition.maxAlternatives).toBe(1);
    });

    it('should initialize speech recognition with custom options', async () => {
      const options = {
        continuous: true,
        interimResults: false,
        language: 'en-US',
        maxAlternatives: 3,
      };

      const recognition = await voiceService.initRecognition(options);
      
      expect(recognition.continuous).toBe(true);
      expect(recognition.interimResults).toBe(false);
      expect(recognition.lang).toBe('en-US');
      expect(recognition.maxAlternatives).toBe(3);
    });

    it('should reject when speech recognition is not supported', async () => {
      const originalCheckSupport = voiceService.checkSupport;
      voiceService.checkSupport = jest.fn(() => false);

      await expect(voiceService.initRecognition()).rejects.toThrow(
        'La reconnaissance vocale n\'est pas supportée par ce navigateur'
      );

      voiceService.checkSupport = originalCheckSupport;
    });
  });

  describe('startListening', () => {
    beforeEach(async () => {
      await voiceService.initRecognition();
    });

    it('should start listening and resolve with recognized text', async () => {
      const mockResult = {
        resultIndex: 0,
        results: [
          {
            0: { transcript: 'Hello world' },
            isFinal: true,
            length: 1,
          },
        ],
        length: 1,
      };

      // Simuler le processus de reconnaissance
      setTimeout(() => {
        voiceService.recognition.onstart();
        voiceService.recognition.onresult(mockResult);
        voiceService.recognition.onend();
      }, 10);

      const result = await voiceService.startListening();
      
      expect(mockSpeechRecognition.start).toHaveBeenCalled();
      expect(result).toBe('Hello world');
    });

    it('should reject when no speech is detected', async () => {
      setTimeout(() => {
        voiceService.recognition.onstart();
        voiceService.recognition.onend();
      }, 10);

      await expect(voiceService.startListening()).rejects.toThrow(
        'Aucune parole détectée'
      );
    });

    it('should reject when already listening', async () => {
      voiceService.isListening = true;

      await expect(voiceService.startListening()).rejects.toThrow(
        'L\'écoute est déjà en cours'
      );

      voiceService.isListening = false;
    });

    it('should handle recognition errors', async () => {
      setTimeout(() => {
        voiceService.recognition.onerror({ error: 'no-speech' });
      }, 10);

      await expect(voiceService.startListening()).rejects.toThrow(
        'Aucune parole détectée'
      );
    });

    it('should timeout after specified duration', async () => {
      const startTime = Date.now();
      
      await expect(
        voiceService.startListening({ timeout: 100 })
      ).rejects.toThrow('Timeout: aucune parole détectée');

      const elapsed = Date.now() - startTime;
      expect(elapsed).toBeGreaterThanOrEqual(100);
    });
  });

  describe('speak', () => {
    it('should create and speak utterance with default options', async () => {
      const text = 'Hello world';
      
      // Simuler la fin de la synthèse
      setTimeout(() => {
        const utterance = mockSpeechSynthesisUtterance.mock.results[0].value;
        utterance.onend();
      }, 10);

      await voiceService.speak(text);
      
      expect(mockSpeechSynthesisUtterance).toHaveBeenCalledWith(text);
      expect(mockSpeechSynthesis.cancel).toHaveBeenCalled();
      expect(mockSpeechSynthesis.speak).toHaveBeenCalled();
    });

    it('should use custom options for speech synthesis', async () => {
      const text = 'Hello world';
      const options = {
        language: 'en-US',
        rate: 1.5,
        pitch: 1.2,
        volume: 0.8,
      };

      setTimeout(() => {
        const utterance = mockSpeechSynthesisUtterance.mock.results[0].value;
        utterance.onend();
      }, 10);

      await voiceService.speak(text, options);
      
      const utterance = mockSpeechSynthesisUtterance.mock.results[0].value;
      expect(utterance.lang).toBe('en-US');
      expect(utterance.rate).toBe(1.5);
      expect(utterance.pitch).toBe(1.2);
      expect(utterance.volume).toBe(0.8);
    });

    it('should reject when synthesis is not supported', async () => {
      const originalSynthesis = window.speechSynthesis;
      delete window.speechSynthesis;

      await expect(voiceService.speak('test')).rejects.toThrow(
        'La synthèse vocale n\'est pas supportée'
      );

      window.speechSynthesis = originalSynthesis;
    });

    it('should handle synthesis errors', async () => {
      setTimeout(() => {
        const utterance = mockSpeechSynthesisUtterance.mock.results[0].value;
        utterance.onerror(new Error('Synthesis error'));
      }, 10);

      await expect(voiceService.speak('test')).rejects.toThrow(
        'Erreur lors de la synthèse vocale'
      );
    });
  });

  describe('stopListening', () => {
    it('should stop recognition when listening', () => {
      voiceService.recognition = mockSpeechRecognition;
      voiceService.isListening = true;

      voiceService.stopListening();

      expect(mockSpeechRecognition.stop).toHaveBeenCalled();
    });

    it('should not call stop when not listening', () => {
      voiceService.recognition = mockSpeechRecognition;
      voiceService.isListening = false;

      voiceService.stopListening();

      expect(mockSpeechRecognition.stop).not.toHaveBeenCalled();
    });
  });

  describe('stopSpeaking', () => {
    it('should cancel speech synthesis', () => {
      voiceService.stopSpeaking();
      expect(mockSpeechSynthesis.cancel).toHaveBeenCalled();
    });
  });

  describe('isSpeaking', () => {
    it('should return synthesis speaking state', () => {
      mockSpeechSynthesis.speaking = true;
      expect(voiceService.isSpeaking()).toBe(true);

      mockSpeechSynthesis.speaking = false;
      expect(voiceService.isSpeaking()).toBe(false);
    });
  });

  describe('requestMicrophonePermission', () => {
    it('should return true when permission is granted', async () => {
      const mockStream = {
        getTracks: () => [{ stop: jest.fn() }],
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      const result = await voiceService.requestMicrophonePermission();
      
      expect(result).toBe(true);
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({ audio: true });
    });

    it('should return false when permission is denied', async () => {
      navigator.mediaDevices.getUserMedia.mockRejectedValue(new Error('Permission denied'));

      const result = await voiceService.requestMicrophonePermission();
      
      expect(result).toBe(false);
    });
  });
});
