use anyhow::Result;
use serde::{Deserialize, Serialize};
use std::env;

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub p2p: P2PConfig,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ServerConfig {
    pub host: String,
    pub port: u16,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct DatabaseConfig {
    pub postgres_url: String,
    pub mongodb_url: String,
    pub mongodb_database: String,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct P2PConfig {
    pub listen_port: u16,
    pub enable_mdns: bool,
    pub enable_relay: bool,
}

impl AppConfig {
    pub fn load() -> Result<Self> {
        // Chargement des variables d'environnement depuis .env si présent
        dotenvy::dotenv().ok();

        let config = AppConfig {
            server: ServerConfig {
                host: env::var("SERVER_HOST").unwrap_or_else(|_| "0.0.0.0".to_string()),
                port: env::var("SERVER_PORT")
                    .unwrap_or_else(|_| "8080".to_string())
                    .parse()
                    .unwrap_or(8080),
            },
            database: DatabaseConfig {
                postgres_url: env::var("POSTGRES_URL")
                    .unwrap_or_else(|_| "postgresql://postgres:password@localhost:5432/meetvoice".to_string()),
                mongodb_url: env::var("MONGODB_URL")
                    .unwrap_or_else(|_| "mongodb://localhost:27017".to_string()),
                mongodb_database: env::var("MONGODB_DATABASE")
                    .unwrap_or_else(|_| "meetvoice".to_string()),
            },
            p2p: P2PConfig {
                listen_port: env::var("P2P_PORT")
                    .unwrap_or_else(|_| "9090".to_string())
                    .parse()
                    .unwrap_or(9090),
                enable_mdns: env::var("P2P_ENABLE_MDNS")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
                enable_relay: env::var("P2P_ENABLE_RELAY")
                    .unwrap_or_else(|_| "true".to_string())
                    .parse()
                    .unwrap_or(true),
            },
        };

        Ok(config)
    }
}
