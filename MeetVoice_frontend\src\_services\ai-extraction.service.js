/**
 * Service IA d'Extraction
 * Extrait et retient les informations personnelles pertinentes
 * Utilise l'API backend pour l'analyse IA
 */

import Axios from './caller.service.js';

class AIExtractionService {
  constructor() {
    this.extractedInfo = {
      names: [],
      personalInfo: {},
      context: [],
      lastUpdate: null
    };
    
    console.log('🧠 Service IA d\'extraction initialisé');
  }

  /**
   * Extraire les informations d'un texte
   * @param {string} text - Texte à analyser
   * @returns {Promise<Object>} Informations extraites
   */
  async extractInformation(text) {
    try {
      console.log('🧠 Extraction IA du texte:', text);

      // TEMPORAIREMENT DÉSACTIVÉ - Endpoint /ai-extract/ n'existe pas
      // Utiliser directement le fallback au lieu de l'API
      console.log('⚠️ API IA extraction désactivée, utilisation du fallback');

      // Passer directement au fallback sans erreur
      throw new Error('API IA extraction temporairement désactivée');

      return extracted;

    } catch (error) {
      console.error('❌ Erreur extraction IA:', error);
      
      // Fallback : extraction simple côté frontend
      return this.fallbackExtraction(text);
    }
  }

  /**
   * Extraction simple côté frontend (fallback)
   * @param {string} text - Texte à analyser
   * @returns {Object} Informations extraites
   */
  fallbackExtraction(text) {
    console.log('🔄 Extraction fallback...');
    
    const extracted = {
      names: [],
      personalInfo: {},
      keywords: [],
      sentiment: 'neutral'
    };

    // Extraction simple des noms (mots commençant par une majuscule)
    const namePattern = /\b[A-Z][a-z]+\b/g;
    const potentialNames = text.match(namePattern) || [];
    
    // Filtrer les noms communs
    const commonWords = ['Bonjour', 'Salut', 'Merci', 'Oui', 'Non', 'Bien', 'Très'];
    extracted.names = potentialNames.filter(name => 
      !commonWords.includes(name) && name.length > 2
    );

    // Extraction d'informations personnelles simples
    const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g;
    const phonePattern = /\b(?:\+33|0)[1-9](?:[0-9]{8})\b/g;
    
    const emails = text.match(emailPattern) || [];
    const phones = text.match(phonePattern) || [];
    
    if (emails.length > 0) extracted.personalInfo.emails = emails;
    if (phones.length > 0) extracted.personalInfo.phones = phones;

    // Mots-clés simples
    extracted.keywords = text.toLowerCase()
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 5);

    console.log('✅ Extraction fallback terminée:', extracted);
    return extracted;
  }

  /**
   * Mettre à jour les informations extraites
   * @param {Object} newInfo - Nouvelles informations
   */
  updateExtractedInfo(newInfo) {
    // Ajouter les nouveaux noms
    if (newInfo.names && newInfo.names.length > 0) {
      newInfo.names.forEach(name => {
        if (!this.extractedInfo.names.includes(name)) {
          this.extractedInfo.names.push(name);
        }
      });
    }

    // Mettre à jour les informations personnelles
    if (newInfo.personalInfo) {
      this.extractedInfo.personalInfo = {
        ...this.extractedInfo.personalInfo,
        ...newInfo.personalInfo
      };
    }

    // Ajouter au contexte
    this.extractedInfo.context.push({
      timestamp: new Date().toISOString(),
      info: newInfo,
      text: newInfo.originalText || ''
    });

    // Garder seulement les 10 derniers contextes
    if (this.extractedInfo.context.length > 10) {
      this.extractedInfo.context = this.extractedInfo.context.slice(-10);
    }

    this.extractedInfo.lastUpdate = new Date().toISOString();
    
    console.log('📝 Informations mises à jour:', this.extractedInfo);
  }

  /**
   * Obtenir toutes les informations extraites
   * @returns {Object} Informations complètes
   */
  getAllExtractedInfo() {
    return { ...this.extractedInfo };
  }

  /**
   * Obtenir seulement les noms extraits
   * @returns {Array} Liste des noms
   */
  getExtractedNames() {
    return [...this.extractedInfo.names];
  }

  /**
   * Obtenir les informations personnelles
   * @returns {Object} Informations personnelles
   */
  getPersonalInfo() {
    return { ...this.extractedInfo.personalInfo };
  }

  /**
   * Générer un résumé des informations
   * @returns {string} Résumé textuel
   */
  generateSummary() {
    const info = this.extractedInfo;
    let summary = '';

    if (info.names.length > 0) {
      summary += `Noms mentionnés: ${info.names.join(', ')}. `;
    }

    if (info.personalInfo.emails && info.personalInfo.emails.length > 0) {
      summary += `Emails: ${info.personalInfo.emails.join(', ')}. `;
    }

    if (info.personalInfo.phones && info.personalInfo.phones.length > 0) {
      summary += `Téléphones: ${info.personalInfo.phones.join(', ')}. `;
    }

    if (summary === '') {
      summary = 'Aucune information personnelle extraite pour le moment.';
    }

    return summary.trim();
  }

  /**
   * Réinitialiser les informations extraites
   */
  reset() {
    console.log('🔄 Reset des informations extraites...');
    this.extractedInfo = {
      names: [],
      personalInfo: {},
      context: [],
      lastUpdate: null
    };
  }

  /**
   * Exporter les informations pour sauvegarde
   * @returns {string} JSON des informations
   */
  exportData() {
    return JSON.stringify(this.extractedInfo, null, 2);
  }

  /**
   * Importer des informations depuis une sauvegarde
   * @param {string} jsonData - Données JSON
   */
  importData(jsonData) {
    try {
      const data = JSON.parse(jsonData);
      this.extractedInfo = {
        names: data.names || [],
        personalInfo: data.personalInfo || {},
        context: data.context || [],
        lastUpdate: data.lastUpdate || null
      };
      console.log('✅ Données importées avec succès');
    } catch (error) {
      console.error('❌ Erreur import données:', error);
    }
  }
}

// Export de l'instance unique
export const aiExtractionService = new AIExtractionService();
export default aiExtractionService;
