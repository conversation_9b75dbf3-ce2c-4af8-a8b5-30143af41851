<template>
  <main class="messages-container">
    <!-- Header -->
    <header class="messages-header">
      <section class="header-content">
        <h1>Messages</h1>
        <p class="subtitle">Vos conversations en cours</p>
      </section>
      
      <nav class="messages-actions" aria-label="Actions messages">
        <button 
          @click="$router.push('/matches')"
          class="btn-matches"
        >
          <span aria-hidden="true">💕</span>
          Mes matchs
        </button>
        
        <button 
          @click="$router.push('/profiles')"
          class="btn-discover"
        >
          <span aria-hidden="true">🔍</span>
          Découvrir
        </button>
      </nav>
    </header>

    <div class="messages-layout">
      <!-- Liste des conversations -->
      <aside class="conversations-sidebar">
        <section class="conversations-header">
          <h2>Conversations</h2>
          <div class="conversations-stats">
            <span class="total-conversations">{{ totalConversations }} conversations</span>
            <span class="unread-count" v-if="totalUnread > 0">{{ totalUnread }} non lues</span>
          </div>
        </section>
        
        <!-- Filtres rapides -->
        <div class="quick-filters">
          <button 
            @click="filterBy = 'all'"
            :class="['filter-btn', { active: filterBy === 'all' }]"
          >
            Toutes
          </button>
          <button 
            @click="filterBy = 'unread'"
            :class="['filter-btn', { active: filterBy === 'unread' }]"
          >
            Non lues ({{ totalUnread }})
          </button>
          <button 
            @click="filterBy = 'favorites'"
            :class="['filter-btn', { active: filterBy === 'favorites' }]"
          >
            Favoris
          </button>
        </div>
        
        <!-- Liste des conversations -->
        <div class="conversations-list">
          <div v-if="loading" class="loading-conversations">
            <div class="loading-spinner"></div>
            <p>Chargement...</p>
          </div>
          
          <div v-else-if="filteredConversations.length === 0" class="empty-conversations">
            <p>{{ getEmptyMessage() }}</p>
          </div>
          
          <article 
            v-else
            v-for="conversation in filteredConversations" 
            :key="conversation.id"
            :class="['conversation-item', { 
              active: selectedConversation?.id === conversation.id,
              unread: conversation.unreadCount > 0 
            }]"
            @click="selectConversation(conversation)"
            role="button"
            tabindex="0"
            @keydown.enter="selectConversation(conversation)"
            @keydown.space.prevent="selectConversation(conversation)"
          >
            <div class="conversation-avatar">
              <img 
                :src="conversation.participant.photos?.[0] || '/default-avatar.jpg'"
                :alt="`Photo de ${conversation.participant.prenom}`"
                class="avatar-image"
              >
              <div v-if="conversation.participant.isOnline" class="online-dot"></div>
              <div v-if="conversation.unreadCount > 0" class="unread-badge">
                {{ conversation.unreadCount }}
              </div>
            </div>
            
            <div class="conversation-info">
              <div class="conversation-header">
                <h3 class="participant-name">{{ conversation.participant.prenom }}</h3>
                <time class="last-message-time">{{ formatMessageTime(conversation.lastMessage?.timestamp) }}</time>
              </div>
              
              <div class="last-message">
                <span class="message-content">{{ getLastMessagePreview(conversation.lastMessage) }}</span>
                <div class="message-indicators">
                  <span v-if="conversation.lastMessage?.isVoiceMessage" class="voice-indicator">🎤</span>
                  <span v-if="conversation.lastMessage?.senderId === user.id" class="sent-indicator">✓</span>
                </div>
              </div>
            </div>
            
            <button 
              @click.stop="toggleFavoriteConversation(conversation.id)"
              :class="['btn-favorite-conversation', { active: conversation.isFavorite }]"
              :aria-label="conversation.isFavorite ? 'Retirer des favoris' : 'Ajouter aux favoris'"
            >
              {{ conversation.isFavorite ? '⭐' : '☆' }}
            </button>
          </article>
        </div>
      </aside>

      <!-- Zone de conversation -->
      <section class="chat-area">
        <div v-if="!selectedConversation" class="no-conversation-selected">
          <div class="welcome-message">
            <h2>Bienvenue dans vos messages</h2>
            <p>Sélectionnez une conversation pour commencer à discuter</p>
            <div class="conversation-tips">
              <h3>Conseils pour bien démarrer :</h3>
              <ul>
                <li>💬 Posez des questions ouvertes</li>
                <li>🎤 Utilisez les messages vocaux pour plus d'authenticité</li>
                <li>😊 Restez positif et bienveillant</li>
                <li>🎯 Partagez vos centres d'intérêt</li>
              </ul>
            </div>
          </div>
        </div>
        
        <div v-else class="active-conversation">
          <!-- En-tête de conversation -->
          <header class="conversation-header">
            <div class="participant-info">
              <img 
                :src="selectedConversation.participant.photos?.[0] || '/default-avatar.jpg'"
                :alt="`Photo de ${selectedConversation.participant.prenom}`"
                class="participant-avatar"
              >
              <div class="participant-details">
                <h2 class="participant-name">{{ selectedConversation.participant.prenom }}</h2>
                <span class="participant-status">
                  {{ getParticipantStatus(selectedConversation.participant) }}
                </span>
              </div>
            </div>
            
            <div class="conversation-actions">
              <button 
                @click="viewParticipantProfile"
                class="btn-view-profile"
                aria-label="Voir le profil"
              >
                <span aria-hidden="true">👤</span>
              </button>
              
              <button 
                @click="startVoiceCall"
                class="btn-voice-call"
                aria-label="Appel vocal"
              >
                <span aria-hidden="true">📞</span>
              </button>
              
              <button 
                @click="showConversationOptions"
                class="btn-options"
                aria-label="Options"
              >
                <span aria-hidden="true">⋮</span>
              </button>
            </div>
          </header>
          
          <!-- Messages -->
          <div class="messages-area" ref="messagesArea">
            <div v-if="loadingMessages" class="loading-messages">
              <div class="loading-spinner"></div>
            </div>
            
            <div v-else class="messages-list">
              <div 
                v-for="message in selectedConversation.messages" 
                :key="message.id"
                :class="['message', { 
                  sent: message.senderId === user.id,
                  received: message.senderId !== user.id 
                }]"
              >
                <div class="message-content">
                  <div v-if="message.isVoiceMessage" class="voice-message">
                    <button 
                      @click="playVoiceMessage(message)"
                      :class="['btn-play-voice', { playing: playingMessageId === message.id }]"
                    >
                      <span aria-hidden="true">{{ playingMessageId === message.id ? '⏸️' : '▶️' }}</span>
                    </button>
                    <div class="voice-waveform">
                      <div class="waveform-bars">
                        <div v-for="i in 20" :key="i" class="waveform-bar"></div>
                      </div>
                      <span class="voice-duration">{{ message.voiceDuration || '0:30' }}</span>
                    </div>
                  </div>
                  
                  <div v-else class="text-message">
                    {{ message.content }}
                  </div>
                  
                  <time class="message-timestamp">
                    {{ formatMessageTime(message.timestamp) }}
                  </time>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Zone de saisie -->
          <footer class="message-input-area">
            <div class="input-container">
              <button 
                @click="toggleVoiceRecording"
                :class="['btn-voice-record', { recording: isRecording }]"
                :aria-label="isRecording ? 'Arrêter l\'enregistrement' : 'Enregistrer un message vocal'"
              >
                <span aria-hidden="true">{{ isRecording ? '⏹️' : '🎤' }}</span>
              </button>
              
              <div class="text-input-container">
                <textarea
                  v-model="newMessage"
                  @keydown.enter.prevent="sendMessage"
                  @input="handleTyping"
                  placeholder="Tapez votre message..."
                  class="message-input"
                  rows="1"
                  :disabled="isRecording"
                ></textarea>
                
                <div v-if="isTyping" class="typing-indicator">
                  {{ selectedConversation.participant.prenom }} est en train d'écrire...
                </div>
              </div>
              
              <button 
                @click="sendMessage"
                :disabled="!newMessage.trim() || isRecording"
                class="btn-send-message"
                aria-label="Envoyer le message"
              >
                <span aria-hidden="true">📤</span>
              </button>
            </div>
            
            <div v-if="isRecording" class="recording-indicator">
              <div class="recording-animation"></div>
              <span>Enregistrement en cours... {{ recordingDuration }}s</span>
              <button @click="cancelRecording" class="btn-cancel-recording">Annuler</button>
            </div>
          </footer>
        </div>
      </section>
    </div>
  </main>
</template>

<script>
import { mapState, mapActions } from 'vuex'

export default {
  name: 'MessagesView',
  
  data() {
    return {
      loading: false,
      loadingMessages: false,
      filterBy: 'all',
      selectedConversation: null,
      newMessage: '',
      isRecording: false,
      recordingDuration: 0,
      playingMessageId: null,
      isTyping: false,
      typingTimeout: null
    }
  },
  
  computed: {
    ...mapState(['conversations', 'user']),
    
    totalConversations() {
      return this.conversations?.length || 0;
    },
    
    totalUnread() {
      return this.conversations?.reduce((total, conv) => total + (conv.unreadCount || 0), 0) || 0;
    },
    
    filteredConversations() {
      if (!this.conversations) return [];
      
      let filtered = [...this.conversations];
      
      switch (this.filterBy) {
        case 'unread':
          filtered = filtered.filter(conv => conv.unreadCount > 0);
          break;
        case 'favorites':
          filtered = filtered.filter(conv => conv.isFavorite);
          break;
      }
      
      // Trier par dernière activité
      return filtered.sort((a, b) => {
        const aTime = a.lastMessage?.timestamp || a.createdAt;
        const bTime = b.lastMessage?.timestamp || b.createdAt;
        return new Date(bTime) - new Date(aTime);
      });
    }
  },
  
  methods: {
    ...mapActions(['loadConversations', 'sendMessage', 'markAsRead']),
    
    formatMessageTime(timestamp) {
      if (!timestamp) return '';
      
      const now = new Date();
      const messageTime = new Date(timestamp);
      const diffMs = now - messageTime;
      const diffMins = Math.floor(diffMs / 60000);
      const diffHours = Math.floor(diffMs / 3600000);
      const diffDays = Math.floor(diffMs / 86400000);
      
      if (diffMins < 1) return 'À l\'instant';
      if (diffMins < 60) return `${diffMins} min`;
      if (diffHours < 24) return `${diffHours}h`;
      if (diffDays < 7) return `${diffDays}j`;
      return messageTime.toLocaleDateString('fr-FR');
    },
    
    getLastMessagePreview(message) {
      if (!message) return 'Aucun message';
      if (message.isVoiceMessage) return '🎤 Message vocal';
      return message.content.length > 50 ? 
        message.content.substring(0, 50) + '...' : 
        message.content;
    },
    
    getParticipantStatus(participant) {
      if (participant.isOnline) return 'En ligne';
      if (participant.lastConnection) {
        return `Vu ${this.formatMessageTime(participant.lastConnection)}`;
      }
      return 'Hors ligne';
    },
    
    getEmptyMessage() {
      switch (this.filterBy) {
        case 'unread': return 'Aucun message non lu';
        case 'favorites': return 'Aucune conversation favorite';
        default: return 'Aucune conversation';
      }
    },
    
    async selectConversation(conversation) {
      this.selectedConversation = conversation;
      this.loadingMessages = true;
      
      try {
        // Marquer comme lu
        if (conversation.unreadCount > 0) {
          await this.markAsRead(conversation.id);
        }
        
        // Charger les messages si nécessaire
        if (!conversation.messages || conversation.messages.length === 0) {
          // Simuler le chargement des messages
          await new Promise(resolve => setTimeout(resolve, 500));
          // Ici on chargerait les messages depuis l'API
        }
      } catch (error) {
        console.error('Erreur lors de la sélection de la conversation:', error);
      } finally {
        this.loadingMessages = false;
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      }
    },
    
    async sendMessage() {
      if (!this.newMessage.trim() || !this.selectedConversation) return;
      
      const messageData = {
        conversationId: this.selectedConversation.id,
        content: this.newMessage.trim(),
        isVoiceMessage: false,
        timestamp: new Date().toISOString()
      };
      
      try {
        await this.sendMessage(messageData);
        this.newMessage = '';
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } catch (error) {
        console.error('Erreur lors de l\'envoi du message:', error);
      }
    },
    
    handleTyping() {
      // Simuler l'indicateur de frappe
      if (this.typingTimeout) {
        clearTimeout(this.typingTimeout);
      }
      
      this.typingTimeout = setTimeout(() => {
        this.isTyping = false;
      }, 3000);
    },
    
    toggleVoiceRecording() {
      if (this.isRecording) {
        this.stopRecording();
      } else {
        this.startRecording();
      }
    },
    
    startRecording() {
      this.isRecording = true;
      this.recordingDuration = 0;
      
      // Simuler l'enregistrement
      this.recordingInterval = setInterval(() => {
        this.recordingDuration++;
        if (this.recordingDuration >= 60) {
          this.stopRecording();
        }
      }, 1000);
    },
    
    stopRecording() {
      this.isRecording = false;
      if (this.recordingInterval) {
        clearInterval(this.recordingInterval);
      }
      
      // Ici on enverrait le message vocal
      if (this.recordingDuration > 1) {
        this.sendVoiceMessage();
      }
      
      this.recordingDuration = 0;
    },
    
    cancelRecording() {
      this.isRecording = false;
      if (this.recordingInterval) {
        clearInterval(this.recordingInterval);
      }
      this.recordingDuration = 0;
    },
    
    async sendVoiceMessage() {
      if (!this.selectedConversation) return;
      
      const messageData = {
        conversationId: this.selectedConversation.id,
        content: '',
        isVoiceMessage: true,
        voiceDuration: `0:${this.recordingDuration.toString().padStart(2, '0')}`,
        timestamp: new Date().toISOString()
      };
      
      try {
        await this.sendMessage(messageData);
        this.$nextTick(() => {
          this.scrollToBottom();
        });
      } catch (error) {
        console.error('Erreur lors de l\'envoi du message vocal:', error);
      }
    },
    
    playVoiceMessage(message) {
      if (this.playingMessageId === message.id) {
        this.playingMessageId = null;
        // Arrêter la lecture
      } else {
        this.playingMessageId = message.id;
        // Démarrer la lecture
        setTimeout(() => {
          this.playingMessageId = null;
        }, 3000); // Simuler la durée de lecture
      }
    },
    
    toggleFavoriteConversation(conversationId) {
      // Implémenter la logique de favori
      console.log('Toggle favorite:', conversationId);
    },
    
    viewParticipantProfile() {
      if (this.selectedConversation) {
        this.$router.push(`/profile/${this.selectedConversation.participant.id}`);
      }
    },
    
    startVoiceCall() {
      // Implémenter l'appel vocal
      console.log('Démarrer appel vocal');
    },
    
    showConversationOptions() {
      // Afficher les options de conversation
      console.log('Options de conversation');
    },
    
    scrollToBottom() {
      if (this.$refs.messagesArea) {
        this.$refs.messagesArea.scrollTop = this.$refs.messagesArea.scrollHeight;
      }
    }
  },
  
  async mounted() {
    this.loading = true;
    try {
      await this.loadConversations();
      
      // Sélectionner la première conversation s'il y en a une
      if (this.conversations && this.conversations.length > 0) {
        await this.selectConversation(this.conversations[0]);
      }
    } catch (error) {
      console.error('Erreur lors du chargement des conversations:', error);
    } finally {
      this.loading = false;
    }
  },
  
  beforeUnmount() {
    if (this.recordingInterval) {
      clearInterval(this.recordingInterval);
    }
    if (this.typingTimeout) {
      clearTimeout(this.typingTimeout);
    }
  }
}
</script>

<style scoped>
/* Variables CSS selon la charte graphique */
:root {
  --header-bg: #1C0F2E;
  --accent-blue: #00CFFF;
  --accent-purple: #D68CFF;
  --text-white: #FFFFFF;
  --slogan-gray: #EDEDED;
  --wall-color: #C1A1B6;
  --sweater-purple: #8F73A3;
  --furniture-white: #F4F1F0;
  --icon-color: #CCCCCC;
  --love-pink: #FF69B4;
  --heart-red: #FF1744;
  --message-bg: #2A1B3D;
}

.messages-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, var(--header-bg) 0%, var(--sweater-purple) 100%);
  min-height: 100vh;
  color: var(--text-white);
}

.messages-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 24px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.header-content h1 {
  font-size: 2rem;
  font-weight: 600;
  color: var(--love-pink);
  margin-bottom: 8px;
}

.subtitle {
  color: var(--slogan-gray);
  font-size: 1.1rem;
}

.messages-actions {
  display: flex;
  gap: 12px;
}

.btn-matches,
.btn-discover {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--love-pink);
  color: var(--text-white);
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-matches:hover,
.btn-discover:hover {
  background: var(--heart-red);
  transform: translateY(-2px);
}

.messages-layout {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: 20px;
  height: calc(100vh - 200px);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
  overflow: hidden;
  backdrop-filter: blur(10px);
}

/* Sidebar des conversations */
.conversations-sidebar {
  background: rgba(255, 255, 255, 0.1);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
}

.conversations-header {
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.conversations-header h2 {
  color: var(--love-pink);
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.conversations-stats {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.unread-count {
  color: var(--heart-red);
  font-weight: 600;
}

.quick-filters {
  display: flex;
  padding: 16px 20px;
  gap: 8px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.filter-btn {
  padding: 6px 12px;
  background: transparent;
  color: var(--slogan-gray);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 16px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn.active {
  background: var(--love-pink);
  color: var(--text-white);
  border-color: var(--love-pink);
}

.filter-btn:hover:not(.active) {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
}

.conversations-list {
  flex: 1;
  overflow-y: auto;
}

.loading-conversations,
.empty-conversations {
  padding: 40px 20px;
  text-align: center;
  color: var(--slogan-gray);
}

.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-left: 3px solid var(--love-pink);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
  position: relative;
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.conversation-item.active {
  background: rgba(255, 105, 180, 0.2);
  border-left: 3px solid var(--love-pink);
}

.conversation-item.unread {
  background: rgba(255, 23, 68, 0.1);
}

.conversation-avatar {
  position: relative;
  flex-shrink: 0;
}

.avatar-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--love-pink);
}

.online-dot {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  background: #4ade80;
  border: 2px solid var(--text-white);
  border-radius: 50%;
}

.unread-badge {
  position: absolute;
  top: -4px;
  right: -4px;
  width: 20px;
  height: 20px;
  background: var(--heart-red);
  color: var(--text-white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.7rem;
  font-weight: 600;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 4px;
}

.participant-name {
  font-size: 1rem;
  font-weight: 600;
  color: var(--love-pink);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.last-message-time {
  font-size: 0.7rem;
  color: var(--icon-color);
  flex-shrink: 0;
}

.last-message {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.message-content {
  font-size: 0.8rem;
  color: var(--slogan-gray);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.message-indicators {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.voice-indicator,
.sent-indicator {
  font-size: 0.7rem;
}

.btn-favorite-conversation {
  background: transparent;
  border: none;
  color: var(--icon-color);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.btn-favorite-conversation.active {
  color: var(--match-gold);
}

.btn-favorite-conversation:hover {
  color: var(--match-gold);
}

/* Zone de chat */
.chat-area {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.05);
}

.no-conversation-selected {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
}

.welcome-message {
  text-align: center;
  max-width: 500px;
}

.welcome-message h2 {
  color: var(--love-pink);
  font-size: 1.5rem;
  margin-bottom: 16px;
}

.conversation-tips {
  margin-top: 30px;
  text-align: left;
}

.conversation-tips h3 {
  color: var(--accent-blue);
  margin-bottom: 16px;
}

.conversation-tips ul {
  list-style: none;
  padding: 0;
}

.conversation-tips li {
  padding: 8px 0;
  color: var(--slogan-gray);
}

.active-conversation {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.conversation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.participant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.participant-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--love-pink);
}

.participant-details h2 {
  color: var(--love-pink);
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.participant-status {
  color: var(--slogan-gray);
  font-size: 0.8rem;
}

.conversation-actions {
  display: flex;
  gap: 8px;
}

.btn-view-profile,
.btn-voice-call,
.btn-options {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-view-profile:hover {
  background: var(--accent-blue);
}

.btn-voice-call:hover {
  background: var(--love-pink);
}

.btn-options:hover {
  background: var(--wall-color);
}

.messages-area {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.loading-messages {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message {
  display: flex;
  max-width: 70%;
}

.message.sent {
  align-self: flex-end;
  justify-content: flex-end;
}

.message.received {
  align-self: flex-start;
  justify-content: flex-start;
}

.message-content {
  background: var(--message-bg);
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.message.sent .message-content {
  background: var(--love-pink);
  border-bottom-right-radius: 6px;
}

.message.received .message-content {
  background: rgba(255, 255, 255, 0.1);
  border-bottom-left-radius: 6px;
}

.voice-message {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 200px;
}

.btn-play-voice {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-play-voice:hover {
  background: rgba(255, 255, 255, 0.3);
}

.btn-play-voice.playing {
  background: var(--accent-blue);
}

.voice-waveform {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}

.waveform-bars {
  display: flex;
  align-items: center;
  gap: 2px;
  flex: 1;
}

.waveform-bar {
  width: 3px;
  height: 16px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  animation: waveform 1.5s ease-in-out infinite;
}

.waveform-bar:nth-child(odd) {
  animation-delay: 0.1s;
}

.waveform-bar:nth-child(even) {
  animation-delay: 0.3s;
}

@keyframes waveform {
  0%, 100% { height: 16px; }
  50% { height: 8px; }
}

.voice-duration {
  font-size: 0.8rem;
  color: var(--slogan-gray);
}

.text-message {
  color: var(--text-white);
  line-height: 1.4;
  word-wrap: break-word;
}

.message-timestamp {
  font-size: 0.7rem;
  color: var(--icon-color);
  margin-top: 4px;
  display: block;
}

.message-input-area {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.05);
}

.input-container {
  display: flex;
  align-items: end;
  gap: 12px;
}

.btn-voice-record {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: var(--love-pink);
  color: var(--text-white);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.btn-voice-record:hover {
  background: var(--heart-red);
}

.btn-voice-record.recording {
  background: var(--heart-red);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.text-input-container {
  flex: 1;
  position: relative;
}

.message-input {
  width: 100%;
  min-height: 44px;
  max-height: 120px;
  padding: 12px 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 22px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-white);
  font-size: 1rem;
  resize: none;
  outline: none;
  transition: all 0.3s ease;
}

.message-input:focus {
  border-color: var(--love-pink);
  background: rgba(255, 255, 255, 0.15);
}

.message-input::placeholder {
  color: var(--slogan-gray);
}

.typing-indicator {
  position: absolute;
  bottom: -20px;
  left: 16px;
  font-size: 0.8rem;
  color: var(--love-pink);
  font-style: italic;
}

.btn-send-message {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 50%;
  background: var(--accent-blue);
  color: var(--text-white);
  font-size: 1.1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.btn-send-message:hover:not(:disabled) {
  background: var(--accent-purple);
}

.btn-send-message:disabled {
  background: var(--icon-color);
  cursor: not-allowed;
}

.recording-indicator {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 12px;
  padding: 12px 16px;
  background: rgba(255, 23, 68, 0.2);
  border-radius: 8px;
  color: var(--heart-red);
}

.recording-animation {
  width: 12px;
  height: 12px;
  background: var(--heart-red);
  border-radius: 50%;
  animation: pulse 1s infinite;
}

.btn-cancel-recording {
  padding: 6px 12px;
  background: transparent;
  color: var(--heart-red);
  border: 1px solid var(--heart-red);
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.8rem;
}

.btn-cancel-recording:hover {
  background: var(--heart-red);
  color: var(--text-white);
}

/* Responsive */
@media (max-width: 1024px) {
  .messages-layout {
    grid-template-columns: 300px 1fr;
  }
}

@media (max-width: 768px) {
  .messages-container {
    padding: 16px;
  }

  .messages-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .messages-layout {
    grid-template-columns: 1fr;
    grid-template-rows: 300px 1fr;
  }

  .conversations-sidebar {
    border-right: none;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .input-container {
    flex-wrap: wrap;
  }

  .text-input-container {
    order: 2;
    width: 100%;
    margin-top: 8px;
  }

  .btn-voice-record,
  .btn-send-message {
    order: 1;
  }
}
</style>
