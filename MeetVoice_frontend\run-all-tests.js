#!/usr/bin/env node

/**
 * Script de lancement pour tous les tests MeetVoice
 * Usage: node run-all-tests.js
 */

const { runAllTests } = require('./test-backend-integration.js');
const { runAllTTSTests } = require('./test-tts-backend.js');

// Couleurs console
const colors = {
    reset: '\x1b[0m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function printHeader() {
    log('\n' + '='.repeat(60), 'magenta');
    log('🧪 SUITE DE TESTS COMPLÈTE MEETVOICE', 'bold');
    log('='.repeat(60), 'magenta');
    log('📋 Tests inclus:', 'cyan');
    log('  1. 🔗 Connexion Backend', 'blue');
    log('  2. 📝 Validation des Données', 'blue');
    log('  3. 🗺️  Mapping des Champs', 'blue');
    log('  4. 📤 Inscription Backend', 'blue');
    log('  5. 🎤 API TTS Backend', 'blue');
    log('  6. 🎭 Types de Voix', 'blue');
    log('  7. 📝 Questions Interview', 'blue');
    log('  8. ⚡ Performance TTS', 'blue');
    log('='.repeat(60), 'magenta');
}

function printSeparator(title) {
    log('\n' + '-'.repeat(60), 'cyan');
    log(`🔍 ${title}`, 'cyan');
    log('-'.repeat(60), 'cyan');
}

function printSummary(backendResults, ttsResults) {
    log('\n' + '='.repeat(60), 'magenta');
    log('📊 RÉSUMÉ GLOBAL DES TESTS', 'bold');
    log('='.repeat(60), 'magenta');
    
    // Tests Backend
    log('\n🔧 TESTS BACKEND:', 'cyan');
    const backendTests = {
        'Connexion Backend': backendResults.connection,
        'Validation Données': backendResults.validation,
        'Mapping Champs': backendResults.mapping,
        'Inscription Backend': backendResults.registration
    };
    
    for (const [name, result] of Object.entries(backendTests)) {
        const status = result ? '✅ RÉUSSI' : '❌ ÉCHOUÉ';
        const color = result ? 'green' : 'red';
        log(`  ${name}: ${status}`, color);
    }
    
    // Tests TTS
    log('\n🎤 TESTS TTS:', 'cyan');
    const ttsEndpointStatus = ttsResults.endpoint ? '✅ RÉUSSI' : '❌ ÉCHOUÉ';
    const ttsEndpointColor = ttsResults.endpoint ? 'green' : 'red';
    log(`  Endpoint TTS: ${ttsEndpointStatus}`, ttsEndpointColor);
    
    if (Object.keys(ttsResults.voices).length > 0) {
        const voiceCount = Object.values(ttsResults.voices).filter(Boolean).length;
        const totalVoices = Object.keys(ttsResults.voices).length;
        const voiceStatus = voiceCount > 0 ? '✅ RÉUSSI' : '❌ ÉCHOUÉ';
        const voiceColor = voiceCount > 0 ? 'green' : 'red';
        log(`  Types de Voix: ${voiceStatus} (${voiceCount}/${totalVoices})`, voiceColor);
    }
    
    if (ttsResults.questions.length > 0) {
        const questionCount = ttsResults.questions.filter(q => q.success).length;
        const totalQuestions = ttsResults.questions.length;
        const questionStatus = questionCount > 0 ? '✅ RÉUSSI' : '❌ ÉCHOUÉ';
        const questionColor = questionCount > 0 ? 'green' : 'red';
        log(`  Questions Interview: ${questionStatus} (${questionCount}/${totalQuestions})`, questionColor);
    }
    
    if (ttsResults.performance) {
        const avgTime = ttsResults.performance.average.toFixed(0);
        const perfStatus = ttsResults.performance.average < 3000 ? '✅ RAPIDE' : '⚠️ LENT';
        const perfColor = ttsResults.performance.average < 3000 ? 'green' : 'yellow';
        log(`  Performance TTS: ${perfStatus} (${avgTime}ms)`, perfColor);
    }
    
    // Score global
    const backendScore = Object.values(backendResults).filter(Boolean).length;
    const backendTotal = Object.keys(backendResults).length;
    
    let ttsScore = 0;
    let ttsTotal = 0;
    
    if (ttsResults.endpoint) {
        ttsScore++;
        ttsTotal++;
        
        if (Object.keys(ttsResults.voices).length > 0) {
            ttsTotal++;
            if (Object.values(ttsResults.voices).some(Boolean)) {
                ttsScore++;
            }
        }
        
        if (ttsResults.questions.length > 0) {
            ttsTotal++;
            if (ttsResults.questions.some(q => q.success)) {
                ttsScore++;
            }
        }
    } else {
        ttsTotal = 1; // Juste l'endpoint
    }
    
    const totalScore = backendScore + ttsScore;
    const totalTests = backendTotal + ttsTotal;
    const percentage = Math.round((totalScore / totalTests) * 100);
    
    log('\n📈 SCORE GLOBAL:', 'cyan');
    log(`  Tests réussis: ${totalScore}/${totalTests} (${percentage}%)`, 'blue');
    
    if (percentage >= 90) {
        log('\n🎉 EXCELLENT! Tous les systèmes fonctionnent parfaitement.', 'green');
        log('✅ L\'interview vocal est prêt pour la production.', 'green');
    } else if (percentage >= 70) {
        log('\n👍 BIEN! La plupart des systèmes fonctionnent.', 'yellow');
        log('⚠️ Quelques ajustements peuvent être nécessaires.', 'yellow');
    } else {
        log('\n⚠️ ATTENTION! Plusieurs problèmes détectés.', 'red');
        log('🔧 Vérifiez la configuration du backend.', 'red');
    }
    
    // Recommandations
    log('\n💡 RECOMMANDATIONS:', 'cyan');
    
    if (!backendResults.connection) {
        log('  🔧 Vérifiez que le backend Django est démarré sur le port 8000', 'yellow');
    }
    
    if (!backendResults.registration) {
        log('  📝 Vérifiez l\'endpoint d\'inscription et les permissions', 'yellow');
    }
    
    if (!ttsResults.endpoint) {
        log('  🎤 Vérifiez que l\'API TTS est configurée et accessible', 'yellow');
    }
    
    if (ttsResults.performance && ttsResults.performance.average > 5000) {
        log('  ⚡ Performance TTS lente, vérifiez les ressources serveur', 'yellow');
    }
    
    log('\n📚 DOCUMENTATION:', 'cyan');
    log('  🌐 Test HTML: Ouvrez test-interview-backend.html dans votre navigateur', 'blue');
    log('  📊 Logs détaillés: Consultez la sortie complète ci-dessus', 'blue');
    log('  🔍 Debug: Activez les logs dans la console du navigateur', 'blue');
}

async function runCompleteTestSuite() {
    printHeader();
    
    let backendResults = {};
    let ttsResults = {};
    
    try {
        // Tests Backend
        printSeparator('TESTS BACKEND INTEGRATION');
        backendResults = await runAllTests();
        
        // Pause entre les suites de tests
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Tests TTS
        printSeparator('TESTS API TTS BACKEND');
        ttsResults = await runAllTTSTests();
        
        // Résumé final
        printSummary(backendResults, ttsResults);
        
    } catch (error) {
        log(`\n💥 ERREUR FATALE: ${error.message}`, 'red');
        log('🔧 Vérifiez que le backend Django est démarré et accessible.', 'yellow');
        process.exit(1);
    }
}

// Gestion des arguments de ligne de commande
const args = process.argv.slice(2);

if (args.includes('--help') || args.includes('-h')) {
    log('\n🧪 SUITE DE TESTS MEETVOICE', 'cyan');
    log('Usage: node run-all-tests.js [options]', 'blue');
    log('\nOptions:', 'cyan');
    log('  --help, -h     Afficher cette aide', 'blue');
    log('  --backend-only Tests backend uniquement', 'blue');
    log('  --tts-only     Tests TTS uniquement', 'blue');
    log('\nExemples:', 'cyan');
    log('  node run-all-tests.js                # Tous les tests', 'blue');
    log('  node run-all-tests.js --backend-only # Backend seulement', 'blue');
    log('  node run-all-tests.js --tts-only     # TTS seulement', 'blue');
    process.exit(0);
}

if (args.includes('--backend-only')) {
    log('🔧 TESTS BACKEND UNIQUEMENT', 'cyan');
    runAllTests().catch(error => {
        log(`💥 ERREUR: ${error.message}`, 'red');
        process.exit(1);
    });
} else if (args.includes('--tts-only')) {
    log('🎤 TESTS TTS UNIQUEMENT', 'cyan');
    runAllTTSTests().catch(error => {
        log(`💥 ERREUR: ${error.message}`, 'red');
        process.exit(1);
    });
} else {
    // Exécution complète
    runCompleteTestSuite();
}

module.exports = { runCompleteTestSuite };
