/**
 * Tests pour la détection et le diagnostic du microphone
 * @format
 */

import { voiceService } from '@/_services/voice.service';

describe('Microphone Detection and Diagnostics', () => {
  // Mock des APIs du navigateur
  beforeEach(() => {
    // Mock navigator.mediaDevices
    global.navigator = {
      mediaDevices: {
        getUserMedia: jest.fn(),
        enumerateDevices: jest.fn()
      }
    };

    // Mock AudioContext
    global.AudioContext = jest.fn().mockImplementation(() => ({
      createAnalyser: jest.fn(() => ({
        fftSize: 256,
        frequencyBinCount: 128,
        getByteFrequencyData: jest.fn()
      })),
      createMediaStreamSource: jest.fn(() => ({
        connect: jest.fn()
      })),
      close: jest.fn()
    }));

    // Mock MediaRecorder
    global.MediaRecorder = jest.fn().mockImplementation(() => ({
      start: jest.fn(),
      stop: jest.fn(),
      state: 'inactive'
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('Voice Service Support Check', () => {
    test('should detect voice support correctly', () => {
      // Mock SpeechRecognition
      global.SpeechRecognition = jest.fn();
      global.speechSynthesis = {};

      const isSupported = voiceService.checkSupport();
      expect(isSupported).toBe(true);
    });

    test('should detect lack of voice support', () => {
      delete global.SpeechRecognition;
      delete global.webkitSpeechRecognition;
      delete global.speechSynthesis;

      const isSupported = voiceService.checkSupport();
      expect(isSupported).toBe(false);
    });
  });

  describe('Microphone Permission Request', () => {
    test('should successfully request microphone permission', async () => {
      const mockStream = {
        getAudioTracks: jest.fn(() => [{
          label: 'Test Microphone',
          getSettings: jest.fn(() => ({
            deviceId: 'test-device-id',
            sampleRate: 44100,
            channelCount: 2,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }))
        }]),
        getTracks: jest.fn(() => [{
          stop: jest.fn()
        }])
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      const result = await voiceService.requestMicrophonePermission();

      expect(result.success).toBe(true);
      expect(result.deviceInfo).toBeDefined();
      expect(result.deviceInfo.label).toBe('Test Microphone');
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
    });

    test('should handle permission denied error', async () => {
      const permissionError = new Error('Permission denied');
      permissionError.name = 'NotAllowedError';
      
      navigator.mediaDevices.getUserMedia.mockRejectedValue(permissionError);

      const result = await voiceService.requestMicrophonePermission();

      expect(result.success).toBe(false);
      expect(result.error).toBe('PERMISSION_DENIED');
      expect(result.message).toContain('Permission microphone refusée');
      expect(result.troubleshooting).toBeDefined();
      expect(result.troubleshooting.length).toBeGreaterThan(0);
    });

    test('should handle no device found error', async () => {
      const deviceError = new Error('No device found');
      deviceError.name = 'NotFoundError';
      
      navigator.mediaDevices.getUserMedia.mockRejectedValue(deviceError);

      const result = await voiceService.requestMicrophonePermission();

      expect(result.success).toBe(false);
      expect(result.error).toBe('NO_DEVICE_FOUND');
      expect(result.message).toContain('Aucun microphone trouvé');
    });

    test('should handle device in use error', async () => {
      const deviceError = new Error('Device in use');
      deviceError.name = 'NotReadableError';
      
      navigator.mediaDevices.getUserMedia.mockRejectedValue(deviceError);

      const result = await voiceService.requestMicrophonePermission();

      expect(result.success).toBe(false);
      expect(result.error).toBe('DEVICE_IN_USE');
      expect(result.message).toContain('Microphone déjà utilisé');
    });

    test('should handle no audio tracks in stream', async () => {
      const mockStream = {
        getAudioTracks: jest.fn(() => []), // Pas de pistes audio
        getTracks: jest.fn(() => [{
          stop: jest.fn()
        }])
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      const result = await voiceService.requestMicrophonePermission();

      expect(result.success).toBe(false);
      expect(result.error).toBe('NO_AUDIO_TRACKS');
      expect(result.message).toContain('Aucun microphone détecté');
    });
  });

  describe('Audio Device Enumeration', () => {
    test('should enumerate audio devices successfully', async () => {
      // Mock successful permission request
      const mockStream = {
        getAudioTracks: jest.fn(() => [{
          label: 'Test Microphone',
          getSettings: jest.fn(() => ({ deviceId: 'test-device-id' }))
        }]),
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      // Mock device enumeration
      const mockDevices = [
        {
          deviceId: 'mic1',
          kind: 'audioinput',
          label: 'Microphone 1',
          groupId: 'group1'
        },
        {
          deviceId: 'mic2',
          kind: 'audioinput',
          label: 'Microphone 2',
          groupId: 'group2'
        },
        {
          deviceId: 'speaker1',
          kind: 'audiooutput',
          label: 'Speaker 1',
          groupId: 'group1'
        }
      ];

      navigator.mediaDevices.enumerateDevices.mockResolvedValue(mockDevices);

      const result = await voiceService.enumerateAudioDevices();

      expect(result.success).toBe(true);
      expect(result.devices.audioInput).toHaveLength(2);
      expect(result.devices.audioOutput).toHaveLength(1);
      expect(result.devices.audioInput[0].label).toBe('Microphone 1');
      expect(result.defaultDevices.audioInput).toBeDefined();
    });

    test('should handle enumeration failure', async () => {
      // Mock permission failure
      const permissionError = new Error('Permission denied');
      permissionError.name = 'NotAllowedError';
      
      navigator.mediaDevices.getUserMedia.mockRejectedValue(permissionError);

      const result = await voiceService.enumerateAudioDevices();

      expect(result.success).toBe(false);
      expect(result.devices.audioInput).toHaveLength(0);
      expect(result.devices.audioOutput).toHaveLength(0);
    });
  });

  describe('Microphone Testing', () => {
    test('should test microphone successfully', async () => {
      const mockStream = {
        getAudioTracks: jest.fn(() => [{
          label: 'Test Microphone',
          getSettings: jest.fn(() => ({
            deviceId: 'test-device-id',
            sampleRate: 44100,
            channelCount: 2,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }))
        }]),
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      // Mock audio analysis
      const mockAnalyser = {
        fftSize: 256,
        frequencyBinCount: 128,
        getByteFrequencyData: jest.fn((dataArray) => {
          // Simuler un niveau audio moyen
          for (let i = 0; i < dataArray.length; i++) {
            dataArray[i] = 50; // Niveau audio modéré
          }
        })
      };

      global.AudioContext.mockImplementation(() => ({
        createAnalyser: jest.fn(() => mockAnalyser),
        createMediaStreamSource: jest.fn(() => ({
          connect: jest.fn()
        })),
        close: jest.fn()
      }));

      const result = await voiceService.testMicrophone('test-device-id');

      expect(result.success).toBe(true);
      expect(result.deviceInfo).toBeDefined();
      expect(result.testResults).toBeDefined();
      expect(result.testResults.isWorking).toBe(true);
      expect(result.testResults.quality).toBeDefined();
    });

    test('should handle microphone test failure', async () => {
      const testError = new Error('Test failed');
      testError.name = 'NotFoundError';
      
      navigator.mediaDevices.getUserMedia.mockRejectedValue(testError);

      const result = await voiceService.testMicrophone('invalid-device-id');

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.message).toContain('Échec du test microphone');
    });
  });

  describe('Troubleshooting Tips', () => {
    test('should provide permission denied troubleshooting', () => {
      const tips = voiceService.getMicrophoneTroubleshooting('PERMISSION_DENIED');
      
      expect(tips).toContain('Cliquez sur l\'icône de microphone dans la barre d\'adresse');
      expect(tips).toContain('Sélectionnez "Toujours autoriser" pour ce site');
      expect(tips.length).toBeGreaterThan(3);
    });

    test('should provide device not found troubleshooting', () => {
      const tips = voiceService.getMicrophoneTroubleshooting('NO_DEVICE_FOUND');
      
      expect(tips).toContain('Vérifiez que votre microphone est connecté');
      expect(tips).toContain('Testez votre microphone dans les paramètres système');
      expect(tips.length).toBeGreaterThan(3);
    });

    test('should provide device in use troubleshooting', () => {
      const tips = voiceService.getMicrophoneTroubleshooting('DEVICE_IN_USE');
      
      expect(tips).toContain('Fermez les autres applications utilisant le microphone');
      expect(tips).toContain('Vérifiez qu\'aucun autre onglet n\'utilise le microphone');
      expect(tips.length).toBeGreaterThan(3);
    });

    test('should provide API not supported troubleshooting', () => {
      const tips = voiceService.getMicrophoneTroubleshooting('API_NOT_SUPPORTED');
      
      expect(tips).toContain('Utilisez un navigateur moderne (Chrome, Firefox, Safari, Edge)');
      expect(tips).toContain('Mettez à jour votre navigateur');
      expect(tips.length).toBeGreaterThan(3);
    });

    test('should provide common tips for unknown errors', () => {
      const tips = voiceService.getMicrophoneTroubleshooting('UNKNOWN_ERROR');
      
      expect(tips).toContain('Vérifiez que votre microphone est correctement connecté');
      expect(tips).toContain('Redémarrez votre navigateur');
      expect(tips.length).toBeGreaterThan(2);
    });
  });

  describe('Audio Analyser', () => {
    test('should create audio analyser successfully', async () => {
      const mockStream = {
        getTracks: jest.fn(() => [{ stop: jest.fn() }])
      };

      navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);

      const analyser = await voiceService.getAudioAnalyser('test-device-id');

      expect(analyser).toBeDefined();
      expect(analyser.stream).toBe(mockStream);
      expect(analyser.getLevel).toBeInstanceOf(Function);
      expect(analyser.stop).toBeInstanceOf(Function);
      expect(navigator.mediaDevices.getUserMedia).toHaveBeenCalledWith({
        audio: {
          deviceId: { exact: 'test-device-id' },
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true
        }
      });
    });

    test('should handle audio analyser creation failure', async () => {
      const error = new Error('Access denied');
      navigator.mediaDevices.getUserMedia.mockRejectedValue(error);

      await expect(voiceService.getAudioAnalyser()).rejects.toThrow('Impossible d\'accéder au microphone');
    });
  });
});

// Tests d'intégration pour les composants
describe('Integration Tests', () => {
  test('should handle complete microphone diagnostic flow', async () => {
    // Mock successful flow
    const mockStream = {
      getAudioTracks: jest.fn(() => [{
        label: 'Test Microphone',
        getSettings: jest.fn(() => ({ deviceId: 'test-device-id' }))
      }]),
      getTracks: jest.fn(() => [{ stop: jest.fn() }])
    };

    navigator.mediaDevices.getUserMedia.mockResolvedValue(mockStream);
    navigator.mediaDevices.enumerateDevices.mockResolvedValue([
      { deviceId: 'mic1', kind: 'audioinput', label: 'Microphone 1' }
    ]);

    // Test permission request
    const permissionResult = await voiceService.requestMicrophonePermission();
    expect(permissionResult.success).toBe(true);

    // Test device enumeration
    const devicesResult = await voiceService.enumerateAudioDevices();
    expect(devicesResult.success).toBe(true);

    // Test microphone testing
    const testResult = await voiceService.testMicrophone('mic1');
    expect(testResult.success).toBe(true);
  });
});
